﻿using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using PSafe.AM.Common;
using PSafe.AM.Models;
using PSafe.Common;
using PSafe.Core.Domains;
using PSafe.Core.Interface;
using PSafe.Core.SharedKernel;
using System;
using System.Collections.Generic;
using System.Linq;
using static PSafe.Common.CommonEnums;
using System.DirectoryServices.AccountManagement;
using Microsoft.Extensions.Configuration;
using PSafe.Common.UserEnums;
using Microsoft.Extensions.Logging;
using System.Diagnostics;

namespace PSafe.AM.Controllers
{
    [AuthorizeActionFilter(EPERMISSIONS_AM.UserManage)]
    public class UserCBCSsController : Controller
    {
        private readonly IUserCBCSRepository _userCBCSRepository;
        private readonly IUserRepository _userRepository;
        private readonly IRoleCBCSRepository _roleCBCSRepository;
        private readonly IUserInRoleCBCSRepository _userInRoleCBCSRepository;
        private readonly IMapper _mapper;
        private readonly IConfiguration _configuration;
        private readonly ILogger<UserCBCSsController> _logger;

        public UserCBCSsController(
            IRoleCBCSRepository roleCBCSRepository,
            IUserCBCSRepository userCBCSRepository,
            IUserRepository userRepository,
            IUserInRoleCBCSRepository userInRoleCBCSRepository,
            IMapper mapper,
            ILogger<UserCBCSsController> logger,
            IConfiguration configuration)
        {
            _roleCBCSRepository = roleCBCSRepository;
            _userCBCSRepository = userCBCSRepository;
            _userInRoleCBCSRepository = userInRoleCBCSRepository;
            _userRepository = userRepository;
           _mapper = mapper;
            _configuration = configuration;
            _logger = logger;
        }

        public ActionResult Index()
        {
            StatusQuery Notification;
            try
            {
                var listRole = _roleCBCSRepository.GetAll();

                var listDropBox = new List<DropDownList>();

                if (listRole != null)
                {
                    foreach (var item in listRole)
                    {
                        var dropDown = new DropDownList()
                        {
                            Id = item.Id,
                            Name = item.RoleName
                        };
                        listDropBox.Add(dropDown);
                    }
                }

                ViewBag.ListRole = listDropBox;

                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Users/Index: " + ex.Message);

                Notification = new StatusQuery("error", "", "Xem danh sách thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;
            }

            return View();
        }

        public JsonResult GetIndex()
        {
            try
            {
                List<UserCBCSModel> _listUserModel = new List<UserCBCSModel>();
                try
                {
                    var _listUser = _userCBCSRepository.GetAll().ToList();

                    _listUserModel = _mapper.Map<List<UserCBCS>, List<UserCBCSModel>>(_listUser);

                    foreach (var item in _listUserModel)
                    {
                        var userInRole = _userInRoleCBCSRepository.Get(x => x.UserId == item.Id, null, "RoleCBCS").ToList();

                        foreach (var role in userInRole)
                        {
                            if (string.IsNullOrWhiteSpace(item.ListRole))
                                item.ListRole += role.RoleCBCS.RoleName;
                            else
                                item.ListRole += ", " + role.RoleCBCS.RoleName;
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("UserCBCS/Index: " + ex.Message);
                }

                return Json(_listUserModel);
            }
            catch (Exception ex)
            {
                _logger.LogError("Devices/GetIndex: " + ex.Message);

                return Json(null);
            }
        }

        [HttpGet]
        public JsonResult Filter(string Role)
        {
            try
            {
                if (Role != null)
                {
                    List<int> myRoleList = Role.Split(',').Select(s => int.Parse(s)).ToList();

                    var userInRoles = _userInRoleCBCSRepository.GetBy(x => myRoleList.Contains(x.RoleId)).Select(x => x.UserId).ToList();

                    if (userInRoles != null)
                    {
                        List<UserCBCS> users = _userCBCSRepository.GetBy(x => userInRoles.Contains(x.Id)).ToList();

                        var _listUserModel = _mapper.Map<List<UserCBCS>, List<UserCBCSModel>>(users);

                        foreach (var item in _listUserModel)
                        {
                            var userInRole = _userInRoleCBCSRepository.Get(x => x.UserId == item.Id, null, "RoleCBCS").ToList();

                            foreach (var role in userInRole)
                            {
                                if (string.IsNullOrWhiteSpace(item.ListRole))
                                    item.ListRole += role.RoleCBCS.RoleName;
                                else
                                    item.ListRole += ", " + role.RoleCBCS.RoleName;
                            }
                        }

                        return Json(_listUserModel);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Devices/GetIndex: " + ex.Message);
            }
            return Json(null);
        }

        // GET: /user/Details/
        public ActionResult Details(int id)
        {
            try
            {
                var _user = _userCBCSRepository.GetById(id);

                var _userModel = _mapper.Map<UserCBCS, UserCBCSModel>(_user);

                if (_userModel != null)
                {
                    if (TempDataHelper.Get<StatusQuery>(TempData, "Notification") is StatusQuery Notification)
                    {
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;
                    }

                    try
                    {
                        ViewBag.CreatedUser = _userCBCSRepository.GetById(_userModel.CreatedUser).UserName;
                        ViewBag.UpdatedUser = _userCBCSRepository.GetById(_userModel.UpdatedUser).UserName;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError("UserCBCS/Details: " + ex.Message);
                        Console.WriteLine(ex.Message);
                    }
                    return View(_userModel);
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại!"));

                    return RedirectToAction("Index");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("UserCBCS/Details: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại!"));

                return RedirectToAction("Index");
            }
        }

        // GET: /user/Create
        public ActionResult Create()
        {
            UserCBCSModel user = new UserCBCSModel
            {
                Actived = true
            };

            return View(user);
        }

        // POST: /user/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create([Bind("UserName, Password, Phone, Email, Actived, Name")] UserCBCSModel userModel)
        {
            StatusQuery Notification;

            if (ModelState.IsValid)
            {
                try
                {
                    var _systemUser = GetSesson();

                    if (_systemUser == null)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                        return RedirectToAction("Logout", "Security");
                    }

                    var obj = _userCBCSRepository.GetAll().FirstOrDefault(item => item.UserName.ToLower() == userModel.UserName.ToLower());
                    if (obj != null)
                    {
                        Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;
                        ModelState.AddModelError("UserName", "Tên người dùng đã tồn tại");

                        return View(userModel);
                    }

                    HashEncoding hashEncoding = new HashEncoding();

                    userModel.CreatedDate = DateTime.Now;
                    userModel.CreatedUser = _systemUser.Id;
                    userModel.UpdatedDate = DateTime.Now;
                    userModel.UpdatedUser = _systemUser.Id;
                    userModel.Password = hashEncoding.HashPassword(userModel.Password, userModel.UserName);
                    var _user = _mapper.Map<UserCBCSModel, UserCBCS>(userModel);
                 


                    _userCBCSRepository.Insert(_user);

                    var addStatus = _userCBCSRepository.SaveChanges();

                    if (addStatus > 0)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Thêm mới thành công"));

                        return RedirectToAction("Index");
                    }
                    else
                    {
                        Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        return View(userModel);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("UserCBCS/Create: " + ex.Message);

                    Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return View(userModel);
                }
            }
            Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;

            return View(userModel);
        }

        // GET: /user/Edit/5
        public ActionResult Edit(int id)
        {
            try
            {
                var _systemUser = GetSesson();

                if (_systemUser == null)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                    return RedirectToAction("Logout", "Security");
                }

                var _user = _userCBCSRepository.GetById(id);

                var _userModel = _mapper.Map<UserCBCS, UserCBCSModel>(_user);

                ViewBag.MySelf = _systemUser.Id == id;

                if (_userModel != null)
                {
                    return View(_userModel);
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                    return RedirectToAction("Index");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("UserCBCS/Edit: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                return RedirectToAction("Index");
            }
        }

        // POST: /user/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit([Bind("Id, UserName, Phone, Email, Actived, Name")] UserCBCSModel userModel)
        {
            StatusQuery Notification;
            if (ModelState.IsValid)
            {
                try
                {
                    var _systemUser = GetSesson();

                    if (_systemUser == null)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                        return RedirectToAction("Logout", "Security");
                    }

                    var user = _userCBCSRepository.GetById(userModel.Id);

                    var _userTemp = _mapper.Map<UserCBCS, UserCBCSModel>(user);
                    var UserOld = _mapper.Map<UserCBCSModel, UserCBCS>(_userTemp);

                    // kiểm tra tên người dùng có phải là người đang online hay không?
                    if (_systemUser.UserName.ToLower() == user.UserName.ToLower())
                    {
                        var obj = _userCBCSRepository.Get(item => item.UserName.ToLower() == userModel.UserName.ToLower()).FirstOrDefault();

                        // Tên người dùng tồn tại nhưng không phải user hiện tại
                        if (obj == null || user.UserName == userModel.UserName)
                        {
                            user.UserName = userModel.UserName;
                            user.UpdatedDate = DateTime.Now;
                            user.UpdatedBy = _systemUser.Id;
                            user.Phone = userModel.Phone;
                            user.Email = userModel.Email;
                            user.Actived = userModel.Actived;

                            _userCBCSRepository.Update(user);

                            var updateStatusNow = _userCBCSRepository.SaveChanges();

                            if (updateStatusNow > 0)
                            {
                                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Sửa thành công, vui lòng đăng nhập lại"));
                                return RedirectToAction("Logout", "Security");
                            }
                            else
                            {
                                Notification = new StatusQuery("error", "", "Sửa thất bại");
                                ViewBag.Status = Notification.Status;
                                ViewBag.Value = Notification.Value;
                                ViewBag.Type = Notification.Type;
                                return View(userModel);
                            }
                        }
                        else
                        {

                            Notification = new StatusQuery("warning", "", "Tên người dùng đã tồn tại");
                            ViewBag.Status = Notification.Status;
                            ViewBag.Value = Notification.Value;
                            ViewBag.Type = Notification.Type;
                            ModelState.AddModelError("UserName", "Vui lòng nhập tên người dùng khác");

                            return View(userModel);
                        }
                    }
                    else
                    {
                        var obj = _userCBCSRepository.Get(item => item.UserName.ToLower() == userModel.UserName.ToLower()).FirstOrDefault();

                        // Tên người dùng chưa tồn tại
                        if (obj == null || obj.UserName.ToLower() == user.UserName.ToLower())
                        {
                            user.UserName = userModel.UserName;
                            user.UpdatedDate = DateTime.Now;
                            user.UpdatedBy = _systemUser.Id;
                            user.Phone = userModel.Phone;
                            user.Email = userModel.Email;
                            user.Actived = userModel.Actived;

                            _userCBCSRepository.Update(user);

                            var updateStatusNow = _userCBCSRepository.SaveChanges();

                            if (updateStatusNow > 0)
                            {
                                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Sửa thành công"));

                                return RedirectToAction("Index");
                            }
                            else
                            {
                                Notification = new StatusQuery("error", "", "Sửa thất bại");
                                ViewBag.Status = Notification.Status;
                                ViewBag.Value = Notification.Value;
                                ViewBag.Type = Notification.Type;

                                return View(userModel);
                            }
                        }
                        else
                        {
                            Notification = new StatusQuery("warning", "", "Tên người dùng đã tồn tại");
                            ViewBag.Status = Notification.Status;
                            ViewBag.Value = Notification.Value;
                            ViewBag.Type = Notification.Type;

                            ModelState.AddModelError("UserName", "Vui lòng nhập tên người dùng khác");

                            return View(userModel);
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("UserCBCS/Edit: " + ex.Message);

                    Notification = new StatusQuery("error", "", "Sửa thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return View(userModel);
                }
            }

            Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;

            return View(userModel);
        }

        // GET: /user/Edit/5
        public ActionResult ResetPassword(int id)
        {
            StatusQuery Notification;
            try
            {
                var systemUserSession = GetSesson();

                if (systemUserSession == null)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                    return RedirectToAction("Logout", "Security");
                }

                var _user = _userCBCSRepository.GetById(id);

                ChangePassword _changePassword = new ChangePassword();

                if (_user != null && systemUserSession.Id == id)
                {
                    return RedirectToAction("ChangePassword", "Users", new { id });
                }
                else if (_user != null && systemUserSession.Id != id)
                {
                    _changePassword.UserName = _user.UserName;
                    return View(_changePassword);
                }

                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("UserCBCS/ResetPassword: " + ex.Message);

                Notification = new StatusQuery("error", "", "Đổi mật khẩu thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;
            }
            return View();
        }

        // POST: /user/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult ResetPassword([Bind("NewPassword, Repeatpassword, UserName")] ChangePassword _changePassword)
        {
            StatusQuery Notification;
            if (ModelState.IsValid)
            {
                try
                {
                    var user = _userCBCSRepository.Get(u => u.UserName == _changePassword.UserName).SingleOrDefault();

                    var _userTemp = _mapper.Map<UserCBCS, UserCBCSModel>(user);
                    var UserOld = _mapper.Map<UserCBCSModel, UserCBCS>(_userTemp);

                    user.Password = BCrypt.Net.BCrypt.HashPassword(_changePassword.NewPassword);

                    _userCBCSRepository.Update(user);

                    var updateStatus = _userCBCSRepository.SaveChanges();

                    if (updateStatus > 0)
                    {
                        var systemUserSession = GetSesson();

                        if (systemUserSession == null)
                        {
                            TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                            return RedirectToAction("Logout", "Security");
                        }
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Đổi mật khẩu thành công"));

                        return RedirectToAction("Index");
                    }
                    else
                    {
                        Notification = new StatusQuery("error", "", "Đổi mật khẩu thất bại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        return View(_changePassword);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("UserCBCS/ResetPassword: " + ex.Message);

                    Notification = new StatusQuery("error", "", "Đổi mật khẩu thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return View(_changePassword);
                }
            }

            Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;

            return View(_changePassword);
        }

        // GET: /user/Edit/5
        public ActionResult ChangePassword(int id)
        {
            StatusQuery Notification;
            try
            {
                var systemUserSession = GetSesson();

                if (systemUserSession == null)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                    return RedirectToAction("Logout", "Security");
                }

                var _user = _userCBCSRepository.GetById(id);

                ChangePassword _changePassword = new ChangePassword();

                if (_user != null)
                {
                    _changePassword.UserName = _user.UserName;
                    return View(_changePassword);
                }

                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("UserCBCS/ChangePassword: " + ex.Message);

                Notification = new StatusQuery("error", "", "Đổi mật khẩu thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;
            }
            return View();
        }

        // POST: /user/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult ChangePassword([Bind("NewPassword, Repeatpassword, UserName", "OldPassword")] ChangePassword _changePassword)
        {
            StatusQuery Notification;
            if (ModelState.IsValid)
            {
                try
                {
                    var obj = _userCBCSRepository.Get(item => item.UserName.ToLower() == _changePassword.UserName.ToLower()).FirstOrDefault();

                    if (BCrypt.Net.BCrypt.Verify(_changePassword.OldPassword, obj.Password) != true)
                    {
                        Notification = new StatusQuery("error", "", "Mật khẩu cũ không đúng, vui lòng kiểm tra lại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        return View(_changePassword);
                    }

                    var _userTemp = _mapper.Map<UserCBCS, UserCBCSModel>(obj);
                    var UserOld = _mapper.Map<UserCBCSModel, UserCBCS>(_userTemp);

                    obj.Password = BCrypt.Net.BCrypt.HashPassword(_changePassword.NewPassword);

                    _userCBCSRepository.Update(obj);

                    var updateStatus = _userCBCSRepository.SaveChanges();

                    if (updateStatus > 0)
                    {
                        var systemUserSession = GetSesson();

                        if (systemUserSession == null)
                        {
                            TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                            return RedirectToAction("Logout", "Security");
                        }
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Đổi mật khẩu thành công"));

                        return RedirectToAction("Index", "Home");
                    }
                    else
                    {
                        Notification = new StatusQuery("error", "", "Đổi mật khẩu thất bại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        return View(_changePassword);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("UserCBCS/ChangePassword: " + ex.Message);

                    Notification = new StatusQuery("error", "", "Đổi mật khẩu thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return View(_changePassword);
                }
            }

            Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;

            return View(_changePassword);
        }

        // GET: /user/Delete/5
        public ActionResult Delete(int id)
        {
            StatusQuery Notification;
            try
            {
                var userSession = GetSesson();

                if (userSession == null)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                    return RedirectToAction("Logout", "Security");
                }

                var _user = _userCBCSRepository.GetById(id);

                if (userSession.Id == _user.Id)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("warning", "", "Không thể xóa người dùng đang hoạt động"));

                    return RedirectToAction("Index");
                }

                var _userModel = _mapper.Map<UserCBCS, UserCBCSModel>(_user);

                try
                {
                    ViewBag.CreatedUser = _userCBCSRepository.GetById(_userModel.CreatedUser).UserName;
                    ViewBag.UpdatedUser = _userCBCSRepository.GetById(_userModel.UpdatedUser).UserName;
                }
                catch (Exception ex)
                {
                    _logger.LogError("UserCBCS/Delete: " + ex.Message);
                    Console.WriteLine(ex.Message);
                }

                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }

                return View(_userModel);
            }
            catch (Exception ex)
            {
                _logger.LogError("UserCBCS/Delete: " + ex.Message);

                Notification = new StatusQuery("warning", "", "Xóa thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;

                return RedirectToAction("Index");
            }
        }

        // POST: /Roles/Delete/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int id)
        {
            try
            {
                var userSession = GetSesson();

                if (userSession == null)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                    return RedirectToAction("Logout", "Security");
                }

                var user = _userCBCSRepository.GetById(id);

                _userCBCSRepository.Delete(user);

                var deleteStatus = _userCBCSRepository.SaveChanges();

                if (deleteStatus > 0)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Xóa thành công"));
                    return RedirectToAction("Index");
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));
                    return RedirectToAction("Delete", "Users", new { id });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("UserCBCS/DeleteConfirmed: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));
                return RedirectToAction("Delete", "Users", new { id });
            }
        }

        public bool FnImp(string userNameAd)
        {
            try
            {
                var AccountAD = Utils.DecodePassword(_configuration.GetSection("AccountAD").Value, Utils.EncodeType.SHA_256);
                var PasswordAD = Utils.DecodePassword(_configuration.GetSection("PasswordAD").Value, Utils.EncodeType.SHA_256);

                var listDomain = _configuration.GetSection("DomainAD").Value.Split(',');
                foreach (var item in listDomain)
                {
                    var _contextTemp = GetContext(item);
                    if (_contextTemp != null)
                    {
                        using (var context = new PrincipalContext(ContextType.Domain, item, AccountAD, PasswordAD))
                        {
                            using (var searcher = new PrincipalSearcher(new UserPrincipal(context) { Enabled = true }))
                            {
                                var UserAD = searcher.FindAll().FirstOrDefault(p => p.SamAccountName.ToLower() == userNameAd.ToLower());

                                if (UserAD != null)
                                {
                                    return true;
                                }
                                else
                                {
                                    return false;
                                }
                            }
                        }
                    }
                }
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError("UserCBCS/FnImp: " + ex.Message);

                return false;
            }
        }

        [HttpGet]
        public JsonResult GetFullNameAD(string UserNameAD)
        {
            try
            {
                var listDomain = _configuration.GetSection("DomainAD").Value;
                var AccountAD = Utils.DecodePassword(_configuration.GetSection("AccountAD").Value, Utils.EncodeType.SHA_256);
                var PasswordAD = Utils.DecodePassword(_configuration.GetSection("PasswordAD").Value, Utils.EncodeType.SHA_256);

                var result = Utils.GetFullNameUserAD(UserNameAD, listDomain, AccountAD, PasswordAD);

                if (result != null)
                {
                    if (result.Name == string.Empty)
                    {
                        result.Name = result.SAMAccountName;
                    }
                }

                return new JsonResult(result);
            }
            catch (Exception ex)
            {
                _logger.LogError("UserCBCS/GetFullNameAD: " + ex.Message);

                return null;
            }
        }

        public PrincipalContext GetContext(string domain)
        {
            try
            {
                PrincipalContext context = new PrincipalContext(ContextType.Domain, domain);
                return context;
            }
            catch (Exception ex)
            {
                _logger.LogError("UserCBCS/GetContext: " + ex.Message);

                return null;
            }
        }
        public bool FnValidateUser(PrincipalContext context, string username, string password)
        {
            bool validation;
            try
            {
                validation = context.ValidateCredentials(username, password);
            }
            catch (Exception ex)
            {
                _logger.LogError("UserCBCS/FnValidateUser: " + ex.Message);
                validation = false;
            }
            return validation;
        }

        public User GetSesson()
        {
            var SessionUserSystem = HttpContext.Session.GetComplexData<User>("SessionUserSystemId_" + HttpContext.Session.GetString("SessionUserSystemId"));
            if (SessionUserSystem != null)
            {
                var _user = _userRepository.GetById(SessionUserSystem.Id);

                if (_user != null)
                {
                    if (!_user.Actived)
                    {
                        HttpContext.Session.Clear();
                        return null;
                    }
                    return _user;
                }
            }
            return null;
        }

        [NonAction]
        private SelectList ToSelectList(List<DropDownList> DropDownList)
        {
            List<SelectListItem> list = new List<SelectListItem>();

            foreach (var item in DropDownList)
            {
                list.Add(new SelectListItem()
                {
                    Text = item.Name,
                    Value = item.Id.ToString()
                });
            }

            return new SelectList(list, "Value", "Text");
        }

        [NonAction]
        private SelectList ToSelectList(List<DropDownList2> DropDownList)
        {
            List<SelectListItem> list = new List<SelectListItem>();

            foreach (var item in DropDownList)
            {
                list.Add(new SelectListItem()
                {
                    Text = item.Name,
                    Value = item.Id
                });
            }

            return new SelectList(list, "Value", "Text");
        }
    }
}