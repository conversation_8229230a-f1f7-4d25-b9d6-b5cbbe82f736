﻿@model IEnumerable<PSafe.AM.Models.DeviceModel>

@{
    ViewBag.Title = "Thùng rác";
}

@section Styles{
    <environment names="Development,Staging,Production">
        <link rel="stylesheet" href="~/lib/dataTables/datatables.min.css" />
        <link rel="stylesheet" href="~/lib/toastr/toastr.min.css" />
    </environment>

<style type="text/css">
    .selected {
       background-color: wheat !important;
    }

    .removeBtn {
        background-color: #f15656 !important;
        color: white !important;
    }
</style>
}

<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-lg-12">
            <div class="ibox">
                <div class="ibox-content table-responsive">
                    <table class="table table-striped table-bordered table-hover dataTables-list" id="example">
                    </table>
                    @using (Html.BeginForm(null, null, FormMethod.Post, new { id = "__AjaxAntiForgeryForm" }))
                    {
                        @Html.AntiForgeryToken()
                    }
                </div>
                </div>
        </div>
    </div>
</div>

@section Scripts {
    <environment names="Development,Staging,Production">
        <script src="~/lib/dataTables/datatables.min.js"></script>
        <script src="~/lib/dataTables/dataTables.bootstrap4.min.js"></script>
        <script src="~/lib/toastr/toastr.js"></script>
        <script src="~/lib/toastr/toastr.min.js"></script>
    </environment>

    @if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "warning")
    {
        <script type="text/javascript">
            toastr.warning('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "error")
    {
        <script type="text/javascript">
            toastr.error('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "success")
    {
        <script type="text/javascript">
            toastr.success('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }

<script type="text/javascript">
        $(document).ready(function () {
            var table = $('#example').DataTable({
                'select': {
                    'style': 'multi'
                },
                "serverSide": false,
                "ajax": {
                    "url": "@Url.Action("GetRecycleBin", "Devices")",
                    "dataType": 'json',
                    "contentType": "application/json; charset=utf-8",
                    "type": "GET",
                    "dataSrc": ''
                },
                "autoWidth": false,
                "columns": [
                    { "title": "@PSafe.AM.Resources.Resource__device.DEVICENAME", "data": "deviceName", "searchable": true },
                    { "title": "@PSafe.AM.Resources.Resource__device.IP", "data": "ip", "searchable": true },
                    { "title": "@PSafe.AM.Resources.Resource__device.TYPEOFSINGNAL", "data": "typeOfSingnal", "searchable": true },
                    { "title": "@PSafe.AM.Resources.Resource__typeOfDevice.TYPENAME", "data": "typeOfDevice", "searchable": true },
                    { "title": "@PSafe.AM.Resources.Resource__device.SERIALNUMBER", "data": "serialNumber", "searchable": true },
                    { "title": "@PSafe.AM.Resources.Resource__device.MacAddress", "data": "macAddress", "searchable": true },
                    { "title": "@PSafe.AM.Resources.Resource__device.DriverName", "data": "driverName", "searchable": true },
                    { "title": "Khu vực", "data": "areaName", "searchable": true },
                    { "title": "Vị trí", "data": "locationName", "searchable": true },
                ],
                "oLanguage": {
                    "sProcessing": "Đang xử lý...",
                    "sLengthMenu": "Xem _MENU_ mục",
                    "sZeroRecords": "Không tìm thấy dòng nào phù hợp",
                    "sInfo": "Đang xem _START_ đến _END_ trong tổng số _TOTAL_ mục",
                    "sInfoEmpty": "Đang xem 0 đến 0 trong tổng số 0 mục",
                    "sInfoFiltered": "(được lọc từ _MAX_ mục)",
                    "sInfoPostFix": "",
                    "sLoadingRecords": '<img src="/images/loadding-icon.gif" width="30%"></span>',
                    "sSearch": "Tìm:",
                    "sUrl": "",
                    "oPaginate": {
                        "sFirst": "Đầu",
                        "sPrevious": "Trước",
                        "sNext": "Tiếp",
                        "sLast": "Cuối"
                    }
                },
                pageLength: pageLength_dataTable(),
                dom: '<"html5buttons"B>lTfgitp',
                select: true,
                buttons: [
                    {
                        text: 'Chọn tất cả',
                        action: function (e, dt, node, config) {
                            table.rows().every(function (index) {
                                var row = table.row(index);
                                $(row.node()).addClass('selected');
                            });
                        }
                    },
                    {
                        text: 'Bỏ tất cả',
                        action: function (e, dt, node, config) {
                            table.rows().every(function (index) {
                                var row = table.row(index);
                                $(row.node()).removeClass('selected');
                            });
                        }
                    },
                    {
                        text: 'Xóa',
                        className: "removeBtn",
                        action: function (e, dt, node, config) {
                            arrIDCamera = [];
                            var data = table.rows('.selected').data();
                            data.each(function (value, index) {
                                arrIDCamera.push(value.id);
                            });

                            if (arrIDCamera.length === 0) {
                                toastr.warning("Vui lòng chọn thiết bị cần xóa", "Cảnh báo");
                                return;
                            }

                            var form = $('#__AjaxAntiForgeryForm');
                            var token = $('input[name="__RequestVerificationToken"]', form).val();

                            $.ajax({
                                url: '@Url.Action("DeleteList", "Devices")',
                                type: 'POST',
                                data: { listId: arrIDCamera, __RequestVerificationToken: token },
                                dataType: 'json',
                                success: function (response) {
                                    if (response.result == 0) {
                                        toastr.success(response.message, "Thông báo");
                                        table.rows('.selected').remove().draw();
                                    } else {
                                        toastr.error("Xóa thiết bị thất bại", "Thông báo");
                                    }
                                }
                            });
                        }
                    }
                ]
            });

            $('#example tbody').on('click', 'tr', function () {
                $(this).toggleClass('selected');
            });

            $('#button').click(function () {

            });

            $('#selectAll').click(function () {
                table.rows().every(function (index) {
                    var row = table.row(index);
                    $(row.node()).addClass('selected');
                });
            });

            $('#removeSelectAll').click(function () {
                table.rows().every(function (index) {
                    var row = table.row(index);
                    $(row.node()).removeClass('selected');
                });
            });
        });
</script>
}