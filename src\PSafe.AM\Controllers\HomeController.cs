﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using PSafe.AM.Common;
using PSafe.AM.Models;
using PSafe.Common;
using PSafe.Core.Interface;

namespace PSafe.AM.Controllers
{
    [AuthorizeActionFilter]
    public class HomeController : Controller
    {
        private readonly ILocationRepository _locationRepository;
        private readonly IAreaRepository _areaRepository;
        private readonly IDeviceRepository _deviceRepository;
        private readonly ITypeOfDeviceRepository _typeOfDeviceRepository;
        private readonly IVehicleBannedListRepository _vehicleBannedListRepository;
        private readonly ILogger<HomeController> _logger; // khai bao o day, controller nao thi dat no vao <>

        public HomeController(ILocationRepository locationRepository, IAreaRepository areaRepository, IDeviceRepository deviceRepository,
            ILogger<HomeController> logger, ITypeOfDeviceRepository typeOfDeviceRepository, IVehicleBannedListRepository vehicleBannedListRepository)
        {
            _locationRepository = locationRepository;
            _areaRepository = areaRepository;
            _deviceRepository = deviceRepository;
            _logger = logger; // gan o day
            _typeOfDeviceRepository = typeOfDeviceRepository;
            _vehicleBannedListRepository = vehicleBannedListRepository;
        }

        public IActionResult Index()
        {
            StatusQuery Notification;
            HomeModel homeModel = new HomeModel
            {
                ListTypeOfDevice = new List<DropDown>()
            };

            try
            {
                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                var listDevice = _deviceRepository.GetAll().ToList();
                var listTypeOfDevice = _typeOfDeviceRepository.GetAll().ToList();

                //--------------------
                List<DropDown> ListTypeOfDevice = new List<DropDown>();
                foreach (var item in listTypeOfDevice)
                {
                    var getCount = listDevice.Where(p => p.TypeOfDeviceId == item.Id).Count();

                    DropDown dropDownList = new DropDown()
                    {
                        Guid = Guid.NewGuid(),
                        Value = getCount,
                        Name = item.TypeName.NonUnicode()
                    };

                    ListTypeOfDevice.Add(dropDownList); 
                }

                homeModel.ListTypeOfDevice = ListTypeOfDevice;

                ViewBag.CountLocations = _locationRepository.GetAll().Count();
                ViewBag.CountAreas = _areaRepository.GetAll().Count();

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }
            }
            catch(Exception ex)
            {
                _logger.LogError("home/Index: " + ex.Message);

                Notification = new StatusQuery("warning", "", "Mất kết nối máy chủ, vui lòng kiểm tra lại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;
            }
            
            return View(homeModel);
        }

        public IActionResult Privacy()
        {
            return View();
        }

        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public IActionResult Error()
        {
            return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
        }

    }
}
