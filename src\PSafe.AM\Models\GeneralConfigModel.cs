﻿using PSafe.AM.Common;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using Microsoft.AspNetCore.Mvc.Rendering;

namespace PSafe.AM.Models
{
    public class GeneralConfigModel
    {
        [Range(0, 2147483647, ErrorMessage = "Vui lòng chọn")]
        [Display(Name = "TimeSendSMS", ResourceType = typeof(Resources.Resource_GeneralConfig))]
        public int TimeSendSMS { get; set; }

        [Range(0, 2147483647, ErrorMessage = "Vui lòng chọn")]
        [Display(Name = "TimeActionReplay", ResourceType = typeof(Resources.Resource_GeneralConfig))]
        public int TimeActionReplay { get; set; }

        [Range(0, 2147483647, ErrorMessage = "Vui lòng chọn")]
        [Display(Name = "TimeCaptureVideo", ResourceType = typeof(Resources.Resource_GeneralConfig))]
        public int TimeCaptureVideo { get; set; }

        [Range(0, 2147483647, ErrorMessage = "Vui lòng chọn")]
        [Display(Name = "MaxSearchDevice", ResourceType = typeof(Resources.Resource_GeneralConfig))]
        public int MaxSearchDevice { get; set; }

        [Display(Name = "MinSOSLevel", ResourceType = typeof(Resources.Resource_GeneralConfig))]
        public string MinSOSLevel { get; set; }

        [Display(Name = "ScreenAlarm", ResourceType = typeof(Resources.Resource_GeneralConfig))]
        public string ScreenAlarm { get; set; }

        [Display(Name = "ScreenProcessEvent", ResourceType = typeof(Resources.Resource_GeneralConfig))]
        public string ScreenProcessEvent { get; set; }

        [Required(ErrorMessage = "Vui lòng nhập Email")]
        [StringLength(255, MinimumLength = 3, ErrorMessage = "Độ dài từ 3-255 ký tự")]
        [Display(Name = "Email", ResourceType = typeof(Resources.Resource_GeneralConfig))]
        public string Email { get; set; }

        [DataType(DataType.Password)]
        [Display(Name = "Password", ResourceType = typeof(Resources.Resource_GeneralConfig))]
        public string Password { get; set; }

        [Display(Name = "UserName", ResourceType = typeof(Resources.Resource_GeneralConfig))]
        public string UserName { get; set; }

        [Display(Name = "ViolateFlag", ResourceType = typeof(Resources.Resource_GeneralConfig))]
        public string ViolateFlag { get; set; }

        [Display(Name = "SiteSetViolation", ResourceType = typeof(Resources.Resource_GeneralConfig))]
        public string SiteSetViolation { get; set; }

        //-------------------------------------------------

        [Range(0, 2147483647, ErrorMessage = "Vui lòng chọn")]
        [Display(Name = "ConnectionLoss", ResourceType = typeof(Resources.Resource_GeneralConfig))]
        public int ConnectionLossLevel { get; set; }

        [Range(0, 2147483647, ErrorMessage = "Vui lòng chọn")]
        [Display(Name = "FireAlarm", ResourceType = typeof(Resources.Resource_GeneralConfig))]
        public int FireAlarmLevel { get; set; }

        [Range(0, 2147483647, ErrorMessage = "Vui lòng chọn")]
        [Display(Name = "CrossLine", ResourceType = typeof(Resources.Resource_GeneralConfig))]
        public int CrossLineLevel { get; set; }

        [Range(0, 2147483647, ErrorMessage = "Vui lòng chọn")]
        [Display(Name = "AreaCrowd", ResourceType = typeof(Resources.Resource_GeneralConfig))]
        public int AreaCrowdLevel { get; set; }

        [Range(0, 2147483647, ErrorMessage = "Vui lòng chọn")]
        [Display(Name = "EnterArea", ResourceType = typeof(Resources.Resource_GeneralConfig))]
        public int EnterAreaLevel { get; set; }

        [Range(0, 2147483647, ErrorMessage = "Vui lòng chọn")]
        [Display(Name = "BlackList", ResourceType = typeof(Resources.Resource_GeneralConfig))]
        public int BlackListLevel { get; set; }

        [Range(0, 2147483647, ErrorMessage = "Vui lòng chọn")]
        [Display(Name = "BannedList", ResourceType = typeof(Resources.Resource_GeneralConfig))]
        public int BannedListLevel { get; set; }

        [Range(0, 2147483647, ErrorMessage = "Vui lòng chọn")]
        [Display(Name = "BannedVehicle", ResourceType = typeof(Resources.Resource_GeneralConfig))]
        public int BannedVehicleLevel { get; set; }

        [Range(0, 2147483647, ErrorMessage = "Vui lòng chọn")]
        [Display(Name = "BanVehicleLevel", ResourceType = typeof(Resources.Resource_GeneralConfig))]
        public int BanVehicleLevel { get; set; }

        [Range(0, 2147483647, ErrorMessage = "Vui lòng chọn")]
        [Display(Name = "CancelBanVehicleLevel", ResourceType = typeof(Resources.Resource_GeneralConfig))]
        public int CancelBanVehicleLevel { get; set; }

        [Display(Name = "TimeCustomerInPort", ResourceType = typeof(Resources.Resource_GeneralConfig))]
        public string TimeCustomerInPort { get; set; }

        [Display(Name = "TimeCustomerExpriedInPort", ResourceType = typeof(Resources.Resource_GeneralConfig))]
        public string TimeCustomerExpriedInPort { get; set; }

        [Display(Name = "TrafficViolationLevel", ResourceType = typeof(Resources.Resource_GeneralConfig))]
        public string TrafficViolationLevel { get; set; }

        //Zalo
        public int ConnectionLossLevelZalo { get; set; }

        public int FireAlarmLevelZalo { get; set; }

        public int CrossLineLevelZalo { get; set; }

        public int AreaCrowdLevelZalo { get; set; }

        public int EnterAreaLevelZalo { get; set; }

        public int BlackListLevelZalo { get; set; }

        public int BannedListLevelZalo { get; set; }

        public int BannedVehicleLevelZalo { get; set; }

        public int BanVehicleLevelZalo { get; set; }

        public int CancelBanVehicleLevelZalo { get; set; }

        public int TimeCustomerInPortZalo { get; set; }
        public int TimeCustomerExpriedInPortZalo { get; set; }
        public int TrafficViolationZalo { get; set; }

        //SMS
        public int ConnectionLossLevelSMS { get; set; }

        public int FireAlarmLevelSMS { get; set; }

        public int CrossLineLevelSMS { get; set; }

        public int AreaCrowdLevelSMS { get; set; }

        public int EnterAreaLevelSMS { get; set; }

        public int BlackListLevelSMS { get; set; }

        public int BannedListLevelSMS { get; set; }

        public int BannedVehicleLevelSMS { get; set; }

        public int BanVehicleLevelSMS { get; set; }

        public int CancelBanVehicleLevelSMS { get; set; }
        public int TimeCustomerInPortSMS { get; set; }
        public int TimeCustomerExpriedInPortSMS { get; set; }
        public int TrafficViolationSMS { get; set; }

        //Email
        public int ConnectionLossLevelEmail { get; set; }

        public int FireAlarmLevelEmail { get; set; }

        public int CrossLineLevelEmail { get; set; }
        
        public int AreaCrowdLevelEmail { get; set; }
        
        public int EnterAreaLevelEmail { get; set; }
        
        public int BlackListLevelEmail { get; set; }
        
        public int BannedListLevelEmail { get; set; }
        
        public int BannedVehicleLevelEmail { get; set; }
        
        public int BanVehicleLevelEmail { get; set; }
        
        public int CancelBanVehicleLevelEmail { get; set; }
        public int TimeCustomerInPortEmail { get; set; }
        public int TimeCustomerExpriedInPortEmail { get; set; }
        public int TrafficViolationEmail { get; set; }

        public List<DropDownList> ListMinSOSLevelSelect { get; set; }
        public List<DropDownList> ListMinSOSLevelNoSelect { get; set; }
        public List<CheckBok> ListViolateSelect { get; set; }
        public List<CheckBok> ListViolateNoSelect { get; set; }

        public List<DropDownList2> ListSiteSetViolationSelect { get; set; }
        public List<DropDownList2> ListScreen { get; set; }
        public List<DropDownList2> ListSiteSetViolationNoSelect { get; set; }
        public SelectList ListsSelect { get; set; }
    }
}
