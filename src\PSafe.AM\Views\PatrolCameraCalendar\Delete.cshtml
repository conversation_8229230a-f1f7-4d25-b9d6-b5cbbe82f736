﻿@model PSafe.AM.Models.PatrolCameraCalendarModel

@{
    ViewBag.Title = "Chi tiết " + PSafe.AM.Resources.Resource.PatrolCameraCalendar;
}
<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-lg-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>@PSafe.AM.Resources.Resource.Detail @PSafe.AM.Resources.Resource.PatrolCameraCalendar</h5>
                    <div class="ibox-tools">
                        @Html.ActionLink(PSafe.AM.Resources.Resource.BackToList, "Index", "PatrolCameraCalendar", null, new { @class = "btn btn-primary btn-xs" })
                    </div>
                </div>
                <div class="ibox-content">

                    @using (Html.BeginForm("DeleteConfirmed", "PatrolCameraCalendar", new { id = Model.Id }, FormMethod.Post))
                    {
                        @Html.AntiForgeryToken()
                        @Html.HiddenFor(model => model.Id)
                        <div class="row">
                            <div class="col-lg-6">
                                <div class="form-group row">
                                    <div class="col-md-5">
                                        <strong>
                                            @Html.DisplayNameFor(model => model.PatrolCameraId)
                                        </strong>
                                    </div>
                                    <div class="col-md-7">
                                        <a href="@Url.Action("Details", "PatrolCamera")/@Model.PatrolCameraId">@Html.DisplayFor(model => model.PatrolCameraName)</a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group row">
                                    <div class="col-md-5">
                                        <strong>
                                            @Html.DisplayNameFor(model => model.RoleId)
                                        </strong>
                                    </div>
                                    <div class="col-md-7">
                                        @Html.DisplayFor(model => model.RoleName)
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-lg-6">
                                <div class="form-group row">
                                    <div class="col-md-5">
                                        <strong>
                                            Thời gian bắt đầu tuần tra
                                        </strong>
                                    </div>
                                    <div class="col-md-7">
                                        @Html.ValueFor(model => model.BeginTime, "{0:HH:mm}")
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group row">
                                    <div class="col-md-5">
                                        <strong>
                                            Thời gian kết thúc tuần tra
                                        </strong>
                                    </div>
                                    <div class="col-md-7">
                                        @Html.ValueFor(model => model.EndTime, "{0:HH:mm}")
                                    </div>
                                </div>
                            </div>
                        </div>
                        <input type="submit" value="@PSafe.AM.Resources.Resource.Delete" class="btn btn-danger" />
                        @Html.ActionLink(PSafe.AM.Resources.Resource.Edit, "Edit", "PatrolCameraCalendar", new { id = Model.Id }, new { @class = "btn btn-primary" })
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <environment names="Development,Staging,Production">
        <link rel="stylesheet" href="~/lib/toastr/toastr.min.css" />
    </environment>
}

@section Scripts {
    <environment names="Development,Staging,Production">
        <script src="~/lib/validate/jquery.validate.min.js"></script>
        <script src="~/lib/toastr/toastr.js"></script>
        <script src="~/lib/toastr/toastr.min.js"></script>
    </environment>

    @if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "warning")
    {
        <script type="text/javascript">
            toastr.warning('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "error")
    {
        <script type="text/javascript">
            toastr.error('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "success")
    {
        <script type="text/javascript">
            toastr.success('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
}