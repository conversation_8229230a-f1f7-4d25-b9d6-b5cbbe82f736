﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.ComponentModel.DataAnnotations;

namespace PSafe.AM.Models
{
    public class SiteModel
    {
        [Key]
        [StringLength(255, MinimumLength = 1, ErrorMessage = "Vui lòng nhập vào SiteID")]
        [Display(Name = "SiteId", ResourceType = typeof(Resources.Resource_site))]
        public string SiteId { get; set; }

        [StringLength(255, MinimumLength = 1, ErrorMessage = "Vui lòng nhập vào Cảng")]
        [Display(Name = "SiteName", ResourceType = typeof(Resources.Resource_site))]
        public string SiteName { get; set; }

        [DataType(DataType.Date)]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd}", ApplyFormatInEditMode = true)]
        [Display(Name = "CreatedDate", ResourceType = typeof(Resources.Resource_site))]
        public DateTime CreatedDate { get; set; }

        [Display(Name = "CreatedUser", ResourceType = typeof(Resources.Resource_site))]
        public int CreatedUser { get; set; }

        [DataType(DataType.Date)]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd}", ApplyFormatInEditMode = true)]
        [Display(Name = "UpdatedDate", ResourceType = typeof(Resources.Resource_site))]
        public DateTime UpdatedDate { get; set; }

        [Display(Name = "UpdatedUser", ResourceType = typeof(Resources.Resource_site))]
        public int UpdatedUser { get; set; }

        [Display(Name = "CreatedUserName", ResourceType = typeof(Resources.Resource_site))]
        public string CreatedUserName { get; set; }
        [Display(Name = "UpdateUserName", ResourceType = typeof(Resources.Resource_site))]
        public string UpdateUserName { get; set; }
    }
}