﻿@model PSafe.AM.Models.PatrolCameraCalendarModel
@{
    ViewBag.Title = "Hiệu chỉ " + PSafe.AM.Resources.Resource.PatrolCameraCalendar;
    var patrolCameraListItems = (List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem>)ViewBag.patrolCameraListItems;
    var roleListItems = (List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem>)ViewBag.roleListItems;
}

<environment names="Development">
    <script src="~/lib/jquery/dist/jquery.js"></script>
    <script src="~/lib/validate/jquery.validate.min.js"></script>
    <script src="~/js/script.js" asp-append-version="true"></script>
</environment>
<environment names="Staging,Production">
    <script src="~/lib/jquery/dist/jquery.js"></script>
    <script src="~/lib/validate/jquery.validate.min.js"></script>
    <script src="~/js/script.js" asp-append-version="true"></script>
</environment>

<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-lg-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>@PSafe.AM.Resources.Resource.Edit @PSafe.AM.Resources.Resource.PatrolCameraCalendar</h5>
                    <div class="ibox-tools">
                        @Html.ActionLink(PSafe.AM.Resources.Resource.BackToList, "Index", "PatrolCameraCalendar", null, new { @class = "btn btn-primary btn-xs" })
                    </div>
                </div>
                <div class="ibox-content">
                    @using (Html.BeginForm("Edit", "PatrolCameraCalendar", FormMethod.Post))
                    {
                        @Html.AntiForgeryToken()

                    <div class="form-horizontal">
                        @Html.ValidationSummary(true)

                        @Html.HiddenFor(model => model.Id)

                        <div class="row form-group">
                            @Html.LabelFor(model => model.PatrolCameraId, new { @class = "control-label col-md-2" })
                            <div class="col-md-8">
                                @Html.DropDownListFor(model => model.PatrolCameraId, patrolCameraListItems, "-- Chọn kịch bản tuần tra --", new { @class = "form-control" })
                                @Html.ValidationMessageFor(model => model.PatrolCameraId, null, new { @class = "text-danger" })
                            </div>
                        </div>
                        <div class="row form-group">
                            @Html.LabelFor(model => model.Dates, new { @class = "control-label col-md-2" })
                            <div class="col-md-8">
                                <div class="input-group date">
                                    @Html.TextBoxFor(model => model.DateStrings, "{0:dd/MM/yyyy}", new { @type = "text", @class = "form-control" })
                                    <span class="input-group-addon">
                                        <span class="glyphicon glyphicon-calendar"></span>
                                    </span>
                                </div>
                                @Html.ValidationMessageFor(model => model.Dates, null, new { @class = "text-danger" })
                            </div>
                        </div>
                        <div class="row form-group">
                            <div class="col-md-6">
                                <div class="row">
                                    @Html.LabelFor(model => model.BeginTime, new { @class = "control-label col-md-4" })
                                    <div class="col-md-8">
                                        <div class="input-group clockpicker">
                                            @Html.TextBoxFor(model => model.BeginTime, "{0:HH:mm}", new { @type = "text", @class = "form-control" })
                                            <span class="input-group-addon">
                                                <span class="glyphicon glyphicon-time"></span>
                                            </span>
                                        </div>
                                        @Html.ValidationMessageFor(model => model.BeginTime, null, new { @class = "text-danger" })
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="input-group clockpicker">
                                            @Html.TextBoxFor(model => model.EndTime, "{0:HH:mm}", new { @type = "text", @class = "form-control" })
                                            <span class="input-group-addon">
                                                <span class="glyphicon glyphicon-time"></span>
                                            </span>
                                        </div>
                                        @Html.ValidationMessageFor(model => model.EndTime, null, new { @class = "text-danger" })
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row form-group">
                            @Html.LabelFor(model => model.RoleId, new { @class = "control-label col-md-2" })
                            <div class="col-md-8">
                                @Html.DropDownListFor(model => model.RoleId, roleListItems, "-- Chọn nhóm/tổ tuần tra --", new { @class = "form-control" })
                                @Html.ValidationMessageFor(model => model.RoleId, null, new { @class = "text-danger" })
                            </div>
                        </div>

                        <div class="row form-group">
                            @Html.LabelFor(model => model.Description, new { @class = "control-label col-md-2" })
                            <div class="col-md-8">
                                @Html.TextAreaFor(model => model.Description, new { @class = "form-control" })
                                @Html.ValidationMessageFor(model => model.Description, null, new { @class = "text-danger" })
                            </div>
                        </div>

                        <div class="form-group row">
                            <div class="col-md-offset-2 col-md-10">
                                <input type="submit" value="@PSafe.AM.Resources.Resource.Save" class="btn btn-primary" />
                                @Html.ActionLink(PSafe.AM.Resources.Resource.Cancel, "Index", "PatrolCameraCalendar", null, new { @class = "btn btn-white" })
                            </div>
                        </div>
                    </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <environment names="Development,Staging,Production">
        <link rel="stylesheet" href="~/lib/toastr/toastr.min.css" />
        <link rel="stylesheet" href="~/lib/bootstrap-clockpicker/bootstrap-clockpicker.min.css" />
        <link href="~/lib/bootstrap-datepicker/dist/css/bootstrap-datepicker.min.css" rel="stylesheet" />

    </environment>
}

@section Scripts {
    <environment names="Development,Staging,Production">
        <script src="~/lib/validate/jquery.validate.min.js"></script>
        <script src="~/lib/toastr/toastr.js"></script>
        <script src="~/lib/toastr/toastr.min.js"></script>
        <script src="~/lib/bootstrap-clockpicker/bootstrap-clockpicker.min.js"></script>
        <script src="~/lib/bootstrap-datepicker/dist/js/bootstrap-datepicker.min.js"></script>
    </environment>

    @if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "warning")
    {
        <script type="text/javascript">
            toastr.warning('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "error")
    {
        <script type="text/javascript">
            toastr.error('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "success")
    {
        <script type="text/javascript">
            toastr.success('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }

    <script>
        $(document).ready(function () {
            $(".clockpicker").clockpicker({
                autoclose: true
            });
        });

        var start = new Date();
        $('.date').datepicker({
            startDate: start,
            //multidate: true,
            format: 'dd/mm/yyyy'
        });
    </script>
}