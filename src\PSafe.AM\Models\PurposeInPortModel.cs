﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace PSafe.AM.Models
{
    public class PurposeInPortModel
    {
        [Key]
        [Display(Name = "Id", ResourceType = typeof(Resources.Resource_purposeInPort))]
        public int Id { get; set; }

        [Required(ErrorMessage = "Vui lòng nhập mục đích vào cảng")]
        [StringLength(255, MinimumLength = 1, ErrorMessage = "Độ dài từ 1-255 ký tự")]
        [Display(Name = "Name", ResourceType = typeof(Resources.Resource_purposeInPort))]
        public string Name { get; set; } = string.Empty;

        [Display(Name = "Description", ResourceType = typeof(Resources.Resource_purposeInPort))]
        public string Description { get; set; } = string.Empty;
    }
}
