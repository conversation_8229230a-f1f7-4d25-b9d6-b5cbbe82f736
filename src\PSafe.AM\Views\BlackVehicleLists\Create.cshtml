﻿@model PSafe.AM.Models.BlackVehicleListModel
@{
    ViewBag.Title = "Tạo mới";
}

<environment names="Development">
    <script src="~/lib/jquery/dist/jquery.js"></script>
    <script src="~/lib/validate/jquery.validate.min.js"></script>
    <script src="~/js/script.js" asp-append-version="true"></script>
</environment>
<environment names="Staging,Production">
    <script src="~/lib/jquery/dist/jquery.js"></script>
    <script src="~/lib/validate/jquery.validate.min.js"></script>
    <script src="~/js/script.js" asp-append-version="true"></script>
</environment>

<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-lg-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>@PSafe.AM.Resources.Resource.AddItem @PSafe.AM.Resources.Resource.BlackList</h5>
                    <div class="ibox-tools">
                        @Html.ActionLink(@PSafe.AM.Resources.Resource.BackToList, "Index", "BlackLists", null, new { @class = "btn btn-primary btn-xs" })
                    </div>
                </div>
                <div class="ibox-content">
                    @using (Html.BeginForm())
                    {
                        @Html.AntiForgeryToken()

                        @Html.ValidationSummary(true)
                        <div class="row">
                            <div class="col-lg-6">
                                <div class="form-group row">
                                    <label class="control-label col-md-4">@Html.LabelFor(model => model.VehicleNumber) (*)</label>
                                    <div class="col-md-8">
                                        @Html.EditorFor(model => model.VehicleNumber)
                                        @Html.ValidationMessageFor(model => model.VehicleNumber, null, new { @class = "text-danger" })
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6" id="hiddenUID">
                                <div class="form-group row">
                                    <label class="control-label col-md-4">@Html.LabelFor(model => model.FullName)</label>
                                    <div class="col-md-8">
                                        @Html.EditorFor(model => model.FullName)
                                        @Html.ValidationMessageFor(model => model.FullName, null, new { @class = "text-danger" })
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group row">
                                    <label class="control-label col-md-4">@Html.LabelFor(model => model.Description)</label>
                                    <div class="col-md-8">
                                        @Html.EditorFor(model => model.Description)
                                        @Html.ValidationMessageFor(model => model.Description, null, new { @class = "text-danger" })
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group row">
                                    <label class="control-label col-md-4">@Html.LabelFor(model => model.Active)</label>
                                    <div class="col-md-8">
                                        @Html.EditorFor(model => model.Active)
                                        @Html.ValidationMessageFor(model => model.Active, null, new { @class = "text-danger" })
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-offset-2 col-md-10">
                                <input type="submit" value="@PSafe.AM.Resources.Resource.Save" class="btn btn-primary" />
                                @Html.ActionLink(PSafe.AM.Resources.Resource.Cancel, "Index", "BlackLists", null, new { @class = "btn btn-white" })
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <environment names="Development,Staging,Production">
        <link rel="stylesheet" href="~/lib/toastr/toastr.min.css" />
    </environment>
}

@section Scripts {
    <environment names="Development,Staging,Production">
        <script src="~/lib/validate/jquery.validate.min.js"></script>
        <script src="~/lib/toastr/toastr.js"></script>
        <script src="~/lib/toastr/toastr.min.js"></script>
    </environment>

    @if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "warning")
    {
        <script type="text/javascript">
            toastr.warning('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "error")
    {
        <script type="text/javascript">
            toastr.error('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "success")
    {
        <script type="text/javascript">
            toastr.success('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
}