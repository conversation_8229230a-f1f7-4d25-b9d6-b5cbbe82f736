﻿@model IEnumerable<PSafe.AM.Models.LocationModel>

@{
    ViewBag.Title = "Index";
}
<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-lg-12">
            <div class="ibox">
                <div class="ibox-title">
                    <h5><PERSON>h sách @PSafe.AM.Resources.Resource.Location</h5>
                    <div class="ibox-tools">
                        @Html.ActionLink(PSafe.AM.Resources.Resource.AddItem, "Create", "Locations", null, new { @class = "btn btn-primary btn-xs" })
                    </div>
                </div>
                <div class="ibox-content table-responsive">
                    <table class="table table-striped table-bordered table-hover dataTables-list" id="dataTables-list">
                        
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal inmodal fade" id="localMapModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header" style="padding:10px 15px">
                <button type="button" class="close" data-dismiss="modal" style="margin: -0.5rem -1rem -1rem auto">&times;</button>
                <h4 class="modal-title">Modal Header</h4>
            </div>
            <div class="modal-body">
                <iframe frameborder="0" title=""></iframe>
                <div>
                    <div class="text-center" style="margin:15% auto">Đang tải...</div>
                </div>
            </div>
        </div>
    </div>
</div>


@section Styles {
    <environment names="Development,Staging,Production">
        <link rel="stylesheet" href="~/lib/dataTables/datatables.min.css" />
        <link rel="stylesheet" href="~/lib/toastr/toastr.min.css" />
    </environment>
}

@section Scripts {
    <environment names="Development,Staging,Production">
        <script src="~/lib/dataTables/datatables.min.js"></script>
        <script src="~/lib/dataTables/dataTables.bootstrap4.min.js"></script>
        <script src="~/lib/toastr/toastr.js"></script>
        <script src="~/lib/toastr/toastr.min.js"></script>
    </environment>

    @if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "warning")
    {
        <script type="text/javascript">
            toastr.warning('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "error")
    {
        <script type="text/javascript">
            toastr.error('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "success")
    {
        <script type="text/javascript">
            toastr.success('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }

        <script type="text/javascript">
            var localMapUrl = '@ViewBag.linkLocalMaps';
        $(document).ready(function () {
            $('.dataTables-list').DataTable({
                "serverSide": false,
                "ajax": {
                    "url": "@Url.Action("GetIndex", "Locations")",
                    "dataType": 'json',
                    "contentType": "application/json; charset=utf-8",
                    "type": "GET",
                    "dataSrc": ''

                },
                "autoWidth": false,
                "columns": [
                    { "title": "@PSafe.AM.Resources.Resource__location.LocationName", "data": "locationName", "searchable": true },
                    { "title": "@PSafe.AM.Resources.Resource__location.Map", "data": "map", "searchable": true },
                    { "title": "@PSafe.AM.Resources.Resource__location.Description", "data": "description", "searchable": true },
                    { "title": "Tên khu vực", "data": "areaName", "searchable": true },
                    {
                        "title": "Hành động",
                        "data": "locationId",
                        "searchable": false,
                        "sortable": false,
                        "render": function(data, type, full, meta) {
                            return '<a href="@Url.Action("Details", "Locations")?id=' + data + '" class="btn btn-primary btn-xs">@PSafe.AM.Resources.Resource.Detail</a> ' +
                                '<a href="@Url.Action("Edit", "Locations")?id=' + data + '" class="btn btn-white btn-xs">@PSafe.AM.Resources.Resource.Edit</a> '+
                                '<a href="@Url.Action("Delete", "Locations")?id=' + data +'" class="btn btn-danger btn-xs">@PSafe.AM.Resources.Resource.Delete</a> '+
                                '<a onclick="return openLocalMap(' + data + ',\'' + full.locationName + '\');" href="@ViewBag.linkLocalMaps' + data +'" class="btn btn-primary btn-xs">Bản đồ</a>';
                        }
                    }
                ],
                "oLanguage": {
                    "sProcessing": "Đang xử lý...",
                    "sLengthMenu": "Xem _MENU_ mục",
                    "sZeroRecords": "Không tìm thấy dòng nào phù hợp",
                    "sInfo": "Đang xem _START_ đến _END_ trong tổng số _TOTAL_ mục",
                    "sInfoEmpty": "Đang xem 0 đến 0 trong tổng số 0 mục",
                    "sInfoFiltered": "(được lọc từ _MAX_ mục)",
                    "sInfoPostFix": "",
                    "sLoadingRecords": '<img src="/images/loadding-icon.gif" width="30%"></span>',
                    "sSearch": "Tìm:",
                    "sUrl": "",
                    "oPaginate": {
                        "sFirst": "Đầu",
                        "sPrevious": "Trước",
                        "sNext": "Tiếp",
                        "sLast": "Cuối"
                    }
                },
                pageLength: pageLength_dataTable(),
                dom: '<"html5buttons"B>lTfgitp',
                buttons: [
                    { extend: 'copy' },
                    { extend: 'csv' },
                    { extend: 'excel', title: 'ExampleFile' },
                    { extend: 'pdf', title: 'ExampleFile' },
                    {
                        extend: 'print',
                        customize: function (win) {
                            $(win.document.body).addClass('white-bg');
                            $(win.document.body).css('font-size', '8px');

                            $(win.document.body).find('table')
                                .addClass('compact')
                                .css('font-size', 'inherit');
                        }
                    }
                ]
            });
            $('#localMapModal').on('hidden.bs.modal', function (e) {
                window.removeEventListener('message', closeLocalMapModalMessage);
                $('#localMapModal .modal-body iframe').attr('src', '');
            });
        });

            function openLocalMap(locationId, locationName) {
            $('#localMapModal .modal-dialog').css({
                maxWidth: document.body.offsetWidth - 50
            });
            $('#localMapModal .modal-title').html(locationName);
            $('#localMapModal .modal-body').css("height", document.body.offsetHeight - 130);
            var cssStyle = {
                position: 'absolute',
                width: '100%',
                height: '100%',
                top: 0,
                left: 0
            };
            $('#localMapModal .modal-body > div').css(cssStyle).show();
            $('#localMapModal .modal-body iframe').css(cssStyle).attr("src", localMapUrl + locationId).on("load", function () {
                $('#localMapModal .modal-body > div').hide();
                window.addEventListener('message', closeLocalMapModalMessage);
                this.contentWindow.postMessage('', "*");
            });
            $('#localMapModal').modal('show');
            return false;
        }

        function closeLocalMapModalMessage(e) {
            $('#localMapModal').modal('hide');
        }
        </script>
}