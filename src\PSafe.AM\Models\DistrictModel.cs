﻿using Microsoft.AspNetCore.Mvc.Rendering;
using System;
using System.ComponentModel.DataAnnotations;

namespace PSafe.AM.Models
{
    public class DistrictModel
    {
        [Key]
        [Display(Name = "Id", ResourceType = typeof(Resources.Resource_district))]
        public int Id { get; set; }

        [Display(Name = "ProvinceId", ResourceType = typeof(Resources.Resource_district))]
        public int ProvinceId { get; set; }

        public SelectList ListProvinces { get; set; }

        [Required(ErrorMessage = "Vui lòng mã Quận/Huyện")]
        [StringLength(255, MinimumLength = 1, ErrorMessage = "Độ dài từ 1-255 ký tự")]
        [Display(Name = "Code", ResourceType = typeof(Resources.Resource_district))]
        public string Code { get; set; }

        [Required(ErrorMessage = "Vui lòng mã liên kết Quận/Huyện")]
        [StringLength(255, MinimumLength = 1, ErrorMessage = "Độ dài từ 1-255 ký tự")]
        [Display(Name = "LinkCode", ResourceType = typeof(Resources.Resource_district))]
        public string LinkCode { get; set; }

        [Required(ErrorMessage = "Vui lòng mã convert ")]
        [Display(Name = "ConvertId", ResourceType = typeof(Resources.Resource_district))]
        public int ConvertId { get; set; }

        [Required(ErrorMessage = "Vui lòng nhập tên Quận/Huyện")]
        [StringLength(255, MinimumLength = 1, ErrorMessage = "Độ dài từ 1-255 ký tự")]
        [Display(Name = "Name", ResourceType = typeof(Resources.Resource_district))]
        public string Name { get; set; } = string.Empty;

        [Display(Name = "Description", ResourceType = typeof(Resources.Resource_district))]
        public string Description { get; set; } = string.Empty;

        [Required(ErrorMessage = "Vui lòng thứ tự")]
        [StringLength(255, MinimumLength = 1, ErrorMessage = "Độ dài từ 1-255 ký tự")]
        [Display(Name = "OrdinalNumber", ResourceType = typeof(Resources.Resource_district))]
        public int OrdinalNumber { get; set; }

        public int State { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime UpdatedDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? UpdatedUserId { get; set; }
    }
}
