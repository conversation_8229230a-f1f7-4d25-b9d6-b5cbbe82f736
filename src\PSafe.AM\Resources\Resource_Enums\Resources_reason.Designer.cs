﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace PSafe.AM.Resources.Resource_Enums {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class Resources_reason {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resources_reason() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("PSafe.AM.Resources.Resource_Enums.Resources_reason", typeof(Resources_reason).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lý do hủy cấm vào ra.
        /// </summary>
        public static string REASON_CANCEL_PROHIBIT {
            get {
                return ResourceManager.GetString("REASON_CANCEL_PROHIBIT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lý do hủy tạm khóa vào ra.
        /// </summary>
        public static string REASON_CANCEL_TEMPORARILY_LOCK {
            get {
                return ResourceManager.GetString("REASON_CANCEL_TEMPORARILY_LOCK", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lý do vào cảng.
        /// </summary>
        public static string REASON_ENTER_YARDS {
            get {
                return ResourceManager.GetString("REASON_ENTER_YARDS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lý do bấm nút mở barier.
        /// </summary>
        public static string REASON_PRESS_BUTTON {
            get {
                return ResourceManager.GetString("REASON_PRESS_BUTTON", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lý do cấm vào ra.
        /// </summary>
        public static string REASON_PROHIBIT {
            get {
                return ResourceManager.GetString("REASON_PROHIBIT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lý do tạm khóa vào ra.
        /// </summary>
        public static string REASON_TEMPORARILY_LOCK {
            get {
                return ResourceManager.GetString("REASON_TEMPORARILY_LOCK", resourceCulture);
            }
        }
    }
}
