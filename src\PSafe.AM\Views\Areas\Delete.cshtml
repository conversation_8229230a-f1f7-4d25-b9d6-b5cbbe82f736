﻿@model PSafe.AM.Models.AreaModel

@{
    ViewBag.Title = "Xóa";
}
<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-lg-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>@PSafe.AM.Resources.Resource.Delete @PSafe.AM.Resources.Resource.Area</h5>
                    <div class="ibox-tools">
                        @Html.ActionLink(PSafe.AM.Resources.Resource.BackToList, "Index", "Areas", null, new { @class = "btn btn-primary btn-xs" })
                    </div>
                </div>
                <div class="ibox-content">

                    @using (Html.BeginForm("DeleteConfirmed", "Areas", new { id = Model.AREAID }, FormMethod.Post))
                    {

                        @Html.AntiForgeryToken()

                        <div class="row">
                            <div class="col-lg-6">
                                <div class="form-group row">
                                    <div class="col-md-4">
                                        <strong>
                                            @Html.DisplayNameFor(model => model.BRANCHNAME)
                                        </strong>
                                    </div>
                                    <div class="col-md-8">
                                        @Html.DisplayFor(model => model.BRANCHNAME)
                                    </div>
                                </div>
                            </div>
                           
                            <div class="col-lg-6">
                                <div class="form-group row">
                                    <div class="col-md-4">
                                        <strong>
                                            @Html.DisplayNameFor(model => model.ADDRESS)
                                        </strong>
                                    </div>
                                    <div class="col-md-8">
                                        @Html.DisplayFor(model => model.ADDRESS)
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group row">
                                    <div class="col-md-4">
                                        <strong>
                                            @Html.DisplayNameFor(model => model.LATITUDE)
                                        </strong>
                                    </div>
                                    <div class="col-md-8">
                                        @Html.DisplayFor(model => model.LATITUDE)
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group row">
                                    <div class="col-md-4">
                                        <strong>
                                            @Html.DisplayNameFor(model => model.LONGITUDE)
                                        </strong>
                                    </div>
                                    <div class="col-md-8">
                                        @Html.DisplayFor(model => model.LONGITUDE)
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group row">
                                    <div class="col-md-4">
                                        <strong>
                                            @Html.DisplayNameFor(model => model.REPRESENTATIVE)
                                        </strong>
                                    </div>
                                    <div class="col-md-8">
                                        @Html.DisplayFor(model => model.REPRESENTATIVE)
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group row">
                                    <div class="col-md-4">
                                        <strong>
                                            @Html.DisplayNameFor(model => model.CONTACTPERSON)
                                        </strong>
                                    </div>
                                    <div class="col-md-8">
                                        @Html.DisplayFor(model => model.CONTACTPERSON)
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group row">
                                    <div class="col-md-4">
                                        <strong>
                                            @Html.DisplayNameFor(model => model.EMAIL)
                                        </strong>
                                    </div>
                                    <div class="col-md-8">
                                        @Html.DisplayFor(model => model.EMAIL)
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group row">
                                    <div class="col-md-4">
                                        <strong>
                                            @Html.DisplayNameFor(model => model.CONTACTPHONE)
                                        </strong>
                                    </div>
                                    <div class="col-md-8">
                                        @Html.DisplayFor(model => model.CONTACTPHONE)
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group row">
                                    <div class="col-md-4">
                                        <strong>
                                            @Html.DisplayNameFor(model => model.MESSAGEPHONE)
                                        </strong>
                                    </div>
                                    <div class="col-md-8">
                                        @Html.DisplayFor(model => model.MESSAGEPHONE)
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group row">
                                    <div class="col-md-4">
                                        <strong>
                                            @Html.DisplayNameFor(model => model.FAX)
                                        </strong>
                                    </div>
                                    <div class="col-md-8">
                                        @Html.DisplayFor(model => model.FAX)
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group row">
                                    <div class="col-md-4">
                                        <strong>
                                            @Html.DisplayNameFor(model => model.DESCRIPTION)
                                        </strong>
                                    </div>
                                    <div class="col-md-8">
                                        @Html.DisplayFor(model => model.DESCRIPTION)
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-lg-6">
                                <div class="form-group row">
                                    <div class="col-md-4">
                                        <strong>
                                            @Html.DisplayNameFor(model => model.CREATEDDATE)
                                        </strong>
                                    </div>
                                    <div class="col-md-8">
                                        @Html.DisplayFor(model => model.CREATEDDATE)
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group row">
                                    <div class="col-md-4">
                                        <strong>
                                            @Html.DisplayNameFor(model => model.CREATEDUSER)
                                        </strong>
                                    </div>
                                    <div class="col-md-8">
                                        @ViewBag.CREATEDUSER
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group row">
                                    <div class="col-md-4">
                                        <strong>
                                            @Html.DisplayNameFor(model => model.UPDATEDDATE)
                                        </strong>
                                    </div>
                                    <div class="col-md-8">
                                        @Html.DisplayFor(model => model.UPDATEDDATE)
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group row">
                                    <div class="col-md-4">
                                        <strong>
                                            @Html.DisplayNameFor(model => model.UPDATEDUSER)
                                        </strong>
                                    </div>
                                    <div class="col-md-8">
                                        @ViewBag.UPDATEDUSER
                                    </div>
                                </div>
                            </div>
                           
                        </div>
                        <input type="button" onclick="DeleteArea(@Model.AREAID)" value="@PSafe.AM.Resources.Resource.Delete" class="btn btn-danger" />
                        @Html.ActionLink(PSafe.AM.Resources.Resource.Cancel, "Index", "Areas", null, new { @class = "btn btn-white" })

                        <button type="button" hidden id="dialogDelete" name="dialogDelete" class="btn btn-primary" data-toggle="modal" data-target="#exampleModal">
                        </button>

                        <!-- Modal -->
                        <div class="modal fade" id="exampleModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
                            <div class="modal-dialog" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h2 class="modal-title text-warning" id="exampleModalLabel"><strong>Cảnh báo!</strong></h2>
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                            <span aria-hidden="true">&times;</span>
                                        </button>
                                    </div>
                                    <div class="modal-body">
                                        <p>Thao tác sẽ xóa thêm @PSafe.AM.Resources.Resource.Location thuộc @PSafe.AM.Resources.Resource.Area này! <br /> <strong>Bạn chắc chắn muốn xóa không?</strong></p>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-white" data-dismiss="modal">@PSafe.AM.Resources.Resource.Cancel</button>
                                        <button type="submit" id="btnDeleteArea" class="btn btn-primary">Có</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }

                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <environment names="Development,Staging,Production">
        <link rel="stylesheet" href="~/lib/toastr/toastr.min.css" />
    </environment>
}

@section Scripts {
    <environment names="Development,Staging,Production">
        <script src="~/lib/validate/jquery.validate.min.js"></script>
        <script src="~/lib/toastr/toastr.js"></script>
        <script src="~/lib/toastr/toastr.min.js"></script>
    </environment>

    @if (!string.IsNullOrEmpty(ViewBag.Type) && ViewBag.Type == "warning")
    {
        <script type="text/javascript">
            toastr.warning('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Status) && ViewBag.Type == "error")
    {
        <script type="text/javascript">
            toastr.error('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Status) && ViewBag.Type == "success")
    {
        <script type="text/javascript">
            toastr.success('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }

    <script type="text/javascript">
        function DeleteArea(id) {
            $.ajax({
                type: "GET",
                url: '@Url.Action("CountLocationOfArea", "Locations")',
                data: { id : id },
                dataType: "Json",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    if (data > 0) {
                        $('#dialogDelete').click();
                    } else {
                        $('#btnDeleteArea').click();
                    }
                },
                error: function () {
                    alert("Không lấy được giá trị!");
                }
            });
        }
    </script>
}