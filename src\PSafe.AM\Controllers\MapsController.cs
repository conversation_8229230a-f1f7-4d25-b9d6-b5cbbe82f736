﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using PSafe.Common;
using PSafe.Common.EventEnums;
using PSafe.Infrastructure.Services;
using System;
using System.Linq;
using System.Threading.Tasks;
using static PSafe.Common.CommonEnums;

namespace PSafe.AM.Controllers
{
    //[Authorize]
    [Route("/[controller]")]
    [ApiController]
    public class MapsController : ControllerBase
    {
        private readonly ILogger<MapsController> _logger;
        private readonly IMapsAPIEndpoint _mapsAPIEndpoint;
        private readonly MapsPlatformService _mapsPlatformService;

        public MapsController(
            ILogger<MapsController> logger,
            IMapsAPIEndpoint mapsAPIEndpoint)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _mapsAPIEndpoint = mapsAPIEndpoint ?? throw new ArgumentNullException(nameof(mapsAPIEndpoint));
            _mapsPlatformService = new MapsPlatformService(_mapsAPIEndpoint, _logger);
        }

        [HttpGet("GetReverseGeocoding")]
        public async Task<HttpFQResponse> ReverseGeocoding(double lat, double lon)
        {
            DateTime dtRecieve = DateTime.Now;
            try
            {
                var result = await _mapsPlatformService.GetReverseGeocoding(lat, lon);
                if(result != null && result.RetCode == (short)ERET_CODE.Success)
                {
                    var geoCodingResponse = JsonConvert.DeserializeObject<GeoCodingResponse>(result.Data.ToString());
                    if(geoCodingResponse != null && geoCodingResponse.Features.Count > 0)
                        return HttpFQResponse.Make(ERESULT_ENUM.RS_OK, geoCodingResponse.Features.FirstOrDefault()?.Properties?.Address, geoCodingResponse.Features.FirstOrDefault()?.Properties?.AddressComponents, dtRecieve);
                    return HttpFQResponse.Make(ERESULT_ENUM.RS_OK, "Không tìm thấy địa chỉ", dtRecieve);
                }
                throw new Exception("Lấy dữ liệu địa chỉ thất bại");
            }
            catch (Exception ex)
            {
                _logger.LogError("GetReverseGeocoding: " + ex.Message + ex.StackTrace);
                return HttpFQResponse.Make(ERESULT_ENUM.RS_EXCEPTION, ex.Message, dtRecieve);
            }
        }


        [HttpGet("GetAutocompleteGeocoding")]
        public async Task<HttpFQResponse> AutocompleteGeocoding(string search)
        {
            DateTime dtRecieve = DateTime.Now;
            try
            {
                var result = await _mapsPlatformService.AutocompleteGeocoding(search);
                if (result != null && result.RetCode == (short)ERET_CODE.Success)
                {
                    var geoCodingResponse = JsonConvert.DeserializeObject<GeoCodingResponse>(result.Data.ToString());
                    if (geoCodingResponse != null && geoCodingResponse.Features.Count > 0)
                        return HttpFQResponse.Make(ERESULT_ENUM.RS_OK, geoCodingResponse.Features, geoCodingResponse.Features.FirstOrDefault()?.Properties?.AddressComponents, dtRecieve);
                    return HttpFQResponse.Make(ERESULT_ENUM.RS_NOT_OK, "Không tìm thấy địa chỉ", dtRecieve);
                }
                throw new Exception("Lấy dữ liệu địa chỉ thất bại");
            }
            catch (Exception ex)
            {
                _logger.LogError("GetReverseGeocoding: " + ex.Message + ex.StackTrace);
                return HttpFQResponse.Make(ERESULT_ENUM.RS_EXCEPTION, ex.Message, dtRecieve);
            }
        }
    }
}
