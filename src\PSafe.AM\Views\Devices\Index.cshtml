﻿@model IEnumerable<PSafe.AM.Models.DeviceModel>


<environment names="Development,Staging,Production">
    <script type="text/javascript">
        function confirm() {
            document.getElementById("Sync").innerHTML = "<i class=\"fa fa-refresh fa-spin\"></i>&nbsp;&nbsp;@PSafe.AM.Resources.Resource.Sync";
        }
    </script>
</environment>

@{
    ViewBag.Title = "Index";
}

@section Styles{
    <environment names="Development,Staging,Production">
        <link rel="stylesheet" href="~/lib/dataTables/datatables.min.css" />
        <link rel="stylesheet" href="~/lib/toastr/toastr.min.css" />
    </environment>

    <style type="text/css">
        .btn-white {
            color: black !important;
        }

        .table-hovert body tr.bg-info:hover {
            background-color: #ff0000;
        }

        .highlight {
            background-color: #B0C4DE !important;
        }
    </style>
}

<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-lg-12">
            <div class="ibox">
                <div class="ibox-title">
                    <h5>Danh sách @PSafe.AM.Resources.Resource.Device</h5>
                    <div class="ibox-tools">
                        @Html.ActionLink(PSafe.AM.Resources.Resource.Sync, "Sync", "Devices", null, new { @id = "Sync", @class = "btn btn-primary btn-xs", onclick = "return confirm();" })
                        @Html.ActionLink(PSafe.AM.Resources.Resource.AddItem, "Create", "Devices", null, new { @class = "btn btn-primary btn-xs" })
                    </div>
                </div>
                <div class="ibox-content table-responsive">
                    <table class="table table-striped table-bordered table-hover dataTables-list">
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <environment names="Development,Staging,Production">
        <script src="~/lib/dataTables/datatables.min.js"></script>
        <script src="~/lib/dataTables/dataTables.bootstrap4.min.js"></script>
        <script src="~/lib/toastr/toastr.js"></script>
        <script src="~/lib/toastr/toastr.min.js"></script>
    </environment>

    @if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "warning")
    {
        <script type="text/javascript">
            toastr.warning('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "error")
    {
        <script type="text/javascript">
            toastr.error('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "success")
    {
        <script type="text/javascript">
            toastr.success('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }

    <script type="text/javascript">
        $(document).ready(function () {
            $('.dataTables-list').DataTable({
                "serverSide": false,
                "ajax": {
                    "url": "@Url.Action("GetIndex", "Devices")",
                    "dataType": 'json',
                    "contentType": "application/json; charset=utf-8",
                    "type": "GET",
                    "dataSrc": ''
                },
                "autoWidth": false,
                "columns": [
                    { "title": "@PSafe.AM.Resources.Resource__device.DEVICENAME", "data": "deviceName", "searchable": true },
                    { "title": "@PSafe.AM.Resources.Resource__device.IP", "data": "ip", "searchable": true },
                    { "title": "@PSafe.AM.Resources.Resource__device.TYPEOFSINGNAL", "data": "typeOfSingnal", "searchable": true },
                    { "title": "@PSafe.AM.Resources.Resource__typeOfDevice.TYPENAME", "data": "typeOfDevice", "searchable": true },
                    { "title": "@PSafe.AM.Resources.Resource__device.SERIALNUMBER", "data": "serialNumber", "searchable": true },
                    { "title": "@PSafe.AM.Resources.Resource__device.MacAddress", "data": "macAddress", "searchable": true },
                    { "title": "@PSafe.AM.Resources.Resource__device.DriverName", "data": "driverName", "searchable": true },
                    { "title": "Khu vực", "data": "areaName", "searchable": true },
                    { "title": "Vị trí", "data": "locationName", "searchable": true },
                    {
                        "title": "Hành động",
                        "data": "id",
                        "searchable": false,
                        "sortable": false,
                        "render": function(data, type, full, meta) {
                            return '<a href="@Url.Action("Details", "Devices")?id=' +
                                data +
                                '" class="btn btn-primary btn-xs">@PSafe.AM.Resources.Resource.Detail</a> ' +
                                '<a href="@Url.Action("Edit", "Devices")?id=' +
                                data +
                                '" class="btn btn-white btn-xs">@PSafe.AM.Resources.Resource.Edit</a> '+
                                '<a href="@Url.Action("Delete", "Devices")?id=' + data +'" class="btn btn-danger btn-xs">@PSafe.AM.Resources.Resource.Delete</a>';
                        }
                    }
                ],
                "fnRowCallback": function (nRow, aData) {
                    if (aData.fromSync == false) {
                        $(nRow).addClass('highlight');
                    }
                    return nRow;
                },
                "oLanguage": {
                    "sProcessing": "Đang xử lý...",
                    "sLengthMenu": "Xem _MENU_ mục",
                    "sZeroRecords": "Không tìm thấy dòng nào phù hợp",
                    "sInfo": "Đang xem _START_ đến _END_ trong tổng số _TOTAL_ mục",
                    "sInfoEmpty": "Đang xem 0 đến 0 trong tổng số 0 mục",
                    "sInfoFiltered": "(được lọc từ _MAX_ mục)",
                    "sInfoPostFix": "",
                    "sLoadingRecords": '<img src="/images/loadding-icon.gif" width="30%"></span>',
                    "sSearch": "Tìm:",
                    "sUrl": "",
                    "oPaginate": {
                        "sFirst": "Đầu",
                        "sPrevious": "Trước",
                        "sNext": "Tiếp",
                        "sLast": "Cuối"
                    }
                },
                pageLength: pageLength_dataTable(),
                dom: '<"html5buttons"B>lTfgitp',
                buttons: [
                    { extend: 'copy' },
                    { extend: 'csv' },
                    { extend: 'excel', title: 'ExampleFile' },
                    { extend: 'pdf', title: 'ExampleFile' },
                    {
                        extend: 'print',
                        customize: function (win) {
                            $(win.document.body).addClass('white-bg');
                            $(win.document.body).css('font-size', '8px');

                            $(win.document.body).find('table')
                                .addClass('compact')
                                .css('font-size', 'inherit');
                        }
                    }
                ]
            });
        });
    </script>
}