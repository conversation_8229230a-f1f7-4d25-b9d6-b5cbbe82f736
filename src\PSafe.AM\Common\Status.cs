﻿using System;
using System.Collections.Generic;
using System.Linq;
using PSafe.AM.Models;
using PSafe.Common.EventEnums;
using PSafe.Common.UserEnums;
using static PSafe.Common.CommonEnums;

namespace PSafe.AM.Common
{
    public class General
    {
        public GeneralConfigModel generalConfig { get; set; }
        public AlarmConfigModel alarmConfig { get; set; }
    }

    public class DropDownList
    {
        public int Id { get; set; }
        public string Name { get; set; }
    }

    public class DropDownListGeneric<T>
    {
        public T Id { get; set; }
        public string Name { get; set; }
    }
    public class DropDownList2
    {
        public string Id { get; set; }
        public string Name { get; set; }
    }

    public class DropDown
    {
        public Guid Guid { get; set; }
        public int Value { get; set; }
        public string Name { get; set; }
    }

    public class CheckBok
    {
        public string Name { get; set; }
        public int Id { get; set; }
        public string Value { get; set; }
        public bool Checked { get; set; }
        public bool Disabled { get; set; }
    }

    public class TweListCheckBox
    {
        public List<CheckBok> ACS { get; set; }
        public List<CheckBok> AM { get; set; }
        public List<CheckBok> TTAN { get; set; }
       
        public List<CheckBok> REPORT { get; set; }
        public List<CheckBok> VMS { get; set; }
        public List<CheckBok> CAMERA { get; set; }
        public int id { get; set; }
        public List<CheckBok> CameraInRole { get; set; }
        public List<CheckBok> CameraNotInRole { get; set; }
    }

    public class ListRoles
    {
        public List<string> ACS { get; set; }
        public List<string> PSASE { get; set; }
    }

    public class CompareTweObject<T> where T : class
    {
        public T NewObject { get; set; }
        public T OldObject { get; set; }
        public string Controller { get; set; }
    }

    public class GetStringHistorySystem
    {
        public string Get(string userName, string type, string value, string controller)
        {
            if (type != PSafe.AM.Resources.Resource.Login)
            {
                return "Tài khoản '" + userName + "' đã " + type.ToLower() + " " + controller.ToLower() + " '" + value.ToLower() + "'";
            }
            else
            {
                return "Tài khoản '" + userName + "' đã đăng nhập.";
            }

        }
    }

    public class ListDeviceStatus
    {
        public List<DropDownList> List
        {
            get
            {
                List<DropDownList> listDeviceStatus = new List<DropDownList>();

                DropDownList DeviceOpen = new DropDownList
                {
                    Id = (int)EDEVICE_STATUS.Khóa,
                    Name = ((EDEVICE_STATUS)((int)EDEVICE_STATUS.Khóa)).ToString()
                };
                DropDownList DeviceLock = new DropDownList
                {
                    Id = (int)EDEVICE_STATUS.Mở,
                    Name = ((EDEVICE_STATUS)((int)EDEVICE_STATUS.Mở)).ToString()
                };

                listDeviceStatus.Add(DeviceOpen);
                listDeviceStatus.Add(DeviceLock);

                return listDeviceStatus;
            }
        }
    }

    public class ListTypeOfSingnal
    {
        public List<DropDownList> List
        {
            get
            {
                List<DropDownList> listTyoeOfSingnal = new List<DropDownList>();

                DropDownList FireSafetyAlarm = new DropDownList
                {
                    Id = (int)PSafe.Common.EventEnums.ETYPE_OF_SIGNAL.FireSafetyAlarm,
                    Name = "Cháy nổ"
                };

                DropDownList SecurityAlarm = new DropDownList
                {
                    Id = (int)PSafe.Common.EventEnums.ETYPE_OF_SIGNAL.SecurityAlarm,
                    Name = "An ninh"
                };

                DropDownList ConnectionAlarm = new DropDownList
                {
                    Id = (int)PSafe.Common.EventEnums.ETYPE_OF_SIGNAL.ConnectionAlarm,
                    Name = "Mất kết nối"
                };

                listTyoeOfSingnal.Add(SecurityAlarm);
                listTyoeOfSingnal.Add(FireSafetyAlarm);
                listTyoeOfSingnal.Add(ConnectionAlarm);

                return listTyoeOfSingnal;
            }
        }
    }

    public class ListViolate
    {
        public List<CheckBok> List
        {
            get
            {
                List<CheckBok> listTyoeOfSingnal = new List<CheckBok>();
                CheckBok ViolateLane = new CheckBok
                {
                    Id = (int)EALARM_STATUS.LANE_VIOLATION,
                    Name = "Xe không đúng làn đường",
                    Checked = false,
                };

                CheckBok ViolateDirection = new CheckBok
                {
                    Id = (int)EALARM_STATUS.DIRECTION_VIOLATION,
                    Name = "Xe không đúng chiều đường quy định (ngược chiều)",
                    Checked = false,
                };

                CheckBok XE_VAO_DUONG_CAM = new CheckBok
                {
                    Id = (int)EALARM_STATUS.FORBIDDEN_ROAD,
                    Name = "Xe đi vào đường cấm",
                    Checked = false,
                };
                CheckBok QUA_TOC_DO = new CheckBok
                {
                    Id = (int)EALARM_STATUS.OVER_SPEED,
                    Name = "Xe chạy quá tốc độ",
                    Checked = false,
                };
                CheckBok DUNG_SAI_QUY_DINH = new CheckBok
                {
                    Id = (int)EALARM_STATUS.PARKING_VIOLATION,
                    Name = "Xe dừng đỗ sai qui định",
                    Checked = false,
                };
                CheckBok XE_UU_TIEN = new CheckBok
                {
                    Id = (int)EALARM_STATUS.PRIORITY_VEHICLE,
                    Name = "Xe ưu tiên",
                    Checked = false,
                };
                CheckBok VUOT_DEN_DO = new CheckBok
                {
                    Id = (int)EALARM_STATUS.REDLIGHT_VIOLATION,
                    Name = "Xe vượt đèn đỏ",
                    Checked = false,
                };
                CheckBok CHO_VAT_LIEU_NGUY_HIEM = new CheckBok
                {
                    Id = (int)EALARM_STATUS.RISK_MATERIAL,
                    Name = "Xe chở vật liệu nguy hiểm",
                    Checked = false,
                };
                CheckBok XE_AN_CAP = new CheckBok
                {
                    Id = (int)EALARM_STATUS.STOLEN_CAR,
                    Name = "Xe ăn cắp",
                    Checked = false,
                };
                CheckBok XE_QUA_KHO = new CheckBok
                {
                    Id = (int)EALARM_STATUS.OVERSIZED_CAR,
                    Name = "Xe quá khổ",
                    Checked = false,
                };
                CheckBok QUA_HAN_DANG_KIEM = new CheckBok
                {
                    Id = (int)EALARM_STATUS.REGISTRATION_EXPIRED,
                    Name = "Xe quá hạn đăng kiểm",
                    Checked = false,
                };
                CheckBok XE_NGOAI_GIAO = new CheckBok
                {
                    Id = (int)EALARM_STATUS.DIPLOMATIC_VEHICLE,
                    Name = "Xe ngoại giao",
                    Checked = false,
                };
                CheckBok CAM_VUOT = new CheckBok
                {
                    Id = (int)EALARM_STATUS.PROHIBITED_PASSAGE,
                    Name = "Xe cấm vượt",
                    Checked = false,
                };
                listTyoeOfSingnal.Add(ViolateLane);
                listTyoeOfSingnal.Add(ViolateDirection);
                listTyoeOfSingnal.Add(QUA_TOC_DO);
                listTyoeOfSingnal.Add(XE_VAO_DUONG_CAM);
                listTyoeOfSingnal.Add(DUNG_SAI_QUY_DINH);
                listTyoeOfSingnal.Add(XE_UU_TIEN);
                listTyoeOfSingnal.Add(VUOT_DEN_DO);
                listTyoeOfSingnal.Add(CHO_VAT_LIEU_NGUY_HIEM);
                listTyoeOfSingnal.Add(XE_AN_CAP);
                listTyoeOfSingnal.Add(XE_QUA_KHO);
                listTyoeOfSingnal.Add(QUA_HAN_DANG_KIEM);
                listTyoeOfSingnal.Add(XE_NGOAI_GIAO);
                listTyoeOfSingnal.Add(CAM_VUOT);
                return listTyoeOfSingnal;
            }
        }
    }

    public class ListEventLevel
    {
        public List<DropDownList> List
        {
            get
            {
                List<DropDownList> listEventLevel = new List<DropDownList>();

                DropDownList Normal = new DropDownList
                {
                    Id = (int)EEVENT_LEVEL.Normal,
                    Name = "Thông thường"
                };
                DropDownList Careful = new DropDownList
                {
                    Id = (int)EEVENT_LEVEL.Careful,
                    Name = "Chú ý"
                };

                DropDownList Dangerous = new DropDownList
                {
                    Id = (int)EEVENT_LEVEL.Dangerous,
                    Name = "Nguy hiểm"
                };
                DropDownList VeryDangerous = new DropDownList
                {
                    Id = (int)EEVENT_LEVEL.VeryDangerous,
                    Name = "Rất nguy hiểm"
                };

                listEventLevel.Add(Normal);
                listEventLevel.Add(Careful);
                listEventLevel.Add(Dangerous);
                listEventLevel.Add(VeryDangerous);

                return listEventLevel;
            }
        }
    }

    public class ListTypeUser
    {
        public List<DropDownList> List
        {
            get
            {
                List<DropDownList> listTypeUser = new List<DropDownList>();

                DropDownList PatrolStaff = new DropDownList
                {
                    Id = (int)ETYPE_USER.NVTT,
                    Name = "Nhân viên tuần tra"
                };
                DropDownList userTTAN = new DropDownList
                {
                    Id = (int)ETYPE_USER.USER_TTAN,
                    Name = "Nhân viên TTAN"
                };
                DropDownList userACS = new DropDownList
                {
                    Id = (int)ETYPE_USER.USER_ACS,
                    Name = "Nhân viên ACS"
                };
                listTypeUser.Add(userTTAN);
                listTypeUser.Add(PatrolStaff);
                listTypeUser.Add(userACS);

                return listTypeUser;
            }
        }
    }
}