﻿@using PSafe.Core.Domains;
@{
    ViewBag.title = "Quản lý danh sách người cấm vào ra";
    List<SelectListItem> reasonForbidList = (List<SelectListItem>)ViewBag.reasonForbidList;
    List<SelectListItem> reasonCancelForbidList = (List<SelectListItem>)ViewBag.reasonCancelForbidList;
    string reasonCancelForbids = string.Empty;
    foreach(var reasonCancelForbid in reasonCancelForbidList)
    {
        if (reasonCancelForbids == string.Empty)
        {
            reasonCancelForbids = reasonCancelForbid.Value;
        }
        else
        {
            reasonCancelForbids += "," + reasonCancelForbid.Value;
        }
    }
}

<input type="text" id="activeDatePicker" class="datePickeModal" />
<input type="text" id="expiryDatePicker" class="datePickeModal" />
<input type="text" id="birthDayDatePicker" class="datePickeModal" />
<input type="text" id="dateOfIssueDatePicker" class="datePickeModal" />
<input type="text" id="thoiGianCamDatePicker" class="datePickeModal" />
<input type="hidden" id="reasonCancelForbidsInput" value="@reasonCancelForbids" />
<div class="wrapper wrapper-content animated fadeInRight">
    <div class="ibox">
        <div class="ibox-title">
            <h5>Quản lý danh sách người cấm vào ra</h5>
            <div class="ibox-tools">
                <button class="btn btn-primary btn-xs" id="addNew">@PSafe.AM.Resources.Resource.AddItem</button>
                <button class="btn btn-danger btn-xs" id="importExcel">Import</button>
            </div>
        </div>
        <div class="ibox-content">
            @Html.AntiForgeryToken()
            <div class="row" id="filter-box">
                <div class="col-sm-3" style="position: relative">
                    <span class="filter-label">Mã giấy tờ</span>
                    <input type="text" class="form-control" id="paperCode" />
                    <em id="clearSearchText">&times;</em>
                </div>

                <div class="col-sm-3">
                    <span class="filter-label">Họ tên</span>
                    <input type="text" class="form-control" id="fullName" />
                </div>



                <div class="col-sm-6">
                    <span class="filter-label">Lý do cấm</span>
                    <select class="form-control" id="reasonViolation" multiple="multiple" style="display: none" data-placeholder="Chọn lý do cấm">
                        @foreach (var reason in reasonForbidList)
                        {
                            <option value="@reason.Value" selected>@reason.Text</option>
                        }
                    </select>

                </div>


                <div class="col-sm-6 col-md-5" style="margin-top: 10px">
                    <div>
                        <span class="filter-label">Ngày thực hiện cấm</span>
                        <div class="clearfix">
                            <div class="input-group pull-left" style="width: 43%; position: relative">
                                <span class="input-group-addon"><em class="fa fa-calendar"></em></span>
                                <input type="text" class="form-control" id="fromActiveTime" />
                                <em class="clearSearchDate">&times;</em>
                            </div>
                            <span class="form-control pull-left text-center" style="width: 14%; background-color: #f7f7f7; padding-left: 0;padding-right:0"> đến </span>
                            <div class="input-group pull-left" style="width: 43%;position: relative">
                                <span class="input-group-addon"><em class="fa fa-calendar"></em></span>
                                <input type="text" class="form-control" id="toActiveTime" />
                                <em class="clearSearchDate">&times;</em>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-sm-6 col-md-5" style="margin-top: 10px">
                    <div>
                        <span class="filter-label">Ngày hết hạn cấm</span>
                        <div class="clearfix">
                            <div class="input-group pull-left" style="width: 43%; position: relative">
                                <span class="input-group-addon"><em class="fa fa-calendar"></em></span>
                                <input type="text" class="form-control" id="fromExpiryTime" />
                                <em class="clearSearchDate">&times;</em>
                            </div>
                            <span class="form-control pull-left text-center" style="width: 14%; background-color: #f7f7f7; padding-left: 0;padding-right:0"> đến </span>
                            <div class="input-group pull-left" style="width: 43%; position: relative">
                                <span class="input-group-addon"><em class="fa fa-calendar"></em></span>
                                <input type="text" class="form-control" id="toExpiryTime" />
                                <em class="clearSearchDate">&times;</em>
                            </div>
                        </div>
                    </div>
                </div>



                <div class="col-sm-2" style="margin-top: 10px">
                    <span class="filter-label" style="color: white">.</span>
                    <button class="btn btn-success" id="searchBtn">Lọc dữ liệu</button>
                </div>

            </div>
        </div>
    </div>

    <div class="ibox">
        <div id="jsdatatable-container" class="ibox-content">
            <div id="table-loading"></div>
            <table id="bannedListTable" cellspacing="2" width="100%" class="table table-bordered">
                <thead>
                    <tr>
                        <th class="not-export">STT</th>
                        <th class="not-export">Hành động</th>
                        <th>Mã giấy tờ</th>
                        <th>Họ tên</th>
                        <th>Ngày thực hiện cấm</th>
                        <th>Ngày hết hạn cấm</th>
                        <th>Lý do cấm</th>
                        <th>Người cấm</th>
                        <th>Ngày cấm</th>
                        <th>Hủy vi phạm</th>
                        <th>Lý do hủy</th>
                        <th>Người hủy</th>
                        <th>Ngày hủy</th>
                        <th>Công ty</th>
                        <th>Số điện thoại</th>
                        <th>Ngày sinh</th>
                        <th>Nơi sinh</th>
                        <th>Ghi chú</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan="5">Đang tải...</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>


<div id="dialog-form" class="modal fade" role="dialog">

    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Thêm mới người cấm vào ra</h4>
                <button type="button" class="close" data-dismiss="modal">&times;</button>
            </div>
            <div class="modal-body">
                <div id="success-message" style="color: green;text-align:center">Lưu thành công!</div>
                <form id="infoForm">
                    <fieldset style="padding: 10px 0px">
                        <legend></legend>

                        <div class="row">
                            <span class="col-sm-5">Loại giấy tờ (*):</span>
                            <div class="col-sm-7">
                                <select class="form-control" name="paperType" id="paperTypeCbb">
                                    <option value="@((int)PSafe.AM.ACS.EnumDocType.NONE)">Không xác định</option>
                                    <option value="@((int)PSafe.AM.ACS.EnumDocType.ID_CARD)">CMND</option>
                                    <option value="@((int)PSafe.AM.ACS.EnumDocType.DRIVER_LICENSE)">Bằng lái xe</option>
                                    <option value="@((int)PSafe.AM.ACS.EnumDocType.CCCD)">Căn cước công dân</option>
                                    <option value="@((int)PSafe.AM.ACS.EnumDocType.PASSPORT)">Passport</option>
                                </select>
                            </div>
                        </div>

                        <br />

                        <div class="row">
                            <span class="col-sm-5">Mã giấy tờ (*):</span>
                            <div class="col-sm-7" style="position: relative">
                                <input type="text" autocomplete="off" class="form-control" name="paperCode" id="paperCode" data-validate="required" />
                                <i class="loader-icon" id="autoCompleteLoader"></i>
                                <small class="error">Vui lòng nhập mã giấy tờ</small>
                                <ul id="paperCodeAutoComplateBox"></ul>
                            </div>
                        </div>

                        <br />

                        <div class="row">
                            <span class="col-sm-5">Họ và tên (*):</span>
                            <div class="col-sm-7">
                                <input type="text" class="form-control" name="fullName" data-validate="required" />
                                <small class="error">Vui lòng nhập họ và tên người vi phạm</small>
                            </div>
                        </div>

                        <br />

                        <div class="row">
                            <span class="col-sm-5">Số điện thoại:</span>
                            <div class="col-sm-7">
                                <input type="text" class="form-control" name="phoneNumber" />
                            </div>
                        </div>

                        <br />

                        <div class="row">
                            <span class="col-sm-5">Công ty:</span>
                            <div class="col-sm-7">
                                <input type="text" class="form-control" name="company" />
                            </div>
                        </div>

                        <br />

                        <div class="text-center">
                            <a class="btn btn-primary" data-toggle="collapse" href="#collapseMoreHumanInfo" role="button" aria-expanded="false" aria-controls="collapseExample">
                                <em class="fa fa-chevron-down" id="collapseIcon"></em> Nhập thông tin cá nhân
                            </a>
                        </div>

                        <div class="collapse" id="collapseMoreHumanInfo" style="margin-top: 10px">
                            <div class="card">
                                <div class="card-body" style="background-color: #f3f3f3">

                                    <div class="row">
                                        <span class="col-sm-5">Nơi cấp giấy tờ:</span>
                                        <div class="col-sm-7">
                                            <input type="text" list="provincials" class="form-control" name="placeOfIssue" />
                                        </div>
                                    </div>

                                    <br />

                                    <div class="row">
                                        <span class="col-sm-5">Ngày cấp giấy tờ:</span>
                                        <div class="col-sm-7">
                                            <div class="input-group">
                                                <span class="input-group-addon"><em class="fa fa-calendar"></em></span>
                                                <input type="text" class="form-control" id="dateOfIssueTimeTxt" data-datepicker="1" name="dateOfIssueDate" />
                                            </div>

                                        </div>
                                    </div>
                                    <br />

                                    <div class="row">
                                        <span class="col-sm-5">Ngày sinh:</span>
                                        <div class="col-sm-7">
                                            <div class="input-group">
                                                <span class="input-group-addon"><em class="fa fa-calendar"></em></span>
                                                <input type="text" class="form-control" id="birthDayTimeTxt" data-datepicker="1" name="birthDayDate" />
                                            </div>

                                        </div>
                                    </div>

                                    <br />

                                    <div class="row">
                                        <span class="col-sm-5">Quê quán:</span>
                                        <div class="col-sm-7">
                                            <input type="text" list="provincials" class="form-control" name="provincialOfBirth" />
                                        </div>
                                    </div>

                                    <datalist id="provincials">
                                        <option value="An Giang">An Giang</option>
                                        <option value="Bà Rịa - Vũng Tàu">Bà Rịa - Vũng Tàu</option>
                                        <option value="Bắc Giang">Bắc Giang</option>
                                        <option value="Bắc Kạn">Bắc Kạn</option>
                                        <option value="Bạc Liêu">Bạc Liêu</option>
                                        <option value="Bắc Ninh">Bắc Ninh</option>
                                        <option value="Bến Tre">Bến Tre</option>
                                        <option value="Bình Định">Bình Định</option>
                                        <option value="Bình Dương">Bình Dương</option>
                                        <option value="Bình Phước">Bình Phước</option>
                                        <option value="Bình Thuận">Bình Thuận</option>
                                        <option value="Cà Mau">Cà Mau</option>
                                        <option value="Cao Bằng">Cao Bằng</option>
                                        <option value="Đắk Lắk">Đắk Lắk</option>
                                        <option value="Đắk Nông">Đắk Nông</option>
                                        <option value="Điện Biên">Điện Biên</option>
                                        <option value="Đồng Nai">Đồng Nai</option>
                                        <option value="Đồng Tháp">Đồng Tháp</option>
                                        <option value="Đồng Tháp">Đồng Tháp</option>
                                        <option value="Gia Lai">Gia Lai</option>
                                        <option value="Hà Giang">Hà Giang</option>
                                        <option value="Hà Nam">Hà Nam</option>
                                        <option value="Hà Tĩnh">Hà Tĩnh</option>
                                        <option value="Hải Dương">Hải Dương</option>
                                        <option value="Hậu Giang">Hậu Giang</option>
                                        <option value="Hòa Bình">Hòa Bình</option>
                                        <option value="Hưng Yên">Hưng Yên</option>
                                        <option value="Khánh Hòa">Khánh Hòa</option>
                                        <option value="Kiên Giang">Kiên Giang</option>
                                        <option value="Kon Tum">Kon Tum</option>
                                        <option value="Lai Châu">Lai Châu</option>
                                        <option value="Lâm Đồng">Lâm Đồng</option>
                                        <option value="Lạng Sơn">Lạng Sơn</option>
                                        <option value="Lào Cai">Lào Cai</option>
                                        <option value="Long An">Long An</option>
                                        <option value="Nam Định">Nam Định</option>
                                        <option value="Nghệ An">Nghệ An</option>
                                        <option value="Ninh Bình">Ninh Bình</option>
                                        <option value="Ninh Thuận">Ninh Thuận</option>
                                        <option value="Phú Thọ">Phú Thọ</option>
                                        <option value="Quảng Bình">Quảng Bình</option>
                                        <option value="Quảng Bình">Quảng Bình</option>
                                        <option value="Quảng Ngãi">Quảng Ngãi</option>
                                        <option value="Quảng Ninh">Quảng Ninh</option>
                                        <option value="Quảng Trị">Quảng Trị</option>
                                        <option value="Sóc Trăng">Sóc Trăng</option>
                                        <option value="Sơn La">Sơn La</option>
                                        <option value="Tây Ninh">Tây Ninh</option>
                                        <option value="Thái Bình">Thái Bình</option>
                                        <option value="Thái Nguyên">Thái Nguyên</option>
                                        <option value="Thanh Hóa">Thanh Hóa</option>
                                        <option value="Thừa Thiên Huế">Thừa Thiên Huế</option>
                                        <option value="Tiền Giang">Tiền Giang</option>
                                        <option value="Trà Vinh">Trà Vinh</option>
                                        <option value="Tuyên Quang">Tuyên Quang</option>
                                        <option value="Vĩnh Long">Vĩnh Long</option>
                                        <option value="Vĩnh Phúc">Vĩnh Phúc</option>
                                        <option value="Yên Bái">Yên Bái</option>
                                        <option value="Phú Yên">Phú Yên</option>
                                        <option value="Cần Thơ">Cần Thơ</option>
                                        <option value="Đà Nẵng">Đà Nẵng</option>
                                        <option value="Hải Phòng">Hải Phòng</option>
                                        <option value="Hà Nội">Hà Nội</option>
                                        <option value="HCM">HCM</option>
                                    </datalist>

                                    <br />

                                    <div class="row">
                                        <span class="col-sm-5">Địa chỉ thường trú:</span>
                                        <div class="col-sm-7">
                                            <textarea type="text" class="form-control" name="address"></textarea>
                                        </div>
                                    </div>

                                </div>

                            </div>
                        </div>
                        <br />

                        <div class="row">
                            <span class="col-sm-5">Ngày thực hiện cấm (*):</span>
                            <div class="col-sm-7">

                                <div class="row">
                                    <div class="col-sm-8" style="padding-right:0px">
                                        <div class="input-group">
                                            <span class="input-group-addon"><em class="fa fa-calendar"></em></span>
                                            <input type="text" class="form-control" id="activeTimeTxt" data-datepicker="1" name="activeTime" data-validate="required-date" />
                                        </div>
                                    </div>
                                    <div class="col-sm-4" style="padding-left:0px;position: relative">
                                        <input type="text" id="activeTimeHourTxt" class="form-control" />
                                    </div>
                                </div>
                                <small class="error">Vui lòng chọn ngày thực hiện cấm</small>
                            </div>
                        </div>

                        <br />

                        <div class="row">
                            <span class="col-sm-5">Ngày hết hạn cấm:</span>
                            <div class="col-sm-7">
                                <div class="row">
                                    <div class="col-sm-8" style="padding-right:0px">
                                        <div class="input-group">
                                            <span class="input-group-addon"><em class="fa fa-calendar"></em></span>
                                            <input type="text" class="form-control" id="expiryTimeTxt" placeholder="dd/mm/yyyy" name="expiryTimeDate" />
                                        </div>
                                    </div>
                                    <div class="col-sm-4" style="padding-left:0px">
                                        <input type="text" id="expiryTimeHourTxt" placeholder="hh:mm" class="form-control" />
                                    </div>
                                </div>

                                <small>Để trống nếu muốn cấm vĩnh viễn</small>
                            </div>
                        </div>

                        <br />

                        <div class="row">
                            <span class="col-sm-5">Lý do cấm (*):</span>
                            <div class="col-sm-7">
                                <select name="reasonViolation" class="form-control">
                                    @foreach (var reason in reasonForbidList)
                                    {
                                        <option value="@reason.Value">@reason.Text</option>
                                    }
                                </select>
                                <div style="display: none">
                                    @foreach (var reason in reasonForbidList)
                                    {
                                        <div id="<EMAIL>">@reason.Text</div>
                                    }
                                </div>
                            </div>
                        </div>

                        <br />
                        <div class="row">
                            <span class="col-sm-5">Ghi chú:</span>
                            <div class="col-sm-7">
                                <textarea class="form-control" name="note"></textarea>
                            </div>
                        </div>
                    </fieldset>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-success" id="saveDialogForm">Lưu lại</button>
                <button type="button" class="btn btn-default" data-dismiss="modal">Đóng</button>
            </div>
        </div>
    </div>
</div>


<div id="dialog-unbanned-form" class="modal fade" role="dialog">

    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Hủy cấm người vào ra</h4>
                <button type="button" class="close" data-dismiss="modal">&times;</button>
            </div>
            <div class="modal-body">
                <form id="unbannedForm">
                    <fieldset style="padding: 10px 0px">
                        <legend></legend>
                        <div class="row form-group">
                            <span class="col-sm-4">Lý do hủy:</span>
                            <div class="col-sm-8">
                                <select name="reasonClearId" class="form-control">
                                    @foreach (var reason in reasonCancelForbidList)
                                    {
                                        <option value="@reason.Value">@reason.Text</option>
                                    }
                                </select>
                                <div style="display: none">
                                    @foreach (var reason in reasonCancelForbidList)
                                    {
                                        <div id="<EMAIL>">@reason.Text</div>
                                    }
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <span class="col-sm-4">Ghi chú:</span>
                            <div class="col-sm-8">
                                <textarea name="note" class="form-control"></textarea>
                            </div>
                        </div>
                    </fieldset>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-success" id="clearBannedListSaveBtn">Lưu lại</button>
                <button type="button" class="btn btn-default" data-dismiss="modal">Đóng</button>
            </div>
        </div>
    </div>
</div>

<div id="dialog-rebanned-form" class="modal fade" role="dialog">

    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Cấm người vào ra</h4>
                <button type="button" class="close" data-dismiss="modal">&times;</button>
            </div>
            <div class="modal-body">
                <form id="rebannedForm">
                    <fieldset style="padding: 10px 0px">
                        <legend></legend>
                        <div class="row form-group">
                            <span class="col-sm-4">Thời gian cấm:</span>
                            <div class="col-sm-8">

                                <div class="row">
                                    <div class="col-sm-8" style="padding-right:0px">
                                        <div class="input-group">
                                            <span class="input-group-addon"><em class="fa fa-calendar"></em></span>
                                            <input type="text" class="form-control" placeholder="dd/mm/yyyy" id="thoiGianCamTxt" name="thoiGianCam" />
                                        </div>
                                    </div>
                                    <div class="col-sm-4" style="padding-left:0px">
                                        <input type="text" id="thoiGianCamHourTxt" placeholder="hh:mm" class="form-control" />
                                    </div>
                                </div>

                                <small>Để trống nếu muốn cấm vĩnh viễn</small>
                            </div>
                        </div>
                        <div class="row form-group">
                            <span class="col-sm-4">Lý do cấm (*):</span>
                            <div class="col-sm-8">
                                <select name="reasonViolationId" class="form-control">
                                    @foreach (var reason in reasonForbidList)
                                    {
                                        <option value="@reason.Value">@reason.Text</option>
                                    }
                                </select>
                                <small class="error">Vui lòng chọn lý do cấm</small>
                            </div>
                        </div>
                        <div class="row">
                            <span class="col-sm-4">Ghi chú:</span>
                            <div class="col-sm-8">
                                <textarea name="note" class="form-control"></textarea>
                            </div>
                        </div>
                    </fieldset>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-success" id="saveReBannedBtn">Lưu lại</button>
                <button type="button" class="btn btn-default" data-dismiss="modal">Đóng</button>
            </div>
        </div>
    </div>
</div>

<div id="modal-confirm" class="modal fade" role="dialog">
    <div class="modal-dialog">
        <!-- Modal content-->
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Thông báo</h4>
                <button type="button" class="close" data-dismiss="modal">&times;</button>
            </div>
            <div class="modal-body">
                <p class="clearfix"><span class="fa fa-warning pull-left" style="font-size: 30px;margin-right: 10px"></span><strong class="pull-left" style="font-weight: normal;margin-top: 5px">Bạn có chắc muốn xóa người cấm vào ra này?</strong></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-success" onclick="deleteBannedList()">Tiếp tục xóa</button>
                <button type="button" class="btn btn-default" data-dismiss="modal">Đóng</button>
            </div>
        </div>

    </div>
</div>

<div id="modal-import" class="modal fade" role="dialog">
    <div class="modal-dialog">
        <!-- Modal content-->
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Import danh sách người cấm vào ra</h4>
                <button type="button" class="close" data-dismiss="modal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="container text-center">
                    <input type="file" id="importFile" />
                </div>
                <br />
                <div class="container text-center">
                    <p><a href="@Url.Action("DownloadExcelTemplate", "HumanBannedList", new { f = "Danh Sach Nguoi Cam Vao Ra.xlsx" })">Tải xuống file mẫu</a></p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-success" id="importBtn">Import</button>
                <button type="button" class="btn btn-default" data-dismiss="modal">Đóng</button>
            </div>
        </div>

    </div>
</div>

<div id="modal-detail" class="modal fade" role="dialog">
    <div class="modal-dialog" style="max-width: 900px">
        <!-- Modal content-->
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Thông tin người cấm vào ra</h4>
                <button type="button" class="close" data-dismiss="modal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-6">
                        <div class="row">
                            <strong class="col-sm-5">Mã giấy tờ: </strong>
                            <span class="col-sm-7 info-lb" name="paperCode"></span>
                        </div><br />
                        <div class="row">
                            <strong class="col-sm-5">Họ và tên: </strong>
                            <span class="col-sm-7 info-lb" name="fullName"></span>
                        </div><br />
                        <div class="row">
                            <strong class="col-sm-5">Ngày sinh: </strong>
                            <span class="col-sm-7 info-lb" name="birthDay"></span>
                        </div><br />
                        <div class="row">
                            <strong class="col-sm-5">Nơi sinh: </strong>
                            <span class="col-sm-7 info-lb" name="placeOfBirth"></span>
                        </div><br />
                        <div class="row">
                            <strong class="col-sm-5">Quê quán: </strong>
                            <span class="col-sm-7 info-lb" name="provincialOfBirth"></span>
                        </div><br />
                        <div class="row">
                            <strong class="col-sm-5">Nơi cấp giấy tờ: </strong>
                            <span class="col-sm-7 info-lb" name="placeOfIssue"></span>
                        </div><br />
                        <div class="row">
                            <strong class="col-sm-5">Ngày cấp giấy tờ: </strong>
                            <span class="col-sm-7 info-lb" name="dateOfIssue"></span>
                        </div><br />
                        <div class="row">
                            <strong class="col-sm-5">Công ty: </strong>
                            <span class="col-sm-7 info-lb" name="company"></span>
                        </div><br />
                        <div class="row">
                            <strong class="col-sm-5">Địa chỉ thường trú: </strong>
                            <span class="col-sm-7 info-lb" name="address"></span>
                        </div><br />

                    </div>
                    <div class="col-sm-6">
                        <div class="row">
                            <strong class="col-sm-5">Ngày thực hiện cấm: </strong>
                            <span class="col-sm-7 info-lb" name="activeTime"></span>
                        </div><br />
                        <div class="row">
                            <strong class="col-sm-5">Ngày hết hạn cấm: </strong>
                            <span class="col-sm-7 info-lb" name="expiryTime"></span>
                        </div><br />
                        <div class="row">
                            <strong class="col-sm-5">Lý do vi phạm: </strong>
                            <span class="col-sm-7 info-lb" name="reasonViolation"></span>
                        </div><br />

                        <div class="row">
                            <strong class="col-sm-5">Người tạo: </strong>
                            <span class="col-sm-7 info-lb" name="createdBy"></span>
                        </div><br />
                        <div class="row">
                            <strong class="col-sm-5">Ngày tạo: </strong>
                            <span class="col-sm-7 info-lb" name="createdDate"></span>
                        </div><br />
                        <div class="row">
                            <strong class="col-sm-5">Hủy vi phạm: </strong>
                            <span class="col-sm-7 info-lb" name="clearFlag"></span>
                        </div><br />
                        <div class="row">
                            <strong class="col-sm-5">Ngày hủy: </strong>
                            <span class="col-sm-7 info-lb" name="clearDate"></span>
                        </div><br />
                        <div class="row">
                            <strong class="col-sm-5">Người hủy: </strong>
                            <span class="col-sm-7 info-lb" name="clearBy"></span>
                        </div><br />
                        <div class="row">
                            <strong class="col-sm-5">Lý do hủy: </strong>
                            <span class="col-sm-7 info-lb" name="reasonClear"></span>
                        </div><br />
                        <div class="row">
                            <strong class="col-sm-5">Ghi chú: </strong>
                            <span class="col-sm-7 info-lb" name="note"></span>
                        </div><br />

                    </div>
                </div>


                <hr style="margin-left: -30px;margin-right: -30px" />
                <div>
                    <h3>Lịch sử cấm vào ra</h3>
                    <table class="table table-bordered" id="table-history" cellspacing="0" width="100%">
                        <thead>
                            <tr>
                                <th class="not-export">STT</th>
                                <th>Loại</th>
                                <th>Ngày thực hiện</th>
                                <th>Người thực hiện</th>

                                <th>Ngày thực hiện cấm</th>
                                <th>Ngày hết hạn cấm</th>

                                <th>Lý do cấm</th>

                                <th>Hủy cấm</th>

                                <th>Lý do hủy</th>
                                <th>Ngày hủy</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Đóng</button>
            </div>
        </div>

    </div>
</div>

@section Styles {
    <environment names="Development,Staging,Production">
        <link rel="stylesheet" href="~/lib/toastr/toastr.min.css" />
        <link href="~/css/vehicle-banned-list/jquery.dataTables.min.css" rel="stylesheet">
        <link rel="stylesheet" href="~/lib/bootstrap-datepicker/dist/css/bootstrap-datepicker3.min.css" />
        <link rel="stylesheet" href="~/lib/bootstrap-clockpicker/bootstrap-clockpicker.min.css" />
        <link href="~/css/vehicle-banned-list/bootstrap-multiselect.css" rel="stylesheet" />
        <link href="~/css/vehicle-banned-list/style.css?v=3.0" rel="stylesheet">
    </environment>

    <style type="text/css">
        .clockpicker-popover {
            z-index: 100000000 !important;
        }
    </style>
}

@section Scripts {
    <script>
        var dataType = @((int)PSafe.Common.Enums.EBANNED_DATA_TYPE.BANNED);
        var dataUrl = "@Url.Action("Index", "HumanBannedList")";
        var baseUrl = "@Url.Action("Index", "HumanBannedList")";
        var _fromDate = "@(DateTime.Now.Date.AddDays(-7).ToString("yyyy-MM-dd"))";
        var _toDate = "@(DateTime.Now.Date.ToString("yyyy-MM-dd"))";
        var _expiryDate = "@(DateTime.Now.Date.AddDays(7).ToString("yyyy-MM-dd"))";
        var loginUrl = "@Url.Action("Login", "Security")";
    </script>

    <environment names="Development,Staging,Production">
        <script src="~/lib/bootstrap-datepicker/dist/js/bootstrap-datepicker.min.js"></script>
        <script src="~/lib/bootstrap-datepicker/dist/locales/bootstrap-datepicker.vi.min.js"></script>
        <script src="~/lib/bootstrap-clockpicker/bootstrap-clockpicker.min.js"></script>
        <script src="~/lib/toastr/toastr.min.js"></script>
        <script src="~/js/vehicle-banned-list/bootstrap-multiselect.js"></script>
        <script src="~/js/vehicle-banned-list/datatables.min.js"></script>
        <script src="~/js/vehicle-banned-list/dataTables.buttons.min.js" type="text/javascript"></script>
        <script src="~/js/vehicle-banned-list/buttons.flash.min.js" type="text/javascript"></script>
        <script src="~/js/vehicle-banned-list/jszip.min.js" type="text/javascript"></script>
        <script src="~/js/vehicle-banned-list/pdfmake.min.js" type="text/javascript"></script>
        <script src="~/js/vehicle-banned-list/vfs_fonts.js" type="text/javascript"></script>
        <script src="~/js/vehicle-banned-list/buttons.html5.min.js" type="text/javascript"></script>
        <script src="~/js/human-banned-list/human-banned.js?v=8.0"></script>
    </environment>

    @if (!string.IsNullOrEmpty(ViewBag.Type) && ViewBag.Type == "warning")
    {
        <script type="text/javascript">
            toastr.warning('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Status) && ViewBag.Type == "error")
    {
        <script type="text/javascript">
            toastr.error('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Status) && ViewBag.Type == "success")
    {
        <script type="text/javascript">
            toastr.success('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
}