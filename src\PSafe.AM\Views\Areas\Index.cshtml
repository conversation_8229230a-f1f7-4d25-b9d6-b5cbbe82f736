﻿@model IEnumerable<PSafe.AM.Models.AreaModel>

@{
    ViewBag.Title = "Danh sách";
}
<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-lg-12">
            <div class="ibox">
                <div class="ibox-title">
                    <h5>Danh sách @PSafe.AM.Resources.Resource.Area</h5>
                    <div class="ibox-tools">
                        @Html.ActionLink(PSafe.AM.Resources.Resource.AddItem, "Create", "Areas", null, new { @class = "btn btn-primary btn-xs" })
                    </div>
                </div>
                <div class="ibox-content table-responsive" style="margin:0 auto;">
                    <table class="table table-striped table-bordered table-hover dataTables-list">
                        
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <environment names="Development,Staging,Production">
        <link rel="stylesheet" href="~/lib/dataTables/datatables.min.css" />
        <link rel="stylesheet" href="~/lib/toastr/toastr.min.css" />
    </environment>
}

@section Scripts {
    <environment names="Development,Staging,Production">
        <script src="~/lib/dataTables/datatables.min.js"></script>
        <script src="~/lib/dataTables/dataTables.bootstrap4.min.js"></script>
        <script src="~/lib/toastr/toastr.js"></script>
        <script src="~/lib/toastr/toastr.min.js"></script>
    </environment>

    @if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "warning")
    {
        <script type="text/javascript">
            toastr.warning('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "error")
    {
        <script type="text/javascript">
            toastr.error('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "success")
    {
        <script type="text/javascript">
            toastr.success('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }

    <script type="text/javascript">
        $(document).ready(function () {
            $('.dataTables-list').DataTable({
                "serverSide": false,
                "ajax": {
                    "url": "@Url.Action("GetIndex", "Areas")",
                    "dataType": 'json',
                    "contentType": "application/json; charset=utf-8",
                    "type": "GET",
                    "dataSrc": ''

                },
                "autoWidth": false,
                "columns": [
                    { "title": "@PSafe.AM.Resources.Resource__area.BRANCHNAME", "data": "areaName", "searchable": true },
                    { "title": "@PSafe.AM.Resources.Resource__area.REPRESENTATIVE", "data": "representative", "searchable": true },
                    { "title": "@PSafe.AM.Resources.Resource__area.EMAIL", "data": "email", "searchable": true },
                    { "title": "@PSafe.AM.Resources.Resource__area.CONTACTPHONE", "data": "contactPhone", "searchable": true },
                    { "title": "@PSafe.AM.Resources.Resource__area.DESCRIPTION", "data": "description", "searchable": true },
                    {
                        "title": "Hành động",
                        "data": "id",
                        "searchable": false,
                        "sortable": false,
                        "render": function(data, type, full, meta) {
                            return '<a href="@Url.Action("Details", "Areas")?id=' +
                                data +
                                '" class="btn btn-primary btn-xs">@PSafe.AM.Resources.Resource.Detail</a> ' +
                                '<a href="@Url.Action("Edit", "Areas")?id=' +
                                data +
                                '" class="btn btn-white btn-xs">@PSafe.AM.Resources.Resource.Edit</a> '+
                                '<a href="@Url.Action("Delete", "Areas")?id=' + data +'" class="btn btn-danger btn-xs">@PSafe.AM.Resources.Resource.Delete</a>';
                        }
                    }
                ],
                "oLanguage": {
                    "sProcessing": "Đang xử lý...",
                    "sLengthMenu": "Xem _MENU_ mục",
                    "sZeroRecords": "Không tìm thấy dòng nào phù hợp",
                    "sInfo": "Đang xem _START_ đến _END_ trong tổng số _TOTAL_ mục",
                    "sInfoEmpty": "Đang xem 0 đến 0 trong tổng số 0 mục",
                    "sInfoFiltered": "(được lọc từ _MAX_ mục)",
                    "sInfoPostFix": "",
                    "sLoadingRecords": '<img src="/images/loadding-icon.gif" width="30%"></span>',
                    "sSearch": "Tìm:",
                    "sUrl": "",
                    "oPaginate": {
                        "sFirst": "Đầu",
                        "sPrevious": "Trước",
                        "sNext": "Tiếp",
                        "sLast": "Cuối"
                    }
                },
                pageLength: pageLength_dataTable(),
                dom: '<"html5buttons"B>lTfgitp',
                buttons: [
                    { extend: 'copy' },
                    { extend: 'csv' },
                    { extend: 'excel', title: 'ExampleFile' },
                    { extend: 'pdf', title: 'ExampleFile' },
                    {
                        extend: 'print',
                        customize: function (win) {
                            $(win.document.body).addClass('white-bg');
                            $(win.document.body).css('font-size', '8px');

                            $(win.document.body).find('table')
                                .addClass('compact')
                                .css('font-size', 'inherit');
                        }
                    }
                ]
            });
        });
    </script>
}