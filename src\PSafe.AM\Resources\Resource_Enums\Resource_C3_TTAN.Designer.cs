﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace PSafe.AM.Resources.Resource_Enums {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class Resource_C3_TTAN {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resource_C3_TTAN() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("PSafe.AM.Resources.Resource_Enums.Resource_C3_TTAN", typeof(Resource_C3_TTAN).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tạo SOS.
        /// </summary>
        public static string CreateSOS {
            get {
                return ResourceManager.GetString("CreateSOS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sửa báo cáo.
        /// </summary>
        public static string EditReportEvent {
            get {
                return ResourceManager.GetString("EditReportEvent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lịch tuần tra.
        /// </summary>
        public static string PatrolCamera {
            get {
                return ResourceManager.GetString("PatrolCamera", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Xem view.
        /// </summary>
        public static string PlayView {
            get {
                return ResourceManager.GetString("PlayView", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to In báo cáo.
        /// </summary>
        public static string PrintReport {
            get {
                return ResourceManager.GetString("PrintReport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Xem chi tiết cảnh báo.
        /// </summary>
        public static string ProcessEvent {
            get {
                return ResourceManager.GetString("ProcessEvent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tìm kiếm thiết bị.
        /// </summary>
        public static string SearchDevice {
            get {
                return ResourceManager.GetString("SearchDevice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hiển thị trạng thái thiết bị.
        /// </summary>
        public static string ViewDeviceStatus {
            get {
                return ResourceManager.GetString("ViewDeviceStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hiện thị tin cảnh báo.
        /// </summary>
        public static string ViewEvent {
            get {
                return ResourceManager.GetString("ViewEvent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Xem lịch sử.
        /// </summary>
        public static string ViewHistory {
            get {
                return ResourceManager.GetString("ViewHistory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Xem lịch sử tuần tra.
        /// </summary>
        public static string ViewHistoryPatrol {
            get {
                return ResourceManager.GetString("ViewHistoryPatrol", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Xem báo cáo.
        /// </summary>
        public static string ViewReport {
            get {
                return ResourceManager.GetString("ViewReport", resourceCulture);
            }
        }
    }
}
