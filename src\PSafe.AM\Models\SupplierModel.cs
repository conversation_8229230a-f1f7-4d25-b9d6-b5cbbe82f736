﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.ComponentModel.DataAnnotations;

namespace PSafe.AM.Models
{
    public class SupplierModel
    {
        [Key]
        [Display(Name = "SUPPLIERID", ResourceType = typeof(Resources.Resource__supplier))]
        public int SUPPLIERID { get; set; } 

        [Required(ErrorMessage = "Vui lòng nhập tên nhà cung cấp")]
        [StringLength(255, MinimumLength = 3, ErrorMessage = "Độ dài từ 3-255 ký tự!")]
        [Display(Name = "NAME", ResourceType = typeof(Resources.Resource__supplier))]
        public string NAME { get; set; } = string.Empty;

        [Display(Name = "DESCRIPTION", ResourceType = typeof(Resources.Resource__supplier))]
        public string DESCRIPTION { get; set; } = string.Empty;

        [DataType(DataType.Date)]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd}", ApplyFormatInEditMode = true)]
        [Display(Name = "CREATEDDATE", ResourceType = typeof(Resources.Resource__supplier))]
        public DateTime CREATEDDATE { get; set; }

        [Display(Name = "CREATEDUSER", ResourceType = typeof(Resources.Resource__supplier))]
        public int CREATEDUSER { get; set; }

        [DataType(DataType.Date)]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd}", ApplyFormatInEditMode = true)]
        [Display(Name = "UPDATEDDATE", ResourceType = typeof(Resources.Resource__supplier))]
        public DateTime UPDATEDDATE { get; set; }

        [Display(Name = "UPDATEDUSER", ResourceType = typeof(Resources.Resource__supplier))]
        public int UPDATEDUSER { get; set; }
    }
}