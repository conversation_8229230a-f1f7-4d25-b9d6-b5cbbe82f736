﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace PSafe.AM.Models
{
    public class PatrolForceModel
    {
        [Key]
        [Display(Name = "Id", ResourceType = typeof(Resources.Resource_patrolForce))]
        public int Id { get; set; }

        [Required(ErrorMessage = "Giá trị không được trống!")]
        [StringLength(50, MinimumLength = 1, ErrorMessage = "Độ dài tối đa 50 ký tự!")]
        [Display(Name = "Name", ResourceType = typeof(Resources.Resource_patrolForce))]
        public string Name { get; set; } = string.Empty;

        [Display(Name = "Directions", ResourceType = typeof(Resources.Resource_patrolForce))]
        public string Directions { get; set; }
    }
}
