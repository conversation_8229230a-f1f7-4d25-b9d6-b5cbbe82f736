﻿@model PSafe.AM.Models.DeviceModel

@{
    ViewBag.Title = "Xóa";
}
<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-lg-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>@PSafe.AM.Resources.Resource.Delete @PSafe.AM.Resources.Resource.Device</h5>
                    <div class="ibox-tools">
                        @Html.ActionLink(PSafe.AM.Resources.Resource.BackToList, "Index", "Devices", null, new { @class = "btn btn-primary btn-xs" })
                    </div>
                </div>
                <div class="ibox-content">
                    @using (Html.BeginForm("DeleteConfirmed", "Devices", new { id = Model.DEVICEID }, FormMethod.Post))
                    {
                        @Html.AntiForgeryToken()
                    <div class="row">
                        <div class="col-lg-6">
                            <div class="form-group row">
                                <div class="col-md-4">
                                    <strong>
                                        @Html.DisplayNameFor(model => model.TYPEOFDEVICEID)
                                    </strong>
                                </div>
                                <div class="col-md-8">
                                    @ViewBag.TYPEOFDEVICEID
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                <div class="col-md-4">
                                    <strong>
                                        @Html.DisplayNameFor(model => model.TYPEOFSINGNAL)
                                    </strong>
                                </div>
                                <div class="col-md-8">
                                    @ViewBag.TypeOfSingnal
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">

                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                <div class="col-md-4">
                                    <strong>
                                        @Html.DisplayNameFor(model => model.LOCATIONID)
                                    </strong>
                                </div>
                                <div class="col-md-8">
                                    @ViewBag.LOCATIONID
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                <div class="col-md-4">
                                    <strong>
                                        @Html.DisplayNameFor(model => model.DEVICENAME)
                                    </strong>
                                </div>
                                <div class="col-md-8">
                                    @Html.DisplayFor(model => model.DEVICENAME)
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                <div class="col-md-4">
                                    <strong>
                                        @Html.DisplayNameFor(model => model.SERIALNUMBER)
                                    </strong>
                                </div>
                                <div class="col-md-8">
                                    @Html.DisplayFor(model => model.SERIALNUMBER)
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-6">
                            <div class="form-group row">
                                <div class="col-md-4">
                                    <strong>
                                        @Html.DisplayNameFor(model => model.ACTIVED)
                                    </strong>
                                </div>
                                <div class="col-md-8">
                                    @Html.DisplayFor(model => model.ACTIVED)
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-6">
                            <div class="form-group row">
                                <div class="col-md-4">
                                    <strong>
                                        @Html.DisplayNameFor(model => model.CREATEDDATE)
                                    </strong>
                                </div>
                                <div class="col-md-8">
                                    @Html.ValueFor(model => model.CREATEDDATE, "{0:MM-dd-yyyy}")
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                <div class="col-md-4">
                                    <strong>
                                        @Html.DisplayNameFor(model => model.CREATEDUSER)
                                    </strong>
                                </div>
                                <div class="col-md-8">
                                    @ViewBag.CREATEDUSER
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                <div class="col-md-4">
                                    <strong>
                                        @Html.DisplayNameFor(model => model.UPDATEDDATE)
                                    </strong>
                                </div>
                                <div class="col-md-8">
                                    @Html.ValueFor(model => model.UPDATEDDATE, "{0:MM-dd-yyyy}")
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                <div class="col-md-4">
                                    <strong>
                                        @Html.DisplayNameFor(model => model.UPDATEDUSER)
                                    </strong>
                                </div>
                                <div class="col-md-8">
                                    @ViewBag.UPDATEDUSER
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                <div class="col-md-4">
                                    <strong>
                                        @Html.DisplayNameFor(model => model.SUPPLIERID)
                                    </strong>
                                </div>
                                <div class="col-md-8">
                                    @ViewBag.SUPPLIERID
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-6">
                            <div class="form-group row">
                                <div class="col-md-4">
                                    <strong>
                                        @Html.DisplayNameFor(model => model.Ip)
                                    </strong>
                                </div>
                                <div class="col-md-8">
                                    @Html.DisplayFor(model => model.Ip)
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                <div class="col-md-4">
                                    <strong>
                                        @Html.DisplayNameFor(model => model.MacAddress)
                                    </strong>
                                </div>
                                <div class="col-md-8">
                                    @Html.DisplayFor(model => model.MacAddress)
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-6">
                            <div class="form-group row">
                                <div class="col-md-4">
                                    <strong>
                                        @Html.DisplayNameFor(model => model.DriverName)
                                    </strong>
                                </div>
                                <div class="col-md-8">
                                    @Html.DisplayFor(model => model.DriverName)
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                <div class="col-md-4">
                                    <strong>
                                        @Html.DisplayNameFor(model => model.DESCRIPTION)
                                    </strong>
                                </div>
                                <div class="col-md-8">
                                    @Html.DisplayFor(model => model.DESCRIPTION)
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                <div class="col-md-4">
                                    <strong>
                                        @Html.DisplayNameFor(model => model.SpeedLimit)
                                    </strong>
                                </div>
                                <div class="col-md-8">
                                    @Html.DisplayFor(model => model.SpeedLimit)
                                </div>
                            </div>
                        </div>
                    </div>
                        <input type="submit" value="@PSafe.AM.Resources.Resource.Delete" class="btn btn-danger" />
                        @Html.ActionLink(PSafe.AM.Resources.Resource.Cancel, "Index", "Devices", null, new { @class = "btn btn-white" })
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <environment names="Development,Staging,Production">
        <link rel="stylesheet" href="~/lib/toastr/toastr.min.css" />
    </environment>
}

@section Scripts {
    <environment names="Development,Staging,Production">
        <script src="~/lib/validate/jquery.validate.min.js"></script>
        <script src="~/lib/toastr/toastr.js"></script>
        <script src="~/lib/toastr/toastr.min.js"></script>
    </environment>

    @if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "warning")
    {
        <script type="text/javascript">
            toastr.warning('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "error")
    {
        <script type="text/javascript">
            toastr.error('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "success")
    {
        <script type="text/javascript">
            toastr.success('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
}