﻿using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Configuration;
using PSafe.AM.Common;
using PSafe.AM.Models;
using PSafe.Common.UserEnums;
using PSafe.Core.Domains;
using PSafe.Core.Interface;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using Microsoft.Extensions.Logging;
using static PSafe.Common.CommonEnums;
using PSafe.Common;

namespace PSafe.AM.Controllers
{
    [AuthorizeActionFilter(EPERMISSIONS_AM.DeviceManage)]
    public class LocationsController : Controller
    {
        private readonly ILocationRepository _locationRepository;
        private readonly IAreaRepository _areaRepository;
        private readonly IUserRepository _userRepository;
        private readonly IMapper _mapper;
        private readonly IDocumentRepository _documentRepository;
        private readonly IDocumentBeLongLocationRepository _documentBeLongLocationRepository;
        private readonly IHistorySystemRepository _historySystemRepository;
        private readonly IHttpContextAccessor _accessor;
        private readonly IConfiguration _configuration;
        private readonly ILogger<LocationsController> _logger;

        public LocationsController(ILocationRepository locationRepository, IAreaRepository areaRepository, IUserRepository userRepository, IMapper mapper, IDocumentRepository documentRepository, IDocumentBeLongLocationRepository documentBeLongLocationRepository, IHistorySystemRepository historySystemRepository, IHttpContextAccessor accessor, ILogger<LocationsController> logger, IConfiguration configuration)
        {
            _locationRepository = locationRepository;
            _areaRepository = areaRepository;
            _userRepository = userRepository;
            _mapper = mapper;
            _documentRepository = documentRepository;
            _documentBeLongLocationRepository = documentBeLongLocationRepository;
            _historySystemRepository = historySystemRepository;
            _accessor = accessor;
            _configuration = configuration;
            _logger = logger;
        }

        public ActionResult Index()
        {
            StatusQuery Notification;
            try
            {
                var builder = new ConfigurationBuilder()
                                    .SetBasePath(Directory.GetCurrentDirectory())
                                    .AddJsonFile("appsettings.json");

                var configuration = builder.Build();

                ViewBag.linkLocalMaps = configuration["linkLocalMaps"];

                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Location/Index: " + ex.Message);

                Notification = new StatusQuery("error", "", "Xem danh sách thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;
            }
            return View();
        }

        public JsonResult GetIndex()
        {
            List<LocationModel> _listLocation = new List<LocationModel>();

            try
            {
                var locations = _locationRepository.Get(null, null, "Area");
                _listLocation = (from p in locations
                                 select new LocationModel
                                 {
                                     AreaId = p.AreaId,
                                     CreatedBy = p.CreatedBy,
                                     CreatedDate = p.CreatedDate,
                                     Description = p.Description,
                                     LocationId = p.Id,
                                     LocationName = p.LocationName,
                                     UpdatedBy = p.UpdatedBy,
                                     UpdatedDate = p.UpdatedDate,
                                     Map = p.Map,
                                     AreaName = p.Area == null ? "Trống" : p.Area.AreaName
                                 }).ToList();

                return Json(_listLocation);
            }
            catch (Exception ex)
            {
                _logger.LogError("Location/GetIndex: " + ex.Message);

                return Json(_listLocation);
            }
        }

        [HttpGet]
        public JsonResult GetListDropdownLocations(int AreasId)
        {
            List<DropDownList> ListLocationTemp = new List<DropDownList>();

            var _listLocation = _locationRepository.GetAll().Where(p => p.AreaId == AreasId);

            if (_listLocation != null)
            {
                foreach (var item in _listLocation)
                {
                    DropDownList Temp = new DropDownList
                    {
                        Id = item.Id,
                        Name = item.LocationName
                    };
                    ListLocationTemp.Add(Temp);
                }
            }

            return Json(ListLocationTemp);
        }

        public ActionResult Details(int id)
        {
            try
            {
                var _location = _locationRepository.Get(p => p.Id == id, null, "Area").SingleOrDefault();
                if (_location == null)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));
                    return RedirectToAction("Index");
                }

                if (TempDataHelper.Get<StatusQuery>(TempData, "Notification") is StatusQuery Notification)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }

                var _locationModel = _mapper.Map<Location, LocationModel>(_location);

                var document = _documentRepository.GetAll().ToList();
                var documentBeLongLocation = _documentBeLongLocationRepository.GetAll().Where(p => p.LocationId == id).ToList();
                bool check = false;

                _locationModel.ListDocumentOnLocation = new List<Document>();

                StringBuilder stringhtml = new StringBuilder("");

                foreach (var item in document)
                {
                    check = false;
                    foreach (var itemOnLocation in documentBeLongLocation)
                    {
                        if (itemOnLocation.DocumentId == item.Id)
                        {
                            check = true;
                        }
                    }

                    if (check)
                    {
                        stringhtml.Append(item.FileName + "; ");
                    }
                }

                ViewBag.AreaName = _location.Area == null ? "" : _location.Area.AreaName;
                ViewBag.ListDocument = stringhtml.ToString();

                try
                {
                    ViewBag.UPDATEDUSER = _userRepository.GetById(_locationModel.UpdatedBy).UserName;
                    ViewBag.CREATEDUSER = _userRepository.GetById(_locationModel.CreatedBy).UserName;
                }
                catch (Exception ex)
                {
                    _logger.LogError("Location/Details: " + ex.Message);
                    Console.WriteLine(ex.Message);
                }

                return View(_locationModel);
            }
            catch (Exception ex)
            {
                _logger.LogError("Location/Details: " + ex.Message);
                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                return RedirectToAction("Index");
            }
        }

        public LocationModel GetListDropdown(LocationModel locationModel)
        {
            List<DropDownList> listDropDownListArea = new List<DropDownList>();

            DropDownList Default = new DropDownList
            {
                Id = -1,
                Name = "Chọn"
            };

            listDropDownListArea.Add(Default);

            var areas = _areaRepository.GetAll();

            foreach (var item in areas)
            {
                DropDownList drop = new DropDownList
                {
                    Id = item.Id,
                    Name = item.AreaName
                };
                listDropDownListArea.Add(drop);
            }

            locationModel.ListAreas = ToSelectList(listDropDownListArea);

            var document = _documentRepository.GetAll().ToList();
            var documentBeLongLocation = _documentBeLongLocationRepository.GetAll().Where(p => p.LocationId == locationModel.LocationId).ToList();
            bool check = false;

            locationModel.ListDocumentNotOnLocation = new List<Document>();
            locationModel.ListDocumentOnLocation = new List<Document>();

            foreach (var item in document)
            {
                check = false;
                foreach (var itemOnLocation in documentBeLongLocation)
                {
                    if (itemOnLocation.DocumentId == item.Id)
                    {
                        check = true;
                    }
                }

                if (check)
                {
                    locationModel.ListDocumentOnLocation.Add(item);
                }
                else
                {
                    locationModel.ListDocumentNotOnLocation.Add(item);
                }
            }

            return locationModel;
        }

        [NonAction]
        private SelectList ToSelectList(List<DropDownList> DropDownList)
        {
            List<SelectListItem> list = new List<SelectListItem>();

            foreach (var item in DropDownList)
            {
                list.Add(new SelectListItem()
                {
                    Text = item.Name,
                    Value = item.Id.ToString()
                });
            }

            return new SelectList(list, "Value", "Text");
        }

        public ActionResult Create()
        {
            LocationModel locationModel = new LocationModel();
            try
            {
                locationModel = GetListDropdown(locationModel);
                locationModel.LATITUDE = 0;
                locationModel.LONGITUDE = 0;
            }
            catch (Exception ex)
            {
                _logger.LogError("Location/Create: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "tải lên danh sách vị trí thất bại"));

                return RedirectToAction("Index");
            }

            return View(locationModel);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create([Bind("AreaId, LocationName, Map, ImageFile, Description, LATITUDE, LONGITUDE, Address")] LocationModel locationModel, List<int> document_select)
        {
            StatusQuery Notification;

            if (ModelState.IsValid)
            {
                try
                {
                    var _systemUser = GetSesson();

                    if (_systemUser == null)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                        return RedirectToAction("Logout", "Security");
                    }

                    if (locationModel.ImageFile != null && locationModel.ImageFile.FileName.Length > 0)
                    {
                        var fileName = DateTime.Now.Date.ToString("MM_dd_yyyy") + "_" + locationModel.ImageFile.FileName.Substring(locationModel.ImageFile.FileName.LastIndexOf("\\") + 1);
                        locationModel.Map = fileName;

                        var userNameFTP = Utils.DecodePassword(_configuration.GetSection("FTP:UserName").Value, Utils.EncodeType.SHA_256);
                        var passwordFTP = Utils.DecodePassword(_configuration.GetSection("FTP:Password").Value, Utils.EncodeType.SHA_256);
                        var hostFTP = _configuration.GetSection("FTP:Host").Value + ":" + _configuration.GetSection("FTP:Port").Value;

                        byte[] fileBytes;

                        using (var ms = new MemoryStream())
                        {
                            locationModel.ImageFile.CopyTo(ms);
                            fileBytes = ms.ToArray();
                        }

                        var pathString = "Images/";
                        var checkDirectory = hostFTP + "/";
                        foreach (var item in pathString.Split("/"))
                        {
                            checkDirectory += item + "/";
                            if (!GetDirectoryExits(checkDirectory, userNameFTP, passwordFTP))
                            {
                                CreateDirectory(checkDirectory, userNameFTP, passwordFTP);
                            }
                        }

                        using var client = new WebClient();
                        client.Credentials = new NetworkCredential(userNameFTP, passwordFTP);
                        client.UploadData(checkDirectory + "/" + fileName, fileBytes);
                    }

                    locationModel.CreatedDate = DateTime.Now;
                    locationModel.CreatedBy = _systemUser.Id;
                    locationModel.UpdatedDate = DateTime.Now;
                    locationModel.UpdatedBy = _systemUser.Id;
                    locationModel.Actived = true;

                    var _location = _mapper.Map<LocationModel, Location>(locationModel);

                    _locationRepository.Insert(_location);

                    var statusInsert = _locationRepository.SaveChanges();

                    if (statusInsert > 0)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Thêm mới thành công"));

                        var idLocationInsert = _location.Id;

                        foreach (var item_select in document_select)
                        {
                            DocumentBeLongLocation documentBeLongLocation = new DocumentBeLongLocation
                            {
                                DocumentId = item_select,
                                LocationId = idLocationInsert
                            };

                            _documentBeLongLocationRepository.Insert(documentBeLongLocation);
                        }

                        _documentBeLongLocationRepository.SaveChanges();

                        string StringDescription = new GetStringHistorySystem().Get(_systemUser.UserName, Resources.Resource.Create, _location.LocationName.ToString(), Resources.Resource.Location);

                        InsertHistorySystem((int)EACTION_TYPE.CREATE, (int)EnumControllerName.LOCATIONS, StringDescription, null, _location);

                        return RedirectToAction("Index");
                    }
                    else
                    {
                        Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        locationModel = GetListDropdown(locationModel);

                        return View(locationModel);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("Location/Create: " + ex.Message);

                    Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    locationModel = GetListDropdown(locationModel);

                    return View(locationModel);
                }
            }
            Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;

            locationModel = GetListDropdown(locationModel);

            return View(locationModel);
        }

        public ActionResult Edit(int id)
        {
            try
            {
                var _location = _locationRepository.GetById(id);

                var _locationModel = _mapper.Map<Location, LocationModel>(_location);

                _locationModel = GetListDropdown(_locationModel);

                if (_locationModel != null)
                {
                    return View(_locationModel);
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                    return RedirectToAction("Index");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Location/Edit: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                return RedirectToAction("Index");
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit([Bind("LocationId, Map, AreaId, ImageFile, LocationName, Description, LATITUDE, LONGITUDE, Address")] LocationModel locationModel, List<int> document_select)
        {
            StatusQuery Notification;
            if (ModelState.IsValid)
            {
                try
                {
                    var _systemUser = GetSesson();

                    if (_systemUser == null)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                        return RedirectToAction("Logout", "Security");
                    }

                    var location = _locationRepository.GetById(locationModel.LocationId);

                    var _locationTemp = _mapper.Map<Location, LocationModel>(location);
                    var locationOld = _mapper.Map<LocationModel, Location>(_locationTemp);

                    if (locationModel.ImageFile != null && locationModel.ImageFile.FileName.Length > 0)
                    {
                        var fileName = DateTime.Now.Date.ToString("MM_dd_yyyy") + "_" + locationModel.ImageFile.FileName.Substring(locationModel.ImageFile.FileName.LastIndexOf("\\") + 1);
                        location.Map = fileName;

                        var userNameFTP = Utils.DecodePassword(_configuration.GetSection("FTP:UserName").Value, Utils.EncodeType.SHA_256);
                        var passwordFTP = Utils.DecodePassword(_configuration.GetSection("FTP:Password").Value, Utils.EncodeType.SHA_256);
                        var hostFTP = _configuration.GetSection("FTP:Host").Value + ":" + _configuration.GetSection("FTP:Port").Value;

                        byte[] fileBytes;

                        using (var ms = new MemoryStream())
                        {
                            locationModel.ImageFile.CopyTo(ms);
                            fileBytes = ms.ToArray();
                        }

                        var pathString = "Images/";
                        var checkDirectory = hostFTP + "/";
                        foreach (var item in pathString.Split("/"))
                        {
                            checkDirectory += item + "/";
                            if (!GetDirectoryExits(checkDirectory, userNameFTP, passwordFTP))
                            {
                                CreateDirectory(checkDirectory, userNameFTP, passwordFTP);
                            }
                        }

                        using var client = new WebClient();
                        client.Credentials = new NetworkCredential(userNameFTP, passwordFTP);
                        client.UploadData(checkDirectory + "/" + fileName, fileBytes);
                    }

                    location.AreaId = locationModel.AreaId;
                    location.LocationName = locationModel.LocationName;
                    location.Actived = locationModel.Actived;
                    location.Description = locationModel.Description;

                    location.Latitude = locationModel.LATITUDE;
                    location.Longitude = locationModel.LONGITUDE;

                    location.UpdatedDate = DateTime.Now;
                    location.UpdatedBy = _systemUser.Id;
                    location.Address = locationModel.Address;

                    var listDocumentBeLongLocationOld = _documentBeLongLocationRepository.GetAll().Where(p => p.LocationId == locationModel.LocationId);

                    foreach (var item in listDocumentBeLongLocationOld)
                    {
                        _documentBeLongLocationRepository.Delete(item);
                    }

                    List<DocumentBeLongLocation> listDocumentBeLongLocationNew = new List<DocumentBeLongLocation>();
                    foreach (var item_select in document_select)
                    {
                        DocumentBeLongLocation documentBeLongLocation = new DocumentBeLongLocation
                        {
                            DocumentId = item_select,
                            LocationId = locationModel.LocationId
                        };

                        listDocumentBeLongLocationNew.Add(documentBeLongLocation);
                        _documentBeLongLocationRepository.Insert(documentBeLongLocation);
                    }

                    var updateDocument = _documentBeLongLocationRepository.SaveChanges();

                    if (updateDocument > 0)
                    {
                        string StringDescription = new GetStringHistorySystem().Get(_systemUser.UserName, Resources.Resource.Edit, "Liên kết tài liệu", Resources.Resource.Location);

                        InsertHistorySystem((int)EACTION_TYPE.EDIT, (int)EnumControllerName.DOCUMENTS_BE_LONG, StringDescription, listDocumentBeLongLocationOld, listDocumentBeLongLocationNew);
                    }

                    _locationRepository.Update(location);

                    var updateStatus = _locationRepository.SaveChanges();

                    if (updateStatus > 0)
                    {
                        string StringDescription = new GetStringHistorySystem().Get(_systemUser.UserName, Resources.Resource.Edit, location.LocationName.ToString(), Resources.Resource.Location);

                        InsertHistorySystem((int)EACTION_TYPE.EDIT, (int)EnumControllerName.LOCATIONS, StringDescription, locationOld, location);

                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Sửa thành công"));

                        return RedirectToAction("Index");
                    }
                    else
                    {
                        Notification = new StatusQuery("error", "", "Sửa thất bại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        locationModel = GetListDropdown(locationModel);

                        return View(locationModel);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("Location/Edit: " + ex.Message);

                    Notification = new StatusQuery("error", "", "Sửa thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    locationModel = GetListDropdown(locationModel);

                    return View(locationModel);
                }
            }

            Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;

            locationModel = GetListDropdown(locationModel);

            return View(locationModel);
        }

        public ActionResult Delete(int id)
        {
            StatusQuery Notification;
            try
            {
                var _location = _locationRepository.GetById(id);

                var _locationModel = _mapper.Map<Location, LocationModel>(_location);

                try
                {
                    if (_locationModel.AreaId != null)
                    {
                        ViewBag.AreaName = _areaRepository.GetById(_locationModel.AreaId).AreaName;
                    }

                    ViewBag.UPDATEDUSER = _userRepository.GetById(_locationModel.UpdatedBy).UserName;
                    ViewBag.CREATEDUSER = _userRepository.GetById(_locationModel.CreatedBy).UserName;
                }
                catch (Exception ex)
                {
                    _logger.LogError("Location/Edit: " + ex.Message);
                    Console.WriteLine(ex.Message);
                }

                if (_locationModel == null)
                {
                    Notification = new StatusQuery("warning", "", "Vị trí không tồn tại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return RedirectToAction("Index");
                }

                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }

                return View(_locationModel);
            }
            catch (Exception ex)
            {
                _logger.LogError("Location/Delete: " + ex.Message);

                Notification = new StatusQuery("warning", "", "Xóa thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;

                return RedirectToAction("Index");
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int id)
        {
            try
            {
                var systemUser = GetSesson();

                if (systemUser == null)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                    return RedirectToAction("Logout", "Security");
                }


                var location = _locationRepository.GetBy(p => p.Id == id).FirstOrDefault();

                _locationRepository.Delete(location);

                var deleteStatus = _locationRepository.SaveChanges();

                if (deleteStatus > 0)
                {

                    string StringDescription = new GetStringHistorySystem().Get(systemUser.UserName, Resources.Resource.Delete, location.LocationName.ToString(), Resources.Resource.Location);

                    InsertHistorySystem((int)EACTION_TYPE.DELETE, (int)EnumControllerName.LOCATIONS, StringDescription, location, null);

                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Xóa thành công"));
                    return RedirectToAction("Index");
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));
                    return RedirectToAction("Delete", "Locations", new { id });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Location/DeleteConfirmed: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));
                return RedirectToAction("Delete", "Locations", new { id });
            }
        }

        [HttpGet]
        public JsonResult CountLocationOfArea(int id)
        {
            try
            {
                var count = _locationRepository.GetAll().Count(p => p.AreaId == id);

                return Json(count);
            }
            catch (Exception ex)
            {
                _logger.LogError("Location/CountLocationOfArea: " + ex.Message);
                return Json(0);
            }
        }

        public User GetSesson()
        {
            var SessionUserSystem = HttpContext.Session.GetComplexData<User>("SessionUserSystemId_" + HttpContext.Session.GetString("SessionUserSystemId"));
            if (SessionUserSystem != null)
            {
                var _user = _userRepository.GetById(SessionUserSystem.Id);

                if (_user != null)
                {
                    if (!_user.Actived)
                    {
                        HttpContext.Session.Clear();
                        return null;
                    }
                    return _user;
                }
            }
            return null;
        }

        private void InsertHistorySystem(int action_type, int controllerName, string description, object OldModel, object NewModel)
        {
            try
            {
                var ipAddress = _accessor.HttpContext.Connection.RemoteIpAddress.ToString();

                string jsonOldObject = Newtonsoft.Json.JsonConvert.SerializeObject(OldModel);
                string jsonNewObject = Newtonsoft.Json.JsonConvert.SerializeObject(NewModel);

                HistorySystem history = new HistorySystem
                {
                    ActionType = action_type,
                    ActionTime = DateTime.Now,
                    Description = description,
                    OldObject = jsonOldObject,
                    NewObject = jsonNewObject,
                    UserId = int.Parse(HttpContext.Session.GetString("SessionUserSystemId")),
                    IpAddress = ipAddress,
                    ControllerName = controllerName
                };

                _historySystemRepository.Insert(history);

                _historySystemRepository.SaveChanges();
            }
            catch (Exception ex)
            {
                _logger.LogError("Locations/InsertHistorySystem: " + ex.Message);
            }
        }


        private bool GetDirectoryExits(string path, string userNameFTP, string passwordFTP)
        {
            try
            {
                WebRequest request = WebRequest.Create(path);
                request.Credentials = new NetworkCredential(userNameFTP, passwordFTP);
                request.Method = WebRequestMethods.Ftp.ListDirectory;

                FtpWebResponse response = (FtpWebResponse)request.GetResponse();
                long size = response.ContentLength;
                response.Close();

                return true;
            }
            catch (WebException e)
            {
                _logger.LogInformation("CreateDirectory Status: {0}", e.Message);
                return false;
            }
        }

        private bool CreateDirectory(string path, string userNameFTP, string passwordFTP)
        {
            try
            {
                WebRequest request = WebRequest.Create(path);
                request.Credentials = new NetworkCredential(userNameFTP, passwordFTP);
                request.Method = WebRequestMethods.Ftp.MakeDirectory;
                using var resp = (FtpWebResponse)request.GetResponse();
                if (resp.StatusCode == FtpStatusCode.PathnameCreated)
                {
                    resp.Close();
                    return true;
                }
                resp.Close();
                return false;

            }
            catch (WebException e)
            {
                String status = ((FtpWebResponse)e.Response).StatusDescription;
                _logger.LogError(status);
                return false;
            }
        }
    }
}