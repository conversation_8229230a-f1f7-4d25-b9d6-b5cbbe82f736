﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Add" xml:space="preserve">
    <value>Thêm</value>
  </data>
  <data name="AddItem" xml:space="preserve">
    <value>Thêm mới</value>
  </data>
  <data name="admin" xml:space="preserve">
    <value>Quản trị</value>
  </data>
  <data name="BackToList" xml:space="preserve">
    <value>Quay lại danh sách</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>Hủy</value>
  </data>
  <data name="Copyright" xml:space="preserve">
    <value>Thiết kế và phát triển bởi công ty OTS</value>
  </data>
  <data name="Create" xml:space="preserve">
    <value>Thêm</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Xóa</value>
  </data>
  <data name="DeleteItem" xml:space="preserve">
    <value>Xóa</value>
  </data>
  <data name="Detail" xml:space="preserve">
    <value>Chi tiết</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>Sửa</value>
  </data>
  <data name="EditItem" xml:space="preserve">
    <value>Hiệu chỉnh</value>
  </data>
  <data name="Login" xml:space="preserve">
    <value>Đăng nhập</value>
  </data>
  <data name="Logout" xml:space="preserve">
    <value>Đăng xuất</value>
  </data>
  <data name="Role" xml:space="preserve">
    <value>Nhóm</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>Lưu</value>
  </data>
  <data name="User" xml:space="preserve">
    <value>Người dùng</value>
  </data>
  <data name="ChangePassword" xml:space="preserve">
    <value>Đổi mật khẩu</value>
  </data>
  <data name="CommandCenter" xml:space="preserve">
    <value>Trung tâm chỉ huy</value>
  </data>
  <data name="Group" xml:space="preserve">
    <value>Nhóm</value>
  </data>
  <data name="EditUser" xml:space="preserve">
    <value>Hiệu chỉnh người dùng</value>
  </data>
  <data name="SystemUsers" xml:space="preserve">
    <value>Người dùng</value>
  </data>
  <data name="Area" xml:space="preserve">
    <value>Khu vực</value>
  </data>
  <data name="Device" xml:space="preserve">
    <value>Thiết bị</value>
  </data>
  <data name="Location" xml:space="preserve">
    <value>Vị trí</value>
  </data>
  <data name="TypeOfDevice" xml:space="preserve">
    <value>Loại thiết bị</value>
  </data>
  <data name="Suppliers" xml:space="preserve">
    <value>Nhà cung cấp</value>
  </data>
  <data name="Document" xml:space="preserve">
    <value>Tài liệu</value>
  </data>
  <data name="HistorySystem" xml:space="preserve">
    <value>Lịch sử</value>
  </data>
  <data name="Decentralization" xml:space="preserve">
    <value>Phân quyền</value>
  </data>
  <data name="Position" xml:space="preserve">
    <value>Chức vụ</value>
  </data>
  <data name="Department" xml:space="preserve">
    <value>Phòng ban</value>
  </data>
  <data name="VehicleBannedList" xml:space="preserve">
    <value>Danh sách xe cấm</value>
  </data>
  <data name="ReasonViolation" xml:space="preserve">
    <value>Lý do vi phạm</value>
  </data>
  <data name="HumanBannedList" xml:space="preserve">
    <value>Danh sách người cấm</value>
  </data>
  <data name="HumanAlarmList" xml:space="preserve">
    <value>Danh sách cảnh báo</value>
  </data>
  <data name="PatrolArea" xml:space="preserve">
    <value>Khu vực tuần tra</value>
  </data>
  <data name="ScheduleTime" xml:space="preserve">
    <value>Ca trực</value>
  </data>
  <data name="UserSchedule" xml:space="preserve">
    <value>Lịch trực</value>
  </data>
  <data name="Sync" xml:space="preserve">
    <value>Đồng bộ</value>
  </data>
  <data name="PatrolCamera" xml:space="preserve">
    <value>Kịch bản tuần tra</value>
  </data>
  <data name="PatrolCameraCalendar" xml:space="preserve">
    <value>Lịch tuần tra</value>
  </data>
  <data name="AlarmConfig" xml:space="preserve">
    <value>Cấu hình gửi tin nhắn cảnh báo</value>
  </data>
  <data name="GeneralConfig" xml:space="preserve">
    <value>Cấu hình chung</value>
  </data>
  <data name="TypeOfMarker" xml:space="preserve">
    <value>Loại lực lượng</value>
  </data>
  <data name="Marker" xml:space="preserve">
    <value>Lực lượng</value>
  </data>
  <data name="BannedType" xml:space="preserve">
    <value>Loại vi phạm</value>
  </data>
  <data name="SecurityRecord" xml:space="preserve">
    <value>Hồ sơ an ninh</value>
  </data>
  <data name="SecurityBlackList" xml:space="preserve">
    <value>Danh sách đen</value>
  </data>
  <data name="Site" xml:space="preserve">
    <value>Cảng</value>
  </data>
  <data name="ContactPlace" xml:space="preserve">
    <value>Nơi liên hệ / Kho</value>
  </data>
  <data name="ContactUnit" xml:space="preserve">
    <value>Đơn vị liên hệ / Công ty</value>
  </data>
  <data name="Reason" xml:space="preserve">
    <value>Lý do</value>
  </data>
  <data name="Companys" xml:space="preserve">
    <value>Công ty</value>
  </data>
  <data name="Warehouse" xml:space="preserve">
    <value>Kho</value>
  </data>
  <data name="Address" xml:space="preserve">
    <value>Địa chỉ</value>
  </data>
  <data name="Company" xml:space="preserve">
    <value>Công ty</value>
  </data>
  <data name="PurposeInPort" xml:space="preserve">
    <value>Mục đích vào cảng</value>
  </data>
  <data name="VehicleType" xml:space="preserve">
    <value>Loại phương tiện</value>
  </data>
  <data name="Weight" xml:space="preserve">
    <value>Tải trọng</value>
  </data>
  <data name="BlackList" xml:space="preserve">
    <value>Danh sách đen</value>
  </data>
  <data name="RecycleBin" xml:space="preserve">
    <value>Thùng rác</value>
  </data>
  <data name="District" xml:space="preserve">
    <value>Quận/Huyện</value>
  </data>
  <data name="Province" xml:space="preserve">
    <value>Tỉnh</value>
  </data>
  <data name="PatrolForce" xml:space="preserve">
    <value>Kịch bản tuần tra chiến sỹ</value>
  </data>
  <data name="PatrolForceCalendar" xml:space="preserve">
    <value>Lịch tuần tra chiến sỹ</value>
  </data>
  <data name="RoleCBCS" xml:space="preserve">
    <value>Nhóm CBCS</value>
  </data>
  <data name="UserCBCS" xml:space="preserve">
    <value>CBCS</value>
  </data>
  <data name="Ward" xml:space="preserve">
    <value>Phường/xã</value>
  </data>
</root>