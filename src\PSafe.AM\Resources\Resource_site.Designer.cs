﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace PSafe.AM.Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class Resource_site {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resource_site() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("PSafe.AM.Resources.Resource_site", typeof(Resource_site).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ngày tạo.
        /// </summary>
        public static string CreatedDate {
            get {
                return ResourceManager.GetString("CreatedDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Người tạo.
        /// </summary>
        public static string CreatedUser {
            get {
                return ResourceManager.GetString("CreatedUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Người tạo.
        /// </summary>
        public static string CreatedUserName {
            get {
                return ResourceManager.GetString("CreatedUserName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mã.
        /// </summary>
        public static string SiteId {
            get {
                return ResourceManager.GetString("SiteId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tên cảng.
        /// </summary>
        public static string SiteName {
            get {
                return ResourceManager.GetString("SiteName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ngày cập nhật.
        /// </summary>
        public static string UpdatedDate {
            get {
                return ResourceManager.GetString("UpdatedDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Người cập nhật.
        /// </summary>
        public static string UpdatedUser {
            get {
                return ResourceManager.GetString("UpdatedUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Người cập nhật.
        /// </summary>
        public static string UpdateUserName {
            get {
                return ResourceManager.GetString("UpdateUserName", resourceCulture);
            }
        }
    }
}
