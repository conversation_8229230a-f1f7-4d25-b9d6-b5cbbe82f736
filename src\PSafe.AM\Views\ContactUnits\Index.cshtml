﻿@model IEnumerable<PSafe.AM.Models.ContactUnitModel>

@{
    ViewBag.Title = "Danh sách";
}
<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-lg-12">
            <div class="ibox">
                <div class="ibox-title">
                    <h5>Danh sách @PSafe.AM.Resources.Resource.ContactUnit</h5>
                    <div class="ibox-tools">
                        @Html.ActionLink(PSafe.AM.Resources.Resource.AddItem, "Create", "ContactUnits", null, new { @class = "btn btn-primary btn-xs" })
                    </div>
                </div>
                <div class="ibox-content table-responsive">
                    <table class="table table-striped table-bordered table-hover dataTables-list" id="dataTables-list">
                        <thead>
                            <tr>
                                <th hidden>

                                </th>
                                <th>
                                    @Html.DisplayNameFor(model => model.Name)
                                </th>
                                <th>
                                    @Html.DisplayNameFor(model => model.ContactPlaceName)
                                </th>
                                <th>
                                    @Html.DisplayNameFor(model => model.Description)
                                </th>
                                <th>
                                    @Html.DisplayNameFor(model => model.Index)
                                </th>
                                <th>Hành động</th>
                            </tr>
                        </thead>
                        <tbody>

                            @foreach (var item in Model)
                            {
                            <tr>
                                <td hidden>
                                    @item.Index
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.Name)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.ContactPlaceName)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.Description)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.Index)
                                </td>

                                <td>
                                    @Html.ActionLink(PSafe.AM.Resources.Resource.Detail, "Details", "ContactUnits", new { id = item.Id }, new { @class = "btn btn-primary btn-xs" })
                                    @Html.ActionLink(PSafe.AM.Resources.Resource.Edit, "Edit", "ContactUnits", new { id = item.Id }, new { @class = "btn btn-white btn-xs" })
                                    @Html.ActionLink(PSafe.AM.Resources.Resource.Delete, "Delete", "ContactUnits", new { id = item.Id }, new { @class = "btn btn-danger btn-xs" })
                                </td>
                            </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>


@section Styles {
    <environment names="Development,Staging,Production">
        <link rel="stylesheet" href="~/lib/dataTables/datatables.min.css" />
        <link rel="stylesheet" href="~/lib/toastr/toastr.min.css" />
    </environment>
}

@section Scripts {
    <environment names="Development,Staging,Production">
        <script src="~/lib/dataTables/datatables.min.js"></script>
        <script src="~/lib/dataTables/dataTables.bootstrap4.min.js"></script>
        <script src="~/lib/toastr/toastr.js"></script>
        <script src="~/lib/toastr/toastr.min.js"></script>
    </environment>

    @if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "warning")
    {
        <script type="text/javascript">
            toastr.warning('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "error")
    {
        <script type="text/javascript">
            toastr.error('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "success")
    {
        <script type="text/javascript">
            toastr.success('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }

    <script type="text/javascript">
        $(document).ready(function () {
            var table = $('.dataTables-list').DataTable({
                "oLanguage": {
                    "sProcessing": "Đang xử lý...",
                    "sLengthMenu": "Xem _MENU_ mục",
                    "sZeroRecords": "Không tìm thấy dòng nào phù hợp",
                    "sInfo": "Đang xem _START_ đến _END_ trong tổng số _TOTAL_ mục",
                    "sInfoEmpty": "Đang xem 0 đến 0 trong tổng số 0 mục",
                    "sInfoFiltered": "(được lọc từ _MAX_ mục)",
                    "sInfoPostFix": "",
                    "sSearch": "Tìm:",
                    "sUrl": "",
                    "oPaginate": {
                        "sFirst": "Đầu",
                        "sPrevious": "Trước",
                        "sNext": "Tiếp",
                        "sLast": "Cuối"
                    }
                },
                pageLength: pageLength_dataTable(),
                dom: '<"html5buttons"B>lTfgitp',
                buttons: [
                    { extend: 'copy' },
                    { extend: 'csv' },
                    { extend: 'excel', title: 'ExampleFile' },
                    { extend: 'pdf', title: 'ExampleFile' },
                    {
                        extend: 'print',
                        customize: function (win) {
                            $(win.document.body).addClass('white-bg');
                            $(win.document.body).css('font-size', '8px');

                            $(win.document.body).find('table')
                                .addClass('compact')
                                .css('font-size', 'inherit');
                        }
                    }
                ]
            });

            /*$('#dataTables-list tbody').on('dblclick', 'tr', function () {
                var data = table.row( this ).data();
                var url = "ContactUnits/Details/" + data[1];
                window.location.href = url;
            } );*/
        });
    </script>
}