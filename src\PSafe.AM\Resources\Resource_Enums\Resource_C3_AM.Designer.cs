﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace PSafe.AM.Resources.Resource_Enums {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class Resource_C3_AM {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resource_C3_AM() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("PSafe.AM.Resources.Resource_Enums.Resource_C3_AM", typeof(Resource_C3_AM).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kiểm soát cấm ra vào cho người đi bộ.
        /// </summary>
        public static string AccessControlForPedestrians {
            get {
                return ResourceManager.GetString("AccessControlForPedestrians", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kiểm soát danh sách cấm xe cho TTAN.
        /// </summary>
        public static string BlacklistControlForTTAN {
            get {
                return ResourceManager.GetString("BlacklistControlForTTAN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hủy cấm xe ra vào cổng sai quy định (Xe mượn đường).
        /// </summary>
        public static string CancelCarInOutImproperly {
            get {
                return ResourceManager.GetString("CancelCarInOutImproperly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hủy cấm vì lý do khác.
        /// </summary>
        public static string CancelOther {
            get {
                return ResourceManager.GetString("CancelOther", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hủy cấm vi phạm tải trọng.
        /// </summary>
        public static string CancelViolatingLoadControl {
            get {
                return ResourceManager.GetString("CancelViolatingLoadControl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hủy cấm vi phạm an toàn giao thông.
        /// </summary>
        public static string CancelViolatingTrafficSafety {
            get {
                return ResourceManager.GetString("CancelViolatingTrafficSafety", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hủy cấm vi phạm văn hóa ứng xử.
        /// </summary>
        public static string CancelViolationOfBehavioralCulture {
            get {
                return ResourceManager.GetString("CancelViolationOfBehavioralCulture", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cấm vi phạm an ninh cảng.
        /// </summary>
        public static string CancelViolationOfPortSecurity {
            get {
                return ResourceManager.GetString("CancelViolationOfPortSecurity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quản lý danh mục.
        /// </summary>
        public static string categoryManage {
            get {
                return ResourceManager.GetString("categoryManage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quản lý cấu hình chung.
        /// </summary>
        public static string ConfigurationGeneralManage {
            get {
                return ResourceManager.GetString("ConfigurationGeneralManage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Xóa xe bị cấm vào ra.
        /// </summary>
        public static string DeleteBlackList {
            get {
                return ResourceManager.GetString("DeleteBlackList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quản lý thiết bị.
        /// </summary>
        public static string DeviceManage {
            get {
                return ResourceManager.GetString("DeviceManage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sửa xe bị cấm vào ra.
        /// </summary>
        public static string EditBlackList {
            get {
                return ResourceManager.GetString("EditBlackList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sửa xe ra vào cổng sai quy định (Xe mượn đường).
        /// </summary>
        public static string EditCarInOutImproperly {
            get {
                return ResourceManager.GetString("EditCarInOutImproperly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sửa loại vi phạm Khác.
        /// </summary>
        public static string EditOther {
            get {
                return ResourceManager.GetString("EditOther", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sửa vi phạm kiểm soát tải trọng.
        /// </summary>
        public static string EditViolatingLoadControl {
            get {
                return ResourceManager.GetString("EditViolatingLoadControl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sửa vi phạm an toàn giao thông.
        /// </summary>
        public static string EditViolatingTrafficSafety {
            get {
                return ResourceManager.GetString("EditViolatingTrafficSafety", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sửa vi phạm văn hóa ứng xử.
        /// </summary>
        public static string EditViolationOfBehavioralCulture {
            get {
                return ResourceManager.GetString("EditViolationOfBehavioralCulture", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sửa vi phạm an ninh cảng.
        /// </summary>
        public static string EditViolationOfPortSecurity {
            get {
                return ResourceManager.GetString("EditViolationOfPortSecurity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cấm xe ra vào cổng sai quy định (Xe mượn đường).
        /// </summary>
        public static string ForbidCarInOutImproperly {
            get {
                return ResourceManager.GetString("ForbidCarInOutImproperly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cấm vì lý do khác.
        /// </summary>
        public static string ForbidOther {
            get {
                return ResourceManager.GetString("ForbidOther", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cấm vi phạm kiểm soát tải trọng.
        /// </summary>
        public static string ForbidViolatingLoadControl {
            get {
                return ResourceManager.GetString("ForbidViolatingLoadControl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cấm vi phạm an toàn giao thông.
        /// </summary>
        public static string ForbidViolatingTrafficSafety {
            get {
                return ResourceManager.GetString("ForbidViolatingTrafficSafety", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cấm vi phạm văn hóa ứng xử.
        /// </summary>
        public static string ForbidViolationOfBehavioralCulture {
            get {
                return ResourceManager.GetString("ForbidViolationOfBehavioralCulture", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cấm vi phạm an ninh cảng.
        /// </summary>
        public static string ForbidViolationOfPortSecurity {
            get {
                return ResourceManager.GetString("ForbidViolationOfPortSecurity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quản lý lịch tuần tra ảo.
        /// </summary>
        public static string SchedulePatrolvirtualManage {
            get {
                return ResourceManager.GetString("SchedulePatrolvirtualManage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kiểm soát danh sách đen an ninh.
        /// </summary>
        public static string SecurityBlacklist {
            get {
                return ResourceManager.GetString("SecurityBlacklist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hồ sơ an ninh.
        /// </summary>
        public static string SecurityRecord {
            get {
                return ResourceManager.GetString("SecurityRecord", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quản lý người dùng.
        /// </summary>
        public static string UserManage {
            get {
                return ResourceManager.GetString("UserManage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Xem xe bị cấm vào ra.
        /// </summary>
        public static string ViewBlackList {
            get {
                return ResourceManager.GetString("ViewBlackList", resourceCulture);
            }
        }
    }
}
