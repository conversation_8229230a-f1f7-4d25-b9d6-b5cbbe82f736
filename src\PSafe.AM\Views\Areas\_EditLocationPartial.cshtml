﻿@model PSafe.AM.Models.LocationModel

@{
    Layout = null;
}

<div class="modal-dialog">
    <div class="modal-content">
        <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
            <h4 class="modal-title">@PSafe.AM.Resources.Resource.Edit @PSafe.AM.Resources.Resource.Location</h4>
        </div>
        @using (Html.BeginForm("EditLocation", "Areas", FormMethod.Post, new { @class = "form-horizontal", role = "form", enctype = "multipart/form-data" }))
        {
            @Html.AntiForgeryToken()
            @Html.HiddenFor(model => model.AreaId)
            <div class="modal-body">
                <div class="form-horizontal">

                    @Html.ValidationSummary(true, "Lỗi nhập liệu!", new { @class = "text-danger" })
                    @Html.HiddenFor(model => model.LocationId)

                    <div class="form-group">
                        <label class="control-label col-md-12">@Html.LabelFor(model => model.LocationName) (*)</label>
                        <div class="col-md-12">
                            @Html.EditorFor(model => model.LocationName, new { htmlAttributes = new { @minlength = "3", @required = "required", @oninput = "this.setCustomValidity('')", @oninvalid = "this.setCustomValidity('Độ dài từ 3-255 ký tự!')" } })
                            @Html.ValidationMessageFor(model => model.LocationName, null, new { @class = "text-danger" })
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="control-label col-md-2">@Html.LabelFor(model => model.Map) (*)</label>
                        <div class="btn-toolbar col-md-10" style="margin-left:-15px;" role="toolbar" aria-label="Toolbar with button groups">
                            <div class="btn-group col-lg-5" style="padding-bottom: 5px;" role="group" aria-label="First group">
                                @Html.TextBoxFor(model => model.Map, new { disabled = "disabled", @class = "form-control" })
                            </div>
                            <div class="btn-group col-lg-4" role="group" aria-label="Third group">
                                <input type="file" name="ImageFile" id="ImageFile" class="btn btn-primary btn-file" accept=".jpg, .jpeg, .gif, .png" />
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        @Html.LabelFor(model => model.Document, new { @class = "control-label col-md-2" })
                        <div class="col-md-12">
                            <select data-placeholder="Chọn tài liệu..." class="chosen-select" name="document_select" multiple tabindex="4">
                                @foreach (var item in Model.ListDocumentOnLocation)
                                {
                                    <option selected="selected" value="@item.Id">@Html.DisplayFor(modelItem => item.FileName)</option>
                                }
                                @foreach (var item in Model.ListDocumentNotOnLocation)
                                {
                                    <option value="@item.Id">@Html.DisplayFor(modelItem => item.FileName)</option>
                                }
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        @Html.LabelFor(model => model.Description, new { @class = "control-label col-md-12" })
                        <div class="col-md-12">
                            @Html.EditorFor(model => model.Description)
                            @Html.ValidationMessageFor(model => model.Description, null, new { @class = "text-danger" })
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <input type="submit" value="@PSafe.AM.Resources.Resource.Save" class="btn btn-primary" />
                <button type="button" class="btn btn-white" data-dismiss="modal">@PSafe.AM.Resources.Resource.Cancel</button>
            </div>
        }
    </div>
</div>

<script type="text/javascript">
        $('.chosen-select').chosen({ width: "100%" });

</script>