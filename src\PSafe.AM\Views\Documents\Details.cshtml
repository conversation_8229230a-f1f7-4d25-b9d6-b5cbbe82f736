﻿@model PSafe.AM.Models.DocumentModel

@{
    ViewBag.Title = "Chi tiết";
}
<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-lg-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>@PSafe.AM.Resources.Resource.Detail @PSafe.AM.Resources.Resource.Document</h5>
                    <div class="ibox-tools">
                        @Html.ActionLink(PSafe.AM.Resources.Resource.BackToList, "Index", "Documents", null, new { @class = "btn btn-primary btn-xs" })
                    </div>
                </div>
                <div class="ibox-content">

                    <div class="row">
                        <div class="col-lg-6">
                            <div class="form-group row">
                                <div class="col-md-4">
                                    <strong>
                                        @Html.DisplayNameFor(model => model.DocumentName)
                                    </strong>
                                </div>
                                <div class="col-md-8">
                                    @Html.DisplayFor(model => model.DocumentName)
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                <div class="col-md-4">
                                    <strong>
                                        @Html.DisplayNameFor(model => model.FileName)
                                    </strong>
                                </div>
                                <div class="col-md-8">
                                    @Html.DisplayFor(model => model.FileName)
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                <div class="col-md-4">
                                    <strong>
                                        @Html.DisplayNameFor(model => model.Type)
                                    </strong>
                                </div>
                                <div class="col-md-8">
                                    @ViewBag.type
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                <div class="col-md-4">
                                    <strong>
                                        @Html.DisplayNameFor(model => model.CreatedDate)
                                    </strong>
                                </div>
                                <div class="col-md-8">
                                    @Html.DisplayFor(model => model.CreatedDate, "{0:MM-dd-yyyy}")
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                <div class="col-md-4">
                                    <strong>
                                        @Html.DisplayNameFor(model => model.CreatedBy)
                                    </strong>
                                </div>
                                <div class="col-md-8">
                                    @ViewBag.CREATEDUSER
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                <div class="col-md-4">
                                    <strong>
                                        @Html.DisplayNameFor(model => model.UpdatedDate)
                                    </strong>
                                </div>
                                <div class="col-md-8">
                                    @Html.DisplayFor(model => model.UpdatedDate, "{0:MM-dd-yyyy}")
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                <div class="col-md-4">
                                    <strong>
                                        @Html.DisplayNameFor(model => model.UpdatedBy)
                                    </strong>
                                </div>
                                <div class="col-md-8">
                                    @ViewBag.UPDATEDUSER
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-6">
                            <div class="form-group row">
                                <div class="col-md-4">
                                    <strong>
                                        @Html.DisplayNameFor(model => model.Description)
                                    </strong>
                                </div>
                                <div class="col-md-8">
                                    @Html.DisplayFor(model => model.Description)
                                </div>
                            </div>
                        </div>
                    </div>

                    @Html.ActionLink(PSafe.AM.Resources.Resource.Edit, "Edit", "Documents", new { id = Model.Id }, new { @class = "btn btn-primary" })
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <environment names="Development,Staging,Production">
        <link rel="stylesheet" href="~/lib/toastr/toastr.min.css" />
    </environment>
}

@section Scripts {
    <environment names="Development,Staging,Production">
        <script src="~/lib/validate/jquery.validate.min.js"></script>
        <script src="~/lib/toastr/toastr.js"></script>
        <script src="~/lib/toastr/toastr.min.js"></script>
    </environment>

    @if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "warning")
    {
        <script type="text/javascript">
            toastr.warning('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "error")
    {
        <script type="text/javascript">
            toastr.error('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "success")
    {
        <script type="text/javascript">
            toastr.success('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
}