﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using PSafe.AM.Common;
using PSafe.AM.Models;
using PSafe.Core.Domains;
using PSafe.Core.Interface;
using Microsoft.AspNetCore.Http;
using PSafe.Common.UserEnums;
using Microsoft.Extensions.Logging;
using System.Globalization;

namespace PSafe.AM.Controllers
{
    [AuthorizeActionFilter(EPERMISSIONS_AM.SchedulePatrolvirtualManage)]
    public class PatrolForceCalendarController : Controller
    {
        private readonly IPatrolForceCalendarRepository _patrolCameraCalendarRepository;
        private readonly IPatrolForceRepository _patrolCameraRepository;
        private readonly IUserRepository _userRepository;
        private readonly IRoleRepository _roleRepository;
        private readonly IUserInRoleRepository _userInRoleRepository;
        private readonly IHistorySystemRepository _historySystemRepository;
        private readonly ILogger<PatrolForceCalendarController> _logger;

        public PatrolForceCalendarController(IPatrolForceCalendarRepository patrolCameraCalendarRepository, IPatrolForceRepository patrolCameraRepository,
            IUserRepository userRepository, IHistorySystemRepository historySystemRepository, ILogger<PatrolForceCalendarController> logger,
            IRoleRepository roleRepository, IUserInRoleRepository userInRoleRepository)
        {
            _patrolCameraCalendarRepository = patrolCameraCalendarRepository;
            _patrolCameraRepository = patrolCameraRepository;
            _userRepository = userRepository;
            _historySystemRepository = historySystemRepository;
            _userInRoleRepository = userInRoleRepository;
            _roleRepository = roleRepository;
            _logger = logger;
        }

        public IActionResult Index()
        {
            StatusQuery Notification;
            List<PatrolCameraCalendarModel> _listPatrolCameraCalendarModel = new List<PatrolCameraCalendarModel>();

            try
            {
                _listPatrolCameraCalendarModel = GetListPatrolCameraCalendarModel().OrderBy(p => p.Dates).ToList();
                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("PatrolLocationCalendar/Index: " + ex.Message);

                Notification = new StatusQuery("error", "", "Xem danh sách thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;
            }

            return View(_listPatrolCameraCalendarModel);
        }

        // GET: /typeOfDevice/Details/
        public ActionResult Details(int id)
        {
            try
            {
                var _patrolForceCalendar = _patrolCameraCalendarRepository.GetById(id);

                if (_patrolForceCalendar != null)
                {
                    var _patrolCameraCalendarModel = new PatrolCameraCalendarModel
                    {
                        Id = id
                    };
                    CopyTo(_patrolCameraCalendarModel, _patrolForceCalendar);
                    var patrolCamera = _patrolCameraRepository.GetBy(x => x.Id == _patrolForceCalendar.PatrolForceId).FirstOrDefault();
                    _patrolCameraCalendarModel.PatrolCameraName = patrolCamera == null ? string.Empty : patrolCamera.Name;
                    try
                    {
                        if (TempDataHelper.Get<StatusQuery>(TempData, "Notification") is StatusQuery Notification)
                        {
                            ViewBag.Status = Notification.Status;
                            ViewBag.Value = Notification.Value;
                            ViewBag.Type = Notification.Type;
                        }

                    }
                    catch (Exception ex)
                    {
                        _logger.LogError("PatrolLocationCalendar/Details: " + ex.Message);
                        Console.WriteLine(ex.Message);
                    }

                    return View(_patrolCameraCalendarModel);
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                    return RedirectToAction("Index");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("PatrolLocationCalendar/Details: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                return RedirectToAction("Index");
            }
        }

        public ActionResult Create()
        {
            SetComboboxData();
            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create(PatrolCameraCalendarModel patrolCameraCalendarModel)
        {
            StatusQuery Notification;
            if (ModelState.IsValid)
            {
                if (patrolCameraCalendarModel.BeginTime >= patrolCameraCalendarModel.EndTime)
                {
                    SetComboboxData();
                    Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                    ModelState.AddModelError("BeginTime", "Thời gian bắt đầu phải nhỏ hơn thời gian kết thúc");
                    return View(patrolCameraCalendarModel);
                }

                var systemUser = GetSesson();
                if (systemUser == null)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                    return RedirectToAction("Logout", "Security");
                }

                var role = _roleRepository.GetById(patrolCameraCalendarModel.RoleId);
                if (role == null)
                {
                    Notification = new StatusQuery("error", "", "Thông tin nhóm/tổ không tồn tại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                    SetComboboxData();
                    return View(patrolCameraCalendarModel);
                }

                var listUser = _userInRoleRepository.GetBy(p => p.RoleId == patrolCameraCalendarModel.RoleId).ToList().Select(p => p.UserId).ToList();
                patrolCameraCalendarModel.UserId = listUser;
                patrolCameraCalendarModel.RoleName = role.RoleName;

                PatrolForceCalendar patrolForceCalendar = new PatrolForceCalendar();
                CopyTo(patrolForceCalendar, patrolCameraCalendarModel);
                try
                {
                    _patrolCameraCalendarRepository.Insert(patrolForceCalendar);
                    var statusInsert = _patrolCameraCalendarRepository.SaveChanges();

                    if (statusInsert > 0)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Thêm mới thành công"));
                        return RedirectToAction("Index");
                    }
                    else
                    {
                        SetComboboxData();
                        Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        return View(patrolCameraCalendarModel);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("PatrolLocationCalendar/Create: " + ex.Message);

                    SetComboboxData();
                    Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return View(patrolCameraCalendarModel);
                }
            }
            SetComboboxData();
            Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;

            return View(patrolCameraCalendarModel);
        }

        // GET: /typeOfDevice/Edit/5
        public ActionResult Edit(int id)
        {
            try
            {
                var patrolCameraCalendar = _patrolCameraCalendarRepository.GetById(id);
                if (patrolCameraCalendar != null)
                {
                    PatrolCameraCalendarModel patrolCameraCalendarModel = new PatrolCameraCalendarModel
                    {
                        Id = patrolCameraCalendar.Id,
                        RoleId = patrolCameraCalendar.RoleId.Value
                    };
                    CopyTo(patrolCameraCalendarModel, patrolCameraCalendar);
                    SetComboboxData();

                    return View(patrolCameraCalendarModel);
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Không tìm thấy kịch bản tuần tra cần sửa"));

                    return RedirectToAction("Index");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("PatrolLocationCalendar/Edit: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                return RedirectToAction("Index");
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit(PatrolCameraCalendarModel patrolCameraCalendarModel)
        {
            StatusQuery Notification;
            if (ModelState.IsValid)
            {
                try
                {
                    if (patrolCameraCalendarModel.BeginTime >= patrolCameraCalendarModel.EndTime)
                    {
                        SetComboboxData();
                        Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;
                        ModelState.AddModelError("BeginTime", "Thời gian bắt đầu phải nhỏ hơn thời gian kết thúc");
                        return View(patrolCameraCalendarModel);
                    }

                    var systemUser = GetSesson();
                    if (systemUser == null)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                        return RedirectToAction("Logout", "Security");
                    }

                    var role = _roleRepository.GetById(patrolCameraCalendarModel.RoleId);
                    if (role == null)
                    {
                        Notification = new StatusQuery("error", "", "Thông tin nhóm/tổ không tồn tại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;
                        SetComboboxData();
                        return View(patrolCameraCalendarModel);
                    }

                    var listUser = _userInRoleRepository.GetBy(p => p.RoleId == patrolCameraCalendarModel.RoleId).ToList().Select(p => p.UserId).ToList();
                    patrolCameraCalendarModel.UserId = listUser;
                    patrolCameraCalendarModel.RoleName = role.RoleName;

                    var _patrolCameraCalendar = _patrolCameraCalendarRepository.GetById(patrolCameraCalendarModel.Id);
                    if (_patrolCameraCalendar == null)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Không tìm thấy lịch tuần tra cần sửa"));

                        return RedirectToAction("Index");
                    }

                    CopyTo(_patrolCameraCalendar, patrolCameraCalendarModel);
                    _patrolCameraCalendarRepository.Update(_patrolCameraCalendar);

                    var updateStatus = _patrolCameraCalendarRepository.SaveChanges();

                    if (updateStatus > 0)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Sửa thành công"));

                        return RedirectToAction("Index");
                    }
                    else
                    {
                        Notification = new StatusQuery("error", "", "Sửa thất bại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;
                        SetComboboxData();
                        return View(patrolCameraCalendarModel);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("PatrolLocationCalendar/Edit: " + ex.Message);

                    Notification = new StatusQuery("error", "", "Sửa thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                    SetComboboxData();
                    return View(patrolCameraCalendarModel);
                }
            }

            Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;
            SetComboboxData();
            return View(patrolCameraCalendarModel);
        }

        // GET: /typeOfDevice/Delete/5
        public ActionResult Delete(int id)
        {
            StatusQuery Notification;
            try
            {
                var _patrolForceCalendar = _patrolCameraCalendarRepository.GetById(id);

                if (_patrolForceCalendar == null)
                {
                    Notification = new StatusQuery("warning", "", "Không tìm thấy lịch tuần tra cần xóa");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return RedirectToAction("Index");
                }

                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }

                PatrolCameraCalendarModel patrolCameraCalendarModel = new PatrolCameraCalendarModel();
                CopyTo(patrolCameraCalendarModel, _patrolForceCalendar);
                var patrolCamera = _patrolCameraRepository.GetBy(x => x.Id == _patrolForceCalendar.PatrolForceId).FirstOrDefault();
                patrolCameraCalendarModel.PatrolCameraName = patrolCamera == null ? string.Empty : patrolCamera.Name;
                return View(patrolCameraCalendarModel);
            }
            catch (Exception ex)
            {
                _logger.LogError("PatrolLocationCalendar/Delete: " + ex.Message);

                Notification = new StatusQuery("warning", "", "Xóa thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;

                return RedirectToAction("Index");
            }
        }

        // POST: /ScheduleTime/Delete/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int id)
        {
            try
            {
                var systemUserSession = GetSesson();

                if (systemUserSession == null)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                    return RedirectToAction("Logout", "Security");
                }

                var patrolCameraCalendar = _patrolCameraCalendarRepository.GetById(id);
                _patrolCameraCalendarRepository.Delete(patrolCameraCalendar);

                var deleteStatus = _patrolCameraCalendarRepository.SaveChanges();

                if (deleteStatus > 0)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Xóa thành công"));
                    return RedirectToAction("Index");
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));
                    return RedirectToAction("Delete", "PatrolLocationCalendar", new { id });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("PatrolLocationCalendar/DeleteConfirmed: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));
                return RedirectToAction("Delete", "PatrolLocationCalendar", new { id });
            }
        }

        public User GetSesson()
        {
            var SessionUserSystem = HttpContext.Session.GetComplexData<User>("SessionUserSystemId_" + HttpContext.Session.GetString("SessionUserSystemId"));
            if (SessionUserSystem != null)
            {
                var _user = _userRepository.GetById(SessionUserSystem.Id);

                if (_user != null)
                {
                    if (!_user.Actived)
                    {
                        HttpContext.Session.Clear();
                        return null;
                    }
                    return _user;
                }
            }
            return null;
        }

        private void InsertHistorySystem(int action_type, int controllerName, string description, object OldModel, object NewModel)
        {
            IPHostEntry heserver = Dns.GetHostEntry(Dns.GetHostName());
            var ipAddress = heserver.AddressList.FirstOrDefault(p => p.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork).ToString();

            string jsonOldObject = Newtonsoft.Json.JsonConvert.SerializeObject(OldModel);
            string jsonNewObject = Newtonsoft.Json.JsonConvert.SerializeObject(NewModel);

            HistorySystem history = new HistorySystem
            {
                ActionType = action_type,
                ActionTime = DateTime.Now,
                Description = description,
                OldObject = jsonOldObject,
                NewObject = jsonNewObject,
                UserId = int.Parse(HttpContext.Session.GetString("SessionUserSystemId")),
                IpAddress = ipAddress,
                ControllerName = controllerName
            };

            _historySystemRepository.Insert(history);

            _historySystemRepository.SaveChanges();
        }

        private void SetComboboxData()
        {
            //List<SelectListItem> userListItems = GetUserList();
            List<SelectListItem> roleListItems = GetRoleList();
            List<SelectListItem> patrolCameraListItems = GetPatrolCameraList();

            ViewBag.roleListItems = roleListItems;
            ViewBag.patrolCameraListItems = patrolCameraListItems;
        }

        private List<SelectListItem> GetUserList()
        {
            List<SelectListItem> userListItems = new List<SelectListItem>();
            userListItems.Add(new SelectListItem()
            {
                Value = "0",
                Text = "Tất cả"
            });
            try
            {
                var userList = _userRepository.GetAll().ToList();
                foreach (var user in userList)
                {
                    userListItems.Add(new SelectListItem()
                    {
                        Text = user.UserName,
                        Value = user.Id.ToString()
                    });
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
            }

            return userListItems;
        }

        private List<SelectListItem> GetRoleList()
        {
            List<SelectListItem> userListItems = new List<SelectListItem>();
            try
            {
                var roleList = _roleRepository.GetAll().ToList();
                foreach (var role in roleList)
                {
                    userListItems.Add(new SelectListItem()
                    {
                        Text = role.RoleName,
                        Value = role.RoleId.ToString()
                    });
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
            }

            return userListItems;
        }

        private List<SelectListItem> GetPatrolCameraList()
        {
            List<SelectListItem> patrolCameraListItems = new List<SelectListItem>();
            try
            {
                var patrolCameraList = _patrolCameraRepository.GetAll().ToList();
                foreach (var patrolCamera in patrolCameraList)
                {
                    patrolCameraListItems.Add(new SelectListItem()
                    {
                        Text = patrolCamera.Name,
                        Value = patrolCamera.Id.ToString()
                    });
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
            }

            return patrolCameraListItems;
        }

        private List<PatrolCameraCalendarModel> GetListPatrolCameraCalendarModel()
        {
            List<PatrolCameraCalendarModel> _listPatrolForceCalendarModel = new List<PatrolCameraCalendarModel>();
            List<PatrolForceCalendar> _listPatrolForceCalendar = _patrolCameraCalendarRepository.GetAll().OrderByDescending(x => x.Id).ToList();

            List<int> listPatrolCameraIdNeedGet = new List<int>();
            foreach (var patrolCameraCalendar in _listPatrolForceCalendar)
            {
                if (!listPatrolCameraIdNeedGet.Contains(patrolCameraCalendar.PatrolForceId))
                    listPatrolCameraIdNeedGet.Add(patrolCameraCalendar.PatrolForceId);
            }

            List<PatrolForce> _listPatrolCamera = _patrolCameraRepository.GetBy(x => listPatrolCameraIdNeedGet.Contains(x.Id)).ToList();

            foreach (var patrolForceCalendar in _listPatrolForceCalendar)
            {
                PatrolCameraCalendarModel patrolCameraCalendarModel = new PatrolCameraCalendarModel
                {
                    Id = patrolForceCalendar.Id
                };

                PatrolForce patrolCamera = _listPatrolCamera.FirstOrDefault(x => x.Id == patrolForceCalendar.PatrolForceId);
                patrolCameraCalendarModel.PatrolCameraName = patrolCamera == null ? string.Empty : patrolCamera.Name;
                CopyTo(patrolCameraCalendarModel, patrolForceCalendar);
                _listPatrolForceCalendarModel.Add(patrolCameraCalendarModel);
            }

            return _listPatrolForceCalendarModel;
        }

        private void CopyTo(PatrolForceCalendar dest, PatrolCameraCalendarModel src)
        {
            dest.PatrolForceId = src.PatrolCameraId;
            dest.PatrollingStatus = src.PatrollingStatus;
            dest.UserId = src.UserId;
            dest.BeginTime = new TimeSpan(src.BeginTime.Hour, src.BeginTime.Minute, src.BeginTime.Second);
            dest.EndTime = new TimeSpan(src.EndTime.Hour, src.EndTime.Minute, src.EndTime.Second);
            dest.Dates = ConvertListStringToListDateTime(src.DateStrings);
            dest.Description = src.Description;
            dest.RoleName = src.RoleName;
            dest.RoleId = src.RoleId;
        }

        private List<DateTime> ConvertListStringToListDateTime(string listDateString)
        {
            List<DateTime> dateList = new List<DateTime>();
            if (listDateString.Length <= 0)
                return dateList;

            var listDateSrc = listDateString.Split(",").ToList();

            CultureInfo provider = CultureInfo.InvariantCulture;
            var format = "dd/MM/yyyy";
            foreach (string dateString in listDateSrc)
            {
                try
                {
                    var date = DateTime.ParseExact(dateString, format, provider);
                    dateList.Add(date);
                }
                catch (FormatException)
                {

                }
            }
            return dateList;
        }

        private void CopyTo(PatrolCameraCalendarModel dest, PatrolForceCalendar src)
        {
            dest.PatrolCameraId = src.PatrolForceId;
            dest.PatrollingStatus = src.PatrollingStatus;
            dest.UserId = src.UserId;
            dest.BeginTime = new DateTime(2019, 10, 10, src.BeginTime.Hours, src.BeginTime.Minutes, src.BeginTime.Seconds);
            dest.EndTime = new DateTime(2019, 10, 10, src.EndTime.Hours, src.EndTime.Minutes, src.EndTime.Seconds);
            if (src.Dates != null && src.Dates.Count > 0)
                dest.DateStrings = string.Join(",", src.Dates.Select(p => p.ToString("dd/MM/yyyy")));
            dest.Description = src.Description;
            dest.RoleName = src.RoleName;
            dest.RoleId = src.RoleId;
        }
    }
}