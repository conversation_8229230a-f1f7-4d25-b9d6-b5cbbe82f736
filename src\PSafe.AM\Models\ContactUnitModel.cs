﻿using Microsoft.AspNetCore.Mvc.Rendering;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace PSafe.AM.Models
{
    public class ContactUnitModel
    {
        [Key]
        [Display(Name = "Id", ResourceType = typeof(Resources.Resource_contactUnit))]
        public int Id { get; set; }

        [Required(ErrorMessage = "Vui lòng nhập tên đơn vị liên hệ")]
        [StringLength(255, MinimumLength = 1, ErrorMessage = "Độ dài từ 1-255 ký tự")]
        [Display(Name = "Name", ResourceType = typeof(Resources.Resource_contactUnit))]
        public string Name { get; set; } = string.Empty;

        [Display(Name = "CreatedBy", ResourceType = typeof(Resources.Resource_contactUnit))]
        public int CreatedBy { get; set; }

        [Display(Name = "UpdatedBy", ResourceType = typeof(Resources.Resource_contactUnit))]
        public int UpdatedBy { get; set; }

        [Display(Name = "CreatedDate", ResourceType = typeof(Resources.Resource_contactUnit))]
        public DateTime CreatedDate { get; set; }

        [Display(Name = "UpdatedDate", ResourceType = typeof(Resources.Resource_contactUnit))]
        public DateTime UpdatedDate { get; set; }

        [Display(Name = "Description", ResourceType = typeof(Resources.Resource_contactUnit))]
        public string Description { get; set; } = string.Empty;

        [Required(ErrorMessage = "Vui lòng chọn nơi liên hệ")]
        [Display(Name = "ContactPlace", ResourceType = typeof(Resources.Resource))]
        public int[] ContactPlaceIds { get; set; } = new int[0];

        [Display(Name = "ContactPlace", ResourceType = typeof(Resources.Resource))]
        public string ContactPlaceName { get; set; } = string.Empty;

        [Display(Name = "Index", ResourceType = typeof(Resources.Resource_contactUnit))]
        [Required]
        [Range(0, int.MaxValue)]
        public int Index { get; set; } = 0;

        public SelectList ContactPlaces { get; set; }
    }
}
