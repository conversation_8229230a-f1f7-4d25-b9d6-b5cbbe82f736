﻿@using PSafe.Core.Domains
@using PSafe.AM.Common

<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-lg-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>@ViewBag.actionTypeName @ViewBag.Resource</h5>
                    <div class="ibox-tools">
                        @Html.ActionLink(PSafe.AM.Resources.Resource.BackToList, "Index", "HistorySystems", null, new { @class = "btn btn-primary btn-xs" })
                    </div>
                </div>
                <div class="ibox-content table-responsive">

                    @if (Model.Controller != null)
                    {
                        @switch (Model.Controller)
                        {
                            case "Area":
                                CompareTweObject<Area> ListArea = new CompareTweObject<Area>();
                                ListArea.NewObject = Model.NewObject;
                                ListArea.OldObject = Model.OldObject;

                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>Tên trường</th>
                                            <th>@PSafe.AM.Resources.Resource__area.BRANCHNAME</th>
                                            <th>@PSafe.AM.Resources.Resource__area.ADDRESS</th>
                                            <th>@PSafe.AM.Resources.Resource__area.CONTACTPERSON</th>
                                            <th>@PSafe.AM.Resources.Resource__area.CONTACTPHONE</th>
                                            <th>@PSafe.AM.Resources.Resource__area.DESCRIPTION</th>
                                            <th>@PSafe.AM.Resources.Resource__area.EMAIL</th>
                                            <th>@PSafe.AM.Resources.Resource__area.FAX</th>
                                            <th>@PSafe.AM.Resources.Resource__area.LATITUDE</th>
                                            <th>@PSafe.AM.Resources.Resource__area.LONGITUDE</th>
                                            <th>@PSafe.AM.Resources.Resource__area.MESSAGEPHONE</th>
                                            <th>@PSafe.AM.Resources.Resource__area.REGISTEREDDATE</th>
                                            <th>@PSafe.AM.Resources.Resource__area.MESSAGEPHONE</th>
                                            <th>@PSafe.AM.Resources.Resource__area.ACTIVED</th>
                                            <th>@PSafe.AM.Resources.Resource__area.ACTIVEEMAIL</th>
                                            <th>@PSafe.AM.Resources.Resource__area.ACTIVEMESSAGEPHONE</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><strong>Dữ liệu mới</strong></td>
                                            <td>@ListArea.NewObject.AreaName</td>
                                            <td>@ListArea.NewObject.Address</td>
                                            <td>@ListArea.NewObject.ContactPerson</td>
                                            <td>@ListArea.NewObject.ContactPhone</td>
                                            <td>@ListArea.NewObject.Description</td>
                                            <td>@ListArea.NewObject.Email</td>
                                            <td>@ListArea.NewObject.Fax</td>
                                            <td>@ListArea.NewObject.Latitude.ToString()</td>
                                            <td>@ListArea.NewObject.Longitude.ToString()</td>
                                            <td>@ListArea.NewObject.MessagePhone</td>
                                            <td>@ListArea.NewObject.RegisteredDate.ToString()</td>
                                            <td>@ListArea.NewObject.Representative</td>
                                            <td>@ListArea.NewObject.Actived.ToString()</td>
                                            <td>@ListArea.NewObject.ActiveEmail.ToString()</td>
                                            <td>@ListArea.NewObject.ActiveMessagePhone.ToString()</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Dữ liệu cũ</strong></td>
                                            <td>@ListArea.OldObject.AreaName</td>
                                            <td>@ListArea.OldObject.Address</td>
                                            <td>@ListArea.OldObject.ContactPerson</td>
                                            <td>@ListArea.OldObject.ContactPhone</td>
                                            <td>@ListArea.OldObject.Description</td>
                                            <td>@ListArea.OldObject.Email</td>
                                            <td>@ListArea.OldObject.Fax</td>
                                            <td>@ListArea.OldObject.Latitude.ToString()</td>
                                            <td>@ListArea.OldObject.Longitude.ToString()</td>
                                            <td>@ListArea.OldObject.MessagePhone</td>
                                            <td>@ListArea.OldObject.RegisteredDate.ToString()</td>
                                            <td>@ListArea.OldObject.Representative</td>
                                            <td>@ListArea.OldObject.Actived.ToString()</td>
                                            <td>@ListArea.OldObject.ActiveEmail.ToString()</td>
                                            <td>@ListArea.OldObject.ActiveMessagePhone.ToString()</td>
                                        </tr>
                                    </tbody>
                                </table>

                                break;
                            case "CommandCenter":
                                CompareTweObject<CommandCenter> ListCommandCenter = new CompareTweObject<CommandCenter>();
                                ListCommandCenter.NewObject = Model.NewObject;
                                ListCommandCenter.OldObject = Model.OldObject;

                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>Tên trường</th>
                                            <th>@PSafe.AM.Resources.Resource__commandCenter.CommandCenter</th>
                                            <th>@PSafe.AM.Resources.Resource__commandCenter.Actived</th>
                                            <th>@PSafe.AM.Resources.Resource__commandCenter.Address</th>
                                            <th>@PSafe.AM.Resources.Resource__commandCenter.ContactPhone</th>
                                            <th>@PSafe.AM.Resources.Resource__commandCenter.CreatedDate</th>
                                            <th>@PSafe.AM.Resources.Resource__commandCenter.Description</th>
                                            <th>@PSafe.AM.Resources.Resource__commandCenter.Email</th>
                                            <th>@PSafe.AM.Resources.Resource__commandCenter.Fax</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><strong>Dữ liệu mới</strong></td>
                                            <td>@ListCommandCenter.NewObject.CenterName</td>
                                            <td>@ListCommandCenter.NewObject.Actived.ToString()</td>
                                            <td>@ListCommandCenter.NewObject.Address</td>
                                            <td>@ListCommandCenter.NewObject.ContactPhone</td>
                                            <td>@ListCommandCenter.NewObject.CreatedDate.ToString()</td>
                                            <td>@ListCommandCenter.NewObject.Description</td>
                                            <td>@ListCommandCenter.NewObject.Email.ToString()</td>
                                            <td>@ListCommandCenter.NewObject.Fax</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Dữ liệu cũ</strong></td>
                                            <td>@ListCommandCenter.OldObject.CenterName</td>
                                            <td>@ListCommandCenter.OldObject.Actived.ToString()</td>
                                            <td>@ListCommandCenter.OldObject.Address</td>
                                            <td>@ListCommandCenter.OldObject.ContactPhone</td>
                                            <td>@ListCommandCenter.OldObject.CreatedDate.ToString()</td>
                                            <td>@ListCommandCenter.OldObject.Description</td>
                                            <td>@ListCommandCenter.OldObject.Email.ToString()</td>
                                            <td>@ListCommandCenter.OldObject.Fax</td>
                                        </tr>
                                    </tbody>
                                </table>

                                break;
                            case "Device":
                                CompareTweObject<Device> ListDevice = new CompareTweObject<Device>();
                                ListDevice.NewObject = Model.NewObject;
                                ListDevice.OldObject = Model.OldObject;

                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>Tên trường</th>
                                            <th>@PSafe.AM.Resources.Resource__device.ACTIVED</th>
                                            <th>@PSafe.AM.Resources.Resource__device.ALLOWUSERREMOTEACCESS</th>
                                            <th>@PSafe.AM.Resources.Resource__device.CONNECTSTATUS</th>
                                            <th>@PSafe.AM.Resources.Resource__device.CREATEDDATE</th>
                                            <th>@PSafe.AM.Resources.Resource__device.DESCRIPTION</th>
                                            <th>@PSafe.AM.Resources.Resource__device.DEVICENAME</th>
                                            <th>@PSafe.AM.Resources.Resource__device.DEVICESTATUS</th>
                                            <th>@PSafe.AM.Resources.Resource__device.EXPDAY</th>
                                            <th>@PSafe.AM.Resources.Resource__device.IPLOCAL</th>
                                            <th>@PSafe.AM.Resources.Resource__device.LASTCONNECTTIME</th>
                                            <th>@PSafe.AM.Resources.Resource__device.PASSWORD</th>

                                            <th>@PSafe.AM.Resources.Resource__device.PORT</th>
                                            <th>@PSafe.AM.Resources.Resource__device.QUANTITYCHANNEL</th>
                                            <th>@PSafe.AM.Resources.Resource__device.RENEWDAY</th>
                                            <th>@PSafe.AM.Resources.Resource__device.SERIALNUMBER</th>
                                            <th>@PSafe.AM.Resources.Resource__device.SERVER</th>
                                            <th>@PSafe.AM.Resources.Resource__device.URL</th>
                                            <th>@PSafe.AM.Resources.Resource__device.USERNAME</th>
                                            <th>@PSafe.AM.Resources.Resource__device.VIDEOLINK</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><strong>Dữ liệu mới</strong></td>
                                            <td>@ListDevice.NewObject.Actived.ToString()</td>
                                            <td>@ListDevice.NewObject.AllowUserRemoteAccess.ToString()</td>
                                            <td>@ListDevice.NewObject.ConnectStatus.ToString()</td>
                                            <td>@ListDevice.NewObject.CreatedDate.ToString()</td>
                                            <td>@ListDevice.NewObject.Description</td>
                                            <td>@ListDevice.NewObject.DeviceName</td>
                                            <td>@ListDevice.NewObject.DeviceStatus.ToString()</td>
                                            <td>@ListDevice.NewObject.Expday.ToString()</td>
                                            <td>@ListDevice.NewObject.IpLocal</td>

                                            <td>@ListDevice.NewObject.LastConnectTime.ToString()</td>
                                            <td>@ListDevice.NewObject.Password</td>
                                            <td>@ListDevice.NewObject.Port.ToString()</td>
                                            <td>@ListDevice.NewObject.QuantityChannel.ToString()</td>
                                            <td>@ListDevice.NewObject.Renewday.ToString()</td>
                                            <td>@ListDevice.NewObject.SerialNumber</td>
                                            <td>@ListDevice.NewObject.Server</td>
                                            <td>@ListDevice.NewObject.Url</td>
                                            <td>@ListDevice.NewObject.Username</td>
                                            <td>@ListDevice.NewObject.VideoLink</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Dữ liệu cũ</strong></td>
                                            <td>@ListDevice.OldObject.Actived.ToString()</td>
                                            <td>@ListDevice.OldObject.AllowUserRemoteAccess.ToString()</td>
                                            <td>@ListDevice.OldObject.ConnectStatus.ToString()</td>
                                            <td>@ListDevice.OldObject.CreatedDate.ToString()</td>
                                            <td>@ListDevice.OldObject.Description</td>
                                            <td>@ListDevice.OldObject.DeviceName</td>
                                            <td>@ListDevice.OldObject.DeviceStatus.ToString()</td>
                                            <td>@ListDevice.OldObject.Expday.ToString()</td>
                                            <td>@ListDevice.OldObject.IpLocal</td>

                                            <td>@ListDevice.OldObject.LastConnectTime.ToString()</td>
                                            <td>@ListDevice.OldObject.Password</td>
                                            <td>@ListDevice.OldObject.Port.ToString()</td>
                                            <td>@ListDevice.OldObject.QuantityChannel.ToString()</td>
                                            <td>@ListDevice.OldObject.Renewday.ToString()</td>
                                            <td>@ListDevice.OldObject.SerialNumber</td>
                                            <td>@ListDevice.OldObject.Server</td>
                                            <td>@ListDevice.OldObject.Url</td>
                                            <td>@ListDevice.OldObject.Username</td>
                                            <td>@ListDevice.OldObject.VideoLink</td>
                                        </tr>
                                    </tbody>
                                </table>

                                break;
                            case "Document":
                                CompareTweObject<Document> ListDocument = new CompareTweObject<Document>();
                                ListDocument.NewObject = Model.NewObject;
                                ListDocument.OldObject = Model.OldObject;

                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>Tên trường</th>
                                            <th>@PSafe.AM.Resources.Resource_document.FileName</th>
                                            <th>@PSafe.AM.Resources.Resource_document.DocumentName</th>
                                            <th>@PSafe.AM.Resources.Resource_document.Description</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><strong>Dữ liệu mới</strong></td>
                                            <td>@ListDocument.NewObject.FileName</td>
                                            <td>@ListDocument.NewObject.DocumentName</td>
                                            <td>@ListDocument.NewObject.Description</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Dữ liệu cũ</strong></td>
                                            <td>@ListDocument.OldObject.FileName</td>
                                            <td>@ListDocument.OldObject.DocumentName</td>
                                            <td>@ListDocument.OldObject.Description</td>
                                        </tr>
                                    </tbody>
                                </table>

                                break;
                            case "DocumentBeLongLocation":
                                CompareTweObject<DocumentBeLongLocation> ListDocumentBeLong = new CompareTweObject<DocumentBeLongLocation>();
                                ListDocumentBeLong.NewObject = Model.NewObject;
                                ListDocumentBeLong.OldObject = Model.OldObject;

                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>Tên trường</th>
                                            <th>DocumentID</th>
                                            <th>LocationID</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><strong>Dữ liệu mới</strong></td>
                                            <td>@ListDocumentBeLong.NewObject.DocumentId.ToString()</td>
                                            <td>@ListDocumentBeLong.NewObject.LocationId.ToString()</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Dữ liệu cũ</strong></td>
                                            <td>@ListDocumentBeLong.OldObject.DocumentId.ToString()</td>
                                            <td>@ListDocumentBeLong.OldObject.LocationId.ToString()</td>
                                        </tr>
                                    </tbody>
                                </table>

                                break;
                            case "Group":
                                CompareTweObject<Group> ListGroup = new CompareTweObject<Group>();
                                ListGroup.NewObject = Model.NewObject;
                                ListGroup.OldObject = Model.OldObject;

                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>Tên trường</th>
                                            <th>@PSafe.AM.Resources.Resource__group.GroupName</th>
                                            <th>@PSafe.AM.Resources.Resource__group.GroupDescription</th>

                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><strong>Dữ liệu mới</strong></td>
                                            <td>@ListGroup.NewObject.GroupName</td>
                                            <td>@ListGroup.NewObject.Description</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Dữ liệu cũ</strong></td>
                                            <td>@ListGroup.OldObject.GroupName</td>
                                            <td>@ListGroup.OldObject.Description</td>
                                        </tr>
                                    </tbody>
                                </table>

                                break;
                            case "HistorySystem":
                                CompareTweObject<HistorySystem> ListHistorySystem = new CompareTweObject<HistorySystem>();
                                ListHistorySystem.NewObject = Model.NewObject;
                                ListHistorySystem.OldObject = Model.OldObject;

                                @Html.Raw(ListHistorySystem.NewObject);

                                <hr />

                                @Html.Raw(ListHistorySystem.OldObject);

                                break;
                            case "Location":
                                CompareTweObject<Location> ListLocation = new CompareTweObject<Location>();
                                ListLocation.NewObject = Model.NewObject as Location;
                                ListLocation.OldObject = Model.OldObject as Location;

                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>Tên trường</th>
                                            <th>@PSafe.AM.Resources.Resource__location.LocationName</th>
                                            <th>@PSafe.AM.Resources.Resource__location.Map</th>
                                            <th>@PSafe.AM.Resources.Resource__location.Actived</th>
                                            <th>@PSafe.AM.Resources.Resource__location.Description</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><strong>Dữ liệu mới</strong></td>
                                            <td>@ListLocation.NewObject.LocationName</td>
                                            <td>@ListLocation.NewObject.Map</td>
                                            <td>@ListLocation.NewObject.Actived.ToString()</td>
                                            <td>@ListLocation.NewObject.Description</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Dữ liệu cũ</strong></td>
                                            <td>@ListLocation.OldObject.LocationName</td>
                                            <td>@ListLocation.OldObject.Map</td>
                                            <td>@ListLocation.OldObject.Actived.ToString()</td>
                                            <td>@ListLocation.OldObject.Description</td>
                                        </tr>
                                    </tbody>
                                </table>

                                break;
                            case "User":
                                CompareTweObject<User> ListUser = new CompareTweObject<User>();
                                ListUser.NewObject = Model.NewObject as User;
                                ListUser.OldObject = Model.OldObject as User;

                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>Tên trường</th>
                                            <th>@PSafe.AM.Resources.Resource__user.UserName</th>
                                            <th>@PSafe.AM.Resources.Resource__user.Phone</th>
                                            <th>@PSafe.AM.Resources.Resource__user.FullName</th>
                                            <th>@PSafe.AM.Resources.Resource__user.Email</th>
                                            <th>@PSafe.AM.Resources.Resource__user.LockReason</th>
                                            <th>@PSafe.AM.Resources.Resource__user.Actived</th>
                                            <th>@PSafe.AM.Resources.Resource__user.DepartmentId</th>
                                            <th>@PSafe.AM.Resources.Resource__user.PositionId</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><strong>Dữ liệu mới</strong></td>
                                            <td>@ListUser.NewObject.UserName</td>
                                            <td>@ListUser.NewObject.Phone</td>
                                            <td>@ListUser.NewObject.FullName</td>
                                            <td>@ListUser.NewObject.Email</td>
                                            <td>@ListUser.NewObject.LockReason</td>
                                            <td>@ListUser.NewObject.Actived.ToString()</td>
                                            <td>@ListUser.NewObject.DepartmentId.ToString()</td>
                                            <td>@ListUser.NewObject.PositionId.ToString()</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Dữ liệu cũ</strong></td>
                                            <td>@ListUser.OldObject.UserName</td>
                                            <td>@ListUser.OldObject.Phone</td>
                                            <td>@ListUser.OldObject.FullName</td>
                                            <td>@ListUser.OldObject.Email</td>
                                            <td>@ListUser.OldObject.LockReason</td>
                                            <td>@ListUser.OldObject.Actived.ToString()</td>
                                            <td>@ListUser.OldObject.DepartmentId.ToString()</td>
                                            <td>@ListUser.OldObject.PositionId.ToString()</td>
                                        </tr>
                                    </tbody>
                                </table>

                                break;
                            case "Role":
                                CompareTweObject<Role> ListRole = new CompareTweObject<Role>();
                                ListRole.NewObject = Model.NewObject as Role;
                                ListRole.OldObject = Model.OldObject as Role;

                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>Tên trường</th>
                                            <th>@PSafe.AM.Resources.Resource__role.RoleName</th>
                                            <th>@PSafe.AM.Resources.Resource__role.RoleDescription</th>
                                            <th>@PSafe.AM.Resources.Resource__role.ListFunction_TTAN</th>
                                            <th>@PSafe.AM.Resources.Resource__role.ListFunction_ACS</th>
                                            <th>@PSafe.AM.Resources.Resource__role.ListFunction_REPORT</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><strong>Dữ liệu mới</strong></td>
                                            <td>@ListRole.NewObject.RoleName</td>
                                            <td>@ListRole.NewObject.RoleDescription</td>
                                            <td>@ListRole.NewObject.ListFunction_AM</td>
                                            <td>@ListRole.NewObject.ListFunction_ACS</td>
                                            <td>@ListRole.NewObject.ListFunction_REPORT</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Dữ liệu cũ</strong></td>
                                            <td>@ListRole.OldObject.RoleName</td>
                                            <td>@ListRole.OldObject.RoleDescription</td>
                                            <td>@ListRole.OldObject.ListFunction_AM</td>
                                            <td>@ListRole.OldObject.ListFunction_ACS</td>
                                            <td>@ListRole.OldObject.ListFunction_REPORT</td>
                                        </tr>
                                    </tbody>
                                </table>
                                break;
                            case "EditUser":
                                CompareTweObject<UserInRoleModel> ListEditUsers = new CompareTweObject<UserInRoleModel>();
                                ListEditUsers.NewObject = Model.NewObject as UserInRoleModel;
                                ListEditUsers.OldObject = Model.OldObject as UserInRoleModel;

                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>Tên trường</th>
                                            <th>UserId</th>
                                            <th>RoleId</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><strong>Dữ liệu mới</strong></td>
                                            <td>@ListEditUsers.NewObject.UserId</td>
                                            <td>@ListEditUsers.NewObject.RoleId</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Dữ liệu cũ</strong></td>
                                            <td>@ListEditUsers.OldObject.UserId</td>
                                            <td>@ListEditUsers.OldObject.RoleId</td>
                                        </tr>
                                    </tbody>
                                </table>
                                break;
                            case "SystemUser":
                                CompareTweObject<SystemUser> ListSystemUser = new CompareTweObject<SystemUser>();

                                ListSystemUser.NewObject = Model.NewObject;
                                ListSystemUser.OldObject = Model.OldObject;

                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>Tên trường</th>
                                            <th>@PSafe.AM.Resources.Resource_systemUser.UserName</th>
                                            <th>@PSafe.AM.Resources.Resource_systemUser.Email</th>
                                            <th>@PSafe.AM.Resources.Resource_systemUser.GroupID.ToString()</th>
                                            <th>@PSafe.AM.Resources.Resource_systemUser.IsOnline</th>
                                            <th>@PSafe.AM.Resources.Resource_systemUser.IsApproved</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><strong>Dữ liệu mới</strong></td>
                                            <td>@ListSystemUser.NewObject.UserName</td>
                                            <td>@ListSystemUser.NewObject.Email</td>
                                            <td>@ListSystemUser.NewObject.GroupId.ToString()</td>
                                            <td>@ListSystemUser.NewObject.IsOnline.ToString()</td>
                                            <td>@ListSystemUser.NewObject.IsApproved.ToString()</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Dữ liệu cũ</strong></td>
                                            <td>@ListSystemUser.OldObject.UserName</td>
                                            <td>@ListSystemUser.OldObject.Email</td>
                                            <td>@ListSystemUser.OldObject.GroupId.ToString()</td>
                                            <td>@ListSystemUser.OldObject.IsOnline.ToString()</td>
                                            <td>@ListSystemUser.OldObject.IsApproved.ToString()</td>
                                        </tr>
                                    </tbody>
                                </table>

                                break;
                            case "Supplier":
                                CompareTweObject<Supplier> ListSupplier = new CompareTweObject<Supplier>();
                                ListSupplier.NewObject = Model.NewObject as Supplier;
                                ListSupplier.OldObject = Model.OldObject as Supplier;

                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>Tên trường</th>
                                            <th>@PSafe.AM.Resources.Resource__supplier.NAME</th>
                                            <th>@PSafe.AM.Resources.Resource__supplier.DESCRIPTION</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><strong>Dữ liệu mới</strong></td>
                                            <td>@ListSupplier.NewObject.Name</td>
                                            <td>@ListSupplier.NewObject.Description</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Dữ liệu cũ</strong></td>
                                            <td>@ListSupplier.OldObject.Name</td>
                                            <td>@ListSupplier.OldObject.Description</td>

                                        </tr>
                                    </tbody>
                                </table>

                                break;
                            case "Core.Domains.TypeOfDevice":
                                CompareTweObject<TypeOfDevice> ListTypeOfDevice = new CompareTweObject<TypeOfDevice>();
                                ListTypeOfDevice.NewObject = Model.NewObject;
                                ListTypeOfDevice.OldObject = Model.OldObject;

                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>Tên trường</th>
                                            <th>@PSafe.AM.Resources.Resource__typeOfDevice.TYPENAME</th>
                                            <th>@PSafe.AM.Resources.Resource__typeOfDevice.ICON</th>
                                            <th>@PSafe.AM.Resources.Resource__typeOfDevice.DESCRIPTION</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><strong>Dữ liệu mới</strong></td>
                                            <td>@ListTypeOfDevice.NewObject.TypeName</td>
                                            <td>@ListTypeOfDevice.NewObject.Icon</td>
                                            <td>@ListTypeOfDevice.NewObject.Description</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Dữ liệu cũ</strong></td>
                                            <td>@ListTypeOfDevice.OldObject.TypeName</td>
                                            <td>@ListTypeOfDevice.OldObject.Icon</td>
                                            <td>@ListTypeOfDevice.OldObject.Description</td>

                                        </tr>
                                    </tbody>
                                </table>

                                break;
                            case "Marker":
                                CompareTweObject<Marker> ListMarker = new CompareTweObject<Marker>();
                                ListMarker.NewObject = Model.NewObject as Marker;
                                ListMarker.OldObject = Model.OldObject as Marker;

                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>Tên trường</th>
                                            <th>@PSafe.AM.Resources.Resource_marker.MarkerID</th>
                                            <th>@PSafe.AM.Resources.Resource_marker.MarkerName</th>
                                            <th>@PSafe.AM.Resources.Resource_marker.MarkerTypeID</th>
                                            <th>@PSafe.AM.Resources.Resource_marker.MarkerCode</th>
                                            <th>@PSafe.AM.Resources.Resource_marker.Phone</th>
                                            <th>@PSafe.AM.Resources.Resource_marker.Description</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><strong>Dữ liệu mới</strong></td>
                                            <td>@ListMarker.NewObject.MarkerID</td>
                                            <td>@ListMarker.NewObject.MarkerName</td>
                                            <td>@ListMarker.NewObject.TypeOfMarkerID</td>
                                            <td>@ListMarker.NewObject.MarkerCode</td>
                                            <td>@ListMarker.NewObject.Phone</td>
                                            <td>@ListMarker.NewObject.Description</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Dữ liệu cũ</strong></td>
                                            <td>@ListMarker.OldObject.MarkerID</td>
                                            <td>@ListMarker.OldObject.MarkerName</td>
                                            <td>@ListMarker.OldObject.TypeOfMarkerID</td>
                                            <td>@ListMarker.OldObject.MarkerCode</td>
                                            <td>@ListMarker.OldObject.Phone</td>
                                            <td>@ListMarker.OldObject.Description</td>
                                        </tr>
                                    </tbody>
                                </table>

                                break;
                            case "TYPE_OF_MARKER":
                                CompareTweObject<TypeOfMarker> ListTYPE_OF_MARKER = new CompareTweObject<TypeOfMarker>();
                                ListTYPE_OF_MARKER.NewObject = Model.NewObject as TypeOfMarker;
                                ListTYPE_OF_MARKER.OldObject = Model.OldObject as TypeOfMarker;

                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>Tên trường</th>
                                            <th>@PSafe.AM.Resources.Resource_typeofmarker.Id</th>
                                            <th>@PSafe.AM.Resources.Resource_typeofmarker.Name</th>
                                            <th>@PSafe.AM.Resources.Resource_typeofmarker.Icon</th>
                                            <th>@PSafe.AM.Resources.Resource_typeofmarker.Description</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><strong>Dữ liệu mới</strong></td>
                                            <td>@ListTYPE_OF_MARKER.NewObject.Id</td>
                                            <td>@ListTYPE_OF_MARKER.NewObject.Name</td>
                                            <td>@ListTYPE_OF_MARKER.NewObject.Icon</td>
                                            <td>@ListTYPE_OF_MARKER.NewObject.Description</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Dữ liệu cũ</strong></td>
                                            <td>@ListTYPE_OF_MARKER.OldObject.Id</td>
                                            <td>@ListTYPE_OF_MARKER.OldObject.Name</td>
                                            <td>@ListTYPE_OF_MARKER.OldObject.Icon</td>
                                            <td>@ListTYPE_OF_MARKER.OldObject.Description</td>
                                        </tr>
                                    </tbody>
                                </table>
                                break;
                            case "VEHICLE_BANNED_LIST":
                                CompareTweObject<VehicleBannedList> ListVEHICLE_BANNED_LIST = new CompareTweObject<VehicleBannedList>();
                                ListVEHICLE_BANNED_LIST.NewObject = Model.NewObject as VehicleBannedList;
                                ListVEHICLE_BANNED_LIST.OldObject = Model.OldObject as VehicleBannedList;

                                <div style="min-width: 2500px;overflow-x: auto">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th>Tên trường</th>
                                                <th>Id</th>
                                                <th>Biển số xe</th>
                                                <th>Ngày bắt đầu cấm</th>
                                                <th>Ngày kết thúc cấm</th>
                                                <th>Loại vi phạm</th>
                                                <th>Lý do vi phạm</th>
                                                <th>Số xe</th>
                                                <th>Chủ xe</th>
                                                <th>Người phát hiện</th>
                                                <th>Đã hủy</th>
                                                <th>Ngày hủy</th>
                                                <th>Người hủy</th>
                                                <th>Lý do hủy</th>
                                                <th>Ngày tạo</th>
                                                <th>Người tạo</th>
                                                <th>Ngày cập nhật</th>
                                                <th>Người cập nhật</th>
                                                <th>Ghi chú</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td><strong>Dữ liệu mới</strong></td>
                                                <td>Đã xóa</td>
                                            </tr>

                                            <tr>
                                                <td><strong>Dữ liệu cũ</strong></td>
                                                <td>@ListVEHICLE_BANNED_LIST.OldObject.Id</td>
                                                <td>@ListVEHICLE_BANNED_LIST.OldObject.TruckID</td>
                                                <td>@ListVEHICLE_BANNED_LIST.OldObject.ActiveTime.ToString("dd/MM/yyyy HH:mm:ss")</td>
                                                <td>@(ListVEHICLE_BANNED_LIST.OldObject.ExpiryTime.HasValue ? ListVEHICLE_BANNED_LIST.OldObject.ExpiryTime.Value.ToString("dd/MM/yyyy HH:mm:ss") : "") </td>
                                                <td>@ListVEHICLE_BANNED_LIST.OldObject.ViolationTypeId</td>
                                                <td>@ListVEHICLE_BANNED_LIST.OldObject.ReasonViolation</td>
                                                <td>@ListVEHICLE_BANNED_LIST.OldObject.TruckKey</td>
                                                <td>@ListVEHICLE_BANNED_LIST.OldObject.OwnerName</td>
                                                <td>@ListVEHICLE_BANNED_LIST.OldObject.FoundBy</td>
                                                <td>@(ListVEHICLE_BANNED_LIST.OldObject.ClearFlag ? "V" : "")</td>
                                                <td>@(ListVEHICLE_BANNED_LIST.OldObject.ClearFlag && ListVEHICLE_BANNED_LIST.OldObject.ClearDate.HasValue ? ListVEHICLE_BANNED_LIST.OldObject.ClearDate.Value.ToString("dd/MM/yyyy HH:mm:ss") : "") </td>
                                                <td>@(ListVEHICLE_BANNED_LIST.OldObject.ClearFlag ? ListVEHICLE_BANNED_LIST.OldObject.ClearBy.ToString() : "")</td>
                                                <td>@(ListVEHICLE_BANNED_LIST.OldObject.ClearFlag ? ListVEHICLE_BANNED_LIST.OldObject.ReasonClear : "")</td>
                                                <td>@ListVEHICLE_BANNED_LIST.OldObject.CreatedDate.ToString("dd/MM/yyyy HH:mm:ss")</td>
                                                <td>@ListVEHICLE_BANNED_LIST.OldObject.CreatedBy</td>
                                                <td>@ListVEHICLE_BANNED_LIST.OldObject.UpdatedDate.ToString("dd/MM/yyyy HH:mm:ss")</td>
                                                <td>@ListVEHICLE_BANNED_LIST.OldObject.UpdatedBy</td>
                                                <td>@ListVEHICLE_BANNED_LIST.OldObject.Note</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                break;
                            case "VEHICLE_BANNED_LIST_HISTORY":
                                CompareTweObject<VehicleBannedListHistory> ListVEHICLE_BANNED_LIST_HISTORY = new CompareTweObject<VehicleBannedListHistory>();
                                ListVEHICLE_BANNED_LIST_HISTORY.NewObject = Model.NewObject as VehicleBannedListHistory;
                                ListVEHICLE_BANNED_LIST_HISTORY.OldObject = Model.OldObject as VehicleBannedListHistory;

                                <div style="min-width: 3000px;overflow-x: auto">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th>Tên trường</th>
                                                <th>Id</th>
                                                <th>Ngày thực hiện</th>
                                                <th>Người thực hiện</th>
                                                <th>Biển số xe</th>
                                                <th>Ngày bắt đầu cấm</th>
                                                <th>Ngày kết thúc cấm</th>
                                                <th>Loại vi phạm</th>
                                                <th>Lý do vi phạm</th>
                                                <th>Số xe</th>
                                                <th>Chủ xe</th>
                                                <th>Người phát hiện</th>
                                                <th>Đã hủy</th>
                                                <th>Ngày hủy</th>
                                                <th>Người hủy</th>
                                                <th>Lý do hủy</th>
                                                <th>Ngày tạo</th>
                                                <th>Người tạo</th>
                                                <th>Ngày cập nhật</th>
                                                <th>Người cập nhật</th>
                                                <th>Ghi chú</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td><strong>Dữ liệu mới</strong></td>
                                                <td>@ListVEHICLE_BANNED_LIST_HISTORY.NewObject.Id</td>
                                                <td>@ListVEHICLE_BANNED_LIST_HISTORY.NewObject.CreatedDate.ToString("dd/MM/yyyy HH:mm:ss")</td>
                                                <td>@ListVEHICLE_BANNED_LIST_HISTORY.NewObject.CreatedUser</td>
                                                <td>@ListVEHICLE_BANNED_LIST_HISTORY.NewObject.TruckID</td>
                                                <td>@ListVEHICLE_BANNED_LIST_HISTORY.NewObject.ActiveTime.ToString("dd/MM/yyyy HH:mm:ss")</td>
                                                <td>@(ListVEHICLE_BANNED_LIST_HISTORY.NewObject.ExpiryTime.HasValue ? ListVEHICLE_BANNED_LIST_HISTORY.NewObject.ExpiryTime.Value.ToString("dd/MM/yyyy HH:mm:ss") : "") </td>
                                                <td>@ListVEHICLE_BANNED_LIST_HISTORY.NewObject.ViolationType</td>
                                                <td>@ListVEHICLE_BANNED_LIST_HISTORY.NewObject.ReasonViolation</td>
                                                <td>@ListVEHICLE_BANNED_LIST_HISTORY.NewObject.TruckKey</td>
                                                <td>@ListVEHICLE_BANNED_LIST_HISTORY.NewObject.OwnerName</td>
                                                <td>@ListVEHICLE_BANNED_LIST_HISTORY.NewObject.FoundBy</td>
                                                <td>@(ListVEHICLE_BANNED_LIST_HISTORY.NewObject.ClearFlag ? "V" : "")</td>
                                                <td>@(ListVEHICLE_BANNED_LIST_HISTORY.NewObject.ClearFlag && ListVEHICLE_BANNED_LIST_HISTORY.NewObject.VehicleClearDate.HasValue ? ListVEHICLE_BANNED_LIST_HISTORY.NewObject.VehicleClearDate.Value.ToString("dd/MM/yyyy HH:mm:ss") : "") </td>
                                                <td>@(ListVEHICLE_BANNED_LIST_HISTORY.NewObject.ClearFlag ? ListVEHICLE_BANNED_LIST_HISTORY.NewObject.VehicleClearBy.ToString() : "")</td>
                                                <td>@(ListVEHICLE_BANNED_LIST_HISTORY.NewObject.ClearFlag ? ListVEHICLE_BANNED_LIST_HISTORY.NewObject.ReasonClear : "")</td>
                                                <td>@ListVEHICLE_BANNED_LIST_HISTORY.NewObject.CreatedDate.ToString("dd/MM/yyyy HH:mm:ss")</td>
                                                <td>@ListVEHICLE_BANNED_LIST_HISTORY.NewObject.VehicleCreateUser</td>
                                                <td>@(ListVEHICLE_BANNED_LIST_HISTORY.NewObject.UpdatedDate.HasValue ? ListVEHICLE_BANNED_LIST_HISTORY.NewObject.UpdatedDate.Value.ToString("dd/MM/yyyy HH:mm:ss") : "") </td>
                                                <td>@(ListVEHICLE_BANNED_LIST_HISTORY.NewObject.UpdatedBy.HasValue ? ListVEHICLE_BANNED_LIST_HISTORY.NewObject.UpdatedBy.Value : 0)</td>
                                                <td>@ListVEHICLE_BANNED_LIST_HISTORY.NewObject.Note</td>
                                            </tr>

                                            <tr>
                                                <td><strong>Dữ liệu cũ</strong></td>
                                                <td>@ListVEHICLE_BANNED_LIST_HISTORY.OldObject.Id</td>
                                                <td>@ListVEHICLE_BANNED_LIST_HISTORY.OldObject.CreatedDate.ToString("dd/MM/yyyy HH:mm:ss")</td>
                                                <td>@ListVEHICLE_BANNED_LIST_HISTORY.OldObject.CreatedUser</td>
                                                <td>@ListVEHICLE_BANNED_LIST_HISTORY.OldObject.TruckID</td>
                                                <td>@ListVEHICLE_BANNED_LIST_HISTORY.OldObject.ActiveTime.ToString("dd/MM/yyyy HH:mm:ss")</td>
                                                <td>@(ListVEHICLE_BANNED_LIST_HISTORY.OldObject.ExpiryTime.HasValue ? ListVEHICLE_BANNED_LIST_HISTORY.OldObject.ExpiryTime.Value.ToString("dd/MM/yyyy HH:mm:ss") : "") </td>
                                                <td>@ListVEHICLE_BANNED_LIST_HISTORY.OldObject.ViolationType</td>
                                                <td>@ListVEHICLE_BANNED_LIST_HISTORY.OldObject.ReasonViolation</td>
                                                <td>@ListVEHICLE_BANNED_LIST_HISTORY.OldObject.TruckKey</td>
                                                <td>@ListVEHICLE_BANNED_LIST_HISTORY.OldObject.OwnerName</td>
                                                <td>@ListVEHICLE_BANNED_LIST_HISTORY.OldObject.FoundBy</td>
                                                <td>@(ListVEHICLE_BANNED_LIST_HISTORY.OldObject.ClearFlag ? "V" : "")</td>
                                                <td>@(ListVEHICLE_BANNED_LIST_HISTORY.OldObject.ClearFlag && ListVEHICLE_BANNED_LIST_HISTORY.OldObject.VehicleClearDate.HasValue ? ListVEHICLE_BANNED_LIST_HISTORY.OldObject.VehicleClearDate.Value.ToString("dd/MM/yyyy HH:mm:ss") : "") </td>
                                                <td>@(ListVEHICLE_BANNED_LIST_HISTORY.OldObject.ClearFlag ? ListVEHICLE_BANNED_LIST_HISTORY.OldObject.VehicleClearBy.ToString() : "")</td>
                                                <td>@(ListVEHICLE_BANNED_LIST_HISTORY.OldObject.ClearFlag ? ListVEHICLE_BANNED_LIST_HISTORY.OldObject.ReasonClear : "")</td>
                                                <td>@ListVEHICLE_BANNED_LIST_HISTORY.OldObject.CreatedDate.ToString("dd/MM/yyyy HH:mm:ss")</td>
                                                <td>@ListVEHICLE_BANNED_LIST_HISTORY.OldObject.VehicleCreateUser</td>
                                                <td>@(ListVEHICLE_BANNED_LIST_HISTORY.OldObject.UpdatedDate.HasValue ? ListVEHICLE_BANNED_LIST_HISTORY.OldObject.UpdatedDate.Value.ToString("dd/MM/yyyy HH:mm:ss") : "") </td>
                                                <td>@(ListVEHICLE_BANNED_LIST_HISTORY.OldObject.UpdatedBy.HasValue ? ListVEHICLE_BANNED_LIST_HISTORY.OldObject.UpdatedBy.Value : 0)</td>
                                                <td>@ListVEHICLE_BANNED_LIST_HISTORY.OldObject.Note</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                break;
                            default:
                                CompareTweObject<object> ListObject = new CompareTweObject<object>();
                                ListObject.NewObject = Model.NewObject as object;
                                ListObject.OldObject = Model.OldObject as object;

                                @Html.Raw(ListObject.NewObject);

                                <hr />

                                @Html.Raw(ListObject.OldObject);

                                break;
                        }
                    }
                </div>
            </div>
        </div>
    </div>
</div>