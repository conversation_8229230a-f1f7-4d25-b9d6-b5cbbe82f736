﻿using Microsoft.AspNetCore.Mvc.Rendering;
using PSafe.Core.SharedKernel;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace PSafe.AM.Models
{
    public class CompanyModel
    {
        [Key]
        [Display(Name = "Id", ResourceType = typeof(Resources.Resource_company))]
        public int Id { get; set; }

        [Required(ErrorMessage = "Vui lòng nhập tên công ty")]
        [StringLength(255, MinimumLength = 1, ErrorMessage = "Độ dài từ 1-255 ký tự")]
        [Display(Name = "Name", ResourceType = typeof(Resources.Resource_company))]
        public string Name { get; set; }

        [Display(Name = "ParentId", ResourceType = typeof(Resources.Resource_company))]
        public int? ParentId { get; set; }

        [Display(Name = "Email", ResourceType = typeof(Resources.Resource_company))]
        public string Email { get; set; }

        [Display(Name = "NickName", ResourceType = typeof(Resources.Resource_company))]
        public string NickName { get; set; }

        [Display(Name = "PhoneNumber", ResourceType = typeof(Resources.Resource_company))]
        public string PhoneNumber { get; set; }

        [Display(Name = "Address", ResourceType = typeof(Resources.Resource_company))]
        public string Address { get; set; }

        [Display(Name = "ContactPerson", ResourceType = typeof(Resources.Resource_company))]
        public string ContactPerson { get; set; }

        [Display(Name = "Status", ResourceType = typeof(Resources.Resource_company))]
        public int Status { get; set; }

        [Display(Name = "Description", ResourceType = typeof(Resources.Resource_company))]
        public string Description { get; set; }

        [Display(Name = "CreatedDate", ResourceType = typeof(Resources.Resource_company))]
        public DateTime CreatedDate { get; set; }

        [Display(Name = "CreatedBy", ResourceType = typeof(Resources.Resource_company))]
        public int CreatedBy { get; set; }

        [Display(Name = "UpdatedDate", ResourceType = typeof(Resources.Resource_company))]
        public DateTime UpdatedDate { get; set; }

        [Display(Name = "UpdatedBy", ResourceType = typeof(Resources.Resource_company))]
        public int UpdatedBy { get; set; }

        [Display(Name = "ParentId", ResourceType = typeof(Resources.Resource_company))]
        public SelectList ListParentId { get; set; }

        [Display(Name = "FormOfFeeCollection", ResourceType = typeof(Resources.Resource_company))]
        public string FormOfFeeCollection { get; set; }

        [Display(Name = "FormOfFeeCollection", ResourceType = typeof(Resources.Resource_company))]
        public SelectList ListFormOfFeeCollection { get; set; }
    }
}
