﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace PSafe.AM.Models
{
    public class BannedTypeModel
    {
        [Key]
        [Display(Name = "Id", ResourceType = typeof(Resources.Resource_bannedType))]
        public int Id { get; set; }

        [Required(ErrorMessage = "Giá trị không được trống!")]
        [StringLength(50, MinimumLength = 1, ErrorMessage = "Độ dài tối đa 50 ký tự!")]
        [Display(Name = "Name", ResourceType = typeof(Resources.Resource_scheduleTime))]
        public string Name { get; set; } = string.Empty;

        [Display(Name = "Description", ResourceType = typeof(Resources.Resource_scheduleTime))]
        public string Description { get; set; } = string.Empty;

        [Display(Name = "LocalDescription", ResourceType = typeof(Resources.Resource_bannedType))]
        public string LocalDescription { get; set; } = string.Empty;

        public DateTime UpdateTime { get; set; }

        
    }
}
