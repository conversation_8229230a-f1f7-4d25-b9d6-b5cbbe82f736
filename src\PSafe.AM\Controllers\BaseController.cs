using System;
using Microsoft.AspNetCore.Mvc;
using PSafe.Core.Interface;
using PSafe.Core.SharedKernel;

namespace PSafeAM.Controllers
{
    public class BaseController : Controller
    {
        protected readonly IGeneralConfigRepository _generalConfigResp;
        protected readonly SiteUIConfigs _uiConfigs;
        public BaseController(IGeneralConfigRepository generalConfigResp)
        {
            _generalConfigResp = generalConfigResp ?? throw new ArgumentNullException(nameof(generalConfigResp));
            _uiConfigs = _generalConfigResp.GetSiteUIConfigs();
        }
    }
}