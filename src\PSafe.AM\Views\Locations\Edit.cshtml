﻿@model PSafe.AM.Models.LocationModel

@{
    ViewBag.Title = "Hiệu chỉnh";
}

<environment names="Development">
    <script src="~/lib/jquery/dist/jquery.js"></script>
    <script src="~/lib/validate/jquery.validate.min.js"></script>
    <script src="~/js/script.js" asp-append-version="true"></script>
</environment>
<environment names="Staging,Production">
    <script src="~/lib/jquery/dist/jquery.js"></script>
    <script src="~/lib/validate/jquery.validate.min.js"></script>
    <script src="~/js/script.js" asp-append-version="true"></script>
</environment>

<script language="javascript" type="text/javascript">
    function openChild(file, window) {
        childWindow = open(file, window, 'resizable=no,width=700,height=400,scrollbars,resizable,toolbar,status');
        if (childWindow.opener == null) childWindow.opener = self;
    }
</script>

<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-lg-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>@PSafe.AM.Resources.Resource.Edit @PSafe.AM.Resources.Resource.Location</h5>
                    <div class="ibox-tools">
                        @Html.ActionLink(PSafe.AM.Resources.Resource.BackToList, "Index", "Locations", null, new { @class = "btn btn-primary btn-xs" })
                    </div>
                </div>
                <div class="ibox-content">

                    @using (Html.BeginForm("Edit", "Locations", FormMethod.Post, new { enctype = "multipart/form-data" }))
                    {
                        @Html.AntiForgeryToken()

                    <div class="form-horizontal">

                        @Html.ValidationSummary(true)

                        @Html.HiddenFor(model => model.LocationId)
                        @Html.HiddenFor(model => model.Map)

                        <div class="form-group">
                            @Html.LabelFor(model => model.AreaId, new { @class = "control-label col-md-2" })
                            <div class="col-md-10">
                                @Html.DropDownListFor(model => model.AreaId, Model.ListAreas as SelectList, new { @class = "form-control" })
                                @Html.ValidationMessageFor(model => model.AreaId, null, new { @class = "text-danger" })
                            </div>
                        </div>

                        <div class="form-group">
                            @Html.LabelFor(model => model.LocationName, new { @class = "control-label col-md-2" })
                            <div class="col-md-10">
                                @Html.EditorFor(model => model.LocationName)
                                @Html.ValidationMessageFor(model => model.LocationName, null, new { @class = "text-danger" })
                            </div>
                        </div>
                        <div class="form-group">
                            @Html.LabelFor(model => model.Map, new { @class = "control-label col-md-2" })
                            <div class="btn-toolbar col-md-10" style="margin-left:-15px;" role="toolbar" aria-label="Toolbar with button groups">
                                <div class="btn-group col-lg-7" style="padding-bottom: 5px;" role="group" aria-label="First group">
                                    @Html.TextBoxFor(model => model.Map, new { disabled = "disabled", @class = "form-control" })
                                </div>
                                <div class="btn-group col-lg-5" role="group" aria-label="Third group">
                                    <input type="file" name="ImageFile" id="ImageFile" class="btn btn-primary btn-file" accept=".xlsx,.xls,image/*,.doc, .docx,.ppt, .pptx,.txt,.pdf" />
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label col-md-2">@Html.LabelFor(model => model.LONGITUDE) (*)</label>
                            <div class="col-md-10">
                                @Html.EditorFor(model => model.LONGITUDE)
                                @Html.ValidationMessageFor(model => model.LONGITUDE, null, new { @class = "text-danger" })
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label col-md-2">@Html.LabelFor(model => model.LATITUDE) (*)</label>
                            <div class="col-md-10">
                                @Html.EditorFor(model => model.LATITUDE)
                                @Html.ValidationMessageFor(model => model.LATITUDE, null, new { @class = "text-danger" })
                            </div>
                            <div class="col-md-2">
                                <input type="button" class="btn btn-primary btn-xs" btn- value="Lấy tọa độ" onClick="openChild('/static/GetLatlon.htm','win2')" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label col-md-2">@Html.LabelFor(model => model.Address) (*)</label>
                            <div class="col-md-10">
                                @Html.EditorFor(model => model.Address)
                                @Html.ValidationMessageFor(model => model.Address, null, new { @class = "text-danger" })
                            </div>
                        </div>

                        <div class="form-group">
                            @Html.LabelFor(model => model.Document, new { @class = "control-label col-md-2" })
                            <div class="col-md-10">
                                <select data-placeholder="Chọn tài liệu..." class="chosen-select" name="document_select" multiple tabindex="4">
                                    @foreach (var item in Model.ListDocumentOnLocation)
                                    {
                                        <option selected="selected" value="@item.Id">@Html.DisplayFor(modelItem => item.FileName)</option>
                                    }
                                    @foreach (var item in Model.ListDocumentNotOnLocation)
                                    {
                                        <option value="@item.Id">@Html.DisplayFor(modelItem => item.FileName)</option>
                                    }
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            @Html.LabelFor(model => model.Description, new { @class = "control-label col-md-2" })
                            <div class="col-md-10">
                                @Html.TextAreaFor(model => model.Description, new { @class = "form-control" })
                                @Html.ValidationMessageFor(model => model.Description, null, new { @class = "text-danger" })
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="col-md-offset-2 col-md-10">
                                <input type="submit" value="@PSafe.AM.Resources.Resource.Save" class="btn btn-primary" />
                                @Html.ActionLink(PSafe.AM.Resources.Resource.Cancel, "Index", "Locations", null, new { @class = "btn btn-white" })
                            </div>
                        </div>
                    </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <environment names="Development,Staging,Production">
        <link rel="stylesheet" href="~/lib/toastr/toastr.min.css" />
    </environment>

    <environment names="Development">
        <link rel="stylesheet" href="~/lib/chosen/bootstrap-chosen.css" />

    </environment>
    <environment names="Staging,Production">
        <link rel="stylesheet" href="~/lib/chosen/bootstrap-chosen.css" />
    </environment>
}

@section Scripts {
    <environment names="Development">
        <script src="~/lib/chosen/chosen.jquery.js"></script>
    </environment>

    <environment names="Development,Staging,Production">
        <script src="~/lib/validate/jquery.validate.min.js"></script>
        <script src="~/lib/toastr/toastr.js"></script>
        <script src="~/lib/toastr/toastr.min.js"></script>
        <script src="~/lib/chosen/chosen.jquery.js"></script>
    </environment>

    @if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "warning")
    {
        <script type="text/javascript">
            toastr.warning('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "error")
    {
        <script type="text/javascript">
            toastr.error('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "success")
    {
        <script type="text/javascript">
            toastr.success('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    <script type="text/javascript">
        var checkChange = false;
        $('.chosen-select').chosen({ width: "100%" });

        $("#AreaId").change(function () {
            var areasId = $(this).val();
            $.ajax({
                url: '@Url.Action("GetArea", "Areas")',
                type: 'GET',
                data: { id: areasId },
                dataType: 'json',
                success: function (response) {
                    if (parseInt($("#LONGITUDE").val()) == 0 && parseInt($("#LATITUDE").val()) == 0)
                        {
                            checkChange = true;
                        }
                        if (checkChange == true)
                            {
                                $("#LONGITUDE").val(response.longitude);
                                $("#LATITUDE").val(response.latitude);
                            }
                        }
            });
        });
    </script>
}