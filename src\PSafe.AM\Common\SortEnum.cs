﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;

namespace PSafe.AM.Common
{
    public class OrderAttribute : Attribute
    {
        public readonly int Order;

        public OrderAttribute(int order)
        {
            Order = order;
        }
    }

    public static class EnumExtenstions
    {
        public static string GetDescription<TEnum>(this TEnum value)
        where TEnum : struct
        {
            var enumType = typeof(TEnum);
            if (!enumType.IsEnum) throw new InvalidOperationException();

            var name = Enum.GetName(enumType, value);
            var field = typeof(TEnum).GetField(name, BindingFlags.Static | BindingFlags.Public);
            return field.GetCustomAttribute<DescriptionAttribute>()?.Description ?? name;
        }
    }
}
