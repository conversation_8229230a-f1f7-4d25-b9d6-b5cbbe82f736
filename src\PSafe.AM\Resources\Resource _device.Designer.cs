﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace PSafe.AM.Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class Resource__device {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resource__device() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("PSafe.AM.Resources.Resource _device", typeof(Resource__device).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kích hoạt.
        /// </summary>
        public static string ACTIVED {
            get {
                return ResourceManager.GetString("ACTIVED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cho phép truy cập từ xa.
        /// </summary>
        public static string ALLOWUSERREMOTEACCESS {
            get {
                return ResourceManager.GetString("ALLOWUSERREMOTEACCESS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Camera quan sát.
        /// </summary>
        public static string CameraMonitorId {
            get {
                return ResourceManager.GetString("CameraMonitorId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Trạng thái kết nối.
        /// </summary>
        public static string CONNECTSTATUS {
            get {
                return ResourceManager.GetString("CONNECTSTATUS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ngày tạo.
        /// </summary>
        public static string CREATEDDATE {
            get {
                return ResourceManager.GetString("CREATEDDATE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Người tạo.
        /// </summary>
        public static string CREATEDUSER {
            get {
                return ResourceManager.GetString("CREATEDUSER", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mô tả.
        /// </summary>
        public static string DESCRIPTION {
            get {
                return ResourceManager.GetString("DESCRIPTION", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mã thiết bị.
        /// </summary>
        public static string DEVICEID {
            get {
                return ResourceManager.GetString("DEVICEID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tên thiết bị.
        /// </summary>
        public static string DEVICENAME {
            get {
                return ResourceManager.GetString("DEVICENAME", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tình trạng thiết bị.
        /// </summary>
        public static string DEVICESTATUS {
            get {
                return ResourceManager.GetString("DEVICESTATUS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quận/Huyện.
        /// </summary>
        public static string DistrictId {
            get {
                return ResourceManager.GetString("DistrictId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DriverName.
        /// </summary>
        public static string DriverName {
            get {
                return ResourceManager.GetString("DriverName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ngày hết hạn.
        /// </summary>
        public static string EXPDAY {
            get {
                return ResourceManager.GetString("EXPDAY", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ip.
        /// </summary>
        public static string IP {
            get {
                return ResourceManager.GetString("IP", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Địa chỉ máy.
        /// </summary>
        public static string IPLOCAL {
            get {
                return ResourceManager.GetString("IPLOCAL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kết nối gần nhất.
        /// </summary>
        public static string LASTCONNECTTIME {
            get {
                return ResourceManager.GetString("LASTCONNECTTIME", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vị trí.
        /// </summary>
        public static string LOCATIONID {
            get {
                return ResourceManager.GetString("LOCATIONID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Địa chỉ MAC.
        /// </summary>
        public static string MacAddress {
            get {
                return ResourceManager.GetString("MacAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mật khẩu.
        /// </summary>
        public static string PASSWORD {
            get {
                return ResourceManager.GetString("PASSWORD", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Port.
        /// </summary>
        public static string PORT {
            get {
                return ResourceManager.GetString("PORT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tỉnh.
        /// </summary>
        public static string ProvinceId {
            get {
                return ResourceManager.GetString("ProvinceId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Số lượng kênh.
        /// </summary>
        public static string QUANTITYCHANNEL {
            get {
                return ResourceManager.GetString("QUANTITYCHANNEL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ngày tạo.
        /// </summary>
        public static string RENEWDAY {
            get {
                return ResourceManager.GetString("RENEWDAY", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Số serial.
        /// </summary>
        public static string SERIALNUMBER {
            get {
                return ResourceManager.GetString("SERIALNUMBER", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Máy chủ.
        /// </summary>
        public static string SERVER {
            get {
                return ResourceManager.GetString("SERVER", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tốc độ giới hạn.
        /// </summary>
        public static string SpeedLimit {
            get {
                return ResourceManager.GetString("SpeedLimit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nhà cung cấp.
        /// </summary>
        public static string SUPPLIERID {
            get {
                return ResourceManager.GetString("SUPPLIERID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Camera giao thông.
        /// </summary>
        public static string TrafficCamera {
            get {
                return ResourceManager.GetString("TrafficCamera", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Loại thiết bị.
        /// </summary>
        public static string TYPEOFDEVICEID {
            get {
                return ResourceManager.GetString("TYPEOFDEVICEID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Loại cảnh báo.
        /// </summary>
        public static string TYPEOFSINGNAL {
            get {
                return ResourceManager.GetString("TYPEOFSINGNAL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ngày cập nhật.
        /// </summary>
        public static string UPDATEDDATE {
            get {
                return ResourceManager.GetString("UPDATEDDATE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Người cập nhật.
        /// </summary>
        public static string UPDATEDUSER {
            get {
                return ResourceManager.GetString("UPDATEDUSER", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Đường dẫn.
        /// </summary>
        public static string URL {
            get {
                return ResourceManager.GetString("URL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tài khoản quản lý.
        /// </summary>
        public static string USERID {
            get {
                return ResourceManager.GetString("USERID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tên đăng nhập.
        /// </summary>
        public static string USERNAME {
            get {
                return ResourceManager.GetString("USERNAME", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Đường dẫn video.
        /// </summary>
        public static string VIDEOLINK {
            get {
                return ResourceManager.GetString("VIDEOLINK", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Phường/Xã.
        /// </summary>
        public static string WardId {
            get {
                return ResourceManager.GetString("WardId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Zone.
        /// </summary>
        public static string Zone {
            get {
                return ResourceManager.GetString("Zone", resourceCulture);
            }
        }
    }
}
