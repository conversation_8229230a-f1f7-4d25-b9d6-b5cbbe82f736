﻿using Microsoft.AspNetCore.Mvc.Rendering;
using System;
using System.ComponentModel.DataAnnotations;

namespace PSafe.AM.Models
{
    public class RoleCBCSModel
    {
        [Key]
        [Display(Name = "RoleId", ResourceType = typeof(Resources.Resource_roleCBCS))]
        public int RoleId { get; set; }

        [Required(ErrorMessage = "Vui lòng nhập tên nhóm")]
        [StringLength(50, MinimumLength = 3, ErrorMessage = "Độ dài từ 3-50 ký tự")]
        [Display(Name = "RoleName", ResourceType = typeof(Resources.Resource_roleCBCS))]
        public string RoleName { get; set; } = string.Empty;

        [Display(Name = "RoleDescription", ResourceType = typeof(Resources.Resource_roleCBCS))]
        public string RoleDescription { get; set; } = string.Empty;
    }
}