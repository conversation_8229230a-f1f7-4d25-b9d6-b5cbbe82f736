﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using PSafe.AM.Common;
using PSafe.AM.Models;
using PSafe.Core.Domains;
using PSafe.Core.Interface;
using Microsoft.AspNetCore.Http;
using static PSafe.Common.CommonEnums;
using PSafe.Common.UserEnums;
using Microsoft.Extensions.Logging;

namespace PSafe.AM.Controllers
{
    [AuthorizeActionFilter(EPERMISSIONS_AM.categoryManage)]
    public class ScheduleTimeController : Controller
    {
        private readonly IScheduleTimeRepository _scheduleTimeRepository;
        private readonly IUserRepository _userRepository;
        private readonly IHistorySystemRepository _historySystemRepository;
        private readonly ILogger<ScheduleTimeController> _logger;
        private readonly IHttpContextAccessor _accessor;

        public ScheduleTimeController(IScheduleTimeRepository scheduleTimeRepository, IUserRepository userRepository, IHistorySystemRepository historySystemRepository,
             ILogger<ScheduleTimeController> logger, IHttpContextAccessor accessor)
        {
            _scheduleTimeRepository = scheduleTimeRepository;
            _userRepository = userRepository;
            _historySystemRepository = historySystemRepository;
            _logger = logger;
            _accessor = accessor;
        }

        public IActionResult Index()
        {
            StatusQuery Notification;
            List<ScheduleTimeModel> _listScheduleTimeModel = new List<ScheduleTimeModel>();
            try
            {
                var _listScheduleTime = _scheduleTimeRepository.GetAll().ToList();

                foreach(var scheduleTime in _listScheduleTime)
                {
                    ScheduleTimeModel scheduleTimeModel = new ScheduleTimeModel();
                    scheduleTimeModel.Id = scheduleTime.Id;
                    CopyTo(scheduleTimeModel, scheduleTime);
                    _listScheduleTimeModel.Add(scheduleTimeModel);
                }

                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }
            }
            catch(Exception ex)
            {
                _logger.LogError("ScheduleTime/Index: " + ex.Message);

                Notification = new StatusQuery("error", "", "Xem danh sách thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;
            }

            return View(_listScheduleTimeModel);
        }

        public ActionResult Details(int id)
        {
            try
            {
                var _scheduleTime = _scheduleTimeRepository.GetById(id);

                if (_scheduleTime != null)
                {
                    var _scheduleTimeModel = new ScheduleTimeModel();
                    _scheduleTimeModel.Id = id;
                    CopyTo(_scheduleTimeModel, _scheduleTime);
                    try
                    {
                        if (TempDataHelper.Get<StatusQuery>(TempData, "Notification") is StatusQuery Notification)
                        {
                            ViewBag.Status = Notification.Status;
                            ViewBag.Value = Notification.Value;
                            ViewBag.Type = Notification.Type;
                        }

                    }
                    catch (Exception ex)
                    {
                        _logger.LogError("ScheduleTime/Details: " + ex.Message);
                        Console.WriteLine(ex.Message);
                    }

                    return View(_scheduleTimeModel);
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                    return RedirectToAction("Index");
                }
            }
            catch(Exception ex)
            {
                _logger.LogError("ScheduleTime/Details: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                return RedirectToAction("Index");
            }
        }

        public ActionResult Create()
        {
            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create(ScheduleTimeModel scheduleTimeModel)
        {
            StatusQuery Notification;
            if (ModelState.IsValid)
            {
                try
                {
                    if(scheduleTimeModel.TimeBegin >= scheduleTimeModel.TimeEnd)
                    {
                        Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;
                        ModelState.AddModelError("TimeBegin", "Thời gian bắt đầu phải nhỏ hơn thời gian kết thúc");
                        return View(scheduleTimeModel);
                    }

                    var systemUser = GetSesson();
                    if (systemUser == null)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                        return RedirectToAction("Logout", "Security");
                    }

                    ScheduleTime _scheduleTime = new ScheduleTime();
                    CopyTo(_scheduleTime, scheduleTimeModel);

                    _scheduleTimeRepository.Insert(_scheduleTime);

                    var statusInsert = _scheduleTimeRepository.SaveChanges();

                    if (statusInsert > 0)
                    {
                        string StringDescription = new GetStringHistorySystem().Get(systemUser.UserName, Resources.Resource.Edit, _scheduleTime.Name.ToString(), Resources.Resource.ScheduleTime);

                        InsertHistorySystem((int)EACTION_TYPE.CREATE, (int)EnumControllerName.SCHEDULE_TIME, StringDescription, null, _scheduleTime);

                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Thêm mới thành công"));
                        return RedirectToAction("Index");
                    }
                    else
                    {
                        Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        return View(scheduleTimeModel);
                    }
                }
                catch(Exception ex)
                {
                    _logger.LogError("ScheduleTime/Create: " + ex.Message);

                    Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return View(scheduleTimeModel);
                }
            }

            Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;

            return View(scheduleTimeModel);
        }

        public ActionResult Edit(int id)
        {
            try
            {
                var _scheduleTime = _scheduleTimeRepository.GetById(id);
                if (_scheduleTime != null)
                {
                    ScheduleTimeModel scheduleTimeModel = new ScheduleTimeModel();
                    CopyTo(scheduleTimeModel, _scheduleTime);
                    return View(scheduleTimeModel);
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                    return RedirectToAction("Index");
                }
            }
            catch(Exception ex)
            {
                _logger.LogError("ScheduleTime/Create: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                return RedirectToAction("Index");
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit(ScheduleTimeModel scheduleTimeModel)
        {
            StatusQuery Notification;
            if (ModelState.IsValid)
            {
                try
                {
                    if (scheduleTimeModel.TimeBegin >= scheduleTimeModel.TimeEnd)
                    {
                        Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;
                        ModelState.AddModelError("TimeBegin", "Thời gian bắt đầu phải nhỏ hơn thời gian kết thúc");
                        return View(scheduleTimeModel);
                    }

                    var systemUser = GetSesson();
                    if (systemUser == null)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                        return RedirectToAction("Logout", "Security");
                    }

                    var _scheduleTime = _scheduleTimeRepository.GetById(scheduleTimeModel.Id);
                    if(_scheduleTime == null)
                    {
                        Notification = new StatusQuery("error", "", "Không tìm thấy ca trực");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        return View(scheduleTimeModel);
                    }

                    CopyTo(_scheduleTime, scheduleTimeModel);

                    _scheduleTimeRepository.Update(_scheduleTime);

                    var updateStatus = _scheduleTimeRepository.SaveChanges();

                    if (updateStatus > 0)
                    {
                        string StringDescription = new GetStringHistorySystem().Get(systemUser.UserName, Resources.Resource.Edit, scheduleTimeModel.Name.ToString(), Resources.Resource.ScheduleTime);
                        InsertHistorySystem((int)EACTION_TYPE.EDIT, (int)EnumControllerName.SCHEDULE_TIME, StringDescription, scheduleTimeModel, _scheduleTime);

                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Sửa thành công"));

                        return RedirectToAction("Index");
                    }
                    else
                    {
                        Notification = new StatusQuery("error", "", "Sửa thất bại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        return View(scheduleTimeModel);
                    }
                }
                catch(Exception ex)
                {
                    _logger.LogError("ScheduleTime/Edit: " + ex.Message);

                    Notification = new StatusQuery("error", "", "Sửa thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return View(scheduleTimeModel);
                }
            }

            Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;

            return View(scheduleTimeModel);
        }

        public ActionResult Delete(int id)
        {
            StatusQuery Notification;
            try
            {
                var _scheduleTime = _scheduleTimeRepository.GetById(id);

                if (_scheduleTime == null)
                {
                    Notification = new StatusQuery("warning", "", "Không tìm thấy ca trực cần xóa");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return RedirectToAction("Index");
                }

                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }

                ScheduleTimeModel scheduleTimeModel = new ScheduleTimeModel();
                CopyTo(scheduleTimeModel, _scheduleTime);
                return View(scheduleTimeModel);
            }
            catch(Exception ex)
            {
                _logger.LogError("ScheduleTime/Delete: " + ex.Message);

                Notification = new StatusQuery("warning", "", "Xóa thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;

                return RedirectToAction("Index");
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int id)
        {
            try
            {
                var systemUserSession = GetSesson();

                if (systemUserSession == null)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                    return RedirectToAction("Logout", "Security");
                }

                var scheduleTime = _scheduleTimeRepository.GetById(id);
                _scheduleTimeRepository.Delete(scheduleTime);

                var deleteStatus = _scheduleTimeRepository.SaveChanges();

                if (deleteStatus > 0)
                {
                    string StringDescription = new GetStringHistorySystem().Get(systemUserSession.UserName, Resources.Resource.Delete, scheduleTime.Name.ToString(), Resources.Resource.TypeOfDevice);

                    InsertHistorySystem((int)EACTION_TYPE.DELETE, (int)EnumControllerName.SCHEDULE_TIME, StringDescription, scheduleTime, null);

                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Xóa thành công"));
                    return RedirectToAction("Index");
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));
                    return RedirectToAction("Delete", "ScheduleTime", new { id });
                }
            }
            catch(Exception ex)
            {
                _logger.LogError("ScheduleTime/DeleteConfirmed: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));
                return RedirectToAction("Delete", "ScheduleTime", new { id });
            }
        }

        private void CopyTo(ScheduleTime dest, ScheduleTimeModel src)
        {
            dest.Name = src.Name;
            dest.TimeBegin = src.TimeBegin;
            dest.TimeEnd = src.TimeEnd;
            dest.Description = src.Description;
        }

        private void CopyTo(ScheduleTimeModel dest, ScheduleTime src)
        {
            dest.Name = src.Name;
            dest.TimeBegin = src.TimeBegin;
            dest.TimeEnd = src.TimeEnd;
            dest.Description = src.Description;
        }

        public User GetSesson()
        {
            var SessionUserSystem = HttpContext.Session.GetComplexData<User>("SessionUserSystemId_" + HttpContext.Session.GetString("SessionUserSystemId"));
            if (SessionUserSystem != null)
            {
                var _user = _userRepository.GetById(SessionUserSystem.Id);

                if (_user != null)
                {
                    if (!_user.Actived)
                    {
                        HttpContext.Session.Clear();
                        return null;
                    }
                    return _user;
                }
            }
            return null;
        }

        private void InsertHistorySystem(int action_type, int controllerName, string description, object OldModel, object NewModel)
        {
            try
            {
                var ipAddress = _accessor.HttpContext.Connection.RemoteIpAddress.ToString();

                string jsonOldObject = Newtonsoft.Json.JsonConvert.SerializeObject(OldModel);
                string jsonNewObject = Newtonsoft.Json.JsonConvert.SerializeObject(NewModel);

                HistorySystem history = new HistorySystem
                {
                    ActionType = action_type,
                    ActionTime = DateTime.Now,
                    Description = description,
                    OldObject = jsonOldObject,
                    NewObject = jsonNewObject,
                    UserId = int.Parse(HttpContext.Session.GetString("SessionUserSystemId")),
                    IpAddress = ipAddress,
                    ControllerName = controllerName
                };

                _historySystemRepository.Insert(history);

                _historySystemRepository.SaveChanges();
            }
            catch (Exception ex)
            {
                _logger.LogError("ScheduleTime/InsertHistorySystem: " + ex.Message);
            }
        }
    }
}