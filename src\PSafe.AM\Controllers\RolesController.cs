﻿using System.Collections.Generic;
using System.Linq;
using PSafe.AM.Models;
using PSafe.Core.Domains;
using PSafe.Core.Interface;
using PSafe.Core.SharedKernel;
using System.Transactions;
using Microsoft.AspNetCore.Mvc;
using AutoMapper;
using PSafe.AM.Common;
using System.Net;
using System;
using Microsoft.AspNetCore.Http;
using static PSafe.Common.CommonEnums;
using Microsoft.AspNetCore.Mvc.Rendering;
using PSafe.Common.UserEnums;
using Microsoft.Extensions.Logging;
using PSafe.Core.Services;
using ExternalServices.VCamService;
using PSafe.Common.VMSEnums;

namespace PSafe.AM.Controllers
{
    [AuthorizeActionFilter(EPERMISSIONS_AM.UserManage)]
    public class RolesController : Controller
    {
        private readonly IRoleRepository _roleRepository;
        private readonly IUserInRoleRepository _userInRoleRepository;
        private readonly IUserRepository _userRepository;
        private readonly IMapper _mapper;
        private readonly IHistorySystemRepository _historySystemRepository;
        private readonly IDeviceRepository _deviceRepository;
        private readonly IHttpContextAccessor _accessor;
        private readonly ILogger<RolesController> _logger;

        public RolesController(IUnitOfWork unitOfWork, IMapper mapper, IDeviceRepository deviceRepository, IHttpContextAccessor accessor, ILogger<RolesController> logger)
        {
            _roleRepository = unitOfWork.RoleRepository;
            _userInRoleRepository = unitOfWork.UserInRoleRepository;
            _userRepository = unitOfWork.UserRepository;
            _mapper = mapper;
            _historySystemRepository = unitOfWork.HistorySystemRepository;
            _accessor = accessor;
            _logger = logger;
            _deviceRepository = deviceRepository;
        }

        public ActionResult Index()
        {
            StatusQuery Notification;
            List<RoleModel> _listRoleModel = new List<RoleModel>();
            try
            {
                var _listRole = _roleRepository.GetAll().ToList();

                _listRoleModel = _mapper.Map<List<Role>, List<RoleModel>>(_listRole);

                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Role/Index: " + ex.Message);

                Notification = new StatusQuery("error", "", "Xem danh sách thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;
            }

            return View(_listRoleModel);
        }

        public ActionResult Details(int id)
        {
            try
            {
                var _role = _roleRepository.GetById(id);

                if (TempDataHelper.Get<StatusQuery>(TempData, "Notification") is StatusQuery Notification)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }

                var _roleModel = _mapper.Map<Role, RoleModel>(_role);

                if (_roleModel != null)
                {
                    return View(_roleModel);
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                    return RedirectToAction("Index");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Role/Details: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                return RedirectToAction("Index");
            }
        }

        public ActionResult EditUser(int id)
        {
            StatusQuery Notification;
            try
            {
                UserViewModel view = new UserViewModel();
                List<User> listViewUserInRole = new List<User>();
                List<User> listViewUserNotInRole = new List<User>();
                List<User> listUserNotInRole = new List<User>();

                var listUserInRole = _userInRoleRepository.GetBy(r => r.RoleId == id).ToList();
                var RoleName = _roleRepository.GetById(id).RoleName;
                var listSystemUser = _userRepository.GetAll();

                ViewBag.Rolename = RoleName;

                var check = true;
                foreach (var itemSystemUser in listSystemUser)
                {
                    check = true;
                    foreach (var itemUserInRol in listUserInRole)
                    {
                        if (itemSystemUser.Id == itemUserInRol.UserId)
                        {
                            check = false;
                            break;
                        }
                    }

                    if (check)
                    {
                        listUserNotInRole.Add(itemSystemUser);
                    }
                }

                foreach (var item in listUserInRole)
                {
                    User user = _userRepository.GetById(item.UserId);
                    listViewUserInRole.Add(user);
                }

                foreach (var item in listUserNotInRole)
                {
                    User user = _userRepository.GetById(item.Id);
                    listViewUserNotInRole.Add(user);
                }

                view.UserInRole = listViewUserInRole;
                view.UserNotInRole = listViewUserNotInRole;

                view.RoleId = id;

                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }

                return View(view);
            }
            catch (Exception ex)
            {
                _logger.LogError("Role/EditUser: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Hiệu chỉnh người dùng thất bại"));

                return RedirectToAction("Index");
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult EditUser(int RoleId, List<int> dual_select)
        {
            using (TransactionScope scope = new TransactionScope())
            {
                try
                {
                    var systemUser = GetSesson();

                    if (systemUser == null)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                        return RedirectToAction("Logout", "Security");
                    }

                    List<UserInRoleModel> listUserInRoleNew = new List<UserInRoleModel>();
                    List<UserInRoleModel> listUserInRoleOld = new List<UserInRoleModel>();
                    var listUserInRole = _userInRoleRepository.GetBy(r => r.RoleId == RoleId).ToList();

                    foreach (var item in listUserInRole)
                    {
                        UserInRoleModel userInRoleModel = new UserInRoleModel
                        {
                            UserId = item.UserId,
                            RoleId = item.RoleId
                        };

                        _userInRoleRepository.Delete(item);
                        listUserInRoleOld.Add(userInRoleModel);
                    }

                    if (dual_select != null)
                    {
                        foreach (var item in dual_select)
                        {
                            UserInRoleModel userInRoleModel = new UserInRoleModel
                            {
                                UserId = item,
                                RoleId = RoleId
                            };

                            UserInRole userInRole = new UserInRole
                            {
                                UserId = item,
                                RoleId = RoleId
                            };

                            listUserInRoleNew.Add(userInRoleModel);
                            _userInRoleRepository.Insert(userInRole);
                        }
                    }
                    var StatusUserInRole = _userInRoleRepository.SaveChanges();

                    if (StatusUserInRole > 0)
                    {
                        string StringDescription = new GetStringHistorySystem().Get(systemUser.UserName, Resources.Resource.Edit, Resources.Resource.EditUser, Resources.Resource.Role);

                        InsertHistorySystem((int)EACTION_TYPE.EDIT, (int)EnumControllerName.EDIT_USER_ON_ROLE, StringDescription, listUserInRoleOld, listUserInRoleNew);

                        scope.Complete();
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("Role/EditUser: " + ex.Message);

                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Sửa thất bại"));
                    return RedirectToAction("EditUser", "Roles", new { RoleId });
                }
            }

            TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Sửa thành công"));
            return RedirectToAction("EditUser", "Roles", new { RoleId });
        }

        public ActionResult Create()
        {
            ListTypeOfSingnal listTypeOfSingnal = new ListTypeOfSingnal();

            RoleModel roleModel = new RoleModel
            {
                ListType = ToSelectList(listTypeOfSingnal.List)
            };

            return View(roleModel);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create([Bind("RoleId, RoleName, RoleDescription, Type, PhoneNumber")] RoleModel roleModel)
        {
            StatusQuery Notification;
            if (ModelState.IsValid)
            {
                try
                {
                    var _role = _mapper.Map<RoleModel, Role>(roleModel);

                    _role.Type = roleModel.Type;
                    _role.PhoneNumber = roleModel.PhoneNumber == null ? string.Empty : roleModel.PhoneNumber.Trim();
                    _role.ListFunction_ACS = null;
                    _role.ListFunction_AM = null;
                    _role.ListFunction_VMS = null;
                    _role.ListFunction_TTAN = null;
                    _role.ListFunction_REPORT = null;

                    _roleRepository.Insert(_role);

                    var statusInsert = _roleRepository.SaveChanges();

                    if (statusInsert > 0)
                    {
                        var systemUser = GetSesson();

                        if (systemUser == null)
                        {
                            TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                            return RedirectToAction("Logout", "Security");
                        }

                        string StringDescription = new GetStringHistorySystem().Get(systemUser.UserName, Resources.Resource.Create, _role.RoleName.ToString(), Resources.Resource.Role);

                        InsertHistorySystem((int)EACTION_TYPE.CREATE, (int)EnumControllerName.ROLES, StringDescription, null, _role);

                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Thêm mới thành công"));
                        return RedirectToAction("Index");
                    }
                    else
                    {
                        Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        return View(roleModel);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("Role/Create: " + ex.Message);

                    Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return View(roleModel);
                }
            }

            Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;
            return View(roleModel);
        }

        public ActionResult Edit(int id)
        {
            try
            {
                var _role = _roleRepository.GetById(id);
                if (_role == null)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));
                    return RedirectToAction("Index");
                }

                var _roleModel = _mapper.Map<Role, RoleModel>(_role);

                ListTypeOfSingnal listTypeOfSingnal = new ListTypeOfSingnal();
                _roleModel.ListType = ToSelectList(listTypeOfSingnal.List);

                return View(_roleModel);
            }
            catch (Exception ex)
            {
                _logger.LogError("Role/Edit: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                return RedirectToAction("Index");
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit([Bind("RoleId,RoleName,RoleDescription,Type, PhoneNumber")] RoleModel roleModel)
        {
            StatusQuery Notification;
            ListTypeOfSingnal listTypeOfSingnal = new ListTypeOfSingnal();
            if (ModelState.IsValid)
            {
                try
                {
                    var role = _roleRepository.GetById(roleModel.RoleId);

                    var _roleTemp = _mapper.Map<Role, RoleModel>(role);
                    var roleOld = _mapper.Map<RoleModel, Role>(_roleTemp);

                    role.RoleName = roleModel.RoleName;
                    role.PhoneNumber = roleModel.PhoneNumber == null ? string.Empty : roleModel.PhoneNumber.Trim();
                    role.RoleDescription = roleModel.RoleDescription;
                    role.PhoneNumber = roleModel.PhoneNumber;

                    _roleRepository.Update(role);

                    var updateStatus = _roleRepository.SaveChanges();

                    if (updateStatus > 0)
                    {
                        var systemUser = GetSesson();

                        if (systemUser == null)
                        {
                            TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                            return RedirectToAction("Logout", "Security");
                        }

                        string StringDescription = new GetStringHistorySystem().Get(systemUser.UserName, Resources.Resource.Edit, role.RoleName.ToString(), Resources.Resource.Role);

                        InsertHistorySystem((int)EACTION_TYPE.EDIT, (int)EnumControllerName.ROLES, StringDescription, roleOld, role);

                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Sửa thành công"));

                        return RedirectToAction("Index");
                    }
                    else
                    {
                        Notification = new StatusQuery("error", "", "Sửa thất bại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        roleModel.ListType = ToSelectList(listTypeOfSingnal.List);

                        return View(roleModel);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("Role/Edit: " + ex.Message);

                    Notification = new StatusQuery("error", "", "Sửa thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    roleModel.ListType = ToSelectList(listTypeOfSingnal.List);

                    return View(roleModel);
                }

            }

            Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;

            roleModel.ListType = ToSelectList(listTypeOfSingnal.List);

            return View(roleModel);
        }

        public ActionResult Delete(int id)
        {
            StatusQuery Notification;

            try
            {
                var _role = _roleRepository.GetById(id);

                var _roleModel = _mapper.Map<Role, RoleModel>(_role);

                if (_roleModel == null)
                {
                    Notification = new StatusQuery("warning", "", "Không tìm thấy nhóm");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return RedirectToAction("Index");
                }

                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }

                return View(_roleModel);
            }
            catch (Exception ex)
            {
                _logger.LogError("Role/Delete: " + ex.Message);

                Notification = new StatusQuery("warning", "", "Xóa thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;

                return RedirectToAction("Index");
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int id)
        {
            try
            {
                var systemUser = GetSesson();

                if (systemUser == null)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                    return RedirectToAction("Logout", "Security");
                }

                var role = _roleRepository.GetById(id);

                _roleRepository.Delete(role);

                var deleteStatus = _roleRepository.SaveChanges();

                if (deleteStatus > 0)
                {
                    string StringDescription = new GetStringHistorySystem().Get(systemUser.UserName, Resources.Resource.Delete, role.RoleName.ToString(), Resources.Resource.Role);

                    InsertHistorySystem((int)EACTION_TYPE.DELETE, (int)EnumControllerName.ROLES, StringDescription, role, null);

                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Xóa thành công"));
                    return RedirectToAction("Index");
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));
                    return RedirectToAction("Delete", "Roles", new { id });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Role/DeleteConfirmed: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));
                return RedirectToAction("Delete", "Roles", new { id });
            }
        }

        public ActionResult Decentralization(int id)
        {
            try
            {
                TweListCheckBox view = new TweListCheckBox();
                List<CheckBok> CAMERA = new List<CheckBok>();
                List<CheckBok> listCameraNotInRole = new List<CheckBok>();
                List<CheckBok> CameraNotInRole = new List<CheckBok>();
                List<CheckBok> CameraInRole = new List<CheckBok>();
                var systemUser = GetSesson();

                if (systemUser == null)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                    return RedirectToAction("Logout", "Security");
                }
                var listDevice = _deviceRepository.GetAll().Select(p => new CheckBok()
                {
                    Name = p.DeviceName,
                    Value = p.Id.ToString(),
                    Disabled = false,
                    Checked = false,
                }).ToList();

                var role = _roleRepository.GetBy(r => r.RoleId == id).SingleOrDefault();

                var listCamera = new string[0];

                if (!string.IsNullOrEmpty(role.ListFunction_CAMERA))
                    listCamera = role.ListFunction_CAMERA.Split('|');

                var listRoleFunction = ListFunction();
                listRoleFunction.id = id;
                listRoleFunction.CAMERA = listDevice;

                if (role.ListFunction_CAMERA != null && role.ListFunction_CAMERA.Trim() != string.Empty)
                {
                    var listFunctionCamera = role.ListFunction_CAMERA.Split('|');

                    foreach (var roleCheckBox in listRoleFunction.CAMERA)
                    {                     
                        var a = listFunctionCamera.Contains(roleCheckBox.Name);
                        if (a == true)
                        {
                            roleCheckBox.Checked = true;
                            CameraInRole.Add(roleCheckBox);
                        }
                        else
                        {
                            roleCheckBox.Checked = false;
                            CameraNotInRole.Add(roleCheckBox);
                        }                       
                    }
                }
                else
                {
                    CameraNotInRole = listRoleFunction.CAMERA;
                }

                listRoleFunction.CameraNotInRole = CameraNotInRole;
                listRoleFunction.CameraInRole = CameraInRole;

                if (role.ListFunction_ACS != null && role.ListFunction_ACS.Trim() != "")
                {
                    var listFunctionACS = role.ListFunction_ACS.Split(',');

                    foreach (var roleCheckBox in listRoleFunction.ACS)
                    {
                        foreach (var item in listFunctionACS)
                        {
                            if (roleCheckBox.Id == int.Parse(item))
                            {
                                roleCheckBox.Checked = true;
                            }
                        }
                    }
                }

                if (role.ListFunction_AM != null && role.ListFunction_AM.Trim() != "")
                {
                    var listFunctionAM = role.ListFunction_AM.Split(',');

                    foreach (var roleCheckBox in listRoleFunction.AM)
                    {
                        foreach (var item in listFunctionAM)
                        {
                            if (roleCheckBox.Id == int.Parse(item))
                            {
                                roleCheckBox.Checked = true;
                            }
                        }
                    }
                }

                if (role.ListFunction_TTAN != null && role.ListFunction_TTAN.Trim() != "")
                {
                    var listFunctionTTAN = role.ListFunction_TTAN.Split(',');

                    foreach (var roleCheckBox in listRoleFunction.TTAN)
                    {
                        foreach (var item in listFunctionTTAN)
                        {
                            if (roleCheckBox.Id == int.Parse(item))
                            {
                                roleCheckBox.Checked = true;
                            }
                        }
                    }
                }

                if (role.ListFunction_REPORT != null && role.ListFunction_REPORT.Trim() != "")
                {
                    var listFunctionREPORT = role.ListFunction_REPORT.Split(',');

                    foreach (var roleCheckBox in listRoleFunction.REPORT)
                    {
                        foreach (var item in listFunctionREPORT)
                        {
                            if (roleCheckBox.Id == int.Parse(item))
                            {
                                roleCheckBox.Checked = true;
                            }
                        }
                    }
                }

                if (role.ListFunction_VMS != null && role.ListFunction_VMS.Trim() != "")
                {
                    var listFunctionVMS = role.ListFunction_VMS.Split(',');

                    foreach (var roleCheckBox in listRoleFunction.VMS)
                    {
                        foreach (var item in listFunctionVMS)
                        {
                            if (roleCheckBox.Id == int.Parse(item))
                            {
                                roleCheckBox.Checked = true;
                            }
                        }
                    }
                }             
                StatusQuery Notification;

                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Tag = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }
                else
                    ViewBag.Tag = "TTAN";

                return View(listRoleFunction);
            }
            catch (Exception ex)
            {
                _logger.LogError("Role/Decentralization: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Hiệu chỉnh người dùng thất bại"));

                return RedirectToAction("Index");
            }

        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Decentralization(List<int> ACS, List<int> AM, List<int> TTAN, List<int> REPORT, List<int> VMS, List<string> dual_select, int id, string type)
        {
            try
            {
                var systemUser = GetSesson();

                if (systemUser == null)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                    return RedirectToAction("Logout", "Security");
                }

                var role = _roleRepository.GetById(id);

                if (role == null)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Không tìm thấy dữ liệu, vui lòng kiểm tra lại"));
                    return RedirectToAction("Index", "Roles");
                }

                var _roleTemp = _mapper.Map<Role, RoleModel>(role);
                var roleOld = _mapper.Map<RoleModel, Role>(_roleTemp);

                var function = string.Empty;

                Role roleNew = new Role();

                switch (type)
                {
                    case "ACS":
                        foreach (var item in ACS)
                        {
                            if (function == string.Empty)
                                function += item;
                            else
                                function += "," + item;
                        }

                        role.ListFunction_ACS = function;
                        roleNew.ListFunction_ACS = role.ListFunction_ACS;
                        roleNew.RoleId = role.RoleId;
                        break;
                    case "AM":
                        foreach (var item in AM)
                        {
                            if (function == string.Empty)
                                function += item;
                            else
                                function += "," + item;
                        }

                        role.ListFunction_AM = function;
                        roleNew.ListFunction_AM = role.ListFunction_AM;
                        roleNew.RoleId = role.RoleId;
                        break;
                    case "TTAN":
                        var BlacklistControlForTTAN = TTAN.Contains((int)EPERMISSIONS_AM.BlacklistControlForTTAN);
                        var BlacklistControlForTTAN_validate = TTAN.Any(p => p >= (int)EPERMISSIONS_AM.ForbidViolatingLoadControl && p <= (int)EPERMISSIONS_AM.CancelOther);

                        if (!BlacklistControlForTTAN && BlacklistControlForTTAN_validate)
                        {
                            TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Giá trị chọn chưa hợp lệ"));
                            return RedirectToAction("Decentralization", "Roles", new { id });
                        }

                        foreach (var item in TTAN)
                        {
                            if (function == string.Empty)
                                function += item;
                            else
                                function += "," + item;
                        }

                        role.ListFunction_TTAN = function;
                        roleNew.ListFunction_TTAN = role.ListFunction_TTAN;
                        roleNew.RoleId = role.RoleId;
                        break;
                    case "VMS":
                        foreach (var item in VMS)
                        {
                            if (function == string.Empty)
                                function += item;
                            else
                                function += "," + item;
                        }

                        var listCamera = string.Empty;

                        foreach (var item in dual_select)
                        {
                            if (listCamera == string.Empty)
                                listCamera += item;
                            else
                                listCamera += "|" + item;
                        }

                        role.ListFunction_VMS = function;
                        role.ListFunction_CAMERA = listCamera;
                        roleNew.ListFunction_VMS = role.ListFunction_VMS;
                        roleNew.RoleId = role.RoleId;
                        break;
                    case "REPORT":
                        foreach (var item in REPORT)
                        {
                            if (function == string.Empty)
                                function += item;
                            else
                                function += "," + item;
                        }

                        role.ListFunction_REPORT = function;
                        roleNew.ListFunction_REPORT = role.ListFunction_REPORT;
                        roleNew.RoleId = role.RoleId;

                        break;
                }

                _roleRepository.Update(role);

                var StatusUpdate = _roleRepository.SaveChanges();

                if (StatusUpdate > 0)
                {
                    var _client = new VCamServiceSoapClient(VCamServiceSoapClient.EndpointConfiguration.VCamServiceSoap);

                    string userFunctions = GetUserFunctions(systemUser);

                    HttpContext.Session.SetString("UserFunctions", userFunctions);

                    string StringDescription = new GetStringHistorySystem().Get(systemUser.UserName, Resources.Resource.Edit, role.RoleName.ToString(), Resources.Resource.Role);

                    InsertHistorySystem((int)EACTION_TYPE.EDIT, (int)EnumControllerName.ROLES, StringDescription, roleOld, roleNew);

                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", type, "Hiệu chỉnh phân quyền thành công!"));
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Hiệu chỉnh phân quyền thất bại!"));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Role/Decentralization: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Hiệu chỉnh phân quyền thất bại!"));
            }

            return RedirectToAction("Decentralization", "Roles", new { id });
        }

        private string GetUserFunctions(User user)
        {
            List<int> userFunctions = new List<int>();
            try
            {
                var userInRoles = _userInRoleRepository.GetBy(x => x.UserId == user.Id).Select(x => x.RoleId).ToList();

                if (userInRoles != null)
                {
                    List<string> roles = _roleRepository.GetBy(x => userInRoles.Contains(x.RoleId)).Select(x => x.ListFunction_AM).ToList();

                    foreach (var role in roles)
                    {
                        if (role != null)
                        {
                            string[] functionArray = role.Split(',');
                            foreach (string functionItem in functionArray)
                            {
                                int function;
                                if (int.TryParse(functionItem, out function))
                                {
                                    if (!userFunctions.Contains(function))
                                        userFunctions.Add(function);
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.Message);
            }
            return string.Join(",", userFunctions);
        }

        public bool CheckExits(List<int> ACS, List<int> TTAN, List<int> AM, List<int> VMS, List<int> REPORT, int id, string type)
        {
            try
            {
                var ListuserInRole = _userInRoleRepository.Get(p => p.UserId == id, null, "Role").ToList();

                bool check;

                switch (type)
                {
                    case "AM":
                        foreach (var item in AM)
                        {
                            check = false;
                            foreach (var listRole in ListuserInRole)
                            {
                                if (listRole.Role.ListFunction_AM != null && listRole.Role.ListFunction_AM.ToString().Contains(item.ToString("0")))
                                {
                                    check = true;
                                    break;
                                }
                            }

                            if (!check)
                            {
                                return false;
                            }
                        }
                        break;
                    case "TTAN":
                        foreach (var item in TTAN)
                        {
                            check = false;
                            foreach (var listRole in ListuserInRole)
                            {
                                if (listRole.Role.ListFunction_TTAN != null && listRole.Role.ListFunction_TTAN.ToString().Contains(item.ToString("0")))
                                {
                                    check = true;
                                    break;
                                }
                            }

                            if (!check)
                            {
                                return false;
                            }
                        }
                        break;
                    case "REPORT":
                        foreach (var item in REPORT)
                        {
                            check = false;
                            foreach (var listRole in ListuserInRole)
                            {
                                if (listRole.Role.ListFunction_REPORT != null && listRole.Role.ListFunction_REPORT.ToString().Contains(item.ToString("0")))
                                {
                                    check = true;
                                    break;
                                }
                            }

                            if (!check)
                            {
                                return false;
                            }
                        }
                        break;
                    case "VMS":
                        foreach (var item in VMS)
                        {
                            check = false;
                            foreach (var listRole in ListuserInRole)
                            {
                                if (listRole.Role.ListFunction_VMS != null && listRole.Role.ListFunction_VMS.ToString().Contains(item.ToString("0")))
                                {
                                    check = true;
                                    break;
                                }
                            }

                            if (!check)
                            {
                                return false;
                            }
                        }
                        break;
                    case "ACS":
                        foreach (var item in ACS)
                        {
                            check = false;
                            foreach (var listRole in ListuserInRole)
                            {
                                if (listRole.Role.ListFunction_ACS != null && listRole.Role.ListFunction_ACS.ToString().Contains(item.ToString("0")))
                                {
                                    check = true;
                                    break;
                                }
                            }

                            if (!check)
                            {
                                return false;
                            }
                        }
                        break;
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("Role/CheckExits: " + ex.Message);

                return false;
            }
        }

        [NonAction]
        private SelectList ToSelectList(List<DropDownList> DropDownList)
        {
            List<SelectListItem> list = new List<SelectListItem>();

            foreach (var item in DropDownList)
            {
                list.Add(new SelectListItem()
                {
                    Text = item.Name,
                    Value = item.Id.ToString()
                });
            }

            return new SelectList(list, "Value", "Text");
        }

        public TweListCheckBox ListFunction()
        {
            TweListCheckBox List = new TweListCheckBox();
            try
            {
                List<CheckBok> listCheckBoxAM = new List<CheckBok>();
                List<CheckBok> listCheckBoxTTAN = new List<CheckBok>();
                List<CheckBok> listCheckBoxReport = new List<CheckBok>();
                List<CheckBok> listCheckBoxACS = new List<CheckBok>();
                List<CheckBok> listCheckBoxVMS = new List<CheckBok>();

                foreach (EPERMISSIONS_AM function in (EPERMISSIONS_AM[])Enum.GetValues(typeof(EPERMISSIONS_AM)))
                {
                    if ((int)function < (int)EPERMISSIONS_AM.UserManage)
                        continue;
                    var statusName = Common.Utilities.GetEnum_C3_AM(function);

                    listCheckBoxAM.Add(new CheckBok()
                    {
                        Id = (int)function,
                        Name = function.ToString(),
                        Value = statusName,
                        Checked = false,
                        Disabled = true
                    });
                }
                foreach (EPERMISSIONS_AM function in (EPERMISSIONS_AM[])Enum.GetValues(typeof(EPERMISSIONS_AM)))
                {
                    if ((int)function >= (int)EPERMISSIONS_AM.UserManage)
                        break;

                    var statusName = Common.Utilities.GetEnum_C3_TTAN(function);

                    listCheckBoxTTAN.Add(new CheckBok()
                    {
                        Id = (int)function,
                        Name = function.ToString(),
                        Value = statusName,
                        Checked = false,
                        Disabled = true
                    });
                }
                foreach (EREPORT_FUNCTION function in (EREPORT_FUNCTION[])Enum.GetValues(typeof(EREPORT_FUNCTION)))
                {
                    var statusName = Common.Utilities.GetEnum_C3_Report(function);

                    listCheckBoxReport.Add(new CheckBok()
                    {
                        Id = (int)function,
                        Name = function.ToString(),
                        Value = statusName,
                        Checked = false,
                        Disabled = true
                    });
                }
                foreach (EACS_PERMISSIONS function in (EACS_PERMISSIONS[])Enum.GetValues(typeof(EACS_PERMISSIONS)))
                {
                    var statusName = Common.Utilities.GetEnum_CheckIO_Client(function);

                    listCheckBoxACS.Add(new CheckBok()
                    {
                        Id = (int)function,
                        Name = function.ToString(),
                        Value = statusName,
                        Checked = false,
                        Disabled = true
                    });
                }

                foreach (EMANAGEMENT_SERVER function in (EMANAGEMENT_SERVER[])Enum.GetValues(typeof(EMANAGEMENT_SERVER)))
                {
                    var statusName = Common.Utilities.GetEnum_Management_Server(function);

                    listCheckBoxVMS.Add(new CheckBok()
                    {
                        Id = (int)function,
                        Name = function.ToString(),
                        Value = statusName,
                        Checked = false,
                        Disabled = true
                    });
                }
                foreach (EVMS_CLIENT function in (EVMS_CLIENT[])Enum.GetValues(typeof(EVMS_CLIENT)))
                {
                    var statusName = Common.Utilities.GetEnum_VMS_Client(function);

                    listCheckBoxVMS.Add(new CheckBok()
                    {
                        Id = (int)function,
                        Name = function.ToString(),
                        Value = statusName,
                        Checked = false,
                        Disabled = true
                    });
                }

                List.TTAN = listCheckBoxTTAN;
                List.AM = listCheckBoxAM;
                List.ACS = listCheckBoxACS;
                List.VMS = listCheckBoxVMS;
                List.REPORT = listCheckBoxReport;

                return List;
            }
            catch (Exception ex)
            {
                _logger.LogError("Role/List: " + ex.Message);

                return null;
            }
        }

        public User GetSesson()
        {
            var SessionUserSystem = HttpContext.Session.GetComplexData<User>("SessionUserSystemId_" + HttpContext.Session.GetString("SessionUserSystemId"));
            if (SessionUserSystem != null)
            {
                var _user = _userRepository.GetById(SessionUserSystem.Id);

                if (_user != null)
                {
                    if (!_user.Actived)
                    {
                        HttpContext.Session.Clear();
                        return null;
                    }
                    return _user;
                }
            }
            return null;
        }

        private void InsertHistorySystem(int action_type, int controllerName, string description, object OldModel, object NewModel)
        {
            try
            {
                var ipAddress = _accessor.HttpContext.Connection.RemoteIpAddress.ToString();

                string jsonOldObject = Newtonsoft.Json.JsonConvert.SerializeObject(OldModel);
                string jsonNewObject = Newtonsoft.Json.JsonConvert.SerializeObject(NewModel);

                HistorySystem history = new HistorySystem
                {
                    ActionType = action_type,
                    ActionTime = DateTime.Now,
                    Description = description,
                    OldObject = jsonOldObject,
                    NewObject = jsonNewObject,
                    UserId = int.Parse(HttpContext.Session.GetString("SessionUserSystemId")),
                    IpAddress = ipAddress,
                    ControllerName = controllerName
                };

                _historySystemRepository.Insert(history);

                _historySystemRepository.SaveChanges();
            }
            catch (Exception ex)
            {
                _logger.LogError("Role/InsertHistorySystem: " + ex.Message);
            }
        }
    }
}
