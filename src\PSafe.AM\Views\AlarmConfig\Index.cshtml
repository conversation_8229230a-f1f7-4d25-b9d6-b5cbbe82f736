﻿@model PSafe.AM.Common.General
@{
    ViewBag.Title = PSafe.AM.Resources.Resource.AlarmConfig;
    var roles = (List<PSafe.Core.Domains.Role>)ViewBag.roles;
}

<environment names="Development">
    <script src="~/lib/jquery/dist/jquery.js"></script>
    <script src="~/lib/validate/jquery.validate.min.js"></script>
    <script src="~/js/script.js" asp-append-version="true"></script>
</environment>
<environment names="Staging,Production">
    <script src="~/lib/jquery/dist/jquery.js"></script>
    <script src="~/lib/validate/jquery.validate.min.js"></script>
    <script src="~/js/script.js" asp-append-version="true"></script>
</environment>

<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-lg-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>@PSafe.AM.Resources.Resource.GeneralConfig</h5>
                    <div class="ibox-tools">
                    </div>
                </div>
                <div class="ibox-content">
                    <ul class="nav nav-tabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" data-toggle="tab" href="#homeGeneralConfig">Cấu hình chung</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-toggle="tab" href="#menuAlertLevel">Cấu hình mức độ cảnh báo</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-toggle="tab" href="#menuAlarmConfig">@PSafe.AM.Resources.Resource.AlarmConfig</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-toggle="tab" href="#menuViolateConfig">Cấu hình phạt nóng</a>
                        </li>
                    </ul>

                    <!-- Tab panes -->
                    <div class="tab-content">
                        <div id="homeGeneralConfig" class="container tab-pane active">
                            <br>
                            @using (Html.BeginForm("GeneralConfig", "AlarmConfig", FormMethod.Post))
                            {
                                @Html.AntiForgeryToken()

                                <div>
                                    <div class="row">
                                        <label class="control-label col-md-4">@Html.LabelFor(model => model.generalConfig.TimeSendSMS) (giây)</label>
                                        <div class="col-md-4">
                                            @Html.EditorFor(model => model.generalConfig.TimeSendSMS, new { htmlAttributes = new { min = 0 } })
                                            @Html.ValidationMessageFor(model => model.generalConfig.TimeSendSMS, null, new { @class = "text-danger" })
                                        </div>
                                    </div>
                                    <div class="row">
                                        <label class="control-label col-md-4">@Html.LabelFor(model => model.generalConfig.TimeActionReplay) (giây)</label>
                                        <div class="col-md-4">
                                            @Html.EditorFor(model => model.generalConfig.TimeActionReplay, new { htmlAttributes = new { min = 0 } })
                                            @Html.ValidationMessageFor(model => model.generalConfig.TimeActionReplay, null, new { @class = "text-danger" })
                                        </div>
                                    </div>
                                    <div class="row">
                                        <label class="control-label col-md-4">@Html.LabelFor(model => model.generalConfig.TimeCaptureVideo) (giây)</label>
                                        <div class="col-md-4">
                                            @Html.EditorFor(model => model.generalConfig.TimeCaptureVideo, new { htmlAttributes = new { min = 0 } })
                                            @Html.ValidationMessageFor(model => model.generalConfig.TimeCaptureVideo, null, new { @class = "text-danger" })
                                        </div>
                                    </div>
                                    <div class="row">
                                        <label class="control-label col-md-4">@Html.LabelFor(model => model.generalConfig.MaxSearchDevice)</label>
                                        <div class="col-md-4">
                                            @Html.EditorFor(model => model.generalConfig.MaxSearchDevice, new { htmlAttributes = new { min = 0 } })
                                            @Html.ValidationMessageFor(model => model.generalConfig.MaxSearchDevice, null, new { @class = "text-danger" })
                                        </div>
                                    </div>
                                    <div class="row" style="padding: 0px 0px 5px 0px">
                                        @Html.LabelFor(model => model.generalConfig.SiteSetViolation, new { @class = "control-label col-md-4" })
                                        <div class="col-md-4">
                                            <select data-placeholder="Vui lòng chọn..." class="chosen-select" name="generalConfig.SiteSetViolation" multiple tabindex="4">
                                                @foreach (var item in Model.generalConfig.ListSiteSetViolationSelect)
                                                {
                                                    <option selected="selected" value="@item.Id">@Html.DisplayFor(modelItem => item.Name)</option>
                                                }
                                                @foreach (var item in Model.generalConfig.ListSiteSetViolationNoSelect)
                                                {
                                                    <option value="@item.Id">@Html.DisplayFor(modelItem => item.Name)</option>
                                                }
                                            </select>
                                            @Html.ValidationMessageFor(model => model.generalConfig.SiteSetViolation, null, new { @class = "text-danger" })
                                        </div>
                                    </div>
                                    <div class="row" style="padding: 0px 0px 5px 0px">
                                        @Html.LabelFor(model => model.generalConfig.MinSOSLevel, new { @class = "control-label col-md-4" })
                                        <div class="col-md-4">
                                            <select data-placeholder="Vui lòng chọn..." class="chosen-select" name="generalConfig.MinSOSLevel" multiple tabindex="4">
                                                @foreach (var item in Model.generalConfig.ListMinSOSLevelSelect)
                                                {
                                                    <option selected="selected" value="@item.Id">@Html.DisplayFor(modelItem => item.Name)</option>
                                                }
                                                @foreach (var item in Model.generalConfig.ListMinSOSLevelNoSelect)
                                                {
                                                    <option value="@item.Id">@Html.DisplayFor(modelItem => item.Name)</option>
                                                }
                                            </select>
                                            @Html.ValidationMessageFor(model => model.generalConfig.MinSOSLevel, null, new { @class = "text-danger" })
                                        </div>
                                    </div>
                                    <div class="row" style="padding: 0px 0px 5px 0px">
                                        @Html.LabelFor(model => model.generalConfig.ScreenAlarm, new { @class = "control-label col-md-4" })
                                        <div class="col-md-4">
                                            <select data-placeholder="Vui lòng chọn..." class="chosen-select" name="generalConfig.ScreenAlarm" id="generalConfig.ScreenAlarm" tabindex="4">
                                                @if (!Model.generalConfig.ListScreen.Any(p => p.Name == Model.generalConfig.ScreenAlarm))
                                                {
                                                    <option value="-1">---Chọn---</option>
                                                }
                                                @foreach (var item in Model.generalConfig.ListScreen)
                                                {
                                                    if (item.Id == Model.generalConfig.ScreenAlarm)
                                                    {
                                                        <option selected="selected" value="@item.Id">@Html.DisplayFor(modelItem => item.Name)</option>
                                                    }
                                                    else
                                                    {
                                                        <option value="@item.Id">@Html.DisplayFor(modelItem => item.Name)</option>
                                                    }
                                                }
                                            </select>
                                            @Html.ValidationMessageFor(model => model.generalConfig.ScreenAlarm, null, new { @class = "text-danger" })
                                        </div>
                                    </div>
                                    <div class="row" style="padding: 0px 0px 5px 0px">
                                        @Html.LabelFor(model => model.generalConfig.ScreenProcessEvent, new { @class = "control-label col-md-4" })
                                        <div class="col-md-4">
                                            <select data-placeholder="Vui lòng chọn..." class="chosen-select" name="generalConfig.ScreenProcessEvent" id="generalConfig.ScreenProcessEvent" tabindex="4">
                                                @if (!Model.generalConfig.ListScreen.Any(p => p.Name == Model.generalConfig.ScreenProcessEvent))
                                                {
                                                    <option value="-1">---Chọn---</option>
                                                }
                                                @foreach (var item in Model.generalConfig.ListScreen)
                                                {
                                                    if (item.Id == Model.generalConfig.ScreenProcessEvent)
                                                    {
                                                        <option selected="selected" value="@item.Id">@Html.DisplayFor(modelItem => item.Name)</option>
                                                    }
                                                    else
                                                    {
                                                        <option value="@item.Id">@Html.DisplayFor(modelItem => item.Name)</option>
                                                    }
                                                }
                                            </select>
                                            @Html.ValidationMessageFor(model => model.generalConfig.ScreenProcessEvent, null, new { @class = "text-danger" })
                                        </div>
                                    </div>
                                    <div class="col-md-8" style="border: 1px solid black; padding: 5px 10px 5px 10px">
                                        <div class="row">
                                            <label class="control-label col-md-6">@Html.LabelFor(model => model.generalConfig.Email)</label>
                                            <div class="col-md-6">
                                                @Html.EditorFor(model => model.generalConfig.Email, Model.generalConfig.Email, new { @class = "form-control" })
                                                @Html.ValidationMessageFor(model => model.generalConfig.Email, null, new { @class = "text-danger" })
                                            </div>
                                        </div>
                                        <div class="row">
                                            <label class="control-label col-md-6">@Html.LabelFor(model => model.generalConfig.UserName)</label>
                                            <div class="col-md-6">
                                                @Html.EditorFor(model => model.generalConfig.UserName, Model.generalConfig.UserName, new { @class = "form-control" })
                                                @Html.ValidationMessageFor(model => model.generalConfig.UserName, null, new { @class = "text-danger" })
                                            </div>
                                        </div>
                                        <div class="row">
                                            <label class="control-label col-md-6">@Html.LabelFor(model => model.generalConfig.Password)</label>
                                            <div class="col-md-6">
                                                @Html.EditorFor(model => model.generalConfig.Password, Model.generalConfig.Password, new { @class = "form-control" })
                                                @Html.ValidationMessageFor(model => model.generalConfig.Email, null, new { @class = "text-danger" })
                                            </div>
                                        </div>
                                    </div>
                                    <br />
                                    <div class="row">
                                        <div class="col-md-8" style="text-align: center;">
                                            <input type="submit" value="@PSafe.AM.Resources.Resource.Save cấu hình" class="btn btn-primary" />
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                        <div id="menuAlertLevel" class="container tab-pane fade">
                            <br>
                            @using (Html.BeginForm("AlertLevelConfig", "AlarmConfig", FormMethod.Post))
                            {
                                @Html.AntiForgeryToken()
                        <div>
                            <div class="row">
                                <div class="col-md-8">
                                    <p style="text-align: center;"><strong>Cấu hình mức độ cảnh báo</strong></p>
                                </div>
                                <div class="col-md-1">
                                    <p style="text-align: center;"><strong>Cảnh báo Zalo</strong></p>
                                </div>
                                <div class="col-md-1">
                                    <p style="text-align: center;"><strong>Cảnh báo SMS</strong></p>
                                </div>
                                <div class="col-md-1">
                                    <p style="text-align: center;"><strong>Cảnh báo Email</strong></p>
                                </div>
                            </div>
                            <div class="row">
                                <label class="control-label col-md-4">@Html.LabelFor(model => model.generalConfig.ConnectionLossLevel)</label>
                                <div class="col-md-4">
                                    @Html.DropDownListFor(model => model.generalConfig.ConnectionLossLevel, Model.generalConfig.ListsSelect as SelectList, new { @class = "form-control" })
                                    @Html.ValidationMessageFor(model => model.generalConfig.ConnectionLossLevel, null, new { @class = "text-danger" })
                                </div>
                                <div class="col-md-1">
                                    <input type="checkbox" name="SentAlarm" id="ConnectionLossLevelZalo" value="ConnectionLossLevelZalo" checked="@(Model.generalConfig.ConnectionLossLevelZalo == 1)" />
                                </div>
                                <div class="col-md-1">
                                    <input type="checkbox" name="SentAlarm" id="ConnectionLossLevelSMS" value="ConnectionLossLevelSMS" checked="@(Model.generalConfig.ConnectionLossLevelSMS == 1)" />
                                </div>
                                <div class="col-md-1">
                                    <input type="checkbox" name="SentAlarm" id="ConnectionLossLevelEmail" value="ConnectionLossLevelEmail" checked="@(Model.generalConfig.ConnectionLossLevelEmail == 1)" />
                                </div>
                            </div>
                            <div class="row">
                                <label class="control-label col-md-4">@Html.LabelFor(model => model.generalConfig.FireAlarmLevel)</label>
                                <div class="col-md-4">
                                    @Html.DropDownListFor(model => model.generalConfig.FireAlarmLevel, Model.generalConfig.ListsSelect as SelectList, new { @class = "form-control" })
                                    @Html.ValidationMessageFor(model => model.generalConfig.FireAlarmLevel, null, new { @class = "text-danger" })
                                </div>
                                <div class="col-md-1">
                                    <input type="checkbox" name="SentAlarm" id="FireAlarmLevelZalo" value="FireAlarmLevelZalo" checked="@(Model.generalConfig.FireAlarmLevelZalo == 1)" />
                                </div>
                                <div class="col-md-1">
                                    <input type="checkbox" name="SentAlarm" id="FireAlarmLevelSMS" value="FireAlarmLevelSMS" checked="@(Model.generalConfig.FireAlarmLevelSMS == 1)" />
                                </div>
                                <div class="col-md-1">
                                    <input type="checkbox" name="SentAlarm" id="FireAlarmLevelEmail" value="FireAlarmLevelEmail" checked="@(Model.generalConfig.FireAlarmLevelEmail == 1)" />
                                </div>
                            </div>
                            <div class="row">
                                <label class="control-label col-md-4">@Html.LabelFor(model => model.generalConfig.CrossLineLevel)</label>
                                <div class="col-md-4">
                                    @Html.DropDownListFor(model => model.generalConfig.CrossLineLevel, Model.generalConfig.ListsSelect as SelectList, new { @class = "form-control" })
                                    @Html.ValidationMessageFor(model => model.generalConfig.CrossLineLevel, null, new { @class = "text-danger" })
                                </div>
                                <div class="col-md-1">
                                    <input type="checkbox" name="SentAlarm" id="CrossLineLevelZalo" value="CrossLineLevelZalo" checked="@(Model.generalConfig.CrossLineLevelZalo == 1)" />
                                </div>
                                <div class="col-md-1">
                                    <input type="checkbox" name="SentAlarm" id="CrossLineLevelSMS" value="CrossLineLevelSMS" checked="@(Model.generalConfig.CrossLineLevelSMS == 1)" />
                                </div>
                                <div class="col-md-1">
                                    <input type="checkbox" name="SentAlarm" id="CrossLineLevelEmail" value="CrossLineLevelEmail" checked="@(Model.generalConfig.CrossLineLevelEmail == 1)" />
                                </div>
                            </div>
                            <div class="row">
                                <label class="control-label col-md-4">@Html.LabelFor(model => model.generalConfig.AreaCrowdLevel)</label>
                                <div class="col-md-4">
                                    @Html.DropDownListFor(model => model.generalConfig.AreaCrowdLevel, Model.generalConfig.ListsSelect as SelectList, new { @class = "form-control" })
                                    @Html.ValidationMessageFor(model => model.generalConfig.AreaCrowdLevel, null, new { @class = "text-danger" })
                                </div>
                                <div class="col-md-1">
                                    <input type="checkbox" name="SentAlarm" id="AreaCrowdLevelZalo" value="AreaCrowdLevelZalo" checked="@(Model.generalConfig.AreaCrowdLevelZalo == 1)" />
                                </div>
                                <div class="col-md-1">
                                    <input type="checkbox" name="SentAlarm" id="AreaCrowdLevelSMS" value="AreaCrowdLevelSMS" checked="@(Model.generalConfig.AreaCrowdLevelSMS == 1)" />
                                </div>
                                <div class="col-md-1">
                                    <input type="checkbox" name="SentAlarm" id="AreaCrowdLevelEmail" value="AreaCrowdLevelEmail" checked="@(Model.generalConfig.AreaCrowdLevelEmail == 1)" />
                                </div>
                            </div>
                            <div class="row">
                                <label class="control-label col-md-4">@Html.LabelFor(model => model.generalConfig.EnterAreaLevel)</label>
                                <div class="col-md-4">
                                    @Html.DropDownListFor(model => model.generalConfig.EnterAreaLevel, Model.generalConfig.ListsSelect as SelectList, new { @class = "form-control" })
                                    @Html.ValidationMessageFor(model => model.generalConfig.EnterAreaLevel, null, new { @class = "text-danger" })
                                </div>
                                <div class="col-md-1">
                                    <input type="checkbox" name="SentAlarm" id="EnterAreaLevelZalo" value="EnterAreaLevelZalo" checked="@(Model.generalConfig.EnterAreaLevelZalo == 1)" />
                                </div>
                                <div class="col-md-1">
                                    <input type="checkbox" name="SentAlarm" id="EnterAreaLevelSMS" value="EnterAreaLevelSMS" checked="@(Model.generalConfig.EnterAreaLevelSMS == 1)" />
                                </div>
                                <div class="col-md-1">
                                    <input type="checkbox" name="SentAlarm" id="EnterAreaLevelEmail" value="EnterAreaLevelEmail" checked="@(Model.generalConfig.EnterAreaLevelEmail == 1)" />
                                </div>
                            </div>
                            <div class="row">
                                <label class="control-label col-md-4">@Html.LabelFor(model => model.generalConfig.BlackListLevel)</label>
                                <div class="col-md-4">
                                    @Html.DropDownListFor(model => model.generalConfig.BlackListLevel, Model.generalConfig.ListsSelect as SelectList, new { @class = "form-control" })
                                    @Html.ValidationMessageFor(model => model.generalConfig.BlackListLevel, null, new { @class = "text-danger" })
                                </div>
                                <div class="col-md-1">
                                    <input type="checkbox" name="SentAlarm" id="BlackListLevelZalo" value="BlackListLevelZalo" checked="@(Model.generalConfig.BlackListLevelZalo == 1)" />
                                </div>
                                <div class="col-md-1">
                                    <input type="checkbox" name="SentAlarm" id="BlackListLevelSMS" value="BlackListLevelSMS" checked="@(Model.generalConfig.BlackListLevelSMS == 1)" />
                                </div>
                                <div class="col-md-1">
                                    <input type="checkbox" name="SentAlarm" id="BlackListLevelEmail" value="BlackListLevelEmail" checked="@(Model.generalConfig.BlackListLevelEmail == 1)" />
                                </div>
                            </div>
                            <div class="row">
                                <label class="control-label col-md-4">@Html.LabelFor(model => model.generalConfig.BannedListLevel)</label>
                                <div class="col-md-4">
                                    @Html.DropDownListFor(model => model.generalConfig.BannedListLevel, Model.generalConfig.ListsSelect as SelectList, new { @class = "form-control" })
                                    @Html.ValidationMessageFor(model => model.generalConfig.BannedListLevel, null, new { @class = "text-danger" })
                                </div>
                                <div class="col-md-1">
                                    <input type="checkbox" name="SentAlarm" id="BannedListLevelZalo" value="BannedListLevelZalo" checked="@(Model.generalConfig.BannedListLevelZalo == 1)" />
                                </div>
                                <div class="col-md-1">
                                    <input type="checkbox" name="SentAlarm" id="BannedListLevelSMS" value="BannedListLevelSMS" checked="@(Model.generalConfig.BannedListLevelSMS == 1)" />
                                </div>
                                <div class="col-md-1">
                                    <input type="checkbox" name="SentAlarm" id="BannedListLevelEmail" value="BannedListLevelEmail" checked="@(Model.generalConfig.BannedListLevelEmail == 1)" />
                                </div>
                            </div>
                            <div class="row">
                                <label class="control-label col-md-4">@Html.LabelFor(model => model.generalConfig.BannedVehicleLevel)</label>
                                <div class="col-md-4">
                                    @Html.DropDownListFor(model => model.generalConfig.BannedVehicleLevel, Model.generalConfig.ListsSelect as SelectList, new { @class = "form-control" })
                                    @Html.ValidationMessageFor(model => model.generalConfig.BannedVehicleLevel, null, new { @class = "text-danger" })
                                </div>
                                <div class="col-md-1">
                                    <input type="checkbox" name="SentAlarm" id="BannedVehicleLevelZalo" value="BannedVehicleLevelZalo" checked="@(Model.generalConfig.BannedVehicleLevelZalo == 1)" />
                                </div>
                                <div class="col-md-1">
                                    <input type="checkbox" name="SentAlarm" id="BannedVehicleLevelSMS" value="BannedVehicleLevelSMS" checked="@(Model.generalConfig.BannedVehicleLevelSMS == 1)" />
                                </div>
                                <div class="col-md-1">
                                    <input type="checkbox" name="SentAlarm" id="BannedVehicleLevelEmail" value="BannedVehicleLevelEmail" checked="@(Model.generalConfig.BannedVehicleLevelEmail == 1)" />
                                </div>
                            </div>
                            <div class="row">
                                <label class="control-label col-md-4">@Html.LabelFor(model => model.generalConfig.BanVehicleLevel)</label>
                                <div class="col-md-4">
                                    @Html.DropDownListFor(model => model.generalConfig.BanVehicleLevel, Model.generalConfig.ListsSelect as SelectList, new { @class = "form-control" })
                                    @Html.ValidationMessageFor(model => model.generalConfig.BanVehicleLevel, null, new { @class = "text-danger" })
                                </div>
                                <div class="col-md-1">
                                    <input type="checkbox" name="SentAlarm" id="BanVehicleLevelZalo" value="BanVehicleLevelZalo" checked="@(Model.generalConfig.BanVehicleLevelZalo == 1)" />
                                </div>
                                <div class="col-md-1">
                                    <input type="checkbox" name="SentAlarm" id="BanVehicleLevelSMS" value="BanVehicleLevelSMS" checked="@(Model.generalConfig.BanVehicleLevelSMS == 1)" />
                                </div>
                                <div class="col-md-1">
                                    <input type="checkbox" name="SentAlarm" id="BanVehicleLevelEmail" value="BanVehicleLevelEmail" checked="@(Model.generalConfig.BanVehicleLevelEmail == 1)" />
                                </div>
                            </div>
                            <div class="row">
                                <label class="control-label col-md-4">@Html.LabelFor(model => model.generalConfig.CancelBanVehicleLevel)</label>
                                <div class="col-md-4">
                                    @Html.DropDownListFor(model => model.generalConfig.CancelBanVehicleLevel, Model.generalConfig.ListsSelect as SelectList, new { @class = "form-control" })
                                    @Html.ValidationMessageFor(model => model.generalConfig.CancelBanVehicleLevel, null, new { @class = "text-danger" })
                                </div>
                                <div class="col-md-1">
                                    <input type="checkbox" name="SentAlarm" id="CancelBanVehicleLevelZalo" value="CancelBanVehicleLevelZalo" checked="@(Model.generalConfig.CancelBanVehicleLevelZalo == 1)" />
                                </div>
                                <div class="col-md-1">
                                    <input type="checkbox" name="SentAlarm" id="CancelBanVehicleLevelSMS" value="CancelBanVehicleLevelSMS" checked="@(Model.generalConfig.CancelBanVehicleLevelSMS == 1)" />
                                </div>
                                <div class="col-md-1">
                                    <input type="checkbox" name="SentAlarm" id="CancelBanVehicleLevelEmail" value="CancelBanVehicleLevelEmail" checked="@(Model.generalConfig.CancelBanVehicleLevelEmail == 1)" />
                                </div>
                            </div>
                            <div class="row">
                                <label class="control-label col-md-4">@Html.LabelFor(model => model.generalConfig.TrafficViolationLevel)</label>
                                <div class="col-md-4">
                                    @Html.DropDownListFor(model => model.generalConfig.TrafficViolationLevel, Model.generalConfig.ListsSelect as SelectList, new { @class = "form-control" })
                                    @Html.ValidationMessageFor(model => model.generalConfig.TrafficViolationLevel, null, new { @class = "text-danger" })
                                </div>
                                <div class="col-md-1">
                                    <input type="checkbox" name="SentAlarm" id="TrafficViolationZalo" value="TrafficViolationZalo" checked="@(Model.generalConfig.TrafficViolationZalo == 1)" />
                                </div>
                                <div class="col-md-1">
                                    <input type="checkbox" name="SentAlarm" id="TrafficViolationSMS" value="TrafficViolationSMS" checked="@(Model.generalConfig.TrafficViolationSMS == 1)" />
                                </div>
                                <div class="col-md-1">
                                    <input type="checkbox" name="SentAlarm" id="TrafficViolationEmail" value="TrafficViolationEmail" checked="@(Model.generalConfig.TrafficViolationEmail == 1)" />
                                </div>
                            </div>
                        </div>

                                <div class="row" style="margin:0px auto; display:block;">
                                    <div style="text-align: center;">
                                        <input type="submit" style="margin:0px auto; display:block;" value="@PSafe.AM.Resources.Resource.Save cấu hình" class="btn btn-primary" />
                                    </div>
                                </div>
                            }
                        </div>

                        <div id="menuAlarmConfig" class="container tab-pane fade">
                            <br>
                            @using (Html.BeginForm("Index", "AlarmConfig", FormMethod.Post, new { id = "AlarmConfig" }))
                            {
                                @Html.AntiForgeryToken()

                                <div class="form-horizontal">
                                    @Html.ValidationSummary(true)

                                    <div class="row form-group">
                                        <div class="col-md-2">

                                        </div>
                                        <div class="col-md-10">
                                            <div class="row">
                                                <div class="col-md-2">
                                                    <strong>Cấp độ 1</strong>
                                                </div>

                                                <div class="col-md-2">
                                                    <strong>Cấp độ 2</strong>
                                                </div>

                                                <div class="col-md-2">
                                                    <strong>Cấp độ 3</strong>

                                                </div>

                                                <div class="col-md-2">
                                                    <strong>Cấp độ 4</strong>

                                                </div>

                                                <div class="col-md-2">
                                                    <strong>Cấp độ 5</strong>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row form-group">
                                        <div class="col-md-2">
                                            Cảnh báo an ninh
                                        </div>
                                        <div class="col-md-10">
                                            <div class="row">
                                                <div class="col-md-2">
                                                    <div class="tselect">
                                                        <input class="form-control" id="AlarmLevel1Security" />
                                                        <input type="hidden" name="AlarmLevel1Security" />
                                                        <div class="itemsselect">

                                                        </div>
                                                        <em>▼</em>
                                                    </div>
                                                </div>

                                                <div class="col-md-2">
                                                    <div class="tselect">
                                                        <input class="form-control" id="AlarmLevel2Security" />
                                                        <input type="hidden" name="AlarmLevel2Security" />
                                                        <div class="itemsselect">

                                                        </div>
                                                        <em>▼</em>
                                                    </div>
                                                </div>

                                                <div class="col-md-2">
                                                    <div class="tselect">
                                                        <input class="form-control" id="AlarmLevel3Security" />
                                                        <input type="hidden" name="AlarmLevel3Security" />
                                                        <div class="itemsselect">

                                                        </div>
                                                        <em>▼</em>
                                                    </div>

                                                </div>

                                                <div class="col-md-2">
                                                    <div class="tselect">
                                                        <input class="form-control" id="AlarmLevel4Security" />
                                                        <input type="hidden" name="AlarmLevel4Security" />
                                                        <div class="itemsselect">

                                                        </div>
                                                        <em>▼</em>
                                                    </div>

                                                </div>

                                                <div class="col-md-2">
                                                    <div class="tselect">
                                                        <input class="form-control" id="AlarmLevel5Security" />
                                                        <input type="hidden" name="AlarmLevel5Security" />
                                                        <div class="itemsselect">

                                                        </div>
                                                        <em>▼</em>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                    </div>

                                    <div class="row form-group">
                                        <div class="col-md-2">
                                            Cảnh báo cháy nổ
                                        </div>
                                        <div class="col-md-10">
                                            <div class="row">
                                                <div class="col-md-2">
                                                    <div class="tselect">
                                                        <input class="form-control" id="AlarmLevel1FireSafety" />
                                                        <input type="hidden" name="AlarmLevel1FireSafety" />
                                                        <div class="itemsselect">

                                                        </div>
                                                        <em>▼</em>
                                                    </div>

                                                </div>

                                                <div class="col-md-2">
                                                    <div class="tselect">
                                                        <input class="form-control" id="AlarmLevel2FireSafety" />
                                                        <input type="hidden" name="AlarmLevel2FireSafety" />
                                                        <div class="itemsselect">

                                                        </div>
                                                        <em>▼</em>
                                                    </div>
                                                </div>

                                                <div class="col-md-2">
                                                    <div class="tselect">
                                                        <input class="form-control" id="AlarmLevel3FireSafety" />
                                                        <input type="hidden" name="AlarmLevel3FireSafety" />
                                                        <div class="itemsselect">

                                                        </div>
                                                        <em>▼</em>
                                                    </div>
                                                </div>

                                                <div class="col-md-2">
                                                    <div class="tselect">
                                                        <input class="form-control" id="AlarmLevel4FireSafety" />
                                                        <input type="hidden" name="AlarmLevel4FireSafety" />
                                                        <div class="itemsselect">

                                                        </div>
                                                        <em>▼</em>
                                                    </div>
                                                </div>

                                                <div class="col-md-2">
                                                    <div class="tselect">
                                                        <input class="form-control" id="AlarmLevel5FireSafety" />
                                                        <input type="hidden" name="AlarmLevel5FireSafety" />
                                                        <div class="itemsselect">

                                                        </div>
                                                        <em>▼</em>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                    </div>

                                    <div class="row form-group">
                                        <div class="col-md-2">
                                            Cảnh báo từ thiết bị
                                        </div>
                                        <div class="col-md-10">
                                            <div class="row">
                                                <div class="col-md-2">
                                                    <div class="tselect">
                                                        <input class="form-control" id="AlarmLevel1Connection" />
                                                        <input type="hidden" name="AlarmLevel1Connection" />
                                                        <div class="itemsselect">

                                                        </div>
                                                        <em>▼</em>
                                                    </div>
                                                </div>

                                                <div class="col-md-2">
                                                    <div class="tselect">
                                                        <input class="form-control" id="AlarmLevel2Connection" />
                                                        <input type="hidden" name="AlarmLevel2Connection" />
                                                        <div class="itemsselect">

                                                        </div>
                                                        <em>▼</em>
                                                    </div>
                                                </div>

                                                <div class="col-md-2">
                                                    <div class="tselect">
                                                        <input class="form-control" id="AlarmLevel3Connection" />
                                                        <input type="hidden" name="AlarmLevel3Connection" />
                                                        <div class="itemsselect">

                                                        </div>
                                                        <em>▼</em>
                                                    </div>
                                                </div>

                                                <div class="col-md-2">
                                                    <div class="tselect">
                                                        <input class="form-control" id="AlarmLevel4Connection" />
                                                        <input type="hidden" name="AlarmLevel4Connection" />
                                                        <div class="itemsselect">

                                                        </div>
                                                        <em>▼</em>
                                                    </div>
                                                </div>

                                                <div class="col-md-2">
                                                    <div class="tselect">
                                                        <input class="form-control" id="AlarmLevel5Connection" />
                                                        <input type="hidden" name="AlarmLevel5Connection" />
                                                        <div class="itemsselect">

                                                        </div>
                                                        <em>▼</em>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                    </div>

                                    <div class="row form-group">
                                        <div class="col-md-2">
                                            Cảnh báo cấm xe
                                        </div>
                                        <div class="col-md-10">
                                            <div class="row">
                                                <div class="col-md-2">
                                                    <div class="tselect">
                                                        <input class="form-control" id="AlarmLevel1VehicleBanned" />
                                                        <input type="hidden" name="AlarmLevel1VehicleBanned" />
                                                        <div class="itemsselect">

                                                        </div>
                                                        <em>▼</em>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                    </div>

                                    <div class="row form-group">
                                        <div class="col-md-2">
                                            Cảnh báo cấm người
                                        </div>
                                        <div class="col-md-10">
                                            <div class="row">
                                                <div class="col-md-2">
                                                    <div class="tselect">
                                                        <input class="form-control" id="AlarmLevel1HumanBanned" />
                                                        <input type="hidden" name="AlarmLevel1HumanBanned" />
                                                        <div class="itemsselect">

                                                        </div>
                                                        <em>▼</em>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>

                                    </div>

                                    <div class="row form-group">
                                        <div class="col-md-2">
                                            Cảnh báo tuần tra bằng camera
                                        </div>
                                        <div class="col-md-10">
                                            <div class="row">
                                                <div class="col-md-2">
                                                    <div class="tselect">
                                                        <input class="form-control" id="AlarmLevel1PatrolCamera" />
                                                        <input type="hidden" name="AlarmLevel1PatrolCamera" />
                                                        <div class="itemsselect">

                                                        </div>
                                                        <em>▼</em>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>

                                    </div>

                                    <div class="row" style="margin:0px auto; display:block;">
                                        <div style="text-align: center;">
                                            <input type="submit" style="margin:0px auto; display:block;" value="@PSafe.AM.Resources.Resource.Save cấu hình" class="btn btn-primary" />
                                        </div>
                                    </div>

                                </div>
                            }
                        </div>

                        <div id="menuViolateConfig" class="container tab-pane fade">
                            <br>
                            @using (Html.BeginForm("ViolateConfig", "AlarmConfig", FormMethod.Post))
                            {
                                @Html.AntiForgeryToken()

                                <div class="row">
                                    @foreach (var item in Model.generalConfig.ListViolateSelect)
                                    {
                                        if (item.Checked)
                                        {
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <input type="checkbox" name="generalConfig.ViolateFlag" value="@item.Id" id="@item.Name" checked />
                                                    <label for="@item.Name"><strong>@item.Name</strong></label>
                                                </div>
                                            </div>
                                        }
                                        else
                                        {
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <input type="checkbox" name="generalConfig.ViolateFlag" value="@item.Id" id="@item.Name" unchecked />
                                                    <label for="@item.Name"><strong>@item.Name</strong></label>
                                                </div>
                                            </div>
                                        }
                                    }



                                </div>

                                <div class="row form-group">
                                    <input type="submit" class="btn btn-primary btn-sm" value="@PSafe.AM.Resources.Resource.Save" />
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@Html.AntiForgeryToken()

@section Styles {
    <environment names="Development,Staging,Production">
        <link rel="stylesheet" href="~/lib/toastr/toastr.min.css" />
    </environment>
    <style>
        .tselect {
            position: relative;
        }

            .tselect em {
                position: absolute;
                top: 9px;
                right: 7px;
                display: block;
                color: #777;
                font-size: 10px;
                background-color: white
            }

            .tselect .itemsselect {
                position: absolute;
                top: 30px;
                left: 0px;
                background-color: white;
                text-align: left;
                z-index: 1000000000;
                overflow-y: auto;
                border: 1px solid #ccc;
                display: none;
            }

                .tselect .itemsselect li {
                    display: block;
                    list-style: none;
                    padding: 5px 10px 5px 5px;
                    font-weight: normal;
                }

                    .tselect .itemsselect li:hover {
                        cursor: pointer;
                        background-color: #09678f;
                        color: white;
                    }

                    .tselect .itemsselect li span {
                        font-size: 15px;
                        padding-right: 5px;
                        visibility: hidden;
                    }

                        .tselect .itemsselect li span.selected {
                            visibility: visible;
                        }

            .tselect input {
                width: 100%;
            }

            .tselect .form-control[readonly] {
                background-color: white !important;
            }
    </style>
}

@section Scripts {
    <environment names="Development">
        <script src="~/lib/chosen/chosen.jquery.js"></script>
        <link rel="stylesheet" href="~/lib/chosen/bootstrap-chosen.css" />
    </environment>

    <environment names="Staging,Production">
        <link rel="stylesheet" href="~/lib/chosen/bootstrap-chosen.css" />
    </environment>

    <environment names="Development,Staging,Production">
        <script src="~/lib/validate/jquery.validate.min.js"></script>
        <script src="~/lib/toastr/toastr.js"></script>
        <script src="~/lib/toastr/toastr.min.js"></script>
        <script src="~/js/multiselect.js"></script>
        <script src="~/lib/chosen/chosen.jquery.js"></script>
    </environment>

    @if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "warning")
    {
        <script type="text/javascript">
            toastr.warning('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "error")
    {
        <script type="text/javascript">
            toastr.error('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "success")
    {
        <script type="text/javascript">
            toastr.success('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }

    <script type="text/javascript">
        $('.chosen-select').chosen({ width: "100%" });
    </script>

    <script>
    var roles = [];
    roles.push({
        Id: 0,
        Name: "Chọn tất cả"
    });
    @foreach(var role in roles)
    {
        @Html.Raw("roles.push({Id:"+ role.RoleId +",Name:'"+role.RoleName+"'});")
    }

    function onChange(data, obj) {
        $("input[name='" + obj.substr(1) + "']").val(data);
    }

    //alarm security - Begin
    var AlarmLevel1SecuritySelectedItems = '@(Model.alarmConfig.AlarmLevel1Security == null ? "" : Model.alarmConfig.AlarmLevel1Security)';

    new MultiSelect("#AlarmLevel1Security", {
        "return_all": true,
        "selectedItems": AlarmLevel1SecuritySelectedItems == '' ? null : AlarmLevel1SecuritySelectedItems.split(','),
        "all": "Tất cả",
        "selected": false,
        "return_type": "string",
        "items": roles,
        "selectChange": onChange
    });

    var AlarmLevel2SecuritySelectedItems = '@(Model.alarmConfig.AlarmLevel2Security == null ? "" : Model.alarmConfig.AlarmLevel2Security)';

    new MultiSelect("#AlarmLevel2Security", {
        "return_all": true,
        "selectedItems": AlarmLevel2SecuritySelectedItems == '' ? null : AlarmLevel2SecuritySelectedItems.split(','),
        "all": "Tất cả",
        "selected": false,
        "return_type": "string",
        "items": roles,
        "selectChange": onChange
    });

    var AlarmLevel3SecuritySelectedItems = '@(Model.alarmConfig.AlarmLevel3Security == null ? "" : Model.alarmConfig.AlarmLevel3Security)';

    new MultiSelect("#AlarmLevel3Security", {
        "return_all": true,
        "selectedItems": AlarmLevel3SecuritySelectedItems == '' ? null : AlarmLevel3SecuritySelectedItems.split(','),
        "all": "Tất cả",
        "selected": false,
        "return_type": "string",
        "items": roles,
        "selectChange": onChange
    });

    var AlarmLevel4SecuritySelectedItems = '@(Model.alarmConfig.AlarmLevel4Security == null ? "" : Model.alarmConfig.AlarmLevel4Security)';

    new MultiSelect("#AlarmLevel4Security", {
        "return_all": true,
        "selectedItems": AlarmLevel4SecuritySelectedItems == '' ? null : AlarmLevel4SecuritySelectedItems.split(','),
        "all": "Tất cả",
        "selected": false,
        "return_type": "string",
        "items": roles,
        "selectChange": onChange
    });

    var AlarmLevel5SecuritySelectedItems = '@(Model.alarmConfig.AlarmLevel5Security == null ? "" : Model.alarmConfig.AlarmLevel5Security)';

    new MultiSelect("#AlarmLevel5Security", {
        "return_all": true,
        "selectedItems": AlarmLevel5SecuritySelectedItems == '' ? null : AlarmLevel5SecuritySelectedItems.split(','),
        "all": "Tất cả",
        "selected": false,
        "return_type": "string",
        "items": roles,
        "selectChange": onChange
    });
    //alarm security - End

    //alarm fire safety - Begin
    var AlarmLevel1FireSafetySelectedItems = '@(Model.alarmConfig.AlarmLevel1FireSafety == null ? "" : Model.alarmConfig.AlarmLevel1FireSafety)';

    new MultiSelect("#AlarmLevel1FireSafety", {
        "return_all": true,
        "selectedItems": AlarmLevel1FireSafetySelectedItems == '' ? null : AlarmLevel1FireSafetySelectedItems.split(','),
        "all": "Tất cả",
        "selected": false,
        "return_type": "string",
        "items": roles,
        "selectChange": onChange
    });

    var AlarmLevel2FireSafetySelectedItems = '@(Model.alarmConfig.AlarmLevel2FireSafety == null ? "" : Model.alarmConfig.AlarmLevel2FireSafety)';

    new MultiSelect("#AlarmLevel2FireSafety", {
        "return_all": true,
        "selectedItems": AlarmLevel2FireSafetySelectedItems == '' ? null : AlarmLevel2FireSafetySelectedItems.split(','),
        "all": "Tất cả",
        "selected": false,
        "return_type": "string",
        "items": roles,
        "selectChange": onChange
    });

    var AlarmLevel3FireSafetySelectedItems = '@(Model.alarmConfig.AlarmLevel3FireSafety == null ? "" : Model.alarmConfig.AlarmLevel3FireSafety)';

    new MultiSelect("#AlarmLevel3FireSafety", {
        "return_all": true,
        "selectedItems": AlarmLevel3FireSafetySelectedItems == '' ? null : AlarmLevel3FireSafetySelectedItems.split(','),
        "all": "Tất cả",
        "selected": false,
        "return_type": "string",
        "items": roles,
        "selectChange": onChange
    });

    var AlarmLevel4FireSafetySelectedItems = '@(Model.alarmConfig.AlarmLevel4FireSafety == null ? "" : Model.alarmConfig.AlarmLevel4FireSafety)';

    new MultiSelect("#AlarmLevel4FireSafety", {
        "return_all": true,
        "selectedItems": AlarmLevel4FireSafetySelectedItems == '' ? null : AlarmLevel4FireSafetySelectedItems.split(','),
        "all": "Tất cả",
        "selected": false,
        "return_type": "string",
        "items": roles,
        "selectChange": onChange
    });

    var AlarmLevel5FireSafetySelectedItems = '@(Model.alarmConfig.AlarmLevel5FireSafety == null ? "" : Model.alarmConfig.AlarmLevel5FireSafety)';

    new MultiSelect("#AlarmLevel5FireSafety", {
        "return_all": true,
        "selectedItems": AlarmLevel5FireSafetySelectedItems == '' ? null : AlarmLevel5FireSafetySelectedItems.split(','),
        "all": "Tất cả",
        "selected": false,
        "return_type": "string",
        "items": roles,
        "selectChange": onChange
    });
    //alarm fire safety - End

    //alarm connection - Begin
    var AlarmLevel1ConnectionSelectedItems = '@(Model.alarmConfig.AlarmLevel1Connection == null ? "" : Model.alarmConfig.AlarmLevel1Connection)';

    new MultiSelect("#AlarmLevel1Connection", {
        "return_all": true,
        "selectedItems": AlarmLevel1ConnectionSelectedItems == '' ? null : AlarmLevel1ConnectionSelectedItems.split(','),
        "all": "Tất cả",
        "selected": false,
        "return_type": "string",
        "items": roles,
        "selectChange": onChange
    });

    var AlarmLevel2ConnectionSelectedItems = '@(Model.alarmConfig.AlarmLevel2Connection == null ? "" : Model.alarmConfig.AlarmLevel2Connection)';

    new MultiSelect("#AlarmLevel2Connection", {
        "return_all": true,
        "selectedItems": AlarmLevel2ConnectionSelectedItems == '' ? null : AlarmLevel2ConnectionSelectedItems.split(','),
        "all": "Tất cả",
        "selected": false,
        "return_type": "string",
        "items": roles,
        "selectChange": onChange
    });

    var AlarmLevel3ConnectionSelectedItems = '@(Model.alarmConfig.AlarmLevel3Connection == null ? "" : Model.alarmConfig.AlarmLevel3Connection)';

    new MultiSelect("#AlarmLevel3Connection", {
        "return_all": true,
        "selectedItems": AlarmLevel3ConnectionSelectedItems == '' ? null : AlarmLevel3ConnectionSelectedItems.split(','),
        "all": "Tất cả",
        "selected": false,
        "return_type": "string",
        "items": roles,
        "selectChange": onChange
    });

    var AlarmLevel4ConnectionSelectedItems = '@(Model.alarmConfig.AlarmLevel4Connection == null ? "" : Model.alarmConfig.AlarmLevel4Connection)';

    new MultiSelect("#AlarmLevel4Connection", {
        "return_all": true,
        "selectedItems": AlarmLevel4ConnectionSelectedItems == '' ? null : AlarmLevel4ConnectionSelectedItems.split(','),
        "all": "Tất cả",
        "selected": false,
        "return_type": "string",
        "items": roles,
        "selectChange": onChange
    });

    var AlarmLevel5ConnectionSelectedItems = '@(Model.alarmConfig.AlarmLevel5Connection == null ? "" : Model.alarmConfig.AlarmLevel5Connection)';

    new MultiSelect("#AlarmLevel5Connection", {
        "return_all": true,
        "selectedItems": AlarmLevel5ConnectionSelectedItems == '' ? null : AlarmLevel5ConnectionSelectedItems.split(','),
        "all": "Tất cả",
        "selected": false,
        "return_type": "string",
        "items": roles,
        "selectChange": onChange
    });
    //alarm connection - End


    //alarm vehicle banned - Begin
    var AlarmLevel1VehicleBannedSelectedItems = '@(Model.alarmConfig.AlarmLevel1VehicleBanned == null ? "" : Model.alarmConfig.AlarmLevel1VehicleBanned)';

    new MultiSelect("#AlarmLevel1VehicleBanned", {
        "return_all": true,
        "selectedItems": AlarmLevel1VehicleBannedSelectedItems == '' ? null : AlarmLevel1VehicleBannedSelectedItems.split(','),
        "all": "Tất cả",
        "selected": false,
        "return_type": "string",
        "items": roles,
        "selectChange": onChange
    });

    //alarm vehicle banned - End


    //alarm human banned - Begin
    var AlarmLevel1HumanBannedSelectedItems = '@(Model.alarmConfig.AlarmLevel1HumanBanned == null ? "" : Model.alarmConfig.AlarmLevel1HumanBanned)';

    new MultiSelect("#AlarmLevel1HumanBanned", {
        "return_all": true,
        "selectedItems": AlarmLevel1HumanBannedSelectedItems == '' ? null : AlarmLevel1HumanBannedSelectedItems.split(','),
        "all": "Tất cả",
        "selected": false,
        "return_type": "string",
        "items": roles,
        "selectChange": onChange
    });


    //alarm human banned - End


    //alarm patrol camera - Begin
    var AlarmLevel1PatrolCameraSelectedItems = '@(Model.alarmConfig.AlarmLevel1PatrolCamera == null ? "" : Model.alarmConfig.AlarmLevel1PatrolCamera)';

    new MultiSelect("#AlarmLevel1PatrolCamera", {
        "return_all": true,
        "selectedItems": AlarmLevel1PatrolCameraSelectedItems == '' ? null : AlarmLevel1PatrolCameraSelectedItems.split(','),
        "all": "Tất cả",
        "selected": false,
        "return_type": "string",
        "items": roles,
        "selectChange": onChange
    });


    //alarm patrol camera - End

    </script>
}