﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using ExternalServices.VCamService;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using PSafe.AM.Common;
using PSafe.AM.Models;
using PSafe.Common;
using PSafe.Common.UserEnums;
using PSafe.Core.Domains;
using PSafe.Core.Interface;

namespace PSafe.AM.Controllers
{
    [AuthorizeActionFilter(EPERMISSIONS_AM.ConfigurationGeneralManage)]
    public class AlarmConfigController : Controller
    {
        private readonly IAlarmConfigRepository _alarmConfigRepository;
        private readonly ISiteRepository _siteRepository;
        private readonly IRoleRepository _roleRepository;
        private readonly IGeneralConfigRepository _generalConfig;
        private readonly IConfiguration _configuration;
        private readonly ILogger<AlarmConfigController> _logger;

        public AlarmConfigController(IAlarmConfigRepository alarmConfigRepository, IRoleRepository roleRepository, IGeneralConfigRepository generalConfig,
            ILogger<AlarmConfigController> logger, ISiteRepository siteRepository, IConfiguration configuration)
        {
            _alarmConfigRepository = alarmConfigRepository;
            _roleRepository = roleRepository;
            _generalConfig = generalConfig;
            _logger = logger;
            _siteRepository = siteRepository;
            _configuration = configuration;
    }

        public ActionResult Index()
        {
            StatusQuery Notification;

            General general = new General();
            GeneralConfigModel generalConfigModel = new GeneralConfigModel();
            AlarmConfigModel alarmConfigModel = new AlarmConfigModel();
            List<DropDownList> ListMinSOSLevelSelect = new List<DropDownList>();
            List<DropDownList> ListMinSOSLevelNoSelect = new List<DropDownList>();
            List<CheckBok> ListViolate = new List<CheckBok>();
            List<DropDownList2> ListSiteSetViolationSelect = new List<DropDownList2>();
            List<DropDownList2> ListSiteSetViolationNoSelect = new List<DropDownList2>();
            generalConfigModel.ListScreen = GetListScreenVMS();

            try
            {
                var listgeneralConfig = _generalConfig.GetAll().ToList();

                //foreach(var item in listgeneralConfig)
                //{
                generalConfigModel.TimeSendSMS = int.Parse(listgeneralConfig.First(p => p.ConfigKey.Trim() == "TimeSendSMS").ConfigValue.Trim());
                generalConfigModel.TimeActionReplay = int.Parse(listgeneralConfig.First(p => p.ConfigKey.Trim() == "TimeActionReplay").ConfigValue.Trim());
                generalConfigModel.TimeCaptureVideo = int.Parse(listgeneralConfig.First(p => p.ConfigKey.Trim() == "TimeCaptureVideo").ConfigValue.Trim());
                generalConfigModel.MaxSearchDevice = int.Parse(listgeneralConfig.First(p => p.ConfigKey.Trim() == "MaxSearchDevice").ConfigValue.Trim());
                generalConfigModel.ConnectionLossLevel = int.Parse(listgeneralConfig.First(p => p.ConfigKey.Trim() == "ConnectionLossLevel").ConfigValue.Trim());
                generalConfigModel.FireAlarmLevel = int.Parse(listgeneralConfig.First(p => p.ConfigKey.Trim() == "FireAlarmLevel").ConfigValue.Trim());
                generalConfigModel.CrossLineLevel = int.Parse(listgeneralConfig.First(p => p.ConfigKey.Trim() == "CrossLineLevel").ConfigValue.Trim());
                generalConfigModel.AreaCrowdLevel = int.Parse(listgeneralConfig.First(p => p.ConfigKey.Trim() == "AreaCrowdLevel").ConfigValue.Trim());
                generalConfigModel.EnterAreaLevel = int.Parse(listgeneralConfig.First(p => p.ConfigKey.Trim() == "EnterAreaLevel").ConfigValue.Trim());
                generalConfigModel.BlackListLevel = int.Parse(listgeneralConfig.First(p => p.ConfigKey.Trim() == "BlackListLevel").ConfigValue.Trim());
                generalConfigModel.BannedListLevel = int.Parse(listgeneralConfig.First(p => p.ConfigKey.Trim() == "BannedListLevel").ConfigValue.Trim());
                generalConfigModel.BannedVehicleLevel = int.Parse(listgeneralConfig.First(p => p.ConfigKey.Trim() == "BannedVehicleLevel").ConfigValue.Trim());
                generalConfigModel.ScreenAlarm = listgeneralConfig.First(p => p.ConfigKey.Trim() == "ScreenAlarm").ConfigValue.Trim();
                generalConfigModel.ScreenProcessEvent = listgeneralConfig.First(p => p.ConfigKey.Trim() == "ScreenProcessEvent").ConfigValue.Trim();
                generalConfigModel.Email = listgeneralConfig.First(p => p.ConfigKey.Trim() == "Email").ConfigValue.Trim();
                generalConfigModel.UserName = listgeneralConfig.First(p => p.ConfigKey.Trim() == "UserName").ConfigValue.Trim();
                generalConfigModel.Password = listgeneralConfig.First(p => p.ConfigKey.Trim() == "Password").ConfigValue.Trim();
                generalConfigModel.BanVehicleLevel = int.Parse(listgeneralConfig.First(p => p.ConfigKey.Trim() == "BanVehicleLevel").ConfigValue.Trim());
                generalConfigModel.CancelBanVehicleLevel = int.Parse(listgeneralConfig.First(p => p.ConfigKey.Trim() == "CancelBanVehicleLevel").ConfigValue.Trim());
                generalConfigModel.TimeCustomerInPort = listgeneralConfig.First(p => p.ConfigKey.Trim() == "TimeCustomerInPort").ConfigValue.Trim();
                generalConfigModel.TimeCustomerExpriedInPort = listgeneralConfig.First(p => p.ConfigKey.Trim() == "TimeCustomerExpriedInPort").ConfigValue.Trim();
                generalConfigModel.TrafficViolationLevel = listgeneralConfig.First(p => p.ConfigKey.Trim() == "TrafficViolationLevel").ConfigValue.Trim();

                //Zalo
                generalConfigModel.ConnectionLossLevelZalo = int.Parse(listgeneralConfig.First(p => p.ConfigKey.Trim() == "ConnectionLossLevelZalo").ConfigValue.Trim());
                generalConfigModel.FireAlarmLevelZalo = int.Parse(listgeneralConfig.First(p => p.ConfigKey.Trim() == "FireAlarmLevelZalo").ConfigValue.Trim());
                generalConfigModel.CrossLineLevelZalo = int.Parse(listgeneralConfig.First(p => p.ConfigKey.Trim() == "CrossLineLevelZalo").ConfigValue.Trim());
                generalConfigModel.AreaCrowdLevelZalo = int.Parse(listgeneralConfig.First(p => p.ConfigKey.Trim() == "AreaCrowdLevelZalo").ConfigValue.Trim());
                generalConfigModel.EnterAreaLevelZalo = int.Parse(listgeneralConfig.First(p => p.ConfigKey.Trim() == "EnterAreaLevelZalo").ConfigValue.Trim());
                generalConfigModel.BlackListLevelZalo = int.Parse(listgeneralConfig.First(p => p.ConfigKey.Trim() == "BlackListLevelZalo").ConfigValue.Trim());
                generalConfigModel.BannedListLevelZalo = int.Parse(listgeneralConfig.First(p => p.ConfigKey.Trim() == "BannedListLevelZalo").ConfigValue.Trim());
                generalConfigModel.BannedVehicleLevelZalo = int.Parse(listgeneralConfig.First(p => p.ConfigKey.Trim() == "BannedVehicleLevelZalo").ConfigValue.Trim());
                generalConfigModel.BanVehicleLevelZalo = int.Parse(listgeneralConfig.First(p => p.ConfigKey.Trim() == "BanVehicleLevelZalo").ConfigValue.Trim());
                generalConfigModel.CancelBanVehicleLevelZalo = int.Parse(listgeneralConfig.First(p => p.ConfigKey.Trim() == "CancelBanVehicleLevelZalo").ConfigValue.Trim());
                generalConfigModel.TimeCustomerInPortZalo = int.Parse(listgeneralConfig.First(p => p.ConfigKey.Trim() == "TimeCustomerInPortZalo").ConfigValue.Trim());
                generalConfigModel.TimeCustomerExpriedInPortZalo = int.Parse(listgeneralConfig.First(p => p.ConfigKey.Trim() == "TimeCustomerExpriedInPortZalo").ConfigValue.Trim());
                generalConfigModel.TrafficViolationZalo = int.Parse(listgeneralConfig.First(p => p.ConfigKey.Trim() == "TrafficViolationZalo").ConfigValue.Trim());

                //SMS
                generalConfigModel.ConnectionLossLevelSMS = int.Parse(listgeneralConfig.First(p => p.ConfigKey.Trim() == "ConnectionLossLevelSMS").ConfigValue.Trim());
                generalConfigModel.FireAlarmLevelSMS = int.Parse(listgeneralConfig.First(p => p.ConfigKey.Trim() == "FireAlarmLevelSMS").ConfigValue.Trim());
                generalConfigModel.CrossLineLevelSMS = int.Parse(listgeneralConfig.First(p => p.ConfigKey.Trim() == "CrossLineLevelSMS").ConfigValue.Trim());
                generalConfigModel.AreaCrowdLevelSMS = int.Parse(listgeneralConfig.First(p => p.ConfigKey.Trim() == "AreaCrowdLevelSMS").ConfigValue.Trim());
                generalConfigModel.EnterAreaLevelSMS = int.Parse(listgeneralConfig.First(p => p.ConfigKey.Trim() == "EnterAreaLevelSMS").ConfigValue.Trim());
                generalConfigModel.BlackListLevelSMS = int.Parse(listgeneralConfig.First(p => p.ConfigKey.Trim() == "BlackListLevelSMS").ConfigValue.Trim());
                generalConfigModel.BannedListLevelSMS = int.Parse(listgeneralConfig.First(p => p.ConfigKey.Trim() == "BannedListLevelSMS").ConfigValue.Trim());
                generalConfigModel.BannedVehicleLevelSMS = int.Parse(listgeneralConfig.First(p => p.ConfigKey.Trim() == "BannedVehicleLevelSMS").ConfigValue.Trim());
                generalConfigModel.BanVehicleLevelSMS = int.Parse(listgeneralConfig.First(p => p.ConfigKey.Trim() == "BanVehicleLevelSMS").ConfigValue.Trim());
                generalConfigModel.CancelBanVehicleLevelSMS = int.Parse(listgeneralConfig.First(p => p.ConfigKey.Trim() == "CancelBanVehicleLevelSMS").ConfigValue.Trim());
                generalConfigModel.TimeCustomerInPortSMS = int.Parse(listgeneralConfig.First(p => p.ConfigKey.Trim() == "TimeCustomerInPortSMS").ConfigValue.Trim());
                generalConfigModel.TimeCustomerExpriedInPortSMS = int.Parse(listgeneralConfig.First(p => p.ConfigKey.Trim() == "TimeCustomerExpriedInPortSMS").ConfigValue.Trim());
                generalConfigModel.TrafficViolationSMS = int.Parse(listgeneralConfig.First(p => p.ConfigKey.Trim() == "TrafficViolationSMS").ConfigValue.Trim());

                //Email
                generalConfigModel.ConnectionLossLevelEmail = int.Parse(listgeneralConfig.First(p => p.ConfigKey.Trim() == "ConnectionLossLevelEmail").ConfigValue.Trim());
                generalConfigModel.FireAlarmLevelEmail = int.Parse(listgeneralConfig.First(p => p.ConfigKey.Trim() == "FireAlarmLevelEmail").ConfigValue.Trim());
                generalConfigModel.CrossLineLevelEmail = int.Parse(listgeneralConfig.First(p => p.ConfigKey.Trim() == "CrossLineLevelEmail").ConfigValue.Trim());
                generalConfigModel.AreaCrowdLevelEmail = int.Parse(listgeneralConfig.First(p => p.ConfigKey.Trim() == "AreaCrowdLevelEmail").ConfigValue.Trim());
                generalConfigModel.EnterAreaLevelEmail = int.Parse(listgeneralConfig.First(p => p.ConfigKey.Trim() == "EnterAreaLevelEmail").ConfigValue.Trim());
                generalConfigModel.BlackListLevelEmail = int.Parse(listgeneralConfig.First(p => p.ConfigKey.Trim() == "BlackListLevelEmail").ConfigValue.Trim());
                generalConfigModel.BannedListLevelEmail = int.Parse(listgeneralConfig.First(p => p.ConfigKey.Trim() == "BannedListLevelEmail").ConfigValue.Trim());
                generalConfigModel.BannedVehicleLevelEmail = int.Parse(listgeneralConfig.First(p => p.ConfigKey.Trim() == "BannedVehicleLevelEmail").ConfigValue.Trim());
                generalConfigModel.BanVehicleLevelEmail = int.Parse(listgeneralConfig.First(p => p.ConfigKey.Trim() == "BanVehicleLevelEmail").ConfigValue.Trim());
                generalConfigModel.CancelBanVehicleLevelEmail = int.Parse(listgeneralConfig.First(p => p.ConfigKey.Trim() == "CancelBanVehicleLevelEmail").ConfigValue.Trim());
                generalConfigModel.TimeCustomerInPortEmail = int.Parse(listgeneralConfig.First(p => p.ConfigKey.Trim() == "TimeCustomerInPortEmail").ConfigValue.Trim());
                generalConfigModel.TimeCustomerExpriedInPortEmail = int.Parse(listgeneralConfig.First(p => p.ConfigKey.Trim() == "TimeCustomerExpriedInPortEmail").ConfigValue.Trim());
                generalConfigModel.TrafficViolationEmail = int.Parse(listgeneralConfig.First(p => p.ConfigKey.Trim() == "TrafficViolationEmail").ConfigValue.Trim());

                ListEventLevel listEventLevel = new ListEventLevel();
                generalConfigModel.ListsSelect = ToSelectList(listEventLevel.List);

                var minSOSLevel = listgeneralConfig.Find(p => p.ConfigKey.Trim() == "MinSOSLevel");
                if (minSOSLevel != null)
                {
                    var listSelect = minSOSLevel.ConfigValue.Trim().Split(',');

                    foreach (var item in listEventLevel.List)
                    {
                        var CheckSelect = listSelect.Contains(item.Id.ToString());
                        if (CheckSelect)
                        {
                            ListMinSOSLevelSelect.Add(item);
                        }
                        else
                        {
                            ListMinSOSLevelNoSelect.Add(item);
                        }
                    }

                    generalConfigModel.ListMinSOSLevelSelect = ListMinSOSLevelSelect;
                    generalConfigModel.ListMinSOSLevelNoSelect = ListMinSOSLevelNoSelect;
                }
                ListViolate listViolate = new ListViolate();
                var violateConfig = listgeneralConfig.Find(p => p.ConfigKey.Trim() == "ViolateFlag");
                if (violateConfig != null)
                {
                    var listSelect = violateConfig.ConfigValue.Trim().Split(',');

                    foreach (var item in listViolate.List)
                    {
                        var CheckSelect = listSelect.Contains(item.Id.ToString());
                        if (CheckSelect)
                        {
                            item.Checked = true;
                            ListViolate.Add(item);
                        }
                        else
                        {
                            ListViolate.Add(item);
                        }
                    }
                    generalConfigModel.ListViolateSelect = ListViolate;
                }

                var siteSetViolation = listgeneralConfig.Find(p => p.ConfigKey.Trim() == "SiteSetViolation");
                var listSites = _siteRepository.GetAll().ToList();

                if (siteSetViolation != null)
                {
                    string userFunctions = string.Format(",{0},", siteSetViolation.ConfigValue.Trim());
                    foreach (var item in listSites)
                    {
                        var drop = new DropDownList2
                        {
                            Id = item.SiteId.Trim(),
                            Name = item.SiteName
                        };
                        string configValueStr = string.Format(",{0},", item.SiteId.ToString().Trim());
                        if (userFunctions.Trim().Contains(configValueStr))
                        {
                            ListSiteSetViolationSelect.Add(drop);
                        }else
                            ListSiteSetViolationNoSelect.Add(drop);
                    }
                }
                else
                {
                    foreach (var item in listSites)
                    {
                        var drop = new DropDownList2
                        {
                            Id = item.SiteId.Trim(),
                            Name = item.SiteName
                        };
                        ListSiteSetViolationNoSelect.Add(drop);
                    }
                }

                generalConfigModel.ListSiteSetViolationSelect = ListSiteSetViolationSelect;
                generalConfigModel.ListSiteSetViolationNoSelect = ListSiteSetViolationNoSelect;

                ViewBag.roles = _roleRepository.GetAll().ToList();
                var alarmConfigs = _alarmConfigRepository.GetAll().ToList();
                foreach (var alarmConfig in alarmConfigs)
                {
                    switch (alarmConfig.Type)
                    {
                        case (int)EALARM.SecurityAlarm:
                            alarmConfigModel.AlarmLevel1Security = alarmConfig.AlarmLevel1;
                            alarmConfigModel.AlarmLevel2Security = alarmConfig.AlarmLevel2;
                            alarmConfigModel.AlarmLevel3Security = alarmConfig.AlarmLevel3;
                            alarmConfigModel.AlarmLevel4Security = alarmConfig.AlarmLevel4;
                            alarmConfigModel.AlarmLevel5Security = alarmConfig.AlarmLevel5;

                            break;
                        case (int)EALARM.FireSafetyAlarm:
                            alarmConfigModel.AlarmLevel1FireSafety = alarmConfig.AlarmLevel1;
                            alarmConfigModel.AlarmLevel2FireSafety = alarmConfig.AlarmLevel2;
                            alarmConfigModel.AlarmLevel3FireSafety = alarmConfig.AlarmLevel3;
                            alarmConfigModel.AlarmLevel4FireSafety = alarmConfig.AlarmLevel4;
                            alarmConfigModel.AlarmLevel5FireSafety = alarmConfig.AlarmLevel5;
                            break;
                        case (int)EALARM.ConnectionAlarm:
                            alarmConfigModel.AlarmLevel1Connection = alarmConfig.AlarmLevel1;
                            alarmConfigModel.AlarmLevel2Connection = alarmConfig.AlarmLevel2;
                            alarmConfigModel.AlarmLevel3Connection = alarmConfig.AlarmLevel3;
                            alarmConfigModel.AlarmLevel4Connection = alarmConfig.AlarmLevel4;
                            alarmConfigModel.AlarmLevel5Connection = alarmConfig.AlarmLevel5;
                            break;
                        case (int)EALARM.VehicleBannedAlarm:
                            alarmConfigModel.AlarmLevel1VehicleBanned = alarmConfig.AlarmLevel1;
                            break;
                        case (int)EALARM.HumanBannedAlarm:
                            alarmConfigModel.AlarmLevel1HumanBanned = alarmConfig.AlarmLevel1;
                            break;
                        case (int)EALARM.PatrolCameraAlarm:
                            alarmConfigModel.AlarmLevel1PatrolCamera = alarmConfig.AlarmLevel1;
                            break;
                    }
                }

                general.generalConfig = generalConfigModel;
                general.alarmConfig = alarmConfigModel;

                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }

            }
            catch(Exception ex)
            {
                _logger.LogError("/AlarmConfig/Index: " + ex.Message);
                ViewBag.roles = _roleRepository.GetAll().ToList();
                ListEventLevel listEventLevel = new ListEventLevel();
                generalConfigModel.ListsSelect = ToSelectList(listEventLevel.List);
                generalConfigModel.ListMinSOSLevelSelect = ListMinSOSLevelSelect;
                generalConfigModel.ListMinSOSLevelNoSelect = ListMinSOSLevelNoSelect;
                general.generalConfig = generalConfigModel;
                general.alarmConfig = alarmConfigModel;

                Notification = new StatusQuery("error", "", "Tải dữ liệu thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;
            }

            return View(general);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Index(AlarmConfigModel alarmConfigModel)
        {
            StatusQuery Notification;

            AlarmConfig alarmConfig = new AlarmConfig();
            alarmConfig.Type = (int)EALARM.SecurityAlarm;
            alarmConfig.AlarmLevel1 = alarmConfigModel.AlarmLevel1Security;
            alarmConfig.AlarmLevel2 = alarmConfigModel.AlarmLevel2Security;
            alarmConfig.AlarmLevel3 = alarmConfigModel.AlarmLevel3Security;
            alarmConfig.AlarmLevel4 = alarmConfigModel.AlarmLevel4Security;
            alarmConfig.AlarmLevel5 = alarmConfigModel.AlarmLevel5Security;
            if(!SaveAlarmConfig(alarmConfig))
            {
                Notification = new StatusQuery("error", "", "Lưu cấu hình cảnh báo an ninh thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;
                ViewBag.roles = _roleRepository.GetAll().ToList();
                return View(alarmConfigModel);
            }

            alarmConfig = new AlarmConfig();
            alarmConfig.Type = (int)EALARM.FireSafetyAlarm;
            alarmConfig.AlarmLevel1 = alarmConfigModel.AlarmLevel1FireSafety;
            alarmConfig.AlarmLevel2 = alarmConfigModel.AlarmLevel2FireSafety;
            alarmConfig.AlarmLevel3 = alarmConfigModel.AlarmLevel3FireSafety;
            alarmConfig.AlarmLevel4 = alarmConfigModel.AlarmLevel4FireSafety;
            alarmConfig.AlarmLevel5 = alarmConfigModel.AlarmLevel5FireSafety;
            if (!SaveAlarmConfig(alarmConfig))
            {
                Notification = new StatusQuery("error", "", "Lưu cấu hình cảnh báo cháy nổ thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;
                ViewBag.roles = _roleRepository.GetAll().ToList();
                return View(alarmConfigModel);
            }

            alarmConfig = new AlarmConfig();
            alarmConfig.Type = (int)EALARM.ConnectionAlarm;
            alarmConfig.AlarmLevel1 = alarmConfigModel.AlarmLevel1Connection;
            alarmConfig.AlarmLevel2 = alarmConfigModel.AlarmLevel2Connection;
            alarmConfig.AlarmLevel3 = alarmConfigModel.AlarmLevel3Connection;
            alarmConfig.AlarmLevel4 = alarmConfigModel.AlarmLevel4Connection;
            alarmConfig.AlarmLevel5 = alarmConfigModel.AlarmLevel5Connection;
            if (!SaveAlarmConfig(alarmConfig))
            {
                Notification = new StatusQuery("error", "", "Lưu cấu hình cảnh báo kết nối thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;
                ViewBag.roles = _roleRepository.GetAll().ToList();
                return View(alarmConfigModel);
            }

            alarmConfig = new AlarmConfig();
            alarmConfig.Type = (int)EALARM.VehicleBannedAlarm;
            alarmConfig.AlarmLevel1 = alarmConfigModel.AlarmLevel1VehicleBanned;
            if (!SaveAlarmConfig(alarmConfig))
            {
                Notification = new StatusQuery("error", "", "Lưu cấu hình cấm xe vào ra thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;
                ViewBag.roles = _roleRepository.GetAll().ToList();
                return View(alarmConfigModel);
            }

            alarmConfig = new AlarmConfig();
            alarmConfig.Type = (int)EALARM.HumanBannedAlarm;
            alarmConfig.AlarmLevel1 = alarmConfigModel.AlarmLevel1HumanBanned;
            if (!SaveAlarmConfig(alarmConfig))
            {
                Notification = new StatusQuery("error", "", "Lưu cấu hình cấm người vào ra thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;
                ViewBag.roles = _roleRepository.GetAll().ToList();
                return View(alarmConfigModel);
            }

            alarmConfig = new AlarmConfig();
            alarmConfig.Type = (int)EALARM.PatrolCameraAlarm;
            alarmConfig.AlarmLevel1 = alarmConfigModel.AlarmLevel1PatrolCamera;
            if (!SaveAlarmConfig(alarmConfig))
            {
                Notification = new StatusQuery("error", "", "Lưu cấu hình cảnh báo tuần tra thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;
                ViewBag.roles = _roleRepository.GetAll().ToList();
                return View(alarmConfigModel);
            }

            TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Lưu cấu hình thành công"));
            return RedirectToAction("Index");
        }

        private bool SaveAlarmConfig(AlarmConfig alarmConfig)
        {
            try
            {
                var alarmConfigDb = _alarmConfigRepository.GetBy(x => x.Type == alarmConfig.Type).FirstOrDefault();
                if (alarmConfigDb != null)
                {
                    alarmConfigDb.Name = string.Empty;
                    alarmConfigDb.AlarmLevel1 = alarmConfig.AlarmLevel1;
                    alarmConfigDb.AlarmLevel2 = alarmConfig.AlarmLevel2;
                    alarmConfigDb.AlarmLevel3 = alarmConfig.AlarmLevel3;
                    alarmConfigDb.AlarmLevel4 = alarmConfig.AlarmLevel4;
                    alarmConfigDb.AlarmLevel5 = alarmConfig.AlarmLevel5;

                    _alarmConfigRepository.Update(alarmConfigDb);
                    _alarmConfigRepository.SaveChanges();
                    return true;
                }
                else
                {
                    _alarmConfigRepository.Insert(alarmConfig);
                    _alarmConfigRepository.SaveChanges();
                    return true;
                }
            }
            catch(Exception ex)
            {
                _logger.LogError("AlarmConfig/SaveAlarmConfig: " + ex.Message);
                return false;
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult GeneralConfig()
        {
            try
            {
                var generalConfigs = Request.Form.ToList();

                bool validateSiteSetViolation = generalConfigs.Any(p => p.Key == "generalConfig.SiteSetViolation");
                if (!validateSiteSetViolation)
                {
                    //TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("warning", "", "Vui lòng chọn \" " + Resources.Resource_GeneralConfig.SiteSetViolation.ToLower() + " \""));
                    //return RedirectToAction("Index", "AlarmConfig");
                    var site = new GeneralConfig() {
                        ConfigKey = "SiteSetViolation",
                        ConfigValue = string.Empty
                    };
                    _generalConfig.Update(site);
                }

                bool validateMinSOSLevel = generalConfigs.Any(p => p.Key == "generalConfig.MinSOSLevel");
                if (!validateMinSOSLevel)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("warning", "", "Vui lòng chọn \" " + Resources.Resource_GeneralConfig.MinSOSLevel.ToLower() + " \""));
                    return RedirectToAction("Index", "AlarmConfig");
                }
                
                var listGeneralConfig = _generalConfig.GetAll().ToList();

                generalConfigs.Remove(generalConfigs.Where(p => p.Key.Trim() == "__RequestVerificationToken").FirstOrDefault());

                string valueMinSOSLevel = string.Empty;

                foreach (var item in generalConfigs)
                {
                    if (item.Key != "generalConfig.Email" && item.Key != "generalConfig.UserName" && item.Key != "generalConfig.Password" &&
                        item.Key != "generalConfig.MinSOSLevel" && item.Key != "generalConfig.SiteSetViolation" && item.Key != "generalConfig.ScreenAlarm" && item.Key != "generalConfig.ScreenProcessEvent")
                    {
                        if (item.Value.FirstOrDefault() == string.Empty || int.Parse(item.Value.FirstOrDefault()) < 0)
                        {
                            _logger.LogWarning("Nội dung \"" + item.Key.ToString() + "\" không hợp lệ, vui lòng kiểm tra lại");
                            TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Nội dung \"" + item.Key.ToString() + "\" không hợp lệ, vui lòng kiểm tra lại"));
                            return RedirectToAction("Index", "AlarmConfig");
                        }
                    }

                    var generalConfig = listGeneralConfig.Where(p => p.ConfigKey.Trim() == item.Key.Split('.')[1]).SingleOrDefault();

                    if (generalConfig.ConfigKey.Trim() == "MinSOSLevel")
                    {
                        if (generalConfig.ConfigValue.Trim() != item.Value.ToString())
                        {
                            generalConfig.ConfigValue = item.Value;
                            _generalConfig.Update(generalConfig);
                        }
                    }
                    else if (generalConfig.ConfigKey.Trim() == "SiteSetViolation")
                    {
                        if (generalConfig.ConfigValue.Trim() != item.Value.ToString())
                        {
                            generalConfig.ConfigValue = item.Value;
                            _generalConfig.Update(generalConfig);
                        }
                    }
                    else if (generalConfig.ConfigKey.Trim() == "Password")
                    {
                        if (item.Value.FirstOrDefault().ToString().Trim() != "" && item.Value.FirstOrDefault().ToString().Trim() != string.Empty)
                        {
                            var password = Utils.EncodePassword(item.Value.FirstOrDefault().ToString().Trim(), Utils.EncodeType.SHA_256);

                            generalConfig.ConfigValue = password;
                            _generalConfig.Update(generalConfig);
                        }
                    }
                    else
                    {
                        if (generalConfig.ConfigValue.Trim() != item.Value.ToString().Trim())
                        {
                            generalConfig.ConfigValue = item.Value.FirstOrDefault().ToString().Trim();
                            _generalConfig.Update(generalConfig);
                        }
                    }
                }

                var statusGeneralConfig = _generalConfig.SaveChanges();

                if (statusGeneralConfig > 0)
                {
                    var _client = new VCamServiceSoapClient(VCamServiceSoapClient.EndpointConfiguration.VCamServiceSoap);
                    _client.LoadSystemConfigAsync();
                }

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Lưu cấu hình thành công"));
                return RedirectToAction("Index", "AlarmConfig");
            }
            catch (Exception ex)
            {
                _logger.LogError("AlarmConfig/GeneralConfig: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Lưu cấu hình thất bại"));

                return RedirectToAction("Index", "AlarmConfig");
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult AlertLevelConfig(List<string> SentAlarm)
        {
            try
            {
                string[] listCheckBok = {
                    "ConnectionLossLevelZalo", "ConnectionLossLevelSMS", "ConnectionLossLevelEmail",
                    "FireAlarmLevelZalo", "FireAlarmLevelSMS", "FireAlarmLevelEmail",
                    "CrossLineLevelZalo", "CrossLineLevelSMS", "CrossLineLevelEmail",
                    "AreaCrowdLevelZalo", "AreaCrowdLevelSMS", "AreaCrowdLevelEmail",
                    "EnterAreaLevelZalo","EnterAreaLevelSMS", "EnterAreaLevelEmail",
                    "BlackListLevelZalo", "BlackListLevelSMS","BlackListLevelEmail",
                    "BannedListLevelZalo","BannedListLevelSMS","BannedListLevelEmail",
                    "BannedVehicleLevelZalo","BannedVehicleLevelSMS","BannedVehicleLevelEmail",
                    "BanVehicleLevelZalo", "BanVehicleLevelSMS", "BanVehicleLevelEmail",
                    "CancelBanVehicleLevelZalo", "CancelBanVehicleLevelSMS","CancelBanVehicleLevelEmail",
                    "TimeCustomerInPortZalo", "TimeCustomerInPortSMS", "TimeCustomerInPortEmail",
                    "TimeCustomerExpriedInPortZalo", "TimeCustomerExpriedInPortSMS", "TimeCustomerExpriedInPortEmail",
                     "TrafficViolationZalo","TrafficViolationSMS","TrafficViolationEmail",
                };

                var generalConfigs = Request.Form.ToList();

                var listGeneralConfig = _generalConfig.GetAll().ToList();

                generalConfigs.Remove(generalConfigs.Where(p => p.Key.Trim() == "__RequestVerificationToken").FirstOrDefault());
                generalConfigs.Remove(generalConfigs.Where(p => p.Key.Trim() == "SentAlarm").FirstOrDefault());

                string valueMinSOSLevel = string.Empty;

                foreach (var item in generalConfigs)
                {
                    var generalConfig = listGeneralConfig.Where(p => p.ConfigKey.Trim() == item.Key.Split('.')[1]).SingleOrDefault();

                    if (generalConfig.ConfigValue.Trim() != item.Value.ToString().Trim())
                    {
                        generalConfig.ConfigValue = item.Value.FirstOrDefault().ToString().Trim();
                        _generalConfig.Update(generalConfig);
                    }
                }

                foreach (var item in listCheckBok)
                {
                    // Lấy danh sách trong database by item
                    var getItemOnData = listGeneralConfig.FirstOrDefault(p => p.ConfigKey.Trim() == item);
                    // Lấy item từ UI
                    var getItemOnUi = SentAlarm.IndexOf(item);

                    if (getItemOnData != null)
                    {
                        if (getItemOnUi > -1)
                        {
                            if (getItemOnData.ConfigValue.Trim() != "1")
                            {
                                _generalConfig.Update(getItemOnData);
                                getItemOnData.ConfigValue = "1";
                            }
                        }
                        else
                        {
                            if (getItemOnData.ConfigValue.Trim() != "0")
                            {
                                getItemOnData.ConfigValue = "0";
                                _generalConfig.Update(getItemOnData);
                            }
                        }
                    }
                }

                var statusGeneralConfig = _generalConfig.SaveChanges();

                if (statusGeneralConfig > 0)
                {
                    var _client = new VCamServiceSoapClient(VCamServiceSoapClient.EndpointConfiguration.VCamServiceSoap);
                    _client.LoadSystemConfigAsync();
                }

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Lưu cấu hình thành công"));
                return RedirectToAction("Index", "AlarmConfig");
            }
            catch (Exception ex)
            {
                _logger.LogError("AlarmConfig/GeneralConfig: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Lưu cấu hình thất bại"));

                return RedirectToAction("Index", "AlarmConfig");
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult ViolateConfig()
        {
            try
            {
                var generalConfigs = Request.Form.ToList();
                generalConfigs.Remove(generalConfigs.Where(p => p.Key.Trim() == "__RequestVerificationToken").FirstOrDefault());
                var listGeneralConfig = _generalConfig.GetAll().ToList();
                var generalConfig = listGeneralConfig.Where(p => p.ConfigKey.Trim() == generalConfigs[0].Key.Split('.')[1]).SingleOrDefault();
                generalConfig.ConfigValue = generalConfigs[0].Value;
                _generalConfig.Update(generalConfig);
                var statusGeneralConfig = _generalConfig.SaveChanges();

                if (statusGeneralConfig > 0)
                {
                    var _client = new VCamServiceSoapClient(VCamServiceSoapClient.EndpointConfiguration.VCamServiceSoap);
                    _client.LoadSystemConfigAsync();
                }

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Lưu cấu hình thành công"));
                return RedirectToAction("Index", "AlarmConfig");
            }
            catch (Exception ex)
            {
                _logger.LogError("AlarmConfig/GeneralConfig: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Lưu cấu hình thất bại"));

                return RedirectToAction("Index", "AlarmConfig");
            }
        }

        [HttpGet]
        public List<DropDownList2> GetListScreenVMS()
        {
            List<DropDownList2> ListScreen = new List<DropDownList2>();
            try
            {
                string baseURL = _configuration.GetSection("linkGetAllCameraMilestone:BaseUrl").Value + "api/ClientNames";

                try
                {
                    string token = GetToken();

                    using (HttpClient client = new HttpClient())
                    {
                        client.DefaultRequestHeaders.Authorization
                        = new AuthenticationHeaderValue("Bearer", token);

                        using (HttpResponseMessage res = client.GetAsync(baseURL).Result)
                        {
                            using (HttpContent content = res.Content)
                            {
                                string data = content.ReadAsStringAsync().Result;
                                var dataObj = JObject.Parse(data);

                                var listScreen = new List<string>();

                                foreach (var item in dataObj["message"])
                                {
                                    var screen = item.ToString().Split(",");
                                    foreach(var scr in screen)
                                    {
                                        listScreen.Add(scr);
                                    }
                                }

                                listScreen.Distinct();

                                foreach (var item in listScreen)
                                {
                                    ListScreen.Add(new DropDownList2()
                                    {
                                        Id = item,
                                        Name = item
                                    });
                                }

                                return ListScreen;
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("Devices/Sync: " + ex.Message);

                    return ListScreen;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Devices/Sync: " + ex.Message);

                return ListScreen;
            }
        }

        private string GetToken()
        {
            var handler = new HttpClientHandler
            {
                //ClientCertificateOptions = ClientCertificateOption.Automatic,  thêm dòng này để tắt SSL
                //ServerCertificateCustomValidationCallback = (httpRequestMessage, cert, cetChain, policyErrors) =>  thêm dòng này để tắt SSL
                //{  thêm dòng này để tắt SSL
                //    return true;  thêm dòng này để tắt SSL
                //},  thêm dòng này để tắt SSL
                UseProxy = false
            };

            string data = string.Empty;

            try
            {
                string baseGetTokenURL = _configuration.GetSection("linkGetAllCameraMilestone:BaseUrl").Value + "auth/getToken";

                try
                {
                    using (HttpClient client = new HttpClient(handler))
                    {
                        var UsernameEncode = Utils.DecodePassword(_configuration.GetSection("linkGetAllCameraMilestone:Username").Value, Utils.EncodeType.SHA_256);
                        var PasswordEncode = Utils.DecodePassword(_configuration.GetSection("linkGetAllCameraMilestone:Password").Value, Utils.EncodeType.SHA_256);

                        var requestData = new Dictionary<string, string>();
                        requestData["Username"] = UsernameEncode;
                        requestData["Password"] = PasswordEncode;

                        using (HttpRequestMessage res = new HttpRequestMessage(HttpMethod.Post, baseGetTokenURL))
                        {
                            var json = JsonConvert.SerializeObject(requestData);
                            using (var stringContent = new StringContent(json, Encoding.UTF8, "application/json"))
                            {
                                res.Content = stringContent;

                                using (var response = client.SendAsync(res, CancellationToken.None))
                                {
                                    response.Result.EnsureSuccessStatusCode();
                                    var responseBody = response.Result.Content.ReadAsStringAsync();

                                    var dataObj = JObject.Parse(responseBody.Result);

                                    data = dataObj["message"].ToString();

                                    _logger.LogInformation("Devices/GetToken: ok");

                                    client.Dispose();
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("Devices/GetToken: " + ex.Message);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Devices/GetToken: " + ex.Message);
            }

            return data;
        }

        [NonAction]
        private SelectList ToSelectList(List<DropDownList> DropDownList)
        {
            List<SelectListItem> list = new List<SelectListItem>();

            foreach (var item in DropDownList)
            {
                list.Add(new SelectListItem()
                {
                    Text = item.Name,
                    Value = item.Id.ToString()
                });
            }

            return new SelectList(list, "Value", "Text");
        }
    }
}