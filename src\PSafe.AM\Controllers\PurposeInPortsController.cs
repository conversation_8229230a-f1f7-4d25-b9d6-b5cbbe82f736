﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using PSafe.AM.Common;
using PSafe.AM.Models;
using PSafe.Common.UserEnums;
using PSafe.Core.Domains;
using PSafe.Core.Interface;
using static PSafe.Common.CommonEnums;

namespace PSafe.AM.Controllers
{
    [AuthorizeActionFilter(EPERMISSIONS_AM.categoryManage)]
    public class PurposeInPortsController : Controller
    {
        private readonly IPurposeInPortRepository _PurposeInPortRepository;
        private readonly IUserRepository _userRepository;
        private readonly IMapper _mapper;
        private readonly IHistorySystemRepository _historySystemRepository;
        private readonly IHttpContextAccessor _accessor;
        private readonly ILogger<PurposeInPortsController> _logger;

        public PurposeInPortsController(IPurposeInPortRepository PurposeInPortRepository, IMapper mapper, IUserRepository userRepository, IHistorySystemRepository historySystemRepository,
            IHttpContextAccessor accessor, ILogger<PurposeInPortsController> logger)
        {
            _PurposeInPortRepository = PurposeInPortRepository;
            _userRepository = userRepository;
            _mapper = mapper;
            _historySystemRepository = historySystemRepository;
            _accessor = accessor;
            _logger = logger;
        }

        public ActionResult Index()
        {
            StatusQuery Notification;
            List<PurposeInPortModel> _listPurposeInPortModel = new List<PurposeInPortModel>();
            try
            {
                var _listPurposeInPort = _PurposeInPortRepository.GetAll().ToList();

                _listPurposeInPortModel = _mapper.Map<List<PurposeInPort>, List<PurposeInPortModel>>(_listPurposeInPort);

                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }
            }
            catch(Exception ex)
            {
                _logger.LogError("PurposeInPort/Index: " + ex.Message);

                Notification = new StatusQuery("error", "", "Xem danh sách thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;
            }

            return View(_listPurposeInPortModel);
        }

        // GET: /supplier/Details/
        public ActionResult Details(int id)
        {
            try
            {
                var _PurposeInPort = _PurposeInPortRepository.GetById(id);

                var _PurposeInPortModel = _mapper.Map<PurposeInPort, PurposeInPortModel>(_PurposeInPort);

                return View(_PurposeInPortModel);
            }
            catch(Exception ex)
            {
                _logger.LogError("PurposeInPort/Details: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                return RedirectToAction("Index");
            }
        }

        // GET: /supplier/Create
        public ActionResult Create()
        {
            return View();
        }

        // POST: /supplier/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create([Bind("Name, Description")] PurposeInPortModel PurposeInPortModel)
        {
            StatusQuery Notification;
            if (ModelState.IsValid)
            {
                try
                {
                    var _systemUser = GetSesson();

                    if (_systemUser == null)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                        return RedirectToAction("Logout", "Security");
                    }

                    var _PurposeInPort = _mapper.Map<PurposeInPortModel, PurposeInPort>(PurposeInPortModel);

                    _PurposeInPortRepository.Insert(_PurposeInPort);

                    var statusInsert = _PurposeInPortRepository.SaveChanges();

                    if (statusInsert > 0)
                    {
                        string StringDescription = new GetStringHistorySystem().Get(_systemUser.UserName, Resources.Resource.Create, _PurposeInPort.Name.ToString(), Resources.Resource.PurposeInPort);

                        InsertHistorySystem((int)EACTION_TYPE.CREATE, (int)EnumControllerName.SUPPLIERS, StringDescription, null, _PurposeInPort);

                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Thêm mới thành công"));
                        return RedirectToAction("Index");
                    }
                    else
                    {
                        Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        return View(PurposeInPortModel);
                    }
                }
                catch(Exception ex)
                {
                    _logger.LogError("PurposeInPort/Create: " + ex.Message);

                    Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return View(PurposeInPortModel);
                }
            }
            Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;

            return View(PurposeInPortModel);
        }

        // GET: /supplier/Edit/5
        public ActionResult Edit(int id)
        {
            try
            {
                var _supplier = _PurposeInPortRepository.GetById(id);

                var _supplierModel = _mapper.Map<PurposeInPort, PurposeInPortModel>(_supplier);

                if (_supplierModel != null)
                {
                    return View(_supplierModel);
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                    return RedirectToAction("Index");
                }
            }
            catch(Exception ex)
            {
                _logger.LogError("PurposeInPort/Edit: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                return RedirectToAction("Index");
            }
        }

        // POST: /supplier/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit([Bind("Id, Name, Description")] PurposeInPortModel PurposeInPortModel)
        {
            StatusQuery Notification;
            if (ModelState.IsValid)
            {
                try
                {
                    var _systemUser = GetSesson();

                    if (_systemUser == null)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                        return RedirectToAction("Logout", "Security");
                    }

                    var PurposeInPort = _PurposeInPortRepository.GetById(PurposeInPortModel.Id);

                    var _PurposeInPortTemp = _mapper.Map<PurposeInPort, PurposeInPortModel>(PurposeInPort);
                    var PurposeInPortOld = _mapper.Map<PurposeInPortModel, PurposeInPort>(_PurposeInPortTemp);

                    PurposeInPort.Name = PurposeInPortModel.Name;
                    PurposeInPort.Description = PurposeInPortModel.Description;

                    //var _supplier = _mapper.Map<SupplierModel, Supplier>(supplierModel);

                    _PurposeInPortRepository.Update(PurposeInPort);

                    var updateStatus = _PurposeInPortRepository.SaveChanges();

                    if (updateStatus > 0)
                    {
                        string StringDescription = new GetStringHistorySystem().Get(_systemUser.UserName, Resources.Resource.Edit, PurposeInPort.Name.ToString(), Resources.Resource.PurposeInPort);

                        //chưa xong
                        InsertHistorySystem((int)EACTION_TYPE.EDIT, (int)EnumControllerName.SUPPLIERS, StringDescription, PurposeInPortOld, PurposeInPort);

                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Sửa thành công"));

                        return RedirectToAction("Index");
                    }
                    else
                    {
                        Notification = new StatusQuery("error", "", "Sửa thất bại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        return View(PurposeInPort);
                    }
                }
                catch(Exception ex)
                {
                    _logger.LogError("PurposeInPort/Edit: " + ex.Message);

                    Notification = new StatusQuery("error", "", "Sửa thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return View(PurposeInPortModel);
                }
            }

            Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;

            return View(PurposeInPortModel);
        }

        // GET: /supplier/Delete/5
        public ActionResult Delete(int id)
        {
            StatusQuery Notification;
            PurposeInPortModel _PurposeInPortModel = new PurposeInPortModel();
            try
            {
                var PurposeInPort = _PurposeInPortRepository.GetById(id);

                _PurposeInPortModel = _mapper.Map<PurposeInPort, PurposeInPortModel>(PurposeInPort);

                if (PurposeInPort == null)
                {
                    Notification = new StatusQuery("warning", "", "Không tìm thấy phòng ban");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return RedirectToAction("Index");
                }
            }
            catch(Exception ex)
            {
                _logger.LogError("PurposeInPort/Delete: " + ex.Message);

                Notification = new StatusQuery("warning", "", "Xóa thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;

                return RedirectToAction("Index");
            }
            return View(_PurposeInPortModel);
        }


        // POST: /Suppliers/Delete/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int id)
        {
            try
            {
                var systemUser = GetSesson();

                if (systemUser == null)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                    return RedirectToAction("Logout", "Security");
                }

                var PurposeInPort = _PurposeInPortRepository.GetById(id);

                _PurposeInPortRepository.Delete(PurposeInPort);

                var deleteStatus = _PurposeInPortRepository.SaveChanges();

                if (deleteStatus > 0)
                {

                    string StringDescription = new GetStringHistorySystem().Get(systemUser.UserName, Resources.Resource.Delete, PurposeInPort.Name.ToString(), Resources.Resource.Suppliers);

                    InsertHistorySystem((int)EACTION_TYPE.DELETE, (int)EnumControllerName.SUPPLIERS, StringDescription, PurposeInPort, null);

                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Xóa thành công"));
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));
                }

                return RedirectToAction("Index");
            }
            catch(Exception ex)
            {
                _logger.LogError("PurposeInPort/DeleteConfirmed: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));
            }
            return RedirectToAction("Index");
        }

        public User GetSesson()
        {
            var SessionUserSystem = HttpContext.Session.GetComplexData<User>("SessionUserSystemId_" + HttpContext.Session.GetString("SessionUserSystemId"));
            if (SessionUserSystem != null)
            {
                var _user = _userRepository.GetById(SessionUserSystem.Id);

                if (_user != null)
                {
                    if (!_user.Actived)
                    {
                        HttpContext.Session.Clear();
                        return null;
                    }
                    return _user;
                }
            }
            return null;
        }

        private void InsertHistorySystem(int action_type, int controllerName, string description, object OldModel, object NewModel)
        {
            try
            {
                //IPHostEntry heserver = Dns.GetHostEntry(Dns.GetHostName());
                //var ipAddress = heserver.AddressList.FirstOrDefault(p => p.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork).ToString();

                var ipAddress = _accessor.HttpContext.Connection.RemoteIpAddress.ToString();

                string jsonOldObject = Newtonsoft.Json.JsonConvert.SerializeObject(OldModel);
                string jsonNewObject = Newtonsoft.Json.JsonConvert.SerializeObject(NewModel);

                HistorySystem history = new HistorySystem
                {
                    ActionType = action_type,
                    ActionTime = DateTime.Now,
                    Description = description,
                    OldObject = jsonOldObject,
                    NewObject = jsonNewObject,
                    UserId = int.Parse(HttpContext.Session.GetString("SessionUserSystemId")),
                    IpAddress = ipAddress,
                    ControllerName = controllerName
                };

                _historySystemRepository.Insert(history);

                _historySystemRepository.SaveChanges();
            }
            catch (Exception ex)
            {
                _logger.LogError("PurposeInPort/InsertHistorySystem: " + ex.Message);
            }
        }
    }
}