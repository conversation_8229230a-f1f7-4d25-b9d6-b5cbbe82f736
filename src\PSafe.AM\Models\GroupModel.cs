﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.ComponentModel.DataAnnotations;

namespace PSafe.AM.Models
{
    public class GroupModel
    {
        #region Properties

        [Key]
        public int GroupID { get; set; }

        [Required(ErrorMessage = "<PERSON><PERSON><PERSON> trị không được trống!")]
        [StringLength(255, MinimumLength = 3, ErrorMessage = "Độ dài từ 3-255 ký tự!")]
        [Display(Name = "GroupName", ResourceType = typeof(Resources.Resource__group))]
        public string GroupName { get; set; } = string.Empty;

        [Display(Name = "GroupDescription", ResourceType = typeof(Resources.Resource__group))]
        public string GroupDescription { get; set; } = string.Empty;
        #endregion
    }
}