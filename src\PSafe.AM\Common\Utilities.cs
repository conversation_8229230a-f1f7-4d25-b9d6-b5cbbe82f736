﻿using Microsoft.AspNetCore.Mvc.Rendering;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Resources;
using System.Threading.Tasks;
using static PSafe.Common.CommonEnums;

namespace PSafe.AM.Common
{
    public class Utilities
    {
        public static string GetEnum_C3_TTAN(Enum _enum)
        {
            var key = _enum.ToString();
            try
            {
                var _resource = new ResourceManager(typeof(Resources.Resource_Enums.Resource_C3_TTAN));

                return _resource.GetString(key);
            }
            catch (Exception)
            {
                return key;
            }
        }

        public static string GetEnum_C3_AM(Enum _enum)
        {
            var key = _enum.ToString();
            try
            {
                var _resource = new ResourceManager(typeof(Resources.Resource_Enums.Resource_C3_AM));

                return _resource.GetString(key);
            }
            catch (Exception)
            {
                return key;
            }
        }

        public static string GetEnum_C3_Report(Enum _enum)
        {
            var key = _enum.ToString();
            try
            {
                var _resource = new ResourceManager(typeof(Resources.Resource_Enums.Resource_C3_Report));

                return _resource.GetString(key);
            }
            catch (Exception)
            {
                return key;
            }
        }

        public static string GetEnum_VMS_Client(Enum _enum)
        {
            var key = _enum.ToString();
            try
            {
                var _resource = new ResourceManager(typeof(Resources.Resource_Enums.Resource_VMS_Client));

                return _resource.GetString(key);
            }
            catch (Exception)
            {
                return key;
            }
        }

        public static string GetEnum_CheckIO_Client(Enum _enum)
        {
            var key = _enum.ToString();
            try
            {
                var _resource = new ResourceManager(typeof(Resources.Resource_Enums.Resource_CheckIO_Client));

                return _resource.GetString(key);
            }
            catch (Exception)
            {
                return key;
            }
        }
        public static string GetEnum_Management_Server(Enum _enum)
        {
            var key = _enum.ToString();
            try
            {
                var _resource = new ResourceManager(typeof(Resources.Resource_Enums.Resource_Management_Server));

                return _resource.GetString(key);
            }
            catch (Exception)
            {
                return key;
            }
        }

        public static List<DropDownList> GetListTypeReason()
        {
            var listDrop = new List<DropDownList>();
            foreach (EREASON_TYPE type in (EREASON_TYPE[])Enum.GetValues(typeof(EREASON_TYPE)))
            {
                var _resource = new ResourceManager(typeof(Resources.Resource_Enums.Resources_reason));

                var typeName = _resource.GetString(type.ToString());

                listDrop.Add(new DropDownList()
                {
                    Id = (int)type,
                    Name = typeName
                });
            }

            return listDrop;
        }
    }
}
