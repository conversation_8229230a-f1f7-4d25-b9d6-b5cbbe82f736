﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace PSafe.AM.Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class Resource_GeneralConfig {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resource_GeneralConfig() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("PSafe.AM.Resources.Resource_GeneralConfig", typeof(Resource_GeneralConfig).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cảnh báo đám đông.
        /// </summary>
        public static string AreaCrowd {
            get {
                return ResourceManager.GetString("AreaCrowd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cảnh báo danh sách cấm.
        /// </summary>
        public static string BannedList {
            get {
                return ResourceManager.GetString("BannedList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cảnh báo xe bị cấm ra vào.
        /// </summary>
        public static string BannedVehicle {
            get {
                return ResourceManager.GetString("BannedVehicle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cấm xe.
        /// </summary>
        public static string BanVehicleLevel {
            get {
                return ResourceManager.GetString("BanVehicleLevel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cảnh báo danh sách đen.
        /// </summary>
        public static string BlackList {
            get {
                return ResourceManager.GetString("BlackList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hủy cấm xe.
        /// </summary>
        public static string CancelBanVehicleLevel {
            get {
                return ResourceManager.GetString("CancelBanVehicleLevel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cảnh báo mất kết nối.
        /// </summary>
        public static string ConnectionLoss {
            get {
                return ResourceManager.GetString("ConnectionLoss", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cảnh báo xâm nhập.
        /// </summary>
        public static string CrossLine {
            get {
                return ResourceManager.GetString("CrossLine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email gửi cảnh báo.
        /// </summary>
        public static string Email {
            get {
                return ResourceManager.GetString("Email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cảnh báo xâm nhập vào vùng cấm.
        /// </summary>
        public static string EnterArea {
            get {
                return ResourceManager.GetString("EnterArea", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cảnh báo cháy nổ.
        /// </summary>
        public static string FireAlarm {
            get {
                return ResourceManager.GetString("FireAlarm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mã.
        /// </summary>
        public static string Id {
            get {
                return ResourceManager.GetString("Id", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Số lượng camera giới hạn khi search.
        /// </summary>
        public static string MaxSearchDevice {
            get {
                return ResourceManager.GetString("MaxSearchDevice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Loại cảnh báo phải xử lý.
        /// </summary>
        public static string MinSOSLevel {
            get {
                return ResourceManager.GetString("MinSOSLevel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mật khẩu.
        /// </summary>
        public static string Password {
            get {
                return ResourceManager.GetString("Password", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Màn hình hiển thị cảnh báo.
        /// </summary>
        public static string ScreenAlarm {
            get {
                return ResourceManager.GetString("ScreenAlarm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Màn hình xử lý sự kiện.
        /// </summary>
        public static string ScreenProcessEvent {
            get {
                return ResourceManager.GetString("ScreenProcessEvent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Danh sách cảng cấm.
        /// </summary>
        public static string SiteSetViolation {
            get {
                return ResourceManager.GetString("SiteSetViolation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thời gian replay trước và sau cảnh báo.
        /// </summary>
        public static string TimeActionReplay {
            get {
                return ResourceManager.GetString("TimeActionReplay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thời gian trích lưu video.
        /// </summary>
        public static string TimeCaptureVideo {
            get {
                return ResourceManager.GetString("TimeCaptureVideo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thời gian gửi số lượng khách hàng quá thời gian trong cảng.
        /// </summary>
        public static string TimeCustomerExpriedInPort {
            get {
                return ResourceManager.GetString("TimeCustomerExpriedInPort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thời gian gửi số lượng khách hàng trong cảng.
        /// </summary>
        public static string TimeCustomerInPort {
            get {
                return ResourceManager.GetString("TimeCustomerInPort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thời gian gửi SMS cho cấp trên.
        /// </summary>
        public static string TimeSendSMS {
            get {
                return ResourceManager.GetString("TimeSendSMS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cảnh báo giao thông.
        /// </summary>
        public static string TrafficViolationLevel {
            get {
                return ResourceManager.GetString("TrafficViolationLevel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tên người dùng.
        /// </summary>
        public static string UserName {
            get {
                return ResourceManager.GetString("UserName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Loại sự kiện phạt nóng.
        /// </summary>
        public static string ViolateFlag {
            get {
                return ResourceManager.GetString("ViolateFlag", resourceCulture);
            }
        }
    }
}
