﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Resources;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Logging;
using PSafe.AM.Common;
using PSafe.AM.Models;
using PSafe.Common.UserEnums;
using PSafe.Core.Domains;
using PSafe.Core.Interface;

namespace PSafe.AM.Controllers
{
    [AuthorizeActionFilter(EPERMISSIONS_AM.categoryManage)]
    public class CompanysController : Controller
    {
        private readonly ICompanyRepository _companyRepository;
        private readonly IUserRepository _userRepository;
        private readonly IMapper _mapper;
        private readonly IHttpContextAccessor _accessor;
        private readonly ILogger<CompanysController> _logger;

        public CompanysController(ICompanyRepository companyRepository, IMapper mapper, IUserRepository userRepository,
            IHttpContextAccessor accessor, ILogger<CompanysController> logger)
        {
            _companyRepository = companyRepository;
            _userRepository = userRepository;
            _mapper = mapper;
            _accessor = accessor;
            _logger = logger;
        }

        public ActionResult Index()
        {
            StatusQuery Notification;
            List<CompanyModel> listCompanyModel = new List<CompanyModel>();
            try
            {
                var listCompany = _companyRepository.GetAll().ToList();

                listCompanyModel = _mapper.Map<List<CompanyModel>>(listCompany);

                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Company/Index: " + ex.Message);

                Notification = new StatusQuery("error", "", "Xem danh sách thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;
            }

            return View(listCompanyModel);
        }

        // GET: /supplier/Details/
        public ActionResult Details(int id)
        {
            try
            {
                var company = _companyRepository.GetById(id);

                var companyModel = _mapper.Map<CompanyModel>(company);

                try
                {
                    try
                    {
                        if (company.ParentId.HasValue && company.ParentId.Value != -1)
                        {
                            ViewBag.ParentName = _companyRepository.GetById(company.ParentId).Name;
                        }
                        
                        ViewBag.CreatedBy = _userRepository.GetById(company.CreatedBy).UserName;
                        ViewBag.UpdatedBy = _userRepository.GetById(company.UpdatedBy).UserName;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError("User/Details: " + ex.Message);
                        Console.WriteLine(ex.Message);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex.Message);
                }

                return View(companyModel);
            }
            catch (Exception ex)
            {
                _logger.LogError("Company/Details: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                return RedirectToAction("Index");
            }
        }

        // GET: /supplier/Create
        public ActionResult Create()
        {
            try
            {
                CompanyModel companyModel = new CompanyModel
                {
                    ListParentId = GetListParen(null),
                    ListFormOfFeeCollection = GetListFormOfFeeCollection()
                };

                return View(companyModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.Message);
            }
            return View();
        }

        private SelectList GetListParen(int? id)
        {
            var listDrop = new List<DropDownList>();
            var drop = new DropDownList()
            {
                Id = -1,
                Name = "---Chọn---"
            };
            listDrop.Add(drop);

            try
            {
                var listCompanys = _companyRepository.GetAll().ToList();

                if (id.HasValue)
                {
                    var _listDrop = listCompanys.Where(p => p.Id != id).Select (u => new DropDownList()
                    {
                        Id = u.Id,
                        Name = u.Name
                    }).ToList();

                    foreach(var item in _listDrop)
                    {
                        listDrop.Add(item);
                    }
                }
                else
                {
                    var _listDrop = listCompanys.Select(u => new DropDownList()
                    {
                        Id = u.Id,
                        Name = u.Name
                    }).ToList();

                    foreach (var item in _listDrop)
                    {
                        listDrop.Add(item);
                    }
                }

                return ToSelectList(listDrop);
            }
            catch(Exception ex)
            {
                _logger.LogError(ex.Message);

                return ToSelectList(listDrop);
            }
        }

        private SelectList GetListFormOfFeeCollection()
        {
            try
            {
                var listType = new List<SelectListItem>() {
                    new SelectListItem()
                    {
                        Value = "-1",
                        Text = "---Chọn---"
                    }
                };
                foreach (FormOfFeeCollection type in (FormOfFeeCollection[])Enum.GetValues(typeof(FormOfFeeCollection)))
                {
                    var key = type.ToString();
                    try
                    {
                        var _resource = new ResourceManager(typeof(Resources.Resource_Enums.Resource_company_formOfFeeCollection));

                        key = _resource.GetString(key);
                    }
                    catch (Exception)
                    {
                        
                    }

                    listType.Add(new SelectListItem()
                    {
                        Value = type.ToString(),
                        Text = key
                    });
                }

                return new SelectList(listType, "Value", "Text");
            }
            catch(Exception ex)
            {

            }
            return new SelectList(new List<SelectListItem>() ,"Value", "Text");
        }

        // POST: /supplier/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create([Bind("Name, Addresss, Email, NickName, PhoneNumber, ParentId, ContactPerson, Status, FormOfFeeCollection, Description")] CompanyModel companyModel)
        {
            StatusQuery Notification;
            if (ModelState.IsValid)
            {
                try
                {
                    var systemUser = GetSesson();

                    if (systemUser == null)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                        return RedirectToAction("Logout", "Security");
                    }

                    var company = _mapper.Map<Company>(companyModel);

                    company.CreatedBy = systemUser.Id;
                    company.UpdatedBy = systemUser.Id;
                    company.CreatedDate = DateTime.Now;
                    company.UpdatedDate = DateTime.Now;
                    company.FormOfFeeCollection = companyModel.FormOfFeeCollection != "-1" ? companyModel.FormOfFeeCollection : null;

                    _companyRepository.Insert(company);

                    var statusInsert = _companyRepository.SaveChanges();

                    if (statusInsert > 0)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Thêm mới thành công"));
                        return RedirectToAction("Index");
                    }
                    else
                    {
                        Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        return View(companyModel);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("Company/Create: " + ex.Message);

                    Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return View(companyModel);
                }
            }
            Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;

            return View(companyModel);
        }

        // GET: /supplier/Edit/5
        public ActionResult Edit(int id)
        {
            try
            {
                var company = _companyRepository.GetById(id);

                if (company != null)
                {
                    var companyModel = _mapper.Map<CompanyModel>(company);

                    companyModel.ListParentId = GetListParen(company.Id);
                    companyModel.ListFormOfFeeCollection = GetListFormOfFeeCollection();

                    return View(companyModel);
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                    return RedirectToAction("Index");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Company/Edit: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                return RedirectToAction("Index");
            }
        }

        // POST: /supplier/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit([Bind("Id, Email, Addresss, Name, NickName, PhoneNumber, ParentId, Status, ContactPerson, FormOfFeeCollection, Description")] CompanyModel companyModel)
        {
            StatusQuery Notification;
            if (ModelState.IsValid)
            {
                try
                {
                    var systemUser = GetSesson();

                    if (systemUser == null)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                        return RedirectToAction("Logout", "Security");
                    }

                    var company = _companyRepository.GetById(companyModel.Id);

                    company.Name = companyModel.Name;
                    company.NickName = companyModel.NickName;
                    company.PhoneNumber = companyModel.PhoneNumber;
                    company.UpdatedBy = systemUser.Id;
                    company.UpdatedDate = DateTime.Now;
                    company.Description = companyModel.Description;
                    company.ParentId = companyModel.ParentId != -1 ? companyModel.ParentId : null;
                    company.Status = companyModel.Status;
                    company.ContactPerson = companyModel.ContactPerson;
                    company.Email = companyModel.Email;
                    company.Address = companyModel.Address;
                    company.FormOfFeeCollection = companyModel.FormOfFeeCollection != "-1" ? companyModel.FormOfFeeCollection : null;

                    _companyRepository.Update(company);

                    var updateStatus = _companyRepository.SaveChanges();

                    if (updateStatus > 0)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Sửa thành công"));

                        return RedirectToAction("Index");
                    }
                    else
                    {
                        Notification = new StatusQuery("error", "", "Sửa thất bại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        return View(company);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("Company/Edit: " + ex.Message);

                    Notification = new StatusQuery("error", "", "Sửa thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return View(companyModel);
                }
            }

            Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;

            return View(companyModel);
        }

        // GET: /supplier/Delete/5
        public ActionResult Delete(int id)
        {
            StatusQuery Notification;
            CompanyModel companyModel = new CompanyModel();
            try
            {
                var company = _companyRepository.GetById(id);

                if (company == null)
                {
                    Notification = new StatusQuery("warning", "", "Không tìm thấy công ty");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return RedirectToAction("Index");
                }

                companyModel = _mapper.Map<CompanyModel>(company);

                try
                {
                    if (company.ParentId.HasValue)
                    {
                        ViewBag.ParentName = _companyRepository.GetById(company.ParentId).Name;
                    }

                    ViewBag.CreatedBy = _userRepository.GetById(company.CreatedBy).UserName;
                    ViewBag.UpdatedBy = _userRepository.GetById(company.UpdatedBy).UserName;
                }
                catch (Exception ex)
                {
                    _logger.LogError("User/Details: " + ex.Message);
                    Console.WriteLine(ex.Message);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Company/Delete: " + ex.Message);

                Notification = new StatusQuery("warning", "", "Xóa thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;

                return RedirectToAction("Index");
            }
            return View(companyModel);
        }


        // POST: /Suppliers/Delete/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int id)
        {
            try
            {
                var systemUser = GetSesson();

                if (systemUser == null)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                    return RedirectToAction("Logout", "Security");
                }

                var Company = _companyRepository.GetById(id);

                _companyRepository.Delete(Company);

                var deleteStatus = _companyRepository.SaveChanges();

                if (deleteStatus > 0)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Xóa thành công"));
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));
                }

                return RedirectToAction("Index");
            }
            catch (Exception ex)
            {
                _logger.LogError("Company/DeleteConfirmed: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));
            }
            return RedirectToAction("Index");
        }

        [NonAction]
        private SelectList ToSelectList(List<DropDownList> DropDownList)
        {
            List<SelectListItem> list = new List<SelectListItem>();

            foreach (var item in DropDownList)
            {
                list.Add(new SelectListItem()
                {
                    Text = item.Name,
                    Value = item.Id.ToString()
                });
            }

            return new SelectList(list, "Value", "Text");
        }

        public User GetSesson()
        {
            var sessionUser = HttpContext.Session.GetString("SessionUserSystemId");
            if (sessionUser != null)
            {
                var _user = _userRepository.GetById(int.Parse(sessionUser));

                if (_user != null)
                {
                    if (!_user.Actived)
                    {
                        HttpContext.Session.Clear();
                        return null;
                    }
                    return _user;
                }
            }
            return null;
        }
    }
}
