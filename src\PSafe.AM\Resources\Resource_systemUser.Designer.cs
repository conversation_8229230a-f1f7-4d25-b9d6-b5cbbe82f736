﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace PSafe.AM.Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "15.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class Resource_systemUser {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resource_systemUser() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("PSafe.AM.Resources.Resource_systemUser", typeof(Resource_systemUser).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kích hoạt.
        /// </summary>
        public static string Actived {
            get {
                return ResourceManager.GetString("Actived", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ngày tạo.
        /// </summary>
        public static string CreatedDate {
            get {
                return ResourceManager.GetString("CreatedDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ngày tạo.
        /// </summary>
        public static string CreatedUser {
            get {
                return ResourceManager.GetString("CreatedUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ngày kích hoạt.
        /// </summary>
        public static string CreationDate {
            get {
                return ResourceManager.GetString("CreationDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Phòng ban.
        /// </summary>
        public static string DepartmentId {
            get {
                return ResourceManager.GetString("DepartmentId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email.
        /// </summary>
        public static string Email {
            get {
                return ResourceManager.GetString("Email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mã nhóm.
        /// </summary>
        public static string GroupID {
            get {
                return ResourceManager.GetString("GroupID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mã.
        /// </summary>
        public static string Id {
            get {
                return ResourceManager.GetString("Id", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tài khoản được duyệt.
        /// </summary>
        public static string IsApproved {
            get {
                return ResourceManager.GetString("IsApproved", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tài khoản đang online.
        /// </summary>
        public static string IsOnline {
            get {
                return ResourceManager.GetString("IsOnline", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thời điểm hoạt động cuối.
        /// </summary>
        public static string LastActivityDate {
            get {
                return ResourceManager.GetString("LastActivityDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lần đăng nhập cuối.
        /// </summary>
        public static string LastLoginDate {
            get {
                return ResourceManager.GetString("LastLoginDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ngày thay đổi password.
        /// </summary>
        public static string LastPasswordChangeDate {
            get {
                return ResourceManager.GetString("LastPasswordChangeDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lý do khóa.
        /// </summary>
        public static string LockReason {
            get {
                return ResourceManager.GetString("LockReason", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mật khẩu.
        /// </summary>
        public static string Password {
            get {
                return ResourceManager.GetString("Password", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Câu trả lời khi quên mật khẩu.
        /// </summary>
        public static string PasswordAnswer {
            get {
                return ResourceManager.GetString("PasswordAnswer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Câu hỏi khi quên mật khẩu.
        /// </summary>
        public static string PasswordQuestion {
            get {
                return ResourceManager.GetString("PasswordQuestion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Điện thoại.
        /// </summary>
        public static string PhoneNumber {
            get {
                return ResourceManager.GetString("PhoneNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Chức vụ.
        /// </summary>
        public static string PositionId {
            get {
                return ResourceManager.GetString("PositionId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mã.
        /// </summary>
        public static string SystemUserId {
            get {
                return ResourceManager.GetString("SystemUserId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Người dùng.
        /// </summary>
        public static string SystemUsers {
            get {
                return ResourceManager.GetString("SystemUsers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ngày cập nhật.
        /// </summary>
        public static string UpdatedDate {
            get {
                return ResourceManager.GetString("UpdatedDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Người cập nhật.
        /// </summary>
        public static string UpdatedUser {
            get {
                return ResourceManager.GetString("UpdatedUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tên đăng nhập.
        /// </summary>
        public static string UserName {
            get {
                return ResourceManager.GetString("UserName", resourceCulture);
            }
        }
    }
}
