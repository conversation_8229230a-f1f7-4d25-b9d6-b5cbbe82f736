﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PSafe.AM.Common;
using PSafe.AM.Models;
using PSafe.Common;
using PSafe.Common.UserEnums;
using PSafe.Core.Domains;
using PSafe.Core.Interface;
using static PSafe.Common.CommonEnums;

namespace PSafe.AM.Controllers
{
    [AuthorizeActionFilter(EPERMISSIONS_AM.SecurityRecord)]
    public class SecurityRecordController : Controller
    {
        private readonly ISecurityRecordRepository _securityRecordRepository;
        private readonly IHistoryEventRepository _historyEventRepository;
        private readonly IAreaRepository _areaRepository;
        private readonly ILocationRepository _locationRepository;
        private readonly IUserRepository _userRepository;
        private readonly IHistorySystemRepository _historySystemRepository;
        private readonly ILogger<SecurityRecordController> _logger;
        private readonly IHttpContextAccessor _accessor;

        public SecurityRecordController(ISecurityRecordRepository securityRecordRepository, IHistoryEventRepository historyEventRepository, IAreaRepository areaRepository, ILocationRepository locationRepository, IUserRepository userRepository, IHistorySystemRepository historySystemRepository, ILogger<SecurityRecordController> logger, IHttpContextAccessor accessor)
        {
            _securityRecordRepository = securityRecordRepository;
            _historyEventRepository = historyEventRepository;
            _areaRepository = areaRepository;
            _locationRepository = locationRepository;
            _userRepository = userRepository;
            _historySystemRepository = historySystemRepository;

            _logger = logger;
            _accessor = accessor;
        }

        public IActionResult Index()
        {
            StatusQuery Notification;
            List<SecurityRecordModel> _securityRecordModels = new List<SecurityRecordModel>();

            try
            {
                ViewBag.areaList = GetAreaList();
                DateTime now = DateTime.Now.Date;
                DateTime fromDate = new DateTime(now.Year, now.Month, now.Day, 0, 0, 0);
                fromDate = fromDate.AddDays(-7);
                DateTime toDate = new DateTime(now.Year, now.Month, now.Day, 23, 59, 59);

                var securityRecordList = GetSecurityRecordList(-1, -1, -1, fromDate, toDate);
                foreach(var securityRecord in securityRecordList)
                {
                    _securityRecordModels.Add(new SecurityRecordModel()
                    {
                        Id = securityRecord.Id,
                        Name = securityRecord.Name,
                        HistoryEvent = securityRecord.HistoryEvent
                    });
                }

                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("SecurityRecord/Index: " + ex.Message);

                Notification = new StatusQuery("error", "", "Xem danh sách thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;
            }

            return View(_securityRecordModels);
        }

        public ActionResult Edit(int id)
        {
            try
            {
                ViewBag.MediaServerUrl = GetMediaServerUrl();
                ViewBag.PSafeC3Url = GetPSafeC3Url();
                var securityRecord = _securityRecordRepository.Get(x => x.Id == id, null, "HistoryEvent").FirstOrDefault();
                if (securityRecord != null)
                {
                    SecurityRecordModel securityRecordModel = new SecurityRecordModel();
                    securityRecordModel.Id = securityRecord.Id;
                    securityRecordModel.HistoryEventId = securityRecord.HistoryEventId;
                    securityRecordModel.Name = securityRecord.Name;
                    securityRecordModel.ListDocument = securityRecord.ListDocument;
                    securityRecordModel.Description = securityRecord.Description;
                    securityRecordModel.EventId = securityRecord.HistoryEvent != null ? securityRecord.HistoryEvent.EventId : 0;
                    return View(securityRecordModel);
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Chỉnh sửa thất bại"));

                    return RedirectToAction("Index");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("SecurityRecord/Edit: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Chỉnh sửa thất bại"));

                return RedirectToAction("Index");
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit(SecurityRecordModel securityRecordModel)
        {
            StatusQuery Notification;
            if (ModelState.IsValid)
            {
                try
                {
                    var _systemUser = GetSesson();

                    if (_systemUser == null)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                        return RedirectToAction("Logout", "Security");
                    }

                    var securityRecord = _securityRecordRepository.GetById(securityRecordModel.Id);
                    securityRecord.Description = securityRecordModel.Description;
                    securityRecord.Name = securityRecordModel.Name;
                    securityRecord.ListDocument = securityRecordModel.ListDocument;
                    _securityRecordRepository.Update(securityRecord);
                    int updateStatus = _securityRecordRepository.SaveChanges();

                    if (updateStatus > 0)
                    {
                        string StringDescription = new GetStringHistorySystem().Get(_systemUser.UserName, Resources.Resource.Edit, securityRecordModel.Name, Resources.Resource.SecurityRecord);

                        InsertHistorySystem((int)EACTION_TYPE.EDIT, (int)EnumControllerName.SECURITY_RECORD, StringDescription, securityRecord, securityRecordModel);
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Sửa thành công"));
                        return RedirectToAction("Index");
                    }

                    return View(securityRecordModel);
                }
                catch (Exception ex)
                {
                    _logger.LogError("SecurityRecord/Edit: " + ex.Message);

                    Notification = new StatusQuery("error", "", "Sửa thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return View(securityRecordModel);
                }
            }

            Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;

            return View(securityRecordModel);
        }

        [HttpPost("SecurityRecord/UploadDocument")]
        public List<object> UploadDocument(List<IFormFile> Files)
        {
            List<object> listDocumentUploaded = new List<object>();
            if (Files != null && Files.Count > 0)
            {
                var builder = new ConfigurationBuilder()
                            .SetBasePath(Directory.GetCurrentDirectory())
                            .AddJsonFile("appsettings.json");
                var configuration = builder.Build();

                var userNameFTP =  Utils.DecodePassword(configuration.GetSection("FTP")["UserName"], Utils.EncodeType.SHA_256);
                var passwordFTP = Utils.DecodePassword(configuration.GetSection("FTP")["Password"], Utils.EncodeType.SHA_256);
                var hostFTP = configuration.GetSection("FTP")["Host"];
                var portFTP = configuration.GetSection("FTP")["Port"];
                if (portFTP != "21")
                {
                    hostFTP = hostFTP + ":" + portFTP;
                }

                foreach (var file in Files)
                {
                    byte[] fileBytes;
                    using (var ms = new MemoryStream())
                    {
                        file.CopyTo(ms);
                        fileBytes = ms.ToArray();
                    }

                    try
                    {
                        string fileName = RemoveUnicodeFromFileName(file.FileName.Substring(file.FileName.LastIndexOf("\\") + 1));
                        fileName = DateTime.Now.ToString("MM_dd_yyyy_HH_mm_ss") + "_" + fileName;
                        using (var client = new WebClient())
                        {
                            client.Credentials = new NetworkCredential(userNameFTP, passwordFTP);
                            client.UploadData(hostFTP + "//Document//" + fileName, fileBytes);
                        }

                        listDocumentUploaded.Add(new {
                            fileName = fileName,
                            oFileName = file.FileName
                        });
                    }
                    catch(Exception ex)
                    {
                        _logger.LogError("SecurityRecord/UpdateImage: " + ex.Message);
                    }
                }
            }

            return listDocumentUploaded;
        }

        public ActionResult Details(int id)
        {
            try
            {
                var securityRecord = _securityRecordRepository.Get(x => x.Id == id, null, "HistoryEvent").FirstOrDefault();
                if (securityRecord == null)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                    return RedirectToAction("Index");
                }

                ViewBag.MediaServerUrl = GetMediaServerUrl();
                ViewBag.PSafeC3Url = GetPSafeC3Url();
                var securityRecordModel = new SecurityRecordModel();
                securityRecordModel.Id = securityRecord.Id;
                securityRecordModel.Name = securityRecord.Name;
                securityRecordModel.HistoryEventId = securityRecord.HistoryEventId;
                securityRecordModel.ListDocument = securityRecord.ListDocument;
                securityRecordModel.Description = securityRecord.Description;
                securityRecordModel.EventId = securityRecord.HistoryEvent != null ? securityRecord.HistoryEvent.EventId : 0;
                return View(securityRecordModel);
            }
            catch (Exception ex)
            {
                _logger.LogError("SecurityRecord/Details: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                return RedirectToAction("Index");
            }
        }

        public ActionResult Delete(int id)
        {
            StatusQuery Notification;
            try
            {
                var securityRecord = _securityRecordRepository.Get(x => x.Id == id, null, "HistoryEvent").FirstOrDefault();
                if (securityRecord == null)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Hồ sơ an ninh không tồn tại"));

                    return RedirectToAction("Index");
                }

                ViewBag.MediaServerUrl = GetMediaServerUrl();
                ViewBag.PSafeC3Url = GetPSafeC3Url();
                var securityRecordModel = new SecurityRecordModel();
                securityRecordModel.Id = securityRecord.Id;
                securityRecordModel.Name = securityRecord.Name;
                securityRecordModel.HistoryEventId = securityRecord.HistoryEventId;
                securityRecordModel.ListDocument = securityRecord.ListDocument;
                securityRecordModel.Description = securityRecord.Description;
                securityRecordModel.EventId = securityRecord.HistoryEvent != null ? securityRecord.HistoryEvent.EventId : 0;
                return View(securityRecordModel);
            }
            catch (Exception ex)
            {
                _logger.LogError("SecurityRecord/Delete: " + ex.Message);

                Notification = new StatusQuery("warning", "", "Xóa thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;

                return RedirectToAction("Index");
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int id)
        {
            try
            {
                var systemUser = GetSesson();

                if (systemUser == null)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                    return RedirectToAction("Logout", "Security");
                }


                var securityRecord = _securityRecordRepository.GetById(id);

                _securityRecordRepository.Delete(securityRecord);

                var deleteStatus = _securityRecordRepository.SaveChanges();

                if (deleteStatus > 0)
                {

                    string StringDescription = new GetStringHistorySystem().Get(systemUser.UserName, Resources.Resource.Delete, securityRecord.Name.ToString(), Resources.Resource.SecurityRecord);

                    InsertHistorySystem((int)EACTION_TYPE.DELETE, (int)EnumControllerName.SECURITY_RECORD, StringDescription, securityRecord, null);

                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Xóa thành công"));
                    return RedirectToAction("Index");
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));
                    return RedirectToAction("Delete", "SecurityRecord", new { id });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("SecurityRecord/DeleteConfirmed: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));
                return RedirectToAction("Delete", "SecurityRecord", new { id });
            }
        }

        private string GetMediaServerUrl()
        {
            var builder = new ConfigurationBuilder()
                            .SetBasePath(Directory.GetCurrentDirectory())
                            .AddJsonFile("appsettings.json");
            var configuration = builder.Build();
            return configuration["MediaServer"];
        }

        private string GetPSafeC3Url()
        {
            var builder = new ConfigurationBuilder()
                            .SetBasePath(Directory.GetCurrentDirectory())
                            .AddJsonFile("appsettings.json");
            var configuration = builder.Build();
            return configuration["PSafeC3Url"];
        }

        private string RemoveUnicodeFromFileName(string fileName)
        {
            Regex regex = new Regex("\\p{IsCombiningDiacriticalMarks}+");
            string temp = fileName.Normalize(NormalizationForm.FormD);
            return regex.Replace(temp, String.Empty).Replace('\u0111', 'd').Replace('\u0110', 'D');
        }

        public User GetSesson()
        {
            var SessionUserSystem = HttpContext.Session.GetComplexData<User>("SessionUserSystemId_" + HttpContext.Session.GetString("SessionUserSystemId"));
            if (SessionUserSystem != null)
            {
                var _user = _userRepository.GetById(SessionUserSystem.Id);

                if (_user != null)
                {
                    if (!_user.Actived)
                    {
                        HttpContext.Session.Clear();
                        return null;
                    }
                    return _user;
                }
            }
            return null;
        }

        private void InsertHistorySystem(int action_type, int controllerName, string description, object OldModel, object NewModel)
        {
            try
            {
                var ipAddress = _accessor.HttpContext.Connection.RemoteIpAddress.ToString();

                string jsonOldObject = Newtonsoft.Json.JsonConvert.SerializeObject(OldModel);
                string jsonNewObject = Newtonsoft.Json.JsonConvert.SerializeObject(NewModel);

                HistorySystem history = new HistorySystem
                {
                    ActionType = action_type,
                    ActionTime = DateTime.Now,
                    Description = description,
                    OldObject = jsonOldObject,
                    NewObject = jsonNewObject,
                    UserId = int.Parse(HttpContext.Session.GetString("SessionUserSystemId")),
                    IpAddress = ipAddress,
                    ControllerName = controllerName
                };

                _historySystemRepository.Insert(history);

                _historySystemRepository.SaveChanges();
            }
            catch (Exception ex)
            {
                _logger.LogError("Document/InsertHistorySystem: " + ex.Message);
            }
        }

        public List<SelectListItem> GetAreaList()
        {
            List<SelectListItem> areaListItems = new List<SelectListItem>();
            try
            {
                var areaList = _areaRepository.GetAll().ToList();
                foreach (var area in areaList)
                {
                    areaListItems.Add(new SelectListItem()
                    {
                        Text = area.AreaName,
                        Value = area.Id.ToString()
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("SecurityRecord/GetAreaList: " + ex.Message);
            }

            return areaListItems;
        }

        private List<SelectListItem> GetListLocationByAreaId(int areaId)
        {
            try
            {
                var locationList = _locationRepository.GetBy(x => x.AreaId == areaId).Select(x => new SelectListItem() { Value = x.Id.ToString(), Text = x.LocationName }).ToList();
                return locationList;
            }
            catch (Exception ex)
            {
                _logger.LogError("SecurityRecord/GetListLocationByAreaId: " + ex.Message);
                Console.WriteLine(ex.Message);
            }

            return new List<SelectListItem>();
        }

        [HttpPost("SecurityRecord/GetLocationByArea")]
        public JsonResult GetLocationByArea(int areaId)
        {
            return Json(GetListLocationByAreaId(areaId));
        }

        [HttpPost("SecurityRecord/GetSecurityRecord")]
        public JsonResult GetSecurityRecord(int areaId, int locationId, int typeOfSignal, DateTime fromDate, DateTime toDate)
        {
            return Json(GetSecurityRecordList(areaId, locationId, typeOfSignal, fromDate, toDate));
        }

        private List<SecurityRecord> GetSecurityRecordList(int areaId, int locationId, int typeOfSignal, DateTime fromDate, DateTime toDate)
        {
            return _securityRecordRepository.Get(x=> 
                (x.HistoryEvent.Timestamp >= fromDate && x.HistoryEvent.Timestamp <= toDate) &&
                (x.HistoryEvent.ProcessTime >= fromDate && x.HistoryEvent.ProcessTime <= toDate) &&
                ((areaId != -1 && x.HistoryEvent.AreaId == areaId) || areaId == -1) &&
                ((locationId != -1 && x.HistoryEvent.LocationId == locationId) || locationId == -1) &&
                ((typeOfSignal != -1 && x.HistoryEvent.TypeOfSignal == typeOfSignal) || typeOfSignal == -1),
                null, "HistoryEvent").OrderByDescending(x=>x.Id).ToList();
        }

        private List<int> GetHistoryEventIdList(List<SecurityRecord> securityRecordList)
        {
            List<int> historyEventIdList = new List<int>();
            foreach(var securityRecord in securityRecordList)
            {
                historyEventIdList.Add(securityRecord.HistoryEventId);
            }

            return historyEventIdList;
        }
    }
}