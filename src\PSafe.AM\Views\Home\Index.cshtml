﻿@model PSafe.AM.Models.HomeModel
@{
    ViewData["Title"] = "Home Page";
}

<environment names="Development">
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.css" />
    <link rel="stylesheet" href="~/lib/font-awesome/css/font-awesome.css" />
    <link rel="stylesheet" href="~/css/animate.css" />
    <link rel="stylesheet" href="~/css/style.css" asp-append-version="true" />
</environment>
<environment names="Staging,Production">
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/lib/font-awesome/css/font-awesome.min.css" />
    <link rel="stylesheet" href="~/css/animate.css" />
    <link rel="stylesheet" href="~/css/style.css" asp-append-version="true" />
</environment>

<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-4">
            <div class="ibox ">
                <div class="ibox-title">
                    <h5>@PSafe.AM.Resources.Resource.Device</h5>
                </div>
                <div class="ibox-content">
                    <div id="morris-donut-chart"></div>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="ibox ">
                <div class="ibox-title">
                    <h5>@PSafe.AM.Resources.Resource.Location</h5>
                </div>
                <div class="ibox-content">
                    <small>Tổng số</small>
                    <h1 class="no-margins">@ViewBag.CountLocations</h1>

                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="ibox ">
                <div class="ibox-title">
                    <h5>@PSafe.AM.Resources.Resource.Area</h5>
                </div>
                <div class="ibox-content">
                    <small>Tổng số</small>
                    <h1 class="no-margins">@ViewBag.CountAreas</h1>

                </div>
            </div>
        </div>

    </div>
</div>

@section Styles {
    <environment names="Development,Staging,Production">
        <link rel="stylesheet" href="~/lib/dataTables/datatables.min.css" />
        <link rel="stylesheet" href="~/lib/toastr/toastr.min.css" />
        <link rel="stylesheet" href="~/lib/morrisjs/morris.css" />
    </environment>
}

@section Scripts {

    <environment names="Development">
        <script src="~/lib/raphael/raphael.js"></script>
        <script src="~/lib/morrisjs/morris.js"></script>
    </environment>

    <environment names="Development,Staging,Production">
        <script src="~/lib/dataTables/datatables.min.js"></script>
        <script src="~/lib/dataTables/dataTables.bootstrap4.min.js"></script>
        <script src="~/lib/toastr/toastr.js"></script>
        <script src="~/lib/toastr/toastr.min.js"></script>
        <script src="~/lib/raphael/raphael-min.js"></script>
        <script src="~/lib/morrisjs/morris.min.js"></script>
    </environment>

    @if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "warning")
    {
        <script type="text/javascript">
            toastr.warning('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "error")
    {
        <script type="text/javascript">
            toastr.error('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "success")
    {
        <script type="text/javascript">
            toastr.success('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }


        <script type="text/javascript">
        $(document).ready(function () {
            Morris.Donut({
                element: 'morris-donut-chart',
                data: [
                @foreach (var item in @Model.ListTypeOfDevice)
                    {
                        @:{ label: "@(item.Name.ToString())", value: '@(item.Value)', labelColor: '#87d6c6' },
                    }
                    ],
                resize: true
            });
        });
        </script>
}
