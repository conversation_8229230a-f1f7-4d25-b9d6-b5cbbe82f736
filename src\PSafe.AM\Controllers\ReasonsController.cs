﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Logging;
using PSafe.AM.Common;
using PSafe.AM.Models;
using PSafe.Common.UserEnums;
using PSafe.Core.Domains;
using PSafe.Core.Interface;

namespace PSafe.AM.Controllers
{
    [AuthorizeActionFilter(EPERMISSIONS_AM.categoryManage)]
    public class ReasonsController : Controller
    {
        private readonly IReasonRepository _reasonRepository;
        private readonly IUserRepository _userRepository;
        private readonly IMapper _mapper;
        private readonly IHttpContextAccessor _accessor;
        private readonly ILogger<ReasonsController> _logger;

        public ReasonsController(IReasonRepository reasonRepository, IMapper mapper, IUserRepository userRepository,
            IHttpContextAccessor accessor, ILogger<ReasonsController> logger)
        {
            _reasonRepository = reasonRepository;
            _userRepository = userRepository;
            _mapper = mapper;
            _accessor = accessor;
            _logger = logger;
        }

        public ActionResult Index()
        {
            StatusQuery Notification;
            List<ReasonModel> listReasonModel = new List<ReasonModel>();
            try
            {
                var listReason = _reasonRepository.GetAll().ToList();

                listReasonModel = _mapper.Map<List<Reason>, List<ReasonModel>>(listReason);

                var listType = Utilities.GetListTypeReason();

                foreach (var item in listReasonModel)
                {
                    item.TypeString = listType.Where(p => p.Id == item.Type).FirstOrDefault().Name;
                }

                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Reason/Index: " + ex.Message);

                Notification = new StatusQuery("error", "", "Xem danh sách thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;
            }

            return View(listReasonModel);
        }

        // GET: /supplier/Details/
        public ActionResult Details(int id)
        {
            try
            {
                var reason = _reasonRepository.GetById(id);

                var reasonModel = _mapper.Map<Reason, ReasonModel>(reason);

                var listType = Utilities.GetListTypeReason();

                reasonModel.TypeString = listType.Where(p => p.Id == reasonModel.Type).FirstOrDefault().Name;

                return View(reasonModel);
            }
            catch (Exception ex)
            {
                _logger.LogError("Reason/Details: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                return RedirectToAction("Index");
            }
        }

        // GET: /supplier/Create
        public ActionResult Create()
        {
            ReasonModel reasonModel = new ReasonModel
            {
                ListType = ToSelectList(Utilities.GetListTypeReason())
            };
            return View(reasonModel);
        }

        // POST: /supplier/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create([Bind("Name, Type, Description")] ReasonModel reasonModel)
        {
            StatusQuery Notification;
            if (ModelState.IsValid)
            {
                try
                {
                    var _systemUser = GetSesson();

                    if (_systemUser == null)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                        return RedirectToAction("Logout", "Security");
                    }

                    var reason = _mapper.Map<ReasonModel, Reason>(reasonModel);
                    reason.CreatedBy = _systemUser.Id;
                    reason.UpdatedBy = _systemUser.Id;
                    reason.Type = reasonModel.Type;
                    reason.CreatedDate = DateTime.Now;
                    reason.UpdatedDate = DateTime.Now;

                    _reasonRepository.Insert(reason);

                    var statusInsert = _reasonRepository.SaveChanges();

                    if (statusInsert > 0)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Thêm mới thành công"));
                        return RedirectToAction("Index");
                    }
                    else
                    {
                        Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        return View(reasonModel);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("Reason/Create: " + ex.Message);

                    Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return View(reasonModel);
                }
            }
            Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;

            return View(reasonModel);
        }

        // GET: /supplier/Edit/5
        public ActionResult Edit(int id)
        {
            try
            {
                var reason = _reasonRepository.GetById(id);

                var reasonModel = _mapper.Map<Reason, ReasonModel>(reason);
                reasonModel.ListType = ToSelectList(Utilities.GetListTypeReason());

                if (reasonModel != null)
                {
                    return View(reasonModel);
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                    return RedirectToAction("Index");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Reason/Edit: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                return RedirectToAction("Index");
            }
        }

        // POST: /supplier/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit([Bind("Id, Name, Type, Description")] ReasonModel reasonModel)
        {
            StatusQuery Notification;
            if (ModelState.IsValid)
            {
                try
                {
                    var _systemUser = GetSesson();

                    if (_systemUser == null)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                        return RedirectToAction("Logout", "Security");
                    }

                    var reason = _reasonRepository.GetById(reasonModel.Id);

                    reason.Name = reasonModel.Name;
                    reason.Description = reasonModel.Description;
                    reason.Type = reasonModel.Type;
                    reason.UpdatedBy = _systemUser.Id;
                    reason.UpdatedDate = DateTime.Now;

                    //var _supplier = _mapper.Map<SupplierModel, Supplier>(supplierModel);

                    _reasonRepository.Update(reason);

                    var updateStatus = _reasonRepository.SaveChanges();

                    if (updateStatus > 0)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Sửa thành công"));

                        return RedirectToAction("Index");
                    }
                    else
                    {
                        Notification = new StatusQuery("error", "", "Sửa thất bại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        return View(reason);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("Reason/Edit: " + ex.Message);

                    Notification = new StatusQuery("error", "", "Sửa thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return View(reasonModel);
                }
            }

            Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;

            return View(reasonModel);
        }

        // GET: /supplier/Delete/5
        public ActionResult Delete(int id)
        {
            StatusQuery Notification;
            ReasonModel reasonModel = new ReasonModel();
            try
            {
                var reason = _reasonRepository.GetById(id);

                reasonModel = _mapper.Map<Reason, ReasonModel>(reason);

                if (reason == null)
                {
                    Notification = new StatusQuery("warning", "", "Không tìm thấy phòng ban");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return RedirectToAction("Index");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Reason/Delete: " + ex.Message);

                Notification = new StatusQuery("warning", "", "Xóa thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;

                return RedirectToAction("Index");
            }
            return View(reasonModel);
        }


        // POST: /Suppliers/Delete/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int id)
        {
            try
            {
                var systemUser = GetSesson();

                if (systemUser == null)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                    return RedirectToAction("Logout", "Security");
                }

                var reason = _reasonRepository.GetById(id);

                _reasonRepository.Delete(reason);

                var deleteStatus = _reasonRepository.SaveChanges();

                if (deleteStatus > 0)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Xóa thành công"));
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));
                }

                return RedirectToAction("Index");
            }
            catch (Exception ex)
            {
                _logger.LogError("Reason/DeleteConfirmed: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));
            }
            return RedirectToAction("Index");
        }

        [NonAction]
        private SelectList ToSelectList(List<DropDownList> DropDownList)
        {
            List<SelectListItem> list = new List<SelectListItem>();

            foreach (var item in DropDownList)
            {
                list.Add(new SelectListItem()
                {
                    Text = item.Name,
                    Value = item.Id.ToString()
                });
            }

            return new SelectList(list, "Value", "Text");
        }

        public User GetSesson()
        {
            var sessionUser = HttpContext.Session.GetString("SessionUserSystemId");
            if (sessionUser != null)
            {
                var _user = _userRepository.GetById(int.Parse(sessionUser));

                if (_user != null)
                {
                    if (!_user.Actived)
                    {
                        HttpContext.Session.Clear();
                        return null;
                    }
                    return _user;
                }
            }
            return null;
        }
    }
}
