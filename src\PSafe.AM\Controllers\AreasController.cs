﻿using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PSafe.AM.Common;
using PSafe.AM.Models;
using PSafe.Common;
using PSafe.Common.UserEnums;
using PSafe.Core.Domains;
using PSafe.Core.Interface;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using static PSafe.Common.CommonEnums;

namespace PSafe.AM.Controllers
{
    [AuthorizeActionFilter(EPERMISSIONS_AM.DeviceManage)]
    public class AreasController : Controller
    {
        private readonly IAreaRepository _areaRepository;
        private readonly ILocationRepository _locRepository;
        private readonly IUserRepository _userRepository;
        private readonly IDocumentRepository _documentRepository;
        private readonly IDocumentBeLongLocationRepository _documentBeLongLocationRepository;
        private readonly ICommandCenterRepository _commandCenterRepository;
        private readonly IHistorySystemRepository _historySystemRepository;
        private readonly IMapper _mapper;
        private readonly IHttpContextAccessor _accessor;
        private readonly IConfiguration _configuration;
        private readonly ILogger<AreasController> _logger;

        public AreasController(IAreaRepository areaRepository, ILocationRepository locRepository, ICommandCenterRepository commandCenterRepository, IUserRepository userRepository, IMapper mapper, IDocumentRepository documentRepository, IDocumentBeLongLocationRepository documentBeLongLocationRepository, IHistorySystemRepository historySystemRepository, IHttpContextAccessor accessor, ILogger<AreasController> logger, IConfiguration configuration)
        {
            _areaRepository = areaRepository;
            _locRepository = locRepository;
            _commandCenterRepository = commandCenterRepository;
            _userRepository = userRepository;
            _mapper = mapper;
            _documentRepository = documentRepository;
            _documentBeLongLocationRepository = documentBeLongLocationRepository;
            _historySystemRepository = historySystemRepository;
            _accessor = accessor;
            _configuration = configuration;
            _logger = logger;
        }

        public ActionResult Index()
        {
            StatusQuery Notification;
            try
            {
                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Area/Index: " + ex.Message);

                Notification = new StatusQuery("error", "Thất bại!", "Xem danh sách");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;
            }

            return View();
        }

        public JsonResult GetIndex()
        {
            List<Area> _listArea = new List<Area>();

            try
            {
                var listArea = _areaRepository.GetAll().ToList();
                return Json(listArea);
            }
            catch (Exception ex)
            {
                _logger.LogError("Area/GetIndex: " + ex.Message);
                return Json(_listArea);
            }
        }

        private void SetViewBagData(AreaModel _areaModel)
        {
            try
            {
                ViewBag.CreatedUser = _userRepository.GetById(_areaModel.CREATEDUSER).UserName;
                ViewBag.UpdatedUser = _userRepository.GetById(_areaModel.UPDATEDUSER).UserName;

            }
            catch (Exception ex)
            {
                _logger.LogError("Area/SetViewBagData: " + ex.Message);
                Console.WriteLine(ex.Message);
            }
        }

        public ActionResult Details(int id)
        {
            try
            {
                var _area = _areaRepository.GetById(id);
                var _areaModel = _mapper.Map<Area, AreaModel>(_area);
                SetViewBagData(_areaModel);

                if (TempDataHelper.Get<StatusQuery>(TempData, "Notification") is StatusQuery Notification)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }

                if (_areaModel != null)
                {
                    return View(_areaModel);
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "Thất bại", "Xem chi tiết"));

                    return RedirectToAction("Index");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Area/Details: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "Thất bại", "Xem chi tiết"));

                return RedirectToAction("Index");
            }
        }

        public ActionResult Create()
        {
            if (TempDataHelper.Get<StatusQuery>(TempData, "Notification") is StatusQuery Notification)
            {
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;
            }

            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create([Bind("BRANCHNAME, LATITUDE, LONGITUDE, REPRESENTATIVE, CONTACTPERSON, EMAIL, MESSAGEPHONE, FAX, DESCRIPTION, CONTACTPHONE, AREABORDER")] AreaModel areaModel)
        {
            if (areaModel.LATITUDE == 0)
            {
                ModelState.Remove("LATITUDE");
                ModelState.SetModelValue("LATITUDE", null, null);
                ModelState.AddModelError("LATITUDE", "Giá trị nhập vào chưa đúng");
            }
            if (areaModel.LONGITUDE == 0)
            {
                ModelState.Remove("LONGITUDE");
                ModelState.SetModelValue("LONGITUDE", null, null);
                ModelState.AddModelError("LONGITUDE", "Giá trị nhập vào chưa đúng");
            }

            StatusQuery Notification;
            if (ModelState.IsValid)
            {
                try
                {
                    var _systemUser = GetSesson();

                    if (_systemUser == null)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                        return RedirectToAction("Logout", "Security");
                    }

                    var area = _areaRepository.Get(p => p.AreaName.ToLower() == areaModel.BRANCHNAME.ToLower()).SingleOrDefault();

                    if (area != null)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("warning", "", "Vui lòng kiểm tra lại"));
                        ModelState.AddModelError("BRANCHNAME", "Tên khu vực đã tồn tại");

                        return View();
                    }

                    areaModel.CREATEDDATE = DateTime.Now;
                    areaModel.CREATEDUSER = _systemUser.Id;
                    areaModel.UPDATEDDATE = DateTime.Now;
                    areaModel.UPDATEDUSER = _systemUser.Id;

                    var _area = _mapper.Map<AreaModel, Area>(areaModel);

                    _areaRepository.Insert(_area);

                    var statusInsert = _areaRepository.SaveChanges();

                    if (statusInsert > 0)
                    {
                        string StringDescription = new GetStringHistorySystem().Get(_systemUser.UserName, Resources.Resource.AddItem, _area.AreaName.ToString(), Resources.Resource.Area);

                        InsertHistorySystem((int)EACTION_TYPE.CREATE, (int)EnumControllerName.AREAS, StringDescription, null, _area);

                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Thêm mới thành công"));
                        return RedirectToAction("Index");
                    }
                    else
                    {
                        Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        return View(areaModel);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("Area/Create: " + ex.Message);

                    Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return View(areaModel);
                }
            }
            Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;

            List<DropDownList> listDropDownListCommandCenter = new List<DropDownList>();

            var commandCenter = _commandCenterRepository.GetAll();

            foreach (var item in commandCenter)
            {
                DropDownList drop = new DropDownList
                {
                    Id = item.Id,
                    Name = item.CenterName
                };
                listDropDownListCommandCenter.Add(drop);
            }

            ViewBag.DropDownListCommandCenter = ToSelectList(listDropDownListCommandCenter);

            return View(areaModel);
        }

        private List<DropDownList> GetListDropDownListCommandCenter()
        {
            List<DropDownList> listDropDownListCommandCenter = new List<DropDownList>();
            try
            {
                var commandCenter = _commandCenterRepository.GetAll();

                foreach (var item in commandCenter)
                {
                    DropDownList drop = new DropDownList
                    {
                        Id = item.Id,
                        Name = item.CenterName
                    };
                    listDropDownListCommandCenter.Add(drop);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Area/GetListDropDownListCommandCenter: " + ex.Message);
                Console.WriteLine(ex.Message);
            }
            return listDropDownListCommandCenter;
        }

        public ActionResult Edit(int id)
        {
            StatusQuery Notification;
            try
            {
                var _area = _areaRepository.GetById(id);

                var _areaModel = _mapper.Map<Area, AreaModel>(_area);

                if (_areaModel != null)
                {
                    Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                    if (Notification != null)
                    {
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;
                    }

                    ViewBag.DropDownListCommandCenter = ToSelectList(GetListDropDownListCommandCenter());

                    var builder = new ConfigurationBuilder()
                                     .SetBasePath(Directory.GetCurrentDirectory())
                                     .AddJsonFile("appsettings.json");

                    var configuration = builder.Build();

                    ViewBag.linkLocalMaps = configuration["linkLocalMaps"];

                    return View(_areaModel);
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                    return RedirectToAction("Index");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Area/Edit: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                return RedirectToAction("Index");
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit([Bind("AREAID, BRANCHNAME, LATITUDE, LONGITUDE, REPRESENTATIVE, CONTACTPERSON, EMAIL, MESSAGEPHONE, FAX, DESCRIPTION, CONTACTPHONE, AREABORDER")] AreaModel areaModel)
        {
            StatusQuery Notification;

            if (areaModel.LATITUDE == 0)
            {
                ModelState.Remove("LATITUDE");
                ModelState.SetModelValue("LATITUDE", null, null);
                ModelState.AddModelError("LATITUDE", "Giá trị nhập vào chưa đúng");
            }
            if (areaModel.LONGITUDE == 0)
            {
                ModelState.Remove("LONGITUDE");
                ModelState.SetModelValue("LONGITUDE", null, null);
                ModelState.AddModelError("LONGITUDE", "Giá trị nhập vào chưa đúng");
            }

            if (ModelState.IsValid)
            {
                try
                {
                    var area = _areaRepository.GetById(areaModel.AREAID);

                    var _areaTemp = _mapper.Map<Area, AreaModel>(area);
                    var areaOld = _mapper.Map<AreaModel, Area>(_areaTemp);

                    var _systemUserSession = GetSesson();
                    var _systemUser = GetSesson();


                    if (_systemUser == null)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                        return RedirectToAction("Logout", "Security");
                    }

                    area.AreaName = areaModel.BRANCHNAME;
                    area.Latitude = areaModel.LATITUDE;
                    area.Longitude = areaModel.LONGITUDE;
                    area.Representative = areaModel.REPRESENTATIVE;
                    area.ContactPerson = areaModel.CONTACTPERSON;
                    area.Email = areaModel.EMAIL;
                    area.ContactPhone = areaModel.CONTACTPHONE;
                    area.MessagePhone = areaModel.MESSAGEPHONE;
                    area.Description = areaModel.DESCRIPTION;
                    area.AreaBorder = areaModel.AREABORDER;
                    area.UpdatedDate = DateTime.Now;
                    area.UpdatedBy = _systemUserSession.Id;

                    _areaRepository.Update(area);

                    var updateStatus = _areaRepository.SaveChanges();

                    if (updateStatus > 0)
                    {
                        string StringDescription = new GetStringHistorySystem().Get(_systemUser.UserName, Resources.Resource.Edit, area.AreaName.ToString(), Resources.Resource.Area);

                        InsertHistorySystem((int)EACTION_TYPE.EDIT, (int)EnumControllerName.AREAS, StringDescription, areaOld, area);

                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Sửa thành công"));

                        return RedirectToAction("Index");
                    }
                    else
                    {
                        Notification = new StatusQuery("error", "", "Sửa thất bại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        return View(areaModel);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("Area/Edit: " + ex.Message);

                    Notification = new StatusQuery("error", "", "Sửa thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    ViewBag.DropDownListCommandCenter = ToSelectList(GetListDropDownListCommandCenter());
                    return View(areaModel);
                }
            }

            Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;

            ViewBag.DropDownListCommandCenter = ToSelectList(GetListDropDownListCommandCenter());

            return View(areaModel);
        }

        public ActionResult Delete(int id)
        {
            StatusQuery Notification;
            try
            {
                var _area = _areaRepository.GetById(id);

                var _areaModel = _mapper.Map<Area, AreaModel>(_area);

                try
                {
                    ViewBag.CreatedUser = _userRepository.GetById(_areaModel.CREATEDUSER).UserName;
                    ViewBag.UpdatedUser = _userRepository.GetById(_areaModel.UPDATEDUSER).UserName;
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex.Message);
                }

                if (_areaModel == null)
                {
                    Notification = new StatusQuery("warning", "", "Không tìm thấy khu vực");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return RedirectToAction("Index");
                }

                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }

                return View(_areaModel);
            }
            catch (Exception ex)
            {
                _logger.LogError("Area/Delete: " + ex.Message);

                Notification = new StatusQuery("warning", "", "Xóa thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;

                return RedirectToAction("Index");
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int id)
        {
            try
            {
                var systemUser = GetSesson();

                if (systemUser == null)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                    return RedirectToAction("Logout", "Security");
                }

                var area = _areaRepository.GetById(id);

                _areaRepository.Delete(area);

                var deleteStatus = _areaRepository.SaveChanges();

                if (deleteStatus > 0)
                {
                    string StringDescription = new GetStringHistorySystem().Get(systemUser.UserName, Resources.Resource.Delete, area.AreaName.ToString(), Resources.Resource.Area);

                    InsertHistorySystem((int)EACTION_TYPE.DELETE, (int)EnumControllerName.AREAS, StringDescription, area, null);

                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Xóa thành công"));
                    return RedirectToAction("Index");
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));
                    return RedirectToAction("Delete", "Areas", new { id });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Area/Delete: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));
                return RedirectToAction("Delete", "Areas", new { id });
            }
        }

        public JsonResult LocationList(int areaId)
        {
            var locations = _locRepository.GetBy(item => item.AreaId == areaId).ToList();

            return Json(locations);

        }

        public ActionResult CreateLocation(int areaId)
        {
            LocationModel locationModel = new LocationModel();
            try
            {
                locationModel.ListDocumentNotOnLocation = new List<Document>();

                var document = _documentRepository.GetAll().ToList();

                locationModel.AreaId = areaId;
                foreach (var item in document)
                {
                    locationModel.ListDocumentNotOnLocation.Add(item);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Area/CreateLocation: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Tải lên tài liệu thất bại"));

                return RedirectToAction("Index");
            }

            return View("_CreateLocationPartial", locationModel);
        }

        Location MapToModel(LocationModel locationModel)
        {
            return _mapper.Map<LocationModel, Location>(locationModel);
        }

        private LocationModel MapToViewModel(Location location)
        {
            return _mapper.Map<Location, LocationModel>(location);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult CreateLocation(LocationModel locationModel, List<int> document_select)
        {
            StatusQuery notification;

            if (locationModel.LocationName == null)
            {
                ModelState.AddModelError("LocationName", "Vui lòng nhập tên vị trí");
            }
            else if (locationModel.LocationName.Length < 3)
            {
                ModelState.AddModelError("LocationName", "Độ dài từ 3-255 ký tự");
            }

            if (ModelState.IsValid)
            {
                try
                {
                    var area = _areaRepository.GetById(locationModel.AreaId);

                    if (locationModel.ImageFile != null && locationModel.ImageFile.FileName.Length > 0)
                    {
                        var fileName = DateTime.Now.Date.ToString("MM_dd_yyyy") + "_" + locationModel.ImageFile.FileName.Substring(locationModel.ImageFile.FileName.LastIndexOf("\\") + 1);
                        locationModel.Map = fileName;

                        var userNameFTP = Utils.DecodePassword(_configuration.GetSection("FTP:UserName").Value, Utils.EncodeType.SHA_256);
                        var passwordFTP = Utils.DecodePassword(_configuration.GetSection("FTP:Password").Value, Utils.EncodeType.SHA_256);
                        var hostFTP = _configuration.GetSection("FTP:Host").Value + ":" + _configuration.GetSection("FTP:Port").Value;

                        byte[] fileBytes;

                        using (var ms = new MemoryStream())
                        {
                            locationModel.ImageFile.CopyTo(ms);
                            fileBytes = ms.ToArray();
                        }

                        using (var client = new WebClient())
                        {
                            client.Credentials = new NetworkCredential(userNameFTP, passwordFTP);
                            client.UploadData(hostFTP + "//Images//" + fileName, fileBytes);
                        }
                    }

                    var _systemUser = GetSesson();

                    if (_systemUser == null)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                        return RedirectToAction("Logout", "Security");
                    }

                    locationModel.LONGITUDE = area.Longitude;
                    locationModel.LATITUDE = area.Latitude;
                    locationModel.CreatedDate = DateTime.Now;
                    locationModel.CreatedBy = _systemUser.Id;
                    locationModel.UpdatedDate = DateTime.Now;
                    locationModel.UpdatedBy = _systemUser.Id;
                    locationModel.Actived = true;

                    var locationTemp = _mapper.Map<LocationModel, Location>(locationModel);

                    _locRepository.Insert(locationTemp);

                    var statusInsert = _locRepository.SaveChanges();

                    if (statusInsert > 0)
                    {
                        int LocationId = (int)locationTemp.Id;

                        string StringDescription = new GetStringHistorySystem().Get(_systemUser.UserName, Resources.Resource.Create, locationTemp.LocationName.ToString(), Resources.Resource.Location + " trong " + Resources.Resource.Area);

                        InsertHistorySystem((int)EACTION_TYPE.CREATE, (int)EnumControllerName.LOCATIONS, StringDescription, null, locationTemp);

                        // Thêm danh sách chọn từ font-end
                        List<DocumentBeLongLocation> listDocumentBeLongLocationNew = new List<DocumentBeLongLocation>();
                        foreach (var item_select in document_select)
                        {
                            DocumentBeLongLocation documentBeLongLocation = new DocumentBeLongLocation
                            {
                                DocumentId = item_select,
                                LocationId = LocationId
                            };
                            listDocumentBeLongLocationNew.Add(documentBeLongLocation);
                            _documentBeLongLocationRepository.Insert(documentBeLongLocation);
                        }

                        // Cập nhật BeLong
                        var updateDocument = _documentBeLongLocationRepository.SaveChanges();

                        if (updateDocument > 0)
                        {
                            // ghi log documentBeLong
                            string StringDescription_History = new GetStringHistorySystem().Get(_systemUser.UserName, Resources.Resource.Create, Resources.Resource.Document, Resources.Resource.Location + " trong " + Resources.Resource.Area);

                            InsertHistorySystem((int)EACTION_TYPE.CREATE, (int)EnumControllerName.DOCUMENTS_BE_LONG, StringDescription_History, null, listDocumentBeLongLocationNew);
                        }

                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Thêm mới thành công"));
                        return RedirectToAction("Edit", "Areas", new { id = locationModel.AreaId });
                    }
                    else
                    {
                        notification = new StatusQuery("error", "", "Thêm mới thất bại");
                        ViewBag.Status = notification.Status;
                        ViewBag.Value = notification.Value;
                        ViewBag.Type = notification.Type;

                        return RedirectToAction("Index", "Areas");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("Area/CreateLocation: " + ex.Message);

                    notification = new StatusQuery("error", "", "Thêm mới thất bại");
                    ViewBag.Status = notification.Status;
                    ViewBag.Value = notification.Value;
                    ViewBag.Type = notification.Type;

                    return RedirectToAction("Edit", "Areas", new { id = locationModel.AreaId });
                }
            }
            notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            ViewBag.Status = notification.Status;
            ViewBag.Value = notification.Value;
            ViewBag.Type = notification.Type;

            return View("_CreateLocationPartial", locationModel);
        }

        public ActionResult EditLocation(int id)
        {
            try
            {
                var location = _locRepository.GetById(id);

                var document = _documentRepository.GetAll().ToList();
                var documentBeLongLocation = _documentBeLongLocationRepository.GetAll().Where(p => p.LocationId == id).ToList();
                var locationTemp = _mapper.Map<Location, LocationModel>(location);
                bool check = false;

                locationTemp.ListDocumentNotOnLocation = new List<Document>();
                locationTemp.ListDocumentOnLocation = new List<Document>();

                foreach (var item in document)
                {
                    check = false;
                    foreach (var itemOnLocation in documentBeLongLocation)
                    {
                        if (itemOnLocation.DocumentId == item.Id)
                        {
                            check = true;
                        }
                    }

                    if (check)
                    {
                        locationTemp.ListDocumentOnLocation.Add(item);
                    }
                    else
                    {
                        locationTemp.ListDocumentNotOnLocation.Add(item);
                    }
                }

                var isAjaxCall = Request.Headers["X-Requested-With"] == "XMLHttpRequest";

                if (isAjaxCall)
                {
                    return PartialView("_EditLocationPartial", locationTemp);
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                    return RedirectToAction("Edit");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Area/EditLocation: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                return RedirectToAction("Edit");
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult EditLocation([Bind("LocationId, AreaId, LocationName, Map, ImageFile, Description, Actived")] LocationModel locationModel, List<int> document_select)
        {
            if (ModelState.IsValid)
            {

                var areaId = locationModel.AreaId;

                try
                {
                    var _systemUserSesson = GetSesson();

                    if (_systemUserSesson == null)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                        return RedirectToAction("Logout", "Security");
                    }

                    var location = _locRepository.GetById(locationModel.LocationId);

                    var locationTemp = _mapper.Map<Location, LocationModel>(location);
                    var locationOld = _mapper.Map<LocationModel, Location>(locationTemp);

                    areaId = location.AreaId;

                    location.LocationName = locationModel.LocationName;
                    location.Description = locationModel.Description;
                    location.Actived = locationModel.Actived;

                    if (locationModel.ImageFile != null && locationModel.ImageFile.FileName.Length > 0)
                    {
                        var fileName = DateTime.Now.Date.ToString("MM_dd_yyyy") + "_" + locationModel.ImageFile.FileName.Substring(locationModel.ImageFile.FileName.LastIndexOf("\\") + 1);
                        location.Map = fileName;

                        var userNameFTP = Utils.DecodePassword(_configuration.GetSection("FTP:UserName").Value, Utils.EncodeType.SHA_256);
                        var passwordFTP = Utils.DecodePassword(_configuration.GetSection("FTP:Password").Value, Utils.EncodeType.SHA_256);
                        var hostFTP = _configuration.GetSection("FTP:Host").Value + ":" + _configuration.GetSection("FTP:Port").Value;

                        byte[] fileBytes;

                        using (var ms = new MemoryStream())
                        {
                            locationModel.ImageFile.CopyTo(ms);
                            fileBytes = ms.ToArray();
                        }

                        using (var client = new WebClient())
                        {
                            client.Credentials = new NetworkCredential(userNameFTP, passwordFTP);
                            client.UploadData(hostFTP + "//Images//" + fileName, fileBytes);
                        }
                    }

                    location.UpdatedDate = DateTime.Now;
                    location.UpdatedBy = _systemUserSesson.Id;

                    // Get danh sách belong của location này
                    var listDocumentBeLongLocationOld = _documentBeLongLocationRepository.GetAll().Where(p => p.LocationId == locationModel.LocationId);

                    // Xóa hết belong thuộc location này
                    foreach (var item in listDocumentBeLongLocationOld)
                    {
                        _documentBeLongLocationRepository.Delete(item);
                    }

                    // Thêm danh sách chọn từ font-end
                    List<DocumentBeLongLocation> listDocumentBeLongLocationNew = new List<DocumentBeLongLocation>();
                    foreach (var item_select in document_select)
                    {
                        DocumentBeLongLocation documentBeLongLocation = new DocumentBeLongLocation
                        {
                            DocumentId = item_select,
                            LocationId = locationModel.LocationId
                        };
                        listDocumentBeLongLocationNew.Add(documentBeLongLocation);
                        _documentBeLongLocationRepository.Insert(documentBeLongLocation);
                    }

                    // Cập nhật BeLong
                    var updateDocument = _documentBeLongLocationRepository.SaveChanges();

                    if (updateDocument > 0)
                    {
                        // ghi log documentBeLong
                        string StringDescription = new GetStringHistorySystem().Get(_systemUserSesson.UserName, Resources.Resource.Edit, "Liên kết tài liệu", Resources.Resource.Location + " trong " + Resources.Resource.Area);

                        InsertHistorySystem((int)EACTION_TYPE.EDIT, (int)EnumControllerName.DOCUMENTS_BE_LONG, StringDescription, listDocumentBeLongLocationOld, listDocumentBeLongLocationNew);
                    }

                    _locRepository.Update(location);

                    var updateStatusLoc = _locRepository.SaveChanges();

                    if (updateStatusLoc > 0)
                    {
                        string StringDescription = new GetStringHistorySystem().Get(_systemUserSesson.UserName, Resources.Resource.Edit, location.LocationName.ToString(), Resources.Resource.Location + " trong " + Resources.Resource.Area);

                        InsertHistorySystem((int)EACTION_TYPE.EDIT, (int)EnumControllerName.LOCATIONS, StringDescription, locationOld, location);

                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Sửa thành công"));

                        return RedirectToAction("Edit", "Areas", new { id = areaId });
                    }
                    else
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Sửa thất bại"));

                        return RedirectToAction("Edit", "Areas", new { id = areaId });
                    }
                }
                catch (Exception ex)
                {

                    _logger.LogError("Area/EditLocation: " + ex.Message);
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Sửa thất bại"));

                    return RedirectToAction("Edit", "Areas", new { id = areaId });
                }
            }

            TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("warning", "", "Vui lòng kiểm tra lại"));
            return RedirectToAction("Edit", "Areas", new { id = locationModel.AreaId });
        }

        public ActionResult DeleteLocation(int id)
        {
            try
            {
                var location = _locRepository.GetById(id);

                var locationTemp = _mapper.Map<Location, LocationModel>(location);

                try
                {
                    if (locationTemp.AreaId != null)
                    {
                        ViewBag.AreaName = _areaRepository.GetById(locationTemp.AreaId).AreaName;
                    }

                    ViewBag.UPDATEDUSER = _userRepository.GetById(locationTemp.UpdatedBy).UserName;
                    ViewBag.CREATEDUSER = _userRepository.GetById(locationTemp.CreatedBy).UserName;
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex.Message);
                }

                var isAjaxCall = Request.Headers["X-Requested-With"] == "XMLHttpRequest";

                if (isAjaxCall)
                    return PartialView("_DeleteLocationPartial", locationTemp);

                return View("_DeleteLocationPartial", locationTemp);
            }
            catch (Exception ex)
            {
                _logger.LogError("Area/DeleteLocation: " + ex.Message);
                return RedirectToAction("Edit");
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteLocationConfirmed(int LocationId, int AreaId)
        {
            try
            {
                var location = _locRepository.GetById(LocationId);

                _locRepository.Delete(location);

                var deleteStatus = _locRepository.SaveChanges();

                if (deleteStatus > 0)
                {
                    string StringDescription = new GetStringHistorySystem().Get(GetSesson().UserName, Resources.Resource.Delete, location.LocationName.ToString(), Resources.Resource.Location + " trong " + Resources.Resource.Area);

                    InsertHistorySystem((int)EACTION_TYPE.DELETE, (int)EnumControllerName.LOCATIONS, StringDescription, location, null);

                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Xóa thành công"));

                    return RedirectToAction("Edit", "Areas", new { id = location.AreaId });
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));
                    return RedirectToAction("Edit", "Areas", new { id = location.AreaId });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Area/DeleteLocationConfirmed: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));

                if (AreaId != 0)
                {
                    return RedirectToAction("Edit", "Areas", new { id = AreaId });
                }

                return RedirectToAction("Index", "Areas");
            }
        }

        [HttpGet]
        public JsonResult GetArea(int id)
        {
            var _area = _areaRepository.GetBy(item => item.Id == id).FirstOrDefault();

            return Json(_area);
        }

        private List<LocationModel> GetLocations(int id)
        {
            var lstLocations = _locRepository.GetBy(item => item.AreaId == id);
            var tempLocations = new List<LocationModel>();

            if (lstLocations != null)
            {
                foreach (var location in lstLocations)
                {
                    tempLocations.Add(MapToViewModel(location));
                }
            }

            return tempLocations;
        }

        [NonAction]
        private SelectList ToSelectList(List<DropDownList> DropDownList)
        {
            List<SelectListItem> list = new List<SelectListItem>();

            foreach (var item in DropDownList)
            {
                list.Add(new SelectListItem()
                {
                    Text = item.Name,
                    Value = item.Id.ToString()
                });
            }

            return new SelectList(list, "Value", "Text");
        }

        public User GetSesson()
        {
            var SessionUserSystem = HttpContext.Session.GetComplexData<User>("SessionUserSystemId_" + HttpContext.Session.GetString("SessionUserSystemId"));
            if (SessionUserSystem != null)
            {
                var _user = _userRepository.GetById(SessionUserSystem.Id);

                if (_user != null)
                {
                    if (!_user.Actived)
                    {
                        HttpContext.Session.Clear();
                        return null;
                    }
                    return _user;
                }
            }
            return null;
        }

        private void InsertHistorySystem(int action_type, int controllerName, string description, object OldModel, object NewModel)
        {
            try
            {
                var ipAddress = _accessor.HttpContext.Connection.RemoteIpAddress.ToString();

                string jsonOldObject = Newtonsoft.Json.JsonConvert.SerializeObject(OldModel);
                string jsonNewObject = Newtonsoft.Json.JsonConvert.SerializeObject(NewModel);

                HistorySystem history = new HistorySystem
                {
                    ActionType = action_type,
                    ActionTime = DateTime.Now,
                    Description = description,
                    OldObject = jsonOldObject,
                    NewObject = jsonNewObject,
                    UserId = int.Parse(HttpContext.Session.GetString("SessionUserSystemId")),
                    IpAddress = ipAddress,
                    ControllerName = controllerName
                };

                _historySystemRepository.Insert(history);

                _historySystemRepository.SaveChanges();
            }
            catch (Exception ex)
            {
                _logger.LogError("Area/InsertHistorySystem: " + ex.Message);
            }
        }
    }
}
