﻿@model IEnumerable<PSafe.AM.Models.HistorySystemModel>
@{
    ViewBag.Title = "Danh sách";
}

<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-lg-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5><PERSON>h sách @PSafe.AM.Resources.Resource.HistorySystem</h5>
                    <div class="ibox-tools">
                    </div>
                </div>
                <div class="ibox-content">
                    @using (Html.BeginForm("Index", "HistorySystems", FormMethod.Post))
                    {
                        @Html.AntiForgeryToken()
                        @Html.ValidationSummary(true)
                        <div class="row">

                            <div class="col-lg-5">

                                <div class="form-group row" id="data_5">
                                    <label class="control-label col-md-4" style="margin: auto; text-align: right;" for="ActionTime">@Html.DisplayNameFor(model => model.ActionTime)</label>
                                    <div class="input-daterange input-group col-md-8" id="datepicker">
                                        <input type="text" class="form-control" id="start" name="start" value="@ViewBag.Start" />
                                        <span class="input-group-addon" style="min-width: 50px;">đến</span>
                                        <input type="text" class="form-control" id="end" name="end" value="@ViewBag.End" />
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-5">
                                <div class="form-group row">
                                    <label class="control-label col-md-4" style="margin: auto;  text-align: right;" for="UserId">@Html.DisplayNameFor(model => model.UserId)</label>
                                    <div class="col-md-8">
                                        @Html.DropDownList("UserId", ViewBag.ListUser as SelectList, new { @class = "form-control" })
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-2">
                                <div class="form-group row">
                                    <input class="btn btn-primary btn-sm" type="submit" value="Tìm kiếm" />
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12">
            <div class="ibox">
                <div class="ibox-title">
                    <div class="ibox-tools">
                    </div>
                </div>
                <div class="ibox-content">
                    <table class="table table-striped table-bordered table-hover dataTables-list" id="dataTables-list">
                        <thead>
                            <tr>
                                <th hidden>

                                </th>
                                <th>
                                    @Html.DisplayNameFor(model => model.ActionTime)
                                </th>
                                <th>
                                    @Html.DisplayNameFor(model => model.ActionType)
                                </th>
                                <th>
                                    @Html.DisplayNameFor(model => model.IpAddress)
                                </th>
                                <th>
                                    @Html.DisplayNameFor(model => model.Description)
                                </th>
                                <th>
                                    @Html.DisplayNameFor(model => model.UserId)
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var item in Model)
                            {
                            <tr data-ctr-type="@item.ControllerType">
                                <td hidden>
                                    @item.Id
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.ActionTime)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.ActionTypeName)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.IpAddress)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.Description)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.UserName)
                                </td>
                            </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <environment names="Development,Staging,Production">
        <link rel="stylesheet" href="~/lib/dataTables/datatables.min.css" />
        <link rel="stylesheet" href="~/lib/toastr/toastr.min.css" />
    </environment>

    <environment names="Development">
        <link rel="stylesheet" href="~/lib/daterangepicker/daterangepicker-bs3.css" />
        <link rel="stylesheet" href="~/lib/bootstrap-datepicker/dist/css/bootstrap-datepicker3.css" />
    </environment>
    <environment names="Staging,Production">
        <link rel="stylesheet" href="~/lib/daterangepicker/daterangepicker-bs3.css" />
        <link rel="stylesheet" href="~/lib/bootstrap-datepicker/dist/css/bootstrap-datepicker3.min.css" />
        <link rel="stylesheet" href="~/lib/cropper/dist/cropper.min.css" />
        <link rel="stylesheet" href="~/lib/select2/dist/css/select2.min.css" />
        <link rel="stylesheet" href="~/lib/bootstrap-touchspin/dist/jquery.bootstrap-touchspin.min.css" />
        <link rel="stylesheet" href="~/lib/bootstrap-tagsinput/bootstrap-tagsinput.css" />
    </environment>
}

@section Scripts {
    <environment names="Development">
        <script src="~/lib/daterangepicker/daterangepicker.js"></script>
        <script src="~/lib/bootstrap-datepicker/dist/js/bootstrap-datepicker.js"></script>
    </environment>

    <environment names="Development,Staging,Production">
        <script src="~/lib/dataTables/datatables.min.js"></script>
        <script src="~/lib/dataTables/dataTables.bootstrap4.min.js"></script>
        <script src="~/lib/toastr/toastr.js"></script>
        <script src="~/lib/toastr/toastr.min.js"></script>
    </environment>

    <environment names="Staging,Production">
        <script src="~/lib/daterangepicker/daterangepicker.js"></script>
        <script src="~/lib/bootstrap-datepicker/dist/js/bootstrap-datepicker.min.js"></script>
    </environment>

    @if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "warning")
    {
        <script type="text/javascript">
            toastr.warning('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "error")
    {
        <script type="text/javascript">
            toastr.error('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "success")
    {
        <script type="text/javascript">
            toastr.success('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }

    <script type="text/javascript">
        var vehicleBannedListControlerType = "@((int)PSafe.AM.Common.EnumControllerName.BANNED_LIST)";
        $(document).ready(function () {
            var table = $('.dataTables-list').DataTable({
                "order": [[ 0, "desc" ]],
                "oLanguage": {
                    "sProcessing": "Đang xử lý...",
                    "sLengthMenu": "Xem _MENU_ mục",
                    "sZeroRecords": "Không tìm thấy dòng nào phù hợp",
                    "sInfo": "Đang xem _START_ đến _END_ trong tổng số _TOTAL_ mục",
                    "sInfoEmpty": "Đang xem 0 đến 0 trong tổng số 0 mục",
                    "sInfoFiltered": "(được lọc từ _MAX_ mục)",
                    "sInfoPostFix": "",
                    "sLoadingRecords": "Đang tải dữ liệu...",
                    "sSearch": "Tìm:",
                    "sUrl": "",
                    "oPaginate": {
                        "sFirst": "Đầu",
                        "sPrevious": "Trước",
                        "sNext": "Tiếp",
                        "sLast": "Cuối"
                    }
                },
                commandCenterModel: pageLength_dataTable(),
                dom: '<"html5buttons"B>lTfgitp',
                buttons: [
                    { extend: 'copy' },
                    { extend: 'csv' },
                    { extend: 'excel', title: 'ExampleFile' },
                    { extend: 'pdf', title: 'ExampleFile' },
                    {
                        extend: 'print',
                        customize: function (win) {
                            $(win.document.body).addClass('white-bg');
                            $(win.document.body).css('font-size', '8px');

                            $(win.document.body).find('table')
                                .addClass('compact')
                                .css('font-size', 'inherit');
                        }
                    }
                ]
            });

            $('#dataTables-list tbody').on('dblclick', 'tr', function () {
                var _data = table.row(this).data();

                if (this.getAttribute("data-ctr-type") == vehicleBannedListControlerType) {
                    var plateNumber = false;
                    var isDel = _data[2] == "DELETE";
                    if (isDel || _data[2] == "EDIT_HISTORY") {
                        var url = "/HistorySystems/Detail?id=" + _data[0];
                        window.open(url, "_blank"); 
                        return;
                    }

                    if (_data[2] == "BANNED") {
                        var index = _data[4].indexOf("Cấm xe ");
                        if (index !== -1) {
                            var endIndex = _data[4].indexOf(" ", index + 9);
                            plateNumber = _data[4].substring(index + 7, endIndex);
                        }
                    } else if (_data[2] == "CLEAR_BANNED" || isDel) {
                        var index = _data[4].indexOf("cấm xe ");
                        if (index !== -1) {
                            var endIndex = _data[4].indexOf(" ", index + 9);
                            plateNumber = _data[4].substring(index + 7, endIndex);
                        }
                    } else if (_data[2] == "EDIT") {
                        var index = _data[4].indexOf("xe cấm ");
                        if (index !== -1) {
                            var endIndex = _data[4].indexOf(" ", index + 9);
                            if (endIndex == -1) endIndex = _data[4].length;
                            plateNumber = _data[4].substring(index + 7, endIndex);
                        }
                    }
                    if (!plateNumber) return;
                    var url = "/VehicleBannedList?plate=" + $.trim(plateNumber) + (isDel ? "&deleted=1" : "");
                    window.open(url, "_blank"); 
                    return;
                }

                switch (_data[2]) {
                    case "LOGIN":
                        alert("Tài khoản '" + _data[5] + "' đã đăng nhập lúc '" + _data[1] + "'");
                        break;
                    default:
                        var url = "/HistorySystems/Detail?id=" + _data[0];
                        window.location.href = url;
                        break;
                }
            });


        });

    </script>

    <script type="text/javascript">
        $('#data_5 .input-daterange').datepicker({
            keyboardNavigation: false,
            forceParse: false,
            autoclose: true
        });

        $('#data_5 .input-daterange').change(function (){
            var startDate = new Date($('#start').val());
            var endDate = new Date($('#end').val());

            if(startDate.getTime() > endDate.getTime()) {
                $('#end').val() = $('#start').val();
            } 
        });
    </script>
}
