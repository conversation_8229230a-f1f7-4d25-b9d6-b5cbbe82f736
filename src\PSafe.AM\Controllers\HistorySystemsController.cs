﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Newtonsoft.Json;
using PSafe.AM.Common;
using PSafe.AM.Models;
using PSafe.Common.UserEnums;
using PSafe.Core.Domains;
using PSafe.Core.Interface;
using PSafe.Core.SharedKernel;
using static PSafe.Common.CommonEnums;

namespace PSafe.AM.Controllers
{
    [AuthorizeActionFilter(EPERMISSIONS_AM.ConfigurationGeneralManage)]
    public class HistorySystemsController : Controller
    {
        private readonly IHistorySystemRepository _historySystemRepository;
        private readonly IUserRepository _userRepository;

        public HistorySystemsController(IUnitOfWork unitOfWork)
        {
            _historySystemRepository = unitOfWork.HistorySystemRepository;
            _userRepository = unitOfWork.UserRepository;
        }

        [NonAction]
        private SelectList ToSelectList(List<DropDownList> DropDownList)
        {
            List<SelectListItem> list = new List<SelectListItem>();

            SelectListItem Default = new SelectListItem()
            {
                Text = "Chọn",
                Value = "-1"
            };

            list.Add(Default);

            foreach (var item in DropDownList)
            {
                list.Add(new SelectListItem()
                {
                    Text = item.Name,
                    Value = item.Id.ToString()
                });
            }

            return new SelectList(list, "Value", "Text");
        }

        public ActionResult Index()
        {
            StatusQuery Notification;
            try
            {
                var listHistotySystems = _historySystemRepository.GetAll().ToList().Join(
                _userRepository.GetAll().ToList(),
                p => p.UserId,
                c => c.Id,
                (p, c) => new HistorySystemModel
                {
                    UserName = c.UserName,
                    ActionTime = p.ActionTime,
                    ActionTypeName = ((EACTION_TYPE)(p.ActionType)).ToString(),
                    Description = p.Description,
                    Id = p.Id,
                    IpAddress = p.IpAddress,
                    OldObject = p.OldObject,
                    NewObject = p.NewObject,
                    ActionType = p.ActionType,
                    UserId = p.UserId,
                    ControllerType = p.ControllerName
                }).Where(p => p.ActionTime.ToString("dd-MM-yyyy") == DateTime.Now.ToString("dd-MM-yyyy")).ToList();

                var listSystemUsers = _userRepository.GetAll().ToList().Select(p => new DropDownList
                {
                    Id = p.Id,
                    Name = p.UserName
                }).ToList();

                if (listSystemUsers != null)
                {
                    ViewBag.ListUser = ToSelectList(listSystemUsers);
                }

                ViewBag.Start = DateTime.Now.ToString("MM/dd/yyyy");
                ViewBag.End = DateTime.Now.ToString("MM/dd/yyyy");


                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }

                return View(listHistotySystems);
            }
            catch(Exception ex)
            {
                Notification = new StatusQuery("error", "Thất bại!", "Xem danh sách");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;

                return View(new List<HistorySystemModel>());
            }
        }

        public JsonResult GetIndex()
        {
            try
            {
                var listHistotySystems = _historySystemRepository.GetAll().Join(
                _userRepository.GetAll(),
                p => p.UserId,
                c => c.Id,
                (p, c) => new HistorySystemModel
                {
                    UserName = c.UserName,
                    ActionTime = p.ActionTime,
                    ActionTypeName = ((EACTION_TYPE)(p.ActionType)).ToString(),
                    Description = p.Description,
                    Id = p.Id,
                    IpAddress = p.IpAddress,
                    OldObject = p.OldObject,
                    NewObject = p.NewObject,
                    ActionType = p.ActionType,
                    UserId = p.UserId,
                    ControllerType = p.ControllerName
                }).ToList();

                return Json(listHistotySystems);
            }
            catch
            {
                return Json(null);
            }
        }

        [HttpPost]
        public ActionResult Index(DateTime Start, DateTime End, int UserId)
        {
            var start = Start.ToString("MM/dd/yyyy");
            var end = End.ToString("MM/dd/yyyy");

            List<HistorySystemModel> listHistotySystems = null;
            if (Start != DateTime.MinValue && End != DateTime.MinValue || UserId != -1)
            {
                if (UserId != -1)
                {
                    listHistotySystems = _historySystemRepository.GetAll().Join(
                    _userRepository.GetAll(),
                    p => p.UserId,
                    c => c.Id,
                    (p, c) => new HistorySystemModel
                    {
                        UserName = c.UserName,
                        ActionTime = p.ActionTime,
                        ActionTypeName = ((EACTION_TYPE)(p.ActionType)).ToString(),
                        Description = p.Description,
                        Id = p.Id,
                        IpAddress = p.IpAddress,
                        OldObject = p.OldObject,
                        NewObject = p.NewObject,
                        ActionType = p.ActionType,
                        UserId = p.UserId
                    }).Where(p => p.UserId == UserId && p.ActionTime.Date >= Start && p.ActionTime.Date <= End).ToList();
                }
                else
                {
                    listHistotySystems = _historySystemRepository.GetAll().Join(
                    _userRepository.GetAll(),
                    p => p.UserId,
                    c => c.Id,
                    (p, c) => new HistorySystemModel
                    {
                        UserName = c.UserName,
                        ActionTime = p.ActionTime,
                        ActionTypeName = ((EACTION_TYPE)(p.ActionType)).ToString(),
                        Description = p.Description,
                        Id = p.Id,
                        IpAddress = p.IpAddress,
                        OldObject = p.OldObject,
                        NewObject = p.NewObject,
                        ActionType = p.ActionType,
                        UserId = p.UserId
                    }).Where(p => p.ActionTime.Date >= Start && p.ActionTime.Date <= End).ToList();
                }
            }
            else
            {
                listHistotySystems = _historySystemRepository.GetAll().Join(
                    _userRepository.GetAll(),
                    p => p.UserId,
                    c => c.Id,
                    (p, c) => new HistorySystemModel
                    {
                        UserName = c.UserName,
                        ActionTime = p.ActionTime,
                        ActionTypeName = ((EACTION_TYPE)(p.ActionType)).ToString(),
                        Description = p.Description,
                        Id = p.Id,
                        IpAddress = p.IpAddress,
                        OldObject = p.OldObject,
                        NewObject = p.NewObject,
                        ActionType = p.ActionType,
                        UserId = p.UserId
                    }).ToList();
            }

            var listSystemUsers = _userRepository.GetAll().Select(p => new DropDownList
            {
                Id = p.Id,
                Name = p.UserName
            }).ToList();

            ViewBag.UserId = 1;
            ViewBag.Start = start;
            ViewBag.End = end;

            if (listSystemUsers != null)
            {
                var listSelected = ToSelectList(listSystemUsers);

                foreach (SelectListItem item in listSelected.Items)
                {
                    if (item.Value == UserId.ToString())
                    {
                        item.Selected = true;
                        break;
                    }
                }

                ViewBag.ListUser = listSelected;
            }

            return View(listHistotySystems);
        }

        public ActionResult Detail(int id)
        {
            try
            {
                var historySystem = _historySystemRepository.GetById(id);

                if (TempDataHelper.Get<StatusQuery>(TempData, "Notification") is StatusQuery Notification)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }

                string actionTypeName = "";
                switch(historySystem.ActionType)
                {
                    case (int)EACTION_TYPE.CREATE:
                        actionTypeName = PSafe.AM.Resources.Resource.Create;
                        break;
                    case (int)EACTION_TYPE.DELETE:
                        actionTypeName = PSafe.AM.Resources.Resource.Delete;
                        break;
                    default:
                        actionTypeName = PSafe.AM.Resources.Resource.Edit;
                        break;
                }
                ViewBag.actionTypeName = actionTypeName;

                switch (historySystem.ControllerName)
                {
                    case (int)EnumControllerName.AREAS :
                        CompareTweObject<Area> _areas = new CompareTweObject<Area>
                        {
                            NewObject = JsonConvert.DeserializeObject<Area>(historySystem.NewObject) ?? new Area(),
                            OldObject = JsonConvert.DeserializeObject<Area>(historySystem.OldObject) ?? new Area()
                        };
                        _areas.Controller = "Area";
                        ViewBag.Resource = Resources.Resource.Area;
                        return View(_areas);
                    case (int)EnumControllerName.COMMANDCENTERS:
                        CompareTweObject<CommandCenter> _commanCenters = new CompareTweObject<CommandCenter>
                        {
                            NewObject = JsonConvert.DeserializeObject<CommandCenter>(historySystem.NewObject) ?? new CommandCenter(),
                            OldObject = JsonConvert.DeserializeObject<CommandCenter>(historySystem.OldObject) ?? new CommandCenter()
                        };
                        _commanCenters.Controller = "CommandCenter";
                        ViewBag.Resource = Resources.Resource.CommandCenter;
                        return View(_commanCenters);
                    case (int)EnumControllerName.DEVICES:
                        CompareTweObject<Device> _devices = new CompareTweObject<Device>
                        {
                            NewObject = JsonConvert.DeserializeObject<Device>(historySystem.NewObject) ?? new Device(),
                            OldObject = JsonConvert.DeserializeObject<Device>(historySystem.OldObject) ?? new Device()
                        };
                        _devices.Controller = "Device";
                        ViewBag.Resource = Resources.Resource.Device;
                        return View(_devices);
                    case (int)EnumControllerName.DOCUMENTS:
                        CompareTweObject<Document> _documents = new CompareTweObject<Document>
                        {
                            NewObject = JsonConvert.DeserializeObject<Document>(historySystem.NewObject) ?? new Document(),
                            OldObject = JsonConvert.DeserializeObject<Document>(historySystem.OldObject) ?? new Document()
                        };
                        _documents.Controller = "Document";
                        ViewBag.Resource = Resources.Resource.Document;
                        return View(_documents);
                    case (int)EnumControllerName.GROUP:
                        CompareTweObject<Group> _groups = new CompareTweObject<Group>
                        {
                            NewObject = JsonConvert.DeserializeObject<Group>(historySystem.NewObject) ?? new Group(),
                            OldObject = JsonConvert.DeserializeObject<Group>(historySystem.OldObject) ?? new Group()
                        };
                        _groups.Controller = "Group";
                        ViewBag.Resource = Resources.Resource.Group;
                        return View(_groups);
                    case (int)EnumControllerName.HISTORYSYSEMS:
                        CompareTweObject<HistorySystem> _historySystems = new CompareTweObject<HistorySystem>
                        {
                            NewObject = JsonConvert.DeserializeObject<HistorySystem>(historySystem.NewObject) ?? new HistorySystem(),
                            OldObject = JsonConvert.DeserializeObject<HistorySystem>(historySystem.OldObject) ?? new HistorySystem()
                        };
                        _historySystems.Controller = "HistorySystem";
                        ViewBag.Resource = Resources.Resource.HistorySystem;
                        return View(_historySystems);
                    case (int)EnumControllerName.LOCATIONS:
                        CompareTweObject<Location> _locations = new CompareTweObject<Location>
                        {
                            NewObject = JsonConvert.DeserializeObject<Location>(historySystem.NewObject) ?? new Location(),
                            OldObject = JsonConvert.DeserializeObject<Location>(historySystem.OldObject) ?? new Location()
                        };
                        _locations.Controller = "Location";
                        ViewBag.Resource = Resources.Resource.Location;
                        return View(_locations);
                    case (int)EnumControllerName.USERS:
                        CompareTweObject<User> _Users = new CompareTweObject<User>
                        {
                            NewObject = JsonConvert.DeserializeObject<User>(historySystem.NewObject) ?? new User(),
                            OldObject = JsonConvert.DeserializeObject<User>(historySystem.OldObject) ?? new User()
                        };
                        _Users.Controller = "User";
                        ViewBag.Resource = Resources.Resource.User;
                        return View(_Users);
                    case (int)EnumControllerName.ROLES:
                        CompareTweObject<Role> _Roles = new CompareTweObject<Role>
                        {
                            NewObject = JsonConvert.DeserializeObject<Role>(historySystem.NewObject) ?? new Role(),
                            OldObject = JsonConvert.DeserializeObject<Role>(historySystem.OldObject) ?? new Role()
                        };
                        _Roles.Controller = "Role";
                        ViewBag.Resource = Resources.Resource.Role;
                        return View(_Roles);
                    case (int)EnumControllerName.SECURITYS:
                        CompareTweObject<SystemUser> _security = new CompareTweObject<SystemUser>
                        {
                            NewObject = JsonConvert.DeserializeObject<SystemUser>(historySystem.NewObject) ?? new SystemUser(),
                            OldObject = JsonConvert.DeserializeObject<SystemUser>(historySystem.OldObject) ?? new SystemUser()
                        };
                        _security.Controller = "SystemUser";
                        ViewBag.Resource = Resources.Resource.SystemUsers;
                        return View(_security);
                    case (int)EnumControllerName.SUPPLIERS:
                        CompareTweObject<Supplier> _suppliers = new CompareTweObject<Supplier>
                        {
                            NewObject = JsonConvert.DeserializeObject<Supplier>(historySystem.NewObject) ?? new Supplier(),
                            OldObject = JsonConvert.DeserializeObject<Supplier>(historySystem.OldObject) ?? new Supplier()
                        };
                        _suppliers.Controller = "Supplier";
                        ViewBag.Resource = Resources.Resource.Suppliers;
                        return View(_suppliers);
                    case (int)EnumControllerName.SYSTEMUSERS:
                        CompareTweObject<SystemUser> _systemUsers = new CompareTweObject<SystemUser>
                        {
                            NewObject = JsonConvert.DeserializeObject<SystemUser>(historySystem.NewObject) ?? new SystemUser(),
                            OldObject = JsonConvert.DeserializeObject<SystemUser>(historySystem.OldObject) ?? new SystemUser()
                        };
                        _systemUsers.Controller = "SystemUser";
                        ViewBag.Resource = Resources.Resource.SystemUsers;
                        return View(_systemUsers);
                    case (int)EnumControllerName.TYPEOFDEVICES:
                        CompareTweObject<Core.Domains.TypeOfDevice> _typeOfDevices = new CompareTweObject<Core.Domains.TypeOfDevice>
                        {
                            NewObject = JsonConvert.DeserializeObject<Core.Domains.TypeOfDevice>(historySystem.NewObject) ?? new Core.Domains.TypeOfDevice(),
                            OldObject = JsonConvert.DeserializeObject<Core.Domains.TypeOfDevice>(historySystem.OldObject) ?? new Core.Domains.TypeOfDevice()
                        };
                        _typeOfDevices.Controller = "Core.Domains.TypeOfDevice";
                        ViewBag.Resource = Resources.Resource.TypeOfDevice;
                        return View(_typeOfDevices);
                    case (int)EnumControllerName.EDIT_USER_ON_ROLE:
                        CompareTweObject<UserInRoleModel> _userInRoles = new CompareTweObject<UserInRoleModel>
                        {
                            NewObject = JsonConvert.DeserializeObject<UserInRoleModel>(historySystem.NewObject) ?? new UserInRoleModel(),
                            OldObject = JsonConvert.DeserializeObject<UserInRoleModel>(historySystem.OldObject) ?? new UserInRoleModel()
                        };
                        _userInRoles.Controller = "EditUser";
                        ViewBag.Resource = Resources.Resource.EditUser;
                        return View(_userInRoles);
                    case (int)EnumControllerName.MARKER:
                        CompareTweObject<Marker> _marker = new CompareTweObject<Marker>
                        {
                            NewObject = JsonConvert.DeserializeObject<Marker>(historySystem.NewObject) ?? new Marker(),
                            OldObject = JsonConvert.DeserializeObject<Marker>(historySystem.OldObject) ?? new Marker()
                        };
                        _marker.Controller = "Marker";
                        ViewBag.Resource = Resources.Resource.Marker;
                        return View(_marker);
                    case (int)EnumControllerName.TYPE_OF_MARKER:
                        CompareTweObject<TypeOfMarker> _TYPE_OF_MARKERrker = new CompareTweObject<TypeOfMarker>
                        {
                            NewObject = JsonConvert.DeserializeObject<TypeOfMarker>(historySystem.NewObject) ?? new TypeOfMarker(),
                            OldObject = JsonConvert.DeserializeObject<TypeOfMarker>(historySystem.OldObject) ?? new TypeOfMarker()
                        };
                        _TYPE_OF_MARKERrker.Controller = "TYPE_OF_MARKER";
                        ViewBag.Resource = Resources.Resource.Marker;
                        return View(_TYPE_OF_MARKERrker);
                    case (int)EnumControllerName.BANNED_LIST:
                        if (historySystem.ActionType == (int)EACTION_TYPE.EDIT_HISTORY)
                        {
                            CompareTweObject<VehicleBannedListHistory> _BANNED_LIST_HISOTRYrker = new CompareTweObject<VehicleBannedListHistory>
                            {
                                NewObject = historySystem.NewObject != null ? (JsonConvert.DeserializeObject<VehicleBannedListHistory>(historySystem.NewObject) ?? new VehicleBannedListHistory()) : new VehicleBannedListHistory(),
                                OldObject = historySystem.OldObject != null ? (JsonConvert.DeserializeObject<VehicleBannedListHistory>(historySystem.OldObject) ?? new VehicleBannedListHistory()) : new VehicleBannedListHistory()
                            };
                            _BANNED_LIST_HISOTRYrker.Controller = "VEHICLE_BANNED_LIST_HISTORY";
                            ViewBag.Resource = Resources.Resource.VehicleBannedList;
                            return View(_BANNED_LIST_HISOTRYrker);
                        }
                        else
                        {
                            CompareTweObject<VehicleBannedList> _BANNED_LISTrker = new CompareTweObject<VehicleBannedList>
                            {
                                NewObject = historySystem.NewObject != null ? (JsonConvert.DeserializeObject<VehicleBannedList>(historySystem.NewObject) ?? new VehicleBannedList()) : new VehicleBannedList(),
                                OldObject = historySystem.OldObject != null ? (JsonConvert.DeserializeObject<VehicleBannedList>(historySystem.OldObject) ?? new VehicleBannedList()) : new VehicleBannedList()
                            };
                            _BANNED_LISTrker.Controller = "VEHICLE_BANNED_LIST";
                            ViewBag.Resource = Resources.Resource.VehicleBannedList;
                            return View(_BANNED_LISTrker);
                        }
                    //case (int)CONTROLLER_NAME.DOCUMENTS_BE_LONG:
                    //    DocumentBeLongLocation Old = new DocumentBeLongLocation
                    //    {
                    //        LocationId = historySystem.NewObject[0],
                    //        DocumentId = historySystem.NewObject[1]
                    //    };

                    //    DocumentBeLongLocation New = new DocumentBeLongLocation
                    //    {
                    //        LocationId = historySystem.OldObject[0],
                    //        DocumentId = historySystem.OldObject[1]
                    //    };
                    //    CompareTweObject<DocumentBeLongLocation> _documentBeLongLocation = new CompareTweObject<DocumentBeLongLocation>
                    //    {
                    //        NewObject = New,
                    //        OldObject = Old
                    //    };
                    //    _documentBeLongLocation.Controller = "DocumentBeLongLocation";
                    //    ViewBag.Resource = "Tài liệu và Vị trí";
                    //    return View(_documentBeLongLocation);
                    default:
                        CompareTweObject<object> _object = new CompareTweObject<object>
                        {
                            NewObject = JsonConvert.DeserializeObject<Area>(historySystem.NewObject) ?? new object(),
                            OldObject = JsonConvert.DeserializeObject<Area>(historySystem.OldObject) ?? new object()
                        };
                        _object.Controller = "Object";
                        ViewBag.Resource = "";
                        return View(_object);
                }
            }
            catch
            {
                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("ư", "Lịch sử", "Xem chi tiết thất bại"));

                return RedirectToAction("Index");
            }
            
        }
    }
}