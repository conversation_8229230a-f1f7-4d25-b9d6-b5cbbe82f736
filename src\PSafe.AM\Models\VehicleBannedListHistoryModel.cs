﻿

using PSafe.Core.Domains;
using System;

namespace PSafe.AM.Models
{
    public class VehicleBannedListHistoryModel : VehicleBannedListHistory
    {
        public new string CreatedDate { get; set; }
        public new string ActionType { get; set; }
        public new string UpdatedDate { get; set; }
        public int ActionTypeVal { get; set; }

        public string UserAgent { get; set; }
        public new string ActiveTime { get; set; }
        public new string ExpiryTime { get; set; }

        public int CreatedByVal { get; set; }
        public int UpdatedByVal { get; set; }
        public string UpdatedByName { get; set; }
        public string ClearBy { get; set; }
        public string ClearDate { get; set; }
        public bool AllowEdit { get; set; } = false;
        public bool AllowClear { get; set; } = false;
        public bool IsCurrentBannedHistory { get; set; } = false;
        public int BannedIndex { get; set; } = -1;
        public bool IsBanned { get; set; }
        public bool IsExpired { get; set; } = false;
    }

    
}
