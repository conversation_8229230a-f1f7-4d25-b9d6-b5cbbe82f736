﻿using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace PSafe.AM.Models
{
    public class PatrolAreaModel
    {
        [Key]
        [Display(Name = "Id", ResourceType = typeof(Resources.Resource_patrolArea))]
        public int Id { get; set; }

        [Required(ErrorMessage = "Giá trị không được trống!")]
        [StringLength(50, MinimumLength = 1, ErrorMessage = "Độ dài tối đa 50 ký tự!")]
        [Display(Name = "Name", ResourceType = typeof(Resources.Resource_patrolArea))]
        public string Name { get; set; } = string.Empty;

        [Display(Name = "Description", ResourceType = typeof(Resources.Resource_patrolArea))]
        public string Description { get; set; } = string.Empty;
    }
}
