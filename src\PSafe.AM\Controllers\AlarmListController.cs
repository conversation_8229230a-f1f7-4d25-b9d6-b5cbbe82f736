﻿using Microsoft.AspNetCore.Mvc;
using PSafe.AM.Common;
using PSafe.Core.Domains;
using PSafe.Core.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.AspNetCore.Http;
using PSafe.AM.Models;
using System.IO;
using System.Threading.Tasks;
using OfficeOpenXml;
using PSafe.Common.UserEnums;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Mvc.Rendering;
using PSafe.AM.ACS;

namespace PSafe.AM.Controllers
{
    [AuthorizeActionFilter(EPERMISSIONS_AM.SecurityBlacklist)]
    public class AlarmListController : Controller
    {

        public AlarmListController()
        {
        }

        public ActionResult Index()
        {
            return View();
        }

    }
}