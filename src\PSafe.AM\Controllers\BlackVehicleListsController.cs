﻿using System;
using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Logging;
using PSafe.AM.Common;
using PSafe.AM.Models;
using PSafe.Common.UserEnums;
using PSafe.Core.Domains;
using PSafe.Core.Interface;

namespace PSafe.AM.Controllers
{
    [AuthorizeActionFilter(EPERMISSIONS_AM.categoryManage)]
    public class BlackVehicleListsController : Controller
    {
        private readonly IBlackVehicleListRepository _blackVehicleListRepository;
        private readonly ILogger<BlackVehicleListsController> _logger;
        private readonly IUserRepository _userRepository;
        private readonly IMapper _mapper;

        public BlackVehicleListsController(ILogger<BlackVehicleListsController> logger, IMapper mapper, IUserRepository userRepository, IBlackVehicleListRepository blackVehicleListRepository)
        {
            _blackVehicleListRepository = blackVehicleListRepository;
            _logger = logger;
            _mapper = mapper;
            _userRepository = userRepository;
        }

        public ActionResult Index()
        {
            StatusQuery Notification;
            List<BlackVehicleListModel> listBlackVehicleListModel = new List<BlackVehicleListModel>();
            try
            {
                var listBlackVehicleList = _blackVehicleListRepository.GetAll().ToList();

                listBlackVehicleListModel = _mapper.Map<List<BlackVehicleListModel>>(listBlackVehicleList);

                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("BlackVehicleList/Index: " + ex.Message);

                Notification = new StatusQuery("error", "", "Xem danh sách thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;
            }

            return View(listBlackVehicleListModel);
        }

        // GET: /supplier/Details/
        public ActionResult Details(int id)
        {
            try
            {
                var blackVehicleList = _blackVehicleListRepository.GetById(id);

                var blackVehicleListModel = _mapper.Map<BlackVehicleListModel>(blackVehicleList);

                return View(blackVehicleListModel);
            }
            catch (Exception ex)
            {
                _logger.LogError("BlackVehicleList/Details: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                return RedirectToAction("Index");
            }
        }

        // GET: /supplier/Create
        public ActionResult Create()
        {
            try
            {
                BlackVehicleListModel blackVehicleListModel = new BlackVehicleListModel();

                return View(blackVehicleListModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.Message);
            }
            return View();
        }

        // POST: /supplier/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create([Bind("VehicleNumber, FullName, Description, Active")] BlackVehicleListModel blackVehicleListModel)
        {
            StatusQuery Notification;
            if (ModelState.IsValid)
            {
                try
                {
                    var systemUser = GetSesson();

                    if (systemUser == null)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                        return RedirectToAction("Logout", "Security");
                    }

                    var blackVehicleList = _mapper.Map<BlackVehicleList>(blackVehicleListModel);

                    _blackVehicleListRepository.Insert(blackVehicleList);

                    var statusInsert = _blackVehicleListRepository.SaveChanges();

                    if (statusInsert > 0)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Thêm mới thành công"));
                        return RedirectToAction("Index");
                    }
                    else
                    {
                        Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        return View(blackVehicleListModel);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("BlackVehicleList/Create: " + ex.Message);

                    Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return View(blackVehicleListModel);
                }
            }
            Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;

            return View(blackVehicleListModel);
        }

        // GET: /supplier/Edit/5
        public ActionResult Edit(int id)
        {
            try
            {
                var blackVehicleList = _blackVehicleListRepository.GetById(id);

                if (blackVehicleList != null)
                {
                    var blackVehicleListModel = _mapper.Map<BlackVehicleListModel>(blackVehicleList);
                    return View(blackVehicleListModel);
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                    return RedirectToAction("Index");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("BlackVehicleList/Edit: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                return RedirectToAction("Index");
            }
        }

        // POST: /supplier/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit([Bind("Id, VehicleNumber, FullName, Description, Active")] BlackVehicleListModel blackVehicleListModel)
        {
            StatusQuery Notification;
            if (ModelState.IsValid)
            {
                try
                {
                    var systemUser = GetSesson();

                    if (systemUser == null)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                        return RedirectToAction("Logout", "Security");
                    }

                    var blackVehicleList = _blackVehicleListRepository.GetById(blackVehicleListModel.Id);

                    blackVehicleList.VehicleNumber = blackVehicleListModel.VehicleNumber;
                    //blackVehicleList.FullName = blackVehicleListModel.FullName;
                    blackVehicleList.Description = blackVehicleListModel.Description;
                    blackVehicleList.Active = blackVehicleListModel.Active;

                    _blackVehicleListRepository.Update(blackVehicleList);

                    var updateStatus = _blackVehicleListRepository.SaveChanges();

                    if (updateStatus > 0)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Sửa thành công"));

                        return RedirectToAction("Index");
                    }
                    else
                    {
                        Notification = new StatusQuery("error", "", "Sửa thất bại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        return View(blackVehicleList);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("BlackVehicleList/Edit: " + ex.Message);

                    Notification = new StatusQuery("error", "", "Sửa thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return View(blackVehicleListModel);
                }
            }

            Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;

            return View(blackVehicleListModel);
        }

        // GET: /supplier/Delete/5
        public ActionResult Delete(int id)
        {
            StatusQuery Notification;
            BlackVehicleListModel blackVehicleListModel = new BlackVehicleListModel();
            try
            {
                var blackVehicleList = _blackVehicleListRepository.GetById(id);

                if (blackVehicleList == null)
                {
                    Notification = new StatusQuery("warning", "", "Không tìm thấy công ty");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return RedirectToAction("Index");
                }

                blackVehicleListModel = _mapper.Map<BlackVehicleListModel>(blackVehicleList);
            }
            catch (Exception ex)
            {
                _logger.LogError("BlackVehicleList/Delete: " + ex.Message);

                Notification = new StatusQuery("warning", "", "Xóa thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;

                return RedirectToAction("Index");
            }
            return View(blackVehicleListModel);
        }


        // POST: /Suppliers/Delete/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int id)
        {
            try
            {
                var systemUser = GetSesson();

                if (systemUser == null)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                    return RedirectToAction("Logout", "Security");
                }

                var BlackVehicleList = _blackVehicleListRepository.GetById(id);

                _blackVehicleListRepository.Delete(BlackVehicleList);

                var deleteStatus = _blackVehicleListRepository.SaveChanges();

                if (deleteStatus > 0)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Xóa thành công"));
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));
                }

                return RedirectToAction("Index");
            }
            catch (Exception ex)
            {
                _logger.LogError("BlackVehicleList/DeleteConfirmed: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));
            }
            return RedirectToAction("Index");
        }

        [NonAction]
        private SelectList ToSelectList(List<DropDownList> DropDownList)
        {
            List<SelectListItem> list = new List<SelectListItem>();

            foreach (var item in DropDownList)
            {
                list.Add(new SelectListItem()
                {
                    Text = item.Name,
                    Value = item.Id.ToString()
                });
            }

            return new SelectList(list, "Value", "Text");
        }

        public User GetSesson()
        {
            var sessionUser = HttpContext.Session.GetString("SessionUserSystemId");
            if (sessionUser != null)
            {
                var _user = _userRepository.GetById(int.Parse(sessionUser));

                if (_user != null)
                {
                    if (!_user.Actived)
                    {
                        HttpContext.Session.Clear();
                        return null;
                    }
                    return _user;
                }
            }
            return null;
        }
    }
}