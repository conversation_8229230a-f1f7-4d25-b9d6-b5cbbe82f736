﻿using PSafe.AM.ACS;
using PSafe.Core.Domains;
using System;

namespace PSafe.AM.Models
{
    public class HumanBannedListModel
    {
        public int Index { get; set; }
        public string IdKhachHang { get; set; }
        public string FullName { get; set; }
        public int PaperType { get; set; }
        public string PaperCode { get; set; }
        public string PlaceOfBirth { get; set; }

        public string BirthDay { get; set; }
        public DateTime? BirthDayDate { get; set; }
        public string DateOfIssue { get; set; }
        public DateTime? DateOfIssueDate { get; set; }
        public string PlaceOfIssue { get; set; }
        public string ActiveTime { get; set; }
        public string ExpiryTime { get; set; }
        public string CreatedBy { get; set; }
        public string CreatedDate { get; set; }

        public DateTime? ExpiryTimeDate { get; set; }

        public bool IsValidBanned { get; set; }

        public string Company { get; set; }
        public string PhoneNumber { get; set; }
        public string ForbidType { get; set; }
        public string ReasonViolation { get; set; }

        public string ViolationType { get; set; }

        public string ReasonClear { get; set; }
        public string ClearBy { get; set; }
        public string ClearDate { get; set; }

        public string ProvincialOfBirth { get; set; }
        public string Address { get; set; }

        public string ClearFlag { get; set; }
        public string Note { get; set; }
    }
}
