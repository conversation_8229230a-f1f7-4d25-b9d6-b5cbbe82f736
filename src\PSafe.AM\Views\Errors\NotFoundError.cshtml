﻿@{
    Layout = null;
}
@model string

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - MVC6_Seed_Project</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <environment names="Development">
        <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.css" />
        <link rel="stylesheet" href="~/lib/font-awesome/css/font-awesome.css" />
        <link rel="stylesheet" href="~/css/animate.css" />
        <link rel="stylesheet" href="~/css/style.css" asp-append-version="true" />
    </environment>
    <environment names="Staging,Production">
        <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
        <link rel="stylesheet" href="~/lib/font-awesome/css/font-awesome.min.css" />
        <link rel="stylesheet" href="~/css/animate.css" />
        <link rel="stylesheet" href="~/css/style.css" asp-append-version="true" />
    </environment>
</head>
<body class="gray-bg">

    <div class="middle-box text-center animated fadeInDown">
        <h1>404</h1>
        <h3 class="font-bold">Trang không tồn tại</h3>
        <div class="error-desc">
            @ViewBag.ErrorString
        </div>
    </div>

</body>
</html>
