﻿@model PSafe.AM.Models.MarkerModel

@{
    ViewBag.Title = "Hiệu chỉnh";
}

<environment names="Development">
    <script src="~/lib/jquery/dist/jquery.js"></script>
    <script src="~/js/script.js" asp-append-version="true"></script>
</environment>
<environment names="Staging,Production">
    <script src="~/lib/jquery/dist/jquery.js"></script>
    <script src="~/js/script.js" asp-append-version="true"></script>
</environment>

<script language="javascript" type="text/javascript">
    function openChild(file, window) {
        childWindow = open(file, window, 'resizable=no,width=700,height=400,scrollbars,resizable,toolbar,status');
        if (childWindow.opener == null) childWindow.opener = self;
    }
</script>

<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-lg-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>@PSafe.AM.Resources.Resource.Edit @PSafe.AM.Resources.Resource.Marker</h5>
                    <div class="ibox-tools">
                        @Html.ActionLink(PSafe.AM.Resources.Resource.BackToList, "Index", "Markers", null, new { @class = "btn btn-primary btn-xs" })
                    </div>
                </div>
                <div class="ibox-content">

                    @using (Html.BeginForm())
                    {
                        @Html.AntiForgeryToken()
                        @Html.ValidationSummary(true)

                        @Html.HiddenFor(model => model.MarkerID)

                    <div class="row">
                        <div class="col-lg-6">
                            <div class="form-group row">
                                <label class="control-label col-md-4">@Html.LabelFor(model => model.MarkerName) (*)</label>
                                <div class="col-md-8">
                                    @Html.EditorFor(model => model.MarkerName)
                                    @Html.ValidationMessageFor(model => model.MarkerName, null, new { @class = "text-danger" })
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                <label class="control-label col-md-4">@Html.LabelFor(model => model.LONGITUDE) (*)</label>
                                <div class="col-md-8">
                                    @Html.EditorFor(model => model.LONGITUDE)
                                    @Html.ValidationMessageFor(model => model.LONGITUDE, null, new { @class = "text-danger" })
                                </div>
                            </div>
                        </div>
                        @*<div class="col-lg-6">
            <div class="form-group row">
                @Html.LabelFor(model => model.MarkerCode, new { @class = "control-label col-md-4" })
                <div class="col-md-8">
                    @Html.EditorFor(model => model.MarkerCode)
                    @Html.ValidationMessageFor(model => model.MarkerCode, null, new { @class = "text-danger" })
                </div>
            </div>
        </div>*@
                        <div class="col-lg-6">
                            <div class="form-group row">
                                @Html.LabelFor(model => model.MarkerTypeID, new { @class = "control-label col-md-4" })
                                <div class="col-md-8">
                                    <select data-placeholder="Vui lòng chọn..." class="form-control chosen-select" name="MarkerTypeID" id="MarkerTypeID" tabindex="4">
                                        <option value="0">-- Chọn --</option>
                                        <option value="1">Trụ nước</option>
                                        <option value="2">Trạm y tế</option>
                                        <option value="3">Trụ sở công an</option>
                                    </select>
                                    @Html.ValidationMessageFor(model => model.MarkerTypeID, null, new { @class = "text-danger" })
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                <label class="control-label col-md-4">@Html.LabelFor(model => model.LATITUDE) (*)</label>
                                <div class="col-md-5">
                                    @Html.EditorFor(model => model.LATITUDE)
                                    @Html.ValidationMessageFor(model => model.LATITUDE, null, new { @class = "text-danger" })
                                </div>
                                <div class="col-md-3">
                                    <input type="button" class="btn btn-primary btn-xs" btn- value="Lấy tọa độ" onClick="openChild('/static/GetLatlon.htm','win2')" />
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-6">
                            <div class="form-group row">
                                @Html.LabelFor(model => model.Phone, new { @class = "control-label col-md-4" })
                                <div class="col-md-8">
                                    @Html.EditorFor(model => model.Phone)
                                    @Html.ValidationMessageFor(model => model.Phone, null, new { @class = "text-danger" })
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                @Html.LabelFor(model => model.Description, new { @class = "control-label col-md-4" })
                                <div class="col-md-8">
                                    @Html.TextAreaFor(model => model.Description, new { @class = "form-control" })
                                    @Html.ValidationMessageFor(model => model.Description, null, new { @class = "text-danger" })
                                </div>
                            </div>
                        </div>
                    </div>
                        <div class="row">
                            <div class="col-md-offset-2 col-md-10">
                                <input type="submit" value="@PSafe.AM.Resources.Resource.Save" class="btn btn-primary" />
                                @Html.ActionLink(PSafe.AM.Resources.Resource.Cancel, "Index", "Markers", null, new { @class = "btn btn-white" })
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <environment names="Development,Staging,Production">
        <link rel="stylesheet" href="~/lib/datatables/datatables.min.css" />
        <link rel="stylesheet" href="~/lib/toastr/toastr.min.css" />
        <link rel="stylesheet" href="~/lib/datatables/datatables.min.css" />
        <link rel="stylesheet" href="~/lib/toastr/toastr.min.css" />
    </environment>

    <environment names="Development">
        <link rel="stylesheet" href="~/lib/chosen/bootstrap-chosen.css" />
    </environment>
    <environment names="Staging,Production">
        <link rel="stylesheet" href="~/lib/chosen/bootstrap-chosen.css" />
    </environment>
}

@section Scripts {
    <environment names="Development">
        <script src="~/lib/chosen/chosen.jquery.js"></script>
    </environment>

    <environment names="Development,Staging,Production">
        <script src="~/lib/validate/jquery.validate.min.js"></script>
        <script src="~/lib/dataTables/datatables.min.js"></script>
        <script src="~/lib/dataTables/dataTables.bootstrap4.min.js"></script>
        <script src="~/lib/toastr/toastr.js"></script>
        <script src="~/lib/toastr/toastr.min.js"></script>
        <script src="~/lib/chosen/chosen.jquery.js"></script>
    </environment>

    @if (!string.IsNullOrEmpty(ViewBag.Type) && ViewBag.Type == "warning")
    {
        <script type="text/javascript">
            toastr.warning('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Status) && ViewBag.Type == "error")
    {
        <script type="text/javascript">
            toastr.error('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Status) && ViewBag.Type == "success")
    {
        <script type="text/javascript">
            toastr.success('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
}
