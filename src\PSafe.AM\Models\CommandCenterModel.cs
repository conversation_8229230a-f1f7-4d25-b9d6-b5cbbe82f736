﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.ComponentModel.DataAnnotations;

namespace PSafe.AM.Models
{
    public class CommandCenterModel
    {
        #region Properties

        [Key]
        public int CommandCenterID { get; set; }

        [Required(ErrorMessage = "Gi<PERSON> trị không được trống!")]
        [StringLength(255, MinimumLength = 3, ErrorMessage = "Độ dài từ 3-255 ký tự!")]
        [Display(Name = "CenterName", ResourceType = typeof(Resources.Resource__commandCenter))]
        public string CenterName { get; set; } = string.Empty;

        [Required(ErrorMessage = "Gi<PERSON> trị không được trống!")]
        [StringLength(255, MinimumLength = 3, ErrorMessage = "Độ dài từ 3-255 ký tự!")]
        [Display(Name = "Representative", ResourceType = typeof(Resources.Resource__commandCenter))]
        public string Representative { get; set; } = string.Empty;

        [Required(ErrorMessage = "<PERSON>i<PERSON> trị không được trống!")]
        [StringLength(255, MinimumLength = 3, ErrorMessage = "Độ dài từ 3-255 ký tự!")]
        [Display(Name = "Address", ResourceType = typeof(Resources.Resource__commandCenter))]
        public string Address { get; set; } = string.Empty;

        [Required(ErrorMessage = "Giá trị không được trống!")]
        [StringLength(255, MinimumLength = 3, ErrorMessage = "Độ dài từ 3-255 ký tự!")]
        [Display(Name = "Email", ResourceType = typeof(Resources.Resource__commandCenter))]
        public string Email { get; set; } = string.Empty;

        [Required(ErrorMessage = "Giá trị không được trống!")]
        [StringLength(255, MinimumLength = 3, ErrorMessage = "Độ dài từ 3-255 ký tự!")]
        [Display(Name = "ContactPhone", ResourceType = typeof(Resources.Resource__commandCenter))]
        public string ContactPhone { get; set; } = string.Empty;

        [Required(ErrorMessage = "Giá trị không được trống!")]
        [StringLength(255, MinimumLength = 3, ErrorMessage = "Độ dài từ 3-255 ký tự!")]
        [Display(Name = "Fax", ResourceType = typeof(Resources.Resource__commandCenter))]
        public string Fax { get; set; } = string.Empty;

        [Display(Name = "Actived", ResourceType = typeof(Resources.Resource__commandCenter))]
        public bool Actived { get; set; }

        [Required(ErrorMessage = "Giá trị không được trống!")]
        [StringLength(255, MinimumLength = 3, ErrorMessage = "Độ dài từ 3-255 ký tự!")]
        [Display(Name = "Description", ResourceType = typeof(Resources.Resource__commandCenter))]
        public string Description { get; set; } = string.Empty;

        [DataType(DataType.Date)]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd}", ApplyFormatInEditMode = true)]
        [Display(Name = "CreatedDate", ResourceType = typeof(Resources.Resource__commandCenter))]
        public DateTime CreatedDate { get; set; }

        [Display(Name = "CreatedUser", ResourceType = typeof(Resources.Resource__commandCenter))]
        public int CreatedUser { get; set; }

        [DataType(DataType.Date)]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd}", ApplyFormatInEditMode = true)]
        [Display(Name = "UpdatedDate", ResourceType = typeof(Resources.Resource__commandCenter))]
        public DateTime UpdatedDate { get; set; }

        [Display(Name = "UpdatedUser", ResourceType = typeof(Resources.Resource__commandCenter))]
        public int UpdatedUser { get; set; }
        #endregion
    }
}