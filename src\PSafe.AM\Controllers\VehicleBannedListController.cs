﻿using Microsoft.AspNetCore.Mvc;
using PSafe.AM.Common;
using PSafe.Core.Domains;
using PSafe.Core.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.AspNetCore.Http;
using PSafe.AM.Models;
using System.IO;
using System.Threading.Tasks;
using OfficeOpenXml;
using PSafe.Common.UserEnums;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.AspNetCore.Hosting;
using PSafe.Common.Enums;
using System.Text;
using PSafe.Core.Services;
using static PSafe.Common.CommonEnums;
using PSafe.Common;
using Newtonsoft.Json;
using Microsoft.EntityFrameworkCore;
using PSafe.AM.ThirdPartyAPI;
using PSafe.Infrastructure.Services.EPort;
using PSafe.AM.CacheData;

namespace PSafe.AM.Controllers
{
    [AuthorizeActionFilter(EPERMISSIONS_AM.BlacklistControlForTTAN, EPERMISSIONS_AM.ViewBlackList)]
    public class VehicleBannedListController : Controller
    {
        private readonly IVehicleBannedListRepository _vehicleBannedListRepository;
        private readonly IUserRepository _userRepository;
        private readonly ISiteRepository _siteRepository;
        private readonly IBannedTypeRepository _bannedTypeRepository;
        private readonly IVehicleBannedListHistoryRepository _vehicleBannedListHistoryRepository;
        private readonly IUserInRoleRepository _userInRoleRepository;
        private readonly IRoleRepository _roleRepository;
        private readonly IAlarmConfigRepository _alarmConfigRepository;
        private readonly IZaloService _zaloService;
        private readonly IEmailSender _emailSender;
        private readonly IEPortSender _ePortSender;
        private readonly IHistorySystemRepository _historySystemRepository;
        private readonly IGeneralConfigRepository _generalConfigRepository;

        private readonly ILogger<VehicleBannedListController> _logger;
        private readonly IHostingEnvironment _hostingEnvironment;

        /// <summary>
        /// Danh sách tên người dùng
        /// Key: userId
        /// Value: UserBannedInfo
        /// </summary>
        private static Dictionary<int, UserBannedInfo> UserBannedInfos = null;
        /// <summary>
        /// Danh sách nhóm có quyền cấm vào ra
        /// Key: Group Id,
        /// Value: BannedGroupUserInfo
        /// </summary>
        private static Dictionary<int, BannedGroupUserInfo> BannedGroupUsers = null;
        /// <summary>
        /// lock
        /// </summary>
        private static object _lock = new object();

        public VehicleBannedListController(IVehicleBannedListRepository vehicleBannedListRepository,
            IUserRepository userRepository,
            IBannedTypeRepository bannedTypeRepository,
            IVehicleBannedListHistoryRepository vehicleBannedListHistoryRepository,
            IUserInRoleRepository userInRoleRepository,
            IRoleRepository roleRepository, ILogger<VehicleBannedListController> logger,
            IHostingEnvironment hostingEnvironment,
            IAlarmConfigRepository alarmConfigRepository,
            IZaloService zaloService,
            IEmailSender emailSender,
            IEPortSender ePortSender,
            IHistorySystemRepository historySystemRepository,
            IGeneralConfigRepository generalConfigRepository,
            ISiteRepository siteRepository)
        {
            _vehicleBannedListRepository = vehicleBannedListRepository;
            _userRepository = userRepository;
            _bannedTypeRepository = bannedTypeRepository;
            _vehicleBannedListHistoryRepository = vehicleBannedListHistoryRepository;
            _userInRoleRepository = userInRoleRepository;
            _roleRepository = roleRepository;
            _logger = logger;
            _hostingEnvironment = hostingEnvironment;
            _alarmConfigRepository = alarmConfigRepository;
            _zaloService = zaloService;
            _emailSender = emailSender;
            _ePortSender = ePortSender;
            _historySystemRepository = historySystemRepository;
            _generalConfigRepository = generalConfigRepository;
            _siteRepository = siteRepository;
        }

        #region Index View
        [HttpGet]
        public ActionResult Index()
        {
            try
            {
                ViewBag.userDataList = GetUserDataList();
                ViewBag.groupUserHasBannedRoleList = GetGroupUsersHasBannedRole();
                ViewBag.bannedTypeList = GetBannedTypeList();
            }
            catch (Exception ex)
            {
                _logger.LogError("VehicleBannedList/Index: " + ex.Message);
                ViewBag.userDataList = new List<SelectListItem>();
                ViewBag.groupUserHasBannedRoleList = new List<SelectListItem>();
                ViewBag.bannedTypeList = new List<SelectListItem>();
            }

            return View();
        }

        /// <summary>
        /// Lấy danh sách loại vi phạm cho từng người dùng 
        /// (mỗi người dùng có danh sách loại vi phạm khác nhau tuy theo quyền)
        /// </summary>
        /// <returns></returns>
        private List<SelectListItem> GetBannedTypeList()
        {
            List<SelectListItem> _bannedTypeList = new List<SelectListItem>();
            try
            {
                var bannedTypeList = _bannedTypeRepository.GetAll();
                var BlacklistControlForTTAN = (int)EPERMISSIONS_AM.BlacklistControlForTTAN;

                var _userLogin = GetCurrentUserLogin();
                if (_userLogin == null)
                    return _bannedTypeList;

                var userInRoles = _userInRoleRepository.GetBy(x => x.UserId == _userLogin.Id).Select(x => x.RoleId).ToList();

                if (userInRoles != null)
                {
                    bool isHasDeleteRole = false;
                    bool isHasEditRole = false;
                    List<string> roles = _roleRepository.GetBy(x => userInRoles.Contains(x.RoleId)).Select(x => x.ListFunction_AM).ToList();
                    foreach (var role in roles)
                    {
                        if (role != null)
                        {
                            string[] functionArray = role.Split(',');
                            var results = functionArray.Any(p => p == BlacklistControlForTTAN.ToString());
                            if (results)
                            {
                                foreach (string functionItem in functionArray)
                                {
                                    int bannedTypeId = int.Parse(functionItem.Trim());
                                    var bannedType = bannedTypeList.Where(x => x.Id == bannedTypeId).FirstOrDefault();
                                    if (bannedType != null)
                                    {
                                        if (_bannedTypeList.Where(x => x.Value == bannedType.Id.ToString()).Count() == 0)
                                        {
                                            _bannedTypeList.Add(new SelectListItem()
                                            {
                                                Text = bannedType.Description,
                                                Value = bannedType.Id.ToString()
                                            });
                                        }
                                    }
                                }
                            }

                            if(isHasDeleteRole == false)
                                isHasDeleteRole = functionArray.Any(p => p == ((int)EPERMISSIONS_AM.DeleteBlackList).ToString());

                            if (isHasEditRole == false)
                                isHasEditRole = functionArray.Any(p => p == ((int)EPERMISSIONS_AM.EditBlackList).ToString());
                        }
                    }

                    ViewBag.isHasDeleteRole = isHasDeleteRole;
                    ViewBag.isHasEditRole = isHasEditRole;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("VehicleBannedList/GetBannedTypeList: " + ex.Message);
            }
            return _bannedTypeList;
        }

        /// <summary>
        /// Lấy danh sách người dùng
        /// </summary>
        /// <returns></returns>
        private List<SelectListItem> GetUserDataList()
        {
            List<SelectListItem> userDataList = new List<SelectListItem>();
            lock (_lock)
            {
                UserBannedInfos = new Dictionary<int, UserBannedInfo>();
                var siteList = _siteRepository.GetAll().ToList();
                var userList = _userRepository.GetAll().ToList();
                foreach (var user in userList)
                {
                    userDataList.Add(new SelectListItem()
                    {
                        Text = string.IsNullOrEmpty(user.FullName) ? user.UserName : user.FullName,
                        Value = user.Id.ToString()
                    });
                    if (!UserBannedInfos.ContainsKey(user.Id))
                        UserBannedInfos.Add(user.Id, new UserBannedInfo(user, siteList));
                }
            }

            return userDataList;
        }

        /// <summary>
        /// Lấy danh sách nhóm người dùng có quyền cấm xe
        /// </summary>
        /// <returns></returns>
        private List<SelectListItem> GetGroupUsersHasBannedRole()
        {
            List<SelectListItem> groupUserDataList = new List<SelectListItem>();
            var roleList = _roleRepository.Get(null, null, "UserInRoles").ToList();

            lock (_lock)
            {
                BannedGroupUsers = new Dictionary<int, BannedGroupUserInfo>();
                foreach (var role in roleList)
                {
                    if (string.IsNullOrWhiteSpace(role.ListFunction_AM))
                        continue;

                    if (string.IsNullOrWhiteSpace(role.RoleName))
                        continue;

                    string[] functions = role.ListFunction_AM.Split(',');
                    if (!functions.Contains(ConvertFunctionEnumToString(EPERMISSIONS_AM.BlacklistControlForTTAN)))
                        continue;

                    if (functions.Contains(ConvertFunctionEnumToString(EPERMISSIONS_AM.ForbidViolatingLoadControl)) ||
                        functions.Contains(ConvertFunctionEnumToString(EPERMISSIONS_AM.CancelViolatingLoadControl)) ||
                        functions.Contains(ConvertFunctionEnumToString(EPERMISSIONS_AM.ForbidViolatingTrafficSafety)) ||
                        functions.Contains(ConvertFunctionEnumToString(EPERMISSIONS_AM.CancelViolatingTrafficSafety)) ||
                        functions.Contains(ConvertFunctionEnumToString(EPERMISSIONS_AM.ForbidViolationOfBehavioralCulture)) ||
                        functions.Contains(ConvertFunctionEnumToString(EPERMISSIONS_AM.CancelViolationOfBehavioralCulture)) ||
                        functions.Contains(ConvertFunctionEnumToString(EPERMISSIONS_AM.ForbidViolationOfPortSecurity)) ||
                        functions.Contains(ConvertFunctionEnumToString(EPERMISSIONS_AM.CancelViolationOfPortSecurity)) ||
                        functions.Contains(ConvertFunctionEnumToString(EPERMISSIONS_AM.ForbidCarInOutImproperly)) ||
                        functions.Contains(ConvertFunctionEnumToString(EPERMISSIONS_AM.CancelCarInOutImproperly)) ||
                        functions.Contains(ConvertFunctionEnumToString(EPERMISSIONS_AM.ForbidOther)) ||
                        functions.Contains(ConvertFunctionEnumToString(EPERMISSIONS_AM.CancelOther)) ||
                        functions.Contains(ConvertFunctionEnumToString(EPERMISSIONS_AM.DeleteBlackList)) ||
                        functions.Contains(ConvertFunctionEnumToString(EPERMISSIONS_AM.EditBlackList)) ||
                        functions.Contains(ConvertFunctionEnumToString(EPERMISSIONS_AM.ViewBlackList))
                        )
                    {
                        groupUserDataList.Add(new SelectListItem()
                        {
                            Value = role.RoleId.ToString(),
                            Text = role.RoleName
                        });

                        if(role.UserInRoles != null && role.UserInRoles.Count > 0)
                        {
                            if (!BannedGroupUsers.ContainsKey(role.RoleId))
                                BannedGroupUsers.Add(role.RoleId, new BannedGroupUserInfo(role.RoleId, role.RoleName, role.PhoneNumber, functions));

                            foreach(var userInRole in role.UserInRoles)
                            {
                                BannedGroupUsers[role.RoleId].UserIds.Add(userInRole.UserId);
                            }
                        }
                    }
                }
            }

            return groupUserDataList;
        }

        /// <summary>
        /// Chuyển đổi enum qua int string
        /// </summary>
        /// <param name="function"></param>
        /// <returns></returns>
        private string ConvertFunctionEnumToString(EPERMISSIONS_AM function)
        {
            return ((int)function).ToString();
        }

        /// <summary>
        /// Lấy danh sách các nhóm có quyền cấm của một người dùng
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="bannedType"></param>
        /// <returns></returns>
        private BannedGroupUserInfo GetBannedGroupUserByUserId(int userId, int bannedType)
        {
            lock(_lock)
            {
                //lay nhom phu hop dieu kien: 
                // + co nguoi dung yeu cau
                // + co so dien thoai ho tro
                // + khong phai la nhom admin
                // + co quyen yeu cau
                foreach(var group in BannedGroupUsers)
                {
                    if(group.Value.PhoneNumber != null &&
                        group.Value.UserIds.Contains(userId) &&
                        group.Value.GroupName.ToLower() != "admin" &&
                        group.Value.Functions.Contains(bannedType.ToString()))
                        return group.Value;
                }

                //lay nhom phu hop dieu kien: 
                // + co nguoi dung yeu cau
                // + co so dien thoai ho tro
                // + co quyen yeu cau
                foreach (var group in BannedGroupUsers)
                {
                    if (group.Value.PhoneNumber != null &&
                        group.Value.UserIds.Contains(userId) &&
                        group.Value.Functions.Contains(bannedType.ToString()))
                        return group.Value;
                }

                //Lay nhom phu hop dieu kien:
                // + co nguoi dung yeu cau
                // + co quyen yeu cau
                foreach (var group in BannedGroupUsers)
                {
                    if (group.Value.UserIds.Contains(userId) &&
                        group.Value.Functions.Contains(bannedType.ToString()))
                        return group.Value;
                }

                //Lay nhom phu hop dieu kien:
                // + co nguoi dung yeu cau
                foreach (var group in BannedGroupUsers)
                {
                    if (group.Value.UserIds.Contains(userId))
                        return group.Value;
                }

                return null;
            }
        }
        #endregion

        #region Get Vehicle Banned List
        [HttpPost]
        public JsonResult Index(
            int draw,
            int start,
            int length,
            int searchType,
            string searchValue,
            string startDate,
            string endDate,
            string createdBy,
            string clearBy,
            int dataType,
            string orderBy,
            string orderType,
            int status,
            string violationTypeId,
            string groupUserCreated,
            string groupUserClear,
            string fromActiveTime,
            string toActiveTime,
            string fromExpiryTime,
            string toExpiryTime,
            string fromBannedTime,
            string toBannedTime)
        {
            var user = GetUserRole();
            if (user == null) //người dùng chưa đăng nhập
            {
                return Json(new
                {
                    draw = 0,
                    recordsFiltered = 0,
                    recordsTotal = 0
                });
            }

            DateTime curDateTime = DateTime.Now.Date;
            int recordsTotal = 0;

            #region Init Filter
            bool isFilterStatusValid = status == (int)EBANNED_STATUS.IN_VALID;
            bool isFilterStatusExpiry = status == (int)EBANNED_STATUS.EXPIRES;
            bool isFilterBannedType = violationTypeId != "-1";
            bool isFilterCreatedBy = createdBy != "-1";
            bool isFilterClearBy = clearBy != "-1";
            bool isFilterGroupUserCreated = groupUserCreated != "-1";
            bool isFilterGroupUserClear = groupUserClear != "-1";
            bool isFilterActiveTime = !string.IsNullOrEmpty(fromActiveTime) && !string.IsNullOrEmpty(toActiveTime);
            bool isFilterExpiryTime = !string.IsNullOrEmpty(fromExpiryTime) && !string.IsNullOrEmpty(toExpiryTime);
            if (!ConvertStringToDateTime(fromActiveTime, "yyyy-MM-dd", out var _fromActiveTime))
                isFilterActiveTime = false;
            if (!ConvertStringToDateTime(toActiveTime, "yyyy-MM-dd", out var _toActiveTime))
                isFilterActiveTime = false;
            if (!ConvertStringToDateTime(fromExpiryTime, "yyyy-MM-dd", out var _fromExpiryTime))
                isFilterExpiryTime = false;
            if (!ConvertStringToDateTime(toExpiryTime, "yyyy-MM-dd", out var _toExpiryTime))
                isFilterExpiryTime = false;

            DateTime _fromBannedTime = DateTime.Now;
            DateTime _toBannedTime = DateTime.Now;
            bool isFilterStatusValidNow = false;
            if (isFilterStatusValid && !ConvertStringToDateTime(fromBannedTime, "yyyy-MM-dd", out _fromBannedTime))
                isFilterStatusValidNow = true;

            if (isFilterStatusValid && ConvertStringToDateTime(toBannedTime, "yyyy-MM-dd", out _toBannedTime))
                isFilterStatusValidNow = false;

            bool isFilterStatusValidFromTo = isFilterStatusValid && !isFilterStatusValidNow;
            if (isFilterStatusValidFromTo)
            {
                _toBannedTime = _toBannedTime.AddDays(1).AddSeconds(-1);
                isFilterStatusValid = false;
            }

            List<int> bannedTypeList = null;
            if (isFilterBannedType)
            {
                bannedTypeList = ConvertStringToListInt(violationTypeId);
                if (bannedTypeList == null || bannedTypeList.Count == 0)
                    isFilterBannedType = false;
            }

            List<int> createdByList = null;
            if(isFilterCreatedBy)
            {
                createdByList = ConvertStringToListInt(createdBy);
                if (createdByList == null || createdByList.Count == 0)
                    isFilterCreatedBy = false;
            }

            List<int> clearByList = null;
            if (isFilterClearBy)
            {
                clearByList = ConvertStringToListInt(clearBy);
                if (clearByList == null || clearByList.Count == 0)
                    isFilterClearBy = false;
            }

            if (isFilterGroupUserCreated)
            {
                var userInGroups = GetUsersInGroups(groupUserCreated);
                if(userInGroups != null && userInGroups.Count > 0)
                {
                    if(isFilterCreatedBy && createdByList != null)
                        createdByList.AddRange(userInGroups);
                    else
                        createdByList = userInGroups;

                    isFilterCreatedBy = true;
                }
            }

            if (isFilterGroupUserClear)
            {
                var userInGroups = GetUsersInGroups(groupUserClear);
                if (userInGroups != null && userInGroups.Count > 0)
                {
                    if (isFilterClearBy && clearByList != null)
                        clearByList.AddRange(userInGroups);
                    else
                        clearByList = userInGroups;

                    isFilterClearBy = true;
                }
            }

            bool isSearch = !string.IsNullOrWhiteSpace(searchValue);
            bool isFilterTruckId = isSearch && searchType == (int)SearchVehicleBannedListType.BIEN_SO_XE;
            bool isFilterCavet = isSearch && searchType == (int)SearchVehicleBannedListType.CAVET;
            bool isFilterOwnerName = isSearch && searchType == (int)SearchVehicleBannedListType.CHU_XE;
            bool isFilterReasonViolation = isSearch && searchType == (int)SearchVehicleBannedListType.LY_DO_VI_PHAM;
            bool isFilterReasonClear = isSearch && searchType == (int)SearchVehicleBannedListType.LY_DO_HUY;
            bool isFilterNote = isSearch && searchType == (int)SearchVehicleBannedListType.GHI_CHU;
            bool isFilterFoundBy = isSearch && searchType == (int)SearchVehicleBannedListType.NGUOI_PHAT_HIEN;
            if (isFilterTruckId)
                searchValue = RemoveSpecialCharactersFromTruckId(searchValue);
            else if (isFilterOwnerName || isFilterReasonViolation || isFilterReasonClear || isFilterNote || isFilterFoundBy)
                searchValue = searchValue.ToLower().Trim();

            bool isFilterCreatedTime = !string.IsNullOrEmpty(startDate);
            isFilterCreatedTime = isFilterCreatedTime && !isFilterActiveTime && !isFilterExpiryTime;
            DateTime _startDate = DateTime.Now;
            DateTime _endDate = DateTime.Now;

            if (isFilterCreatedTime)
            {
                if (!ConvertStringToDateTime(startDate, "yyyy-MM-dd", out _startDate))
                    isFilterCreatedTime = false;

                if (!ConvertStringToDateTime(endDate, "yyyy-MM-dd", out _endDate))
                    _endDate = DateTime.Now;
            }

            if (isFilterCreatedTime)
            {
                _startDate = _startDate.Date;
                _endDate = _endDate.Date.AddDays(1).AddSeconds(-1);
            }

            if (status == (int)EBANNED_STATUS.DELTED_BANNED) //lấy dữ liệu đã xóa
                dataType = (int)EBANNED_DATA_TYPE.DELTED_BANNED;

            bool isGetVehicleBannedListDeleted = status == (int)EBANNED_STATUS.FILTER_DELETED;
            #endregion

            #region Filter
            var vehicleBannedListsCache = OrderQuery(VehicleBannedListCache.Instance.Get(_vehicleBannedListRepository, _logger).Where(x => ((isGetVehicleBannedListDeleted || x.DataType == dataType) &&
                ((isFilterBannedType && bannedTypeList.Contains(x.ViolationTypeId)) || !isFilterBannedType) &&
                ((isFilterCreatedTime && x.CreatedDate >= _startDate && x.CreatedDate <= _endDate) || !isFilterCreatedTime)) &&
                ((isFilterTruckId && RemoveSpecialCharactersFromTruckId(x.TruckID).Contains(searchValue)) || !isFilterTruckId) &&
                ((isFilterCavet && x.TruckKey != null && x.TruckKey.Contains(searchValue)) || !isFilterCavet) &&
                ((isFilterOwnerName && x.OwnerName != null && x.OwnerName.ToLower().Contains(searchValue)) || !isFilterOwnerName) &&
                ((isFilterNote && x.Note != null && x.Note.ToLower().Contains(searchValue)) || !isFilterNote)), orderBy, orderType);
            #endregion

            List<VehicleBannedListModel> vehicleBannedLists = null;
            if (isFilterStatusValid ||
                isFilterStatusValidFromTo ||
                isFilterClearBy ||
                isFilterCreatedBy ||
                isFilterReasonViolation ||
                isFilterReasonClear ||
                isFilterStatusExpiry ||
                isFilterActiveTime ||
                isFilterExpiryTime ||
                isFilterFoundBy
                )
            {
                #region Filter
                vehicleBannedLists = GetAvailableBannedFromHistory(vehicleBannedListsCache, user, (availableBanned, firstClearBannedHistory, bannedList) =>
                {
                    if (availableBanned == null) //hết lệnh cấm
                    {
                        if (isFilterStatusValid || isFilterStatusValidFromTo) 
                            return false;

                        if (!isFilterActiveTime || (firstClearBannedHistory != null && firstClearBannedHistory.ActiveTime >= _fromActiveTime && firstClearBannedHistory.ActiveTime <= _toActiveTime))
                        { } else { return false; }

                        if (!isFilterExpiryTime || (firstClearBannedHistory != null && firstClearBannedHistory.ExpiryTime >= _fromExpiryTime && firstClearBannedHistory.ExpiryTime <= _toExpiryTime))
                        { } else { return false; }
                    }
                    else //đang cấm
                    {
                        if (isFilterStatusExpiry || isFilterReasonClear) return false;
                        if (!isFilterStatusValidFromTo || (availableBanned.ExpiryTime.Value >= _fromBannedTime && availableBanned.ActiveTime <= _toBannedTime))
                        { } else { return false; }

                        if (!isFilterActiveTime || (availableBanned.ActiveTime >= _fromActiveTime && availableBanned.ActiveTime <= _toActiveTime))
                        { } else { return false; }

                        if (!isFilterExpiryTime || (availableBanned.ExpiryTime >= _fromExpiryTime && availableBanned.ExpiryTime <= _toExpiryTime))
                        { } else { return false; }
                    }

                    if (isFilterClearBy || isFilterCreatedBy || isFilterReasonClear ||
                        isFilterReasonViolation || isFilterFoundBy)
                    {
                        bool isFoundClearBy = !isFilterClearBy;
                        bool isFoundCreatedBy = !isFilterCreatedBy;
                        bool isFoundReasonClear = !isFilterReasonClear;
                        bool isFoundReasonViolation = !isFilterReasonViolation;
                        bool isFoundFoundBy = !isFilterFoundBy;

                        foreach (var history in bannedList.VehicleBannedListHistorys)
                        {
                            if (isFilterClearBy)
                            {
                                if (history.ActionType == (int)EBANNED_HISTORY_ACTION_TYPE.HUY_CAM && clearByList.Contains(history.CreatedUser))
                                {
                                    isFoundClearBy = true;
                                    break;
                                }
                            }

                            if (isFilterCreatedBy)
                            {
                                if (history.ActionType == (int)EBANNED_HISTORY_ACTION_TYPE.CAM && createdByList.Contains(history.CreatedUser))
                                {
                                    isFoundCreatedBy = true;
                                    break;
                                }
                            }

                            if(isFilterReasonClear)
                            {
                                if (history.ActionType == (int)EBANNED_HISTORY_ACTION_TYPE.HUY_CAM && history.ReasonClear != null && history.ReasonClear.ToLower().Contains(searchValue))
                                {
                                    isFoundReasonClear = true;
                                    break;
                                }
                            }

                            if(isFilterReasonViolation)
                            {
                                if (history.ActionType == (int)EBANNED_HISTORY_ACTION_TYPE.CAM && history.ReasonViolation != null && history.ReasonViolation.ToLower().Contains(searchValue))
                                {
                                    isFoundReasonViolation = true;
                                    break;
                                }
                            }

                            if(isFilterFoundBy)
                            {
                                if (history.ActionType == (int)EBANNED_HISTORY_ACTION_TYPE.CAM && history.FoundBy != null && history.FoundBy.ToLower().Contains(searchValue))
                                {
                                    isFoundFoundBy = true;
                                    break;
                                }
                            }
                        }

                        if (!isFoundClearBy || !isFoundCreatedBy || 
                        !isFoundReasonClear || !isFoundReasonViolation ||
                        !isFoundFoundBy) 
                            return false;
                    }

                    recordsTotal++;
                    return true;

                }, start, length);
                #endregion
            }
            else
            {
                recordsTotal = vehicleBannedListsCache.Count();
                if (length != -1)
                    vehicleBannedListsCache = vehicleBannedListsCache.Skip(start).Take(length).ToList();

                vehicleBannedLists = GetAvailableBannedFromHistory(vehicleBannedListsCache, user);
            }

            return Json(new
            {
                draw = draw,
                recordsFiltered = recordsTotal,
                recordsTotal = recordsTotal,
                data = vehicleBannedLists
            });
        }

        /// <summary>
        /// Lấy danh sách các lệnh cấm còn hiệu lực trong lịch sử cấm
        /// </summary>
        /// <param name="_vehicleBannedLists"></param>
        /// <param name="user"></param>
        /// <param name="filter"></param>
        /// <param name="start"></param>
        /// <param name="length"></param>
        /// <returns></returns>
        private List<VehicleBannedListModel> GetAvailableBannedFromHistory(List<VehicleBannedList> _vehicleBannedLists, User user, Func<VehicleBannedListHistory, VehicleBannedListHistory, VehicleBannedList, bool> filter = null, int start = 0, int length = -1)
        {
            var vehicleBannedLists = new List<VehicleBannedListModel>();
            int index = -1;
            foreach (var _bannedList in _vehicleBannedLists)
            {
                VehicleBannedListModel bannedList = null;
                var availableBanned = FindAvailableBannedHistory(_bannedList.VehicleBannedListHistorys);
                if (availableBanned == null) //không còn lệnh cấm
                {
                    var firstClearBannedHistory = _bannedList.VehicleBannedListHistorys.Where(x => x.ActionType == (int)EBANNED_HISTORY_ACTION_TYPE.HUY_CAM).FirstOrDefault();
                    var firstrBannedHistory = _bannedList.VehicleBannedListHistorys.Where(x => x.ActionType == (int)EBANNED_HISTORY_ACTION_TYPE.CAM).FirstOrDefault();

                    if (filter != null && !filter(null, firstClearBannedHistory, _bannedList))
                        continue;

                    index++;
                    if (length != -1 && (index < start || index >= start + length))
                        continue;

                    if (length != -1 && vehicleBannedLists.Count >= length)
                        continue;

                    bannedList = VehicleBannedListModel.Parse(_bannedList);
                    bannedList.IsValidBanned = false;
                    bannedList.ClearFlag = "Y";
                    bannedList.ClearBy = GetUserNameById(firstClearBannedHistory != null ? firstClearBannedHistory.CreatedUser : bannedList.ClearByVal);
                    bannedList.ClearDate = Utils.GetDefaultString(firstClearBannedHistory != null ? firstClearBannedHistory.CreatedDate : bannedList.ClearDateVal);
                    bannedList.ReasonClear = Utils.GetDefaultString(firstClearBannedHistory != null ? firstClearBannedHistory.ReasonClear : bannedList.ReasonClear);
                    bannedList.ViolationTypeId = firstClearBannedHistory != null ? firstClearBannedHistory.ViolationTypeId : bannedList.ViolationTypeId;
                    bannedList.ViolationType = Utils.GetDefaultString(firstClearBannedHistory != null ? firstClearBannedHistory.ViolationType : bannedList.ViolationType);
                    bannedList.Note = Utils.GetDefaultString(firstClearBannedHistory != null ? firstClearBannedHistory.Note : bannedList.Note);
                    bannedList.BannedBy = GetUserNameById(firstrBannedHistory != null ? firstrBannedHistory.CreatedUser : bannedList.CreatedByVal);
                    bannedList.BannedTime = firstrBannedHistory != null ? firstrBannedHistory.CreatedDate.ToString("dd/MM/yyyy HH:mm") : bannedList.CreatedDate;
                    bannedList.SiteName = GetUserSiteNameById(firstClearBannedHistory != null ? firstClearBannedHistory.CreatedUser : bannedList.CreatedByVal);
                    bannedList.SiteId = GetUserSiteIdById(firstClearBannedHistory != null ? firstClearBannedHistory.CreatedUser : bannedList.CreatedByVal);
                }
                else
                {
                    if (filter != null && !filter(availableBanned, null, _bannedList))
                        continue;

                    index++;
                    if (length != -1 && (index < start || index >= start + length))
                        continue;

                    if (length != -1 && vehicleBannedLists.Count >= length)
                        continue;

                    bannedList = VehicleBannedListModel.Parse(_bannedList);
                    bannedList.IsValidBanned = true;
                    bannedList.ReasonViolation = availableBanned.ReasonViolation == null ? string.Empty : availableBanned.ReasonViolation;
                    bannedList.FoundBy = Utils.GetDefaultString(availableBanned.FoundBy);
                    bannedList.ActiveTime = availableBanned.ActiveTime.ToString("dd/MM/yyyy HH:mm");
                    bannedList.ExpiryTime = availableBanned.ExpiryTime.HasValue ? availableBanned.ExpiryTime.Value.ToString("dd/MM/yyyy HH:mm") : string.Empty;
                    bannedList.ClearFlag = string.Empty;
                    bannedList.ClearBy = string.Empty;
                    bannedList.ClearDate = string.Empty;
                    bannedList.ReasonClear = string.Empty;
                    bannedList.ViolationTypeId = availableBanned.ViolationTypeId;
                    bannedList.ViolationType = Utils.GetDefaultString(availableBanned.ViolationType);
                    bannedList.Note = Utils.GetDefaultString(availableBanned.Note);
                    bannedList.BannedBy = GetUserNameById(availableBanned.CreatedUser);
                    bannedList.BannedTime = availableBanned.CreatedDate.ToString("dd/MM/yyyy HH:mm");
                    bannedList.SiteName = GetUserSiteNameById(availableBanned.CreatedUser);
                    bannedList.SiteId = GetUserSiteIdById(availableBanned.CreatedUser);
                }

                if (bannedList == null) continue;

                bannedList.NumOfBannedTimes = _bannedList.VehicleBannedListHistorys.Count(x => x.ActionType == (int)EBANNED_HISTORY_ACTION_TYPE.CAM);
                bannedList.CreatedBy = bannedList.CreatedByVal > 0 ? GetUserNameById(bannedList.CreatedByVal) : string.Empty;
                bannedList.IsHasClearRole = IsHasUserRole(user, bannedList.ViolationTypeId + 1);
                bannedList.IsHasForbidRole = IsHasUserRole(user, bannedList.ViolationTypeId);

                vehicleBannedLists.Add(bannedList);
            }

            return vehicleBannedLists;
        }

        /// <summary>
        /// Tìm lệnh cấm còn hiệu lực trong danh sách các lịch sử của một lệnh cấm
        /// </summary>
        /// <param name="historys"></param>
        /// <returns></returns>
        private VehicleBannedListHistory FindAvailableBannedHistory(List<VehicleBannedListHistory> historys)
        {
            DateTime curDateTime = DateTime.Now.Date;
            foreach (var history in historys)
            {
                if (history.ActionType == (int)EBANNED_HISTORY_ACTION_TYPE.CAM &&
                    (history.ActiveTime.Date <= curDateTime && history.ExpiryTime.Value >= curDateTime))
                {
                    var clearHistory = historys.Find(x => x.BannedHistoryId == history.Id);
                    if (clearHistory == null) //chưa có lịch sử hủy
                        return history;
                }
            }

            return null;
        }

        /// <summary>
        /// Tìm lệnh cấm còn hiệu lực trong danh sách các lịch sử của một lệnh cấm
        /// </summary>
        /// <param name="historys"></param>
        /// <returns></returns>
        private VehicleBannedListHistory FindAvailableBannedHistory(ICollection<VehicleBannedListHistory> historys)
        {
            DateTime curDateTime = DateTime.Now.Date;
            foreach (var history in historys)
            {
                if (history.ActionType == (int)EBANNED_HISTORY_ACTION_TYPE.CAM &&
                    (history.ActiveTime.Date <= curDateTime && history.ExpiryTime.Value >= curDateTime))
                {
                    var clearHistory = historys.Count(x => x.BannedHistoryId == history.Id);
                    if (clearHistory == 0) //chưa có lịch sử hủy
                        return history;
                }
            }

            return null;
        }

        /// <summary>
        /// order by vehicle banned list
        /// </summary>
        /// <param name="query"></param>
        /// <param name="orderBy"></param>
        /// <param name="orderType"></param>
        /// <returns></returns>
        private List<VehicleBannedList> OrderQuery(IEnumerable<VehicleBannedList> query, string orderBy, string orderType)
        {
            if (!string.IsNullOrEmpty(orderBy) && !string.IsNullOrEmpty(orderType))
            {
                IOrderedEnumerable<VehicleBannedList> vehicleBannedLists = null;
                bool isOrderAsc = orderType == "asc";
                switch (orderBy)
                {
                    case "id":
                        vehicleBannedLists = isOrderAsc ? query.OrderBy(x => x.ActiveTime).OrderBy(x => x.Id) : query.OrderByDescending(x => x.ActiveTime).OrderByDescending(x => x.Id);
                        break;
                    case "truckID":
                        vehicleBannedLists = isOrderAsc ? query.OrderBy(x => x.TruckID) : query.OrderByDescending(x => x.TruckID);
                        break;
                    case "activeTime":
                        vehicleBannedLists = isOrderAsc ? query.OrderBy(x => x.ActiveTime) : query.OrderByDescending(x => x.ActiveTime);
                        break;
                    case "expiryTime":
                        vehicleBannedLists = isOrderAsc ? query.OrderBy(x => x.ExpiryTime) : query.OrderByDescending(x => x.ExpiryTime);
                        break;
                    case "truckKey":
                        vehicleBannedLists = isOrderAsc ? query.OrderBy(x => x.TruckKey) : query.OrderByDescending(x => x.TruckKey);
                        break;
                    case "createdDate": //giống order theo id
                        vehicleBannedLists = isOrderAsc ? query.OrderBy(x => x.Id) : query.OrderByDescending(x => x.Id);
                        break;
                    case "updatedDate":
                        vehicleBannedLists = isOrderAsc ? query.OrderBy(x => x.UpdatedDate) : query.OrderByDescending(x => x.UpdatedDate);
                        break;
                    case "clearFlag":
                        vehicleBannedLists = isOrderAsc ? query.OrderBy(x => x.ClearFlag) : query.OrderByDescending(x => x.ClearFlag);
                        break;
                    case "violationType":
                        vehicleBannedLists = isOrderAsc ? query.OrderBy(x => x.ViolationType) : query.OrderByDescending(x => x.ViolationType);
                        break;
                    case "ownerName":
                        vehicleBannedLists = isOrderAsc ? query.OrderBy(x => x.OwnerName) : query.OrderByDescending(x => x.OwnerName);
                        break;
                    case "reasonViolation":
                        vehicleBannedLists = isOrderAsc ? query.OrderBy(x => x.ReasonViolation) : query.OrderByDescending(x => x.ReasonViolation);
                        break;
                    case "foundBy":
                        vehicleBannedLists = isOrderAsc ? query.OrderBy(x => x.FoundBy) : query.OrderByDescending(x => x.FoundBy);
                        break;
                    case "clearBy":
                        vehicleBannedLists = isOrderAsc ? query.OrderBy(x => x.ClearBy) : query.OrderByDescending(x => x.ClearBy);
                        break;
                    case "clearDate":
                        vehicleBannedLists = isOrderAsc ? query.OrderBy(x => x.ClearDate) : query.OrderByDescending(x => x.ClearDate);
                        break;
                    case "reasonClear":
                        vehicleBannedLists = isOrderAsc ? query.OrderBy(x => x.ReasonClear) : query.OrderByDescending(x => x.ReasonClear);
                        break;
                    case "numOfBannedTimes":
                        vehicleBannedLists = isOrderAsc ? query.OrderBy(x => x.VehicleBannedListHistorys.Count) : query.OrderByDescending(x => x.VehicleBannedListHistorys.Count);
                        break;
                }
                return vehicleBannedLists.ToList();
            }
            return query.ToList();
        }

        /// <summary>
        /// Lấu danh sách người dùng trong danh sách các nhóm
        /// Dùng đề filter danh sách nhóm cấm
        /// </summary>
        /// <param name="groupIds"></param>
        /// <returns></returns>
        private List<int> GetUsersInGroups(string groupIds)
        {
            List<int> UserIdList = new List<int>();
            var groupIdList = ConvertStringToListInt(groupIds);
            lock (_lock)
            {
                foreach (var groupId in groupIdList)
                {
                    if (BannedGroupUsers.ContainsKey(groupId))
                        UserIdList.AddRange(BannedGroupUsers[groupId].UserIds);
                }
            }

            if (UserIdList.Count == 0)
                return null;

            return UserIdList;
        }
        #endregion

        #region Lịch sử cấm vào ra
        [HttpPost("VehicleBannedList/GetVehicleBannedListHistorys")]
        public JsonResult GetVehicleBannedListHistorys(int draw, int start, int length, string keyword, int id)
        {
            var userLogin = GetUserRole();
            if (userLogin == null)
                return Json(new
                {
                    draw = 0,
                    recordsFiltered = 0,
                    recordsTotal = 0
                });

            bool isSearch = !string.IsNullOrEmpty(keyword);
            IQueryable<VehicleBannedListHistory> query = _vehicleBannedListHistoryRepository.GetIQueryable();
            query = query.Where(x => x.VehicleBannedListId == id);

            int recordsTotal = query.Count();
            if (length == -1)
                length = int.MaxValue;

            DateTime curDateTime = DateTime.Now;
            int bannedActionType = (int)EBANNED_HISTORY_ACTION_TYPE.CAM;

            var historyList = query.Select(x => new VehicleBannedListHistoryModel
            {
                Id = x.Id,
                ActionTypeVal = x.ActionType,
                ActionType = GetHistoryActionTypeText(x.ActionType),
                CreatedByVal = x.CreatedUser,
                UpdatedByVal = x.UpdatedBy.HasValue ? x.UpdatedBy.Value : 0,
                UserAgent = string.Empty,
                TruckID = x.TruckID,
                IsBanned = (x.ActionType == bannedActionType && x.ActiveTime.Date <= curDateTime && x.ExpiryTime.Value >= curDateTime),
                ActiveTime = x.ActiveTime.ToString("dd/MM/yyyy HH:mm"),
                ExpiryTime = x.ExpiryTime.HasValue ? x.ExpiryTime.Value.ToString("dd/MM/yyyy HH:mm") : string.Empty,
                ReasonViolation = x.ReasonViolation == null ? string.Empty : x.ReasonViolation,
                CreatedDate = x.CreatedDate.ToString("dd/MM/yyyy HH:mm:ss"),
                UpdatedDate = x.UpdatedDate.HasValue ? x.UpdatedDate.Value.ToString("dd/MM/yyyy HH:mm") : string.Empty,
                TruckKey = x.TruckKey == null ? string.Empty : x.TruckKey,
                FoundBy = x.FoundBy == null ? string.Empty : x.FoundBy,
                ClearFlag = x.ActionType == (int)EBANNED_HISTORY_ACTION_TYPE.HUY_CAM,
                ClearBy = string.Empty,
                UpdatedByName = string.Empty,
                VehicleClearBy = x.VehicleClearBy,
                ReasonClear = x.ActionType != (int)EBANNED_HISTORY_ACTION_TYPE.HUY_CAM ? "" : x.ReasonClear,
                Note = x.Note == null ? string.Empty : x.Note,
                OwnerName = x.OwnerName == null ? string.Empty : x.OwnerName,
                ClearDate = (x.ActionType != (int)EBANNED_HISTORY_ACTION_TYPE.HUY_CAM) ? "" : x.CreatedDate.ToString("dd/MM/yyyy HH:mm"),
                ViolationType = x.ViolationType,
                ViolationTypeId = x.ViolationTypeId,
                EditHistoryJson = x.EditHistoryJson,
                BannedHistoryId = x.BannedHistoryId,
            }).OrderByDescending(x => x.Id).ToList();

            //Kiểm tra có hiển thị nút hủy cấm - Begin
            bool hasCurrentBannedHistory = false;
            foreach (var item in historyList)
            {
                if (item.ActionTypeVal == (int)EBANNED_HISTORY_ACTION_TYPE.CAM)
                {
                    var clearHistory = historyList.Find(x => x.BannedHistoryId == item.Id);
                    item.AllowClear = (clearHistory == null) && IsHasUserRole(userLogin, item.ViolationTypeId + 1);
                   
                    if (clearHistory == null && !hasCurrentBannedHistory && item.IsBanned)
                    {
                        hasCurrentBannedHistory = true;
                        item.IsCurrentBannedHistory = true;
                    }

                    if (clearHistory == null && !item.IsBanned)
                    {
                        item.AllowClear = false;
                        item.IsExpired = true;
                    }
                }
                else if (item.ActionTypeVal == (int)EBANNED_HISTORY_ACTION_TYPE.HUY_CAM && item.BannedHistoryId.HasValue && item.BannedHistoryId.Value > 0)
                {
                    item.BannedIndex = historyList.FindIndex(x => x.Id == item.BannedHistoryId.Value);
                    if (item.BannedIndex != -1) item.BannedIndex++;
                }
            }
            //Kiểm tra có hiển thị nút hủy cấm - End

            historyList = historyList.Skip(start).Take(length).ToList();
            RemoveNotMatchVehicleBannedListHistory(historyList, isSearch, keyword);

            //Hiển thị nút sửa - Begin
            List<string> functions = GetUserRoleFunctionsList(userLogin);
            foreach (var item in historyList)
            {
                if (string.IsNullOrEmpty(item.UserAgent)) item.UserAgent = "NONE";
                item.AllowEdit = item.ActionTypeVal == (int)EBANNED_HISTORY_ACTION_TYPE.CAM &&
                    (item.CreatedByVal == userLogin.Id || HasRoleEditVehicleBannedListHistory(functions, item.ViolationTypeId));
            }
            //Hiển thị nút sửa - End

            return Json(new
            {
                draw = draw,
                recordsFiltered = recordsTotal,
                recordsTotal = recordsTotal,
                data = historyList
            });
        }

        /// <summary>
        /// Xóa các lịch sử không trùng với search
        /// </summary>
        /// <param name="historyList"></param>
        /// <param name="isSearch"></param>
        /// <param name="keyword"></param>
        private void RemoveNotMatchVehicleBannedListHistory(List<VehicleBannedListHistoryModel> historyList, bool isSearch, string keyword)
        {
            List<VehicleBannedListHistoryModel> removeList = new List<VehicleBannedListHistoryModel>();
            lock (_lock)
            {
                foreach (var item in historyList)
                {
                    if (item.CreatedByVal > 0)
                    {
                        foreach (var user in UserBannedInfos)
                        {
                            if (user.Key == item.CreatedByVal)
                            {
                                item.UserAgent = user.Value.Name;
                                if (item.ClearFlag)
                                    item.ClearBy = user.Value.Name;
                                break;
                            }
                        }
                    }

                    if (item.UpdatedByVal > 0)
                    {
                        foreach (var user in UserBannedInfos)
                        {
                            if (user.Key == item.UpdatedByVal)
                            {
                                item.UpdatedByName = user.Value.Name;
                                break;
                            }
                        }
                    }

                    if (isSearch && !item.UserAgent.Contains(keyword) &&
                                !item.ClearBy.Contains(keyword) &&
                                !item.ActionType.Equals(keyword) &&
                                !item.CreatedDate.Contains(keyword) &&
                                !item.ReasonViolation.Contains(keyword) &&
                                !item.TruckID.Contains(keyword) &&
                                !item.TruckKey.Contains(keyword) &&
                                !item.ActiveTime.Contains(keyword) &&
                                !item.ExpiryTime.Contains(keyword) &&
                                !item.OwnerName.Contains(keyword))
                    {
                        removeList.Add(item);
                    }
                }
            }

            foreach (var item in removeList)
            {
                historyList.Remove(item);
            }
        }

        /// <summary>
        /// Lấy action của lịch sử cấm
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        private string GetHistoryActionTypeText(int type)
        {
            switch (type)
            {
                case (int)EBANNED_HISTORY_ACTION_TYPE.CAM:
                    return "Cấm";
                case (int)EBANNED_HISTORY_ACTION_TYPE.HUY_CAM:
                    return "Hủy cấm";
                case (int)EBANNED_HISTORY_ACTION_TYPE.DELETED:
                    return "Xóa";
                case (int)EBANNED_HISTORY_ACTION_TYPE.UPDATED:
                    return "Sửa";
            }

            return "";
        }

        private List<string> GetUserRoleFunctionsList(User user)
        {
            List<string> functions = new List<string>();
            foreach (var userInRole in user.UserInRoles)
            {
                string[] functionArray = userInRole.Role.ListFunction_AM.Split(',');
                functions.AddRange(functionArray);
            }
            return functions;
        }

        private bool HasRoleEditVehicleBannedListHistory(List<string> functions, int violationTypeId)
        {
            if (!functions.Contains(ConvertFunctionEnumToString(EPERMISSIONS_AM.EditBlackList)))
                return false;

            if (violationTypeId == (int)EPERMISSIONS_AM.ForbidViolatingLoadControl && functions.Contains(ConvertFunctionEnumToString(EPERMISSIONS_AM.EditViolatingLoadControl)))
                return true;

            if (violationTypeId == (int)EPERMISSIONS_AM.ForbidViolatingTrafficSafety && functions.Contains(ConvertFunctionEnumToString(EPERMISSIONS_AM.EditViolatingTrafficSafety)))
                return true;

            if (violationTypeId == (int)EPERMISSIONS_AM.ForbidViolationOfBehavioralCulture && functions.Contains(ConvertFunctionEnumToString(EPERMISSIONS_AM.EditViolationOfBehavioralCulture)))
                return true;

            if (violationTypeId == (int)EPERMISSIONS_AM.ForbidViolationOfPortSecurity && functions.Contains(ConvertFunctionEnumToString(EPERMISSIONS_AM.EditViolationOfPortSecurity)))
                return true;

            if (violationTypeId == (int)EPERMISSIONS_AM.ForbidCarInOutImproperly && functions.Contains(ConvertFunctionEnumToString(EPERMISSIONS_AM.EditCarInOutImproperly)))
                return true;

            if (violationTypeId == (int)EPERMISSIONS_AM.ForbidOther && functions.Contains(ConvertFunctionEnumToString(EPERMISSIONS_AM.EditOther)))
                return true;

            return false;
        }



        /// <summary>
        /// Cập nhật lịch sử vào ra
        /// </summary>
        /// <param name="vehicleBannedListHistory"></param>
        /// <returns></returns>
        [HttpPost("VehicleBannedList/UpdateVehicleBannedListHistory")]
        public JsonResult UpdateVehicleBannedListHistory(VehicleBannedListHistory vehicleBannedListHistory)
        {
            var user = GetUserRole();
            if (user == null)
            {
                Response.StatusCode = 401;
                return Json("Security/Login"); //yêu cầu đăng nhập lại
            }

            if (!vehicleBannedListHistory.ExpiryTime.HasValue)
            {
                return Json(new
                {
                    success = false,
                    message = "Vui lòng nhập đầy đủ thông tin"
                });
            }

            var _vehicleBannedListHistory = _vehicleBannedListHistoryRepository.GetById(vehicleBannedListHistory.Id);
            if (_vehicleBannedListHistory == null || _vehicleBannedListHistory.ActionType != (int)EBANNED_HISTORY_ACTION_TYPE.CAM)
            {
                return Json(new
                {
                    success = false,
                    message = "Không tìm thấy lịch sử"
                });
            }

            if (_vehicleBannedListHistory.CreatedUser != user.Id && 
                !IsHasUserRole(user, (int)EPERMISSIONS_AM.EditBlackList))
            {
                return Json(new
                {
                    success = false,
                    message = "Bạn không có quyền sửa lịch sử cấm!"
                });
            }

            string oldExpiryTime = _vehicleBannedListHistory.ExpiryTime.HasValue ? _vehicleBannedListHistory.ExpiryTime.Value.ToString("dd/MM/yyyy HH:mm:ss") : "";
            string oldReasonViolation = _vehicleBannedListHistory.ReasonViolation;

            string oldObject = JsonConvert.SerializeObject(new
            {
                ViolationType = _vehicleBannedListHistory.ViolationType,
                VehicleBannedListId = _vehicleBannedListHistory.VehicleBannedListId,
                VehicleClearBy = _vehicleBannedListHistory.VehicleClearBy,
                CreatedDate = _vehicleBannedListHistory.CreatedDate,
                ClearFlag = _vehicleBannedListHistory.ClearFlag,
                ActionType = _vehicleBannedListHistory.ActionType,
                TruckID = _vehicleBannedListHistory.TruckID,
                ActiveTime = _vehicleBannedListHistory.ActiveTime,
                ExpiryTime = _vehicleBannedListHistory.ExpiryTime,
                ReasonViolation = _vehicleBannedListHistory.ReasonViolation
            });

            _vehicleBannedListHistory.EditHistoryJson = AddEditVehicleBannedListHistoryLog(_vehicleBannedListHistory, vehicleBannedListHistory.Note, string.IsNullOrWhiteSpace(user.FullName) ? user.UserName :  user.FullName);
            _vehicleBannedListHistory.ExpiryTime = vehicleBannedListHistory.ExpiryTime;
            _vehicleBannedListHistory.ReasonViolation = vehicleBannedListHistory.ReasonViolation;
            _vehicleBannedListHistory.UpdatedDate = DateTime.Now;
            _vehicleBannedListHistory.UpdatedBy = user.Id;

            _logger.LogInformation("User " + user.UserName + ", cap nhat lich su cam vao ra " + _vehicleBannedListHistory.CreatedDate.ToString("dd/MM/yyyy HH:mm:ss")
                + ", cho xe cam " + _vehicleBannedListHistory.TruckID);
            try
            {
                bool isUpdateCurrentHistory = false;
                var lastVehicleBannedListHistory = _vehicleBannedListHistoryRepository.GetBy(x => x.VehicleBannedListId == _vehicleBannedListHistory.VehicleBannedListId).OrderByDescending(x => x.Id).FirstOrDefault();
                if (lastVehicleBannedListHistory != null && lastVehicleBannedListHistory.Id == _vehicleBannedListHistory.Id)
                {
                    //cập lại lại vehicle banned list
                    var vehicleBannedList = _vehicleBannedListRepository.GetById(_vehicleBannedListHistory.VehicleBannedListId);
                    if(vehicleBannedList != null)
                    {
                        isUpdateCurrentHistory = true;
                        vehicleBannedList.ExpiryTime = _vehicleBannedListHistory.ExpiryTime;
                        vehicleBannedList.ReasonViolation = _vehicleBannedListHistory.ReasonViolation;
                        vehicleBannedList.UpdatedDate = DateTime.Now;
                        vehicleBannedList.UpdatedBy = user.Id;

                        _vehicleBannedListRepository.Update(vehicleBannedList);
                        _vehicleBannedListRepository.SaveChanges();
                    }
                }

                _vehicleBannedListHistoryRepository.Update(_vehicleBannedListHistory);
                _vehicleBannedListHistoryRepository.SaveChanges();

                SendEditHistoryAlarmMessage(user.Id, string.IsNullOrEmpty(user.FullName) ? user.UserName : user.FullName, 
                    oldExpiryTime, oldReasonViolation, vehicleBannedListHistory.Note, 
                    _vehicleBannedListHistory);

                InsertHistorySystem(user.Id, EACTION_TYPE.EDIT_HISTORY, EnumControllerName.BANNED_LIST, "Chỉnh sửa lịch sử cấm vào ra " + _vehicleBannedListHistory.CreatedDate.ToString("dd/MM/yyyy HH:mm:ss") 
                    + ", cho xe cấm " + _vehicleBannedListHistory.TruckID,
                    _vehicleBannedListHistory.VehicleBannedListId, oldObject, new 
                    {
                        ViolationType = _vehicleBannedListHistory.ViolationType,
                        VehicleBannedListId = _vehicleBannedListHistory.VehicleBannedListId,
                        VehicleClearBy = _vehicleBannedListHistory.VehicleClearBy,
                        CreatedDate = _vehicleBannedListHistory.CreatedDate,
                        ClearFlag = _vehicleBannedListHistory.ClearFlag,
                        ActionType = _vehicleBannedListHistory.ActionType,
                        TruckID = _vehicleBannedListHistory.TruckID,
                        ActiveTime = _vehicleBannedListHistory.ActiveTime,
                        ExpiryTime = _vehicleBannedListHistory.ExpiryTime,
                        ReasonViolation = _vehicleBannedListHistory.ReasonViolation
                    });
                    
                return Json(new
                {
                    success = true,
                    isUpdateCurrentHistory = isUpdateCurrentHistory
                });
            }
            catch(Exception ex)
            {
                _logger.LogError("VehicleBannedList/UpdateVehicleBannedListHistory: " + ex.Message);
                return Json(new
                {
                    success = false,
                    message = "Lưu thất bại"
                });
            }
        }

        /// <summary>
        /// Lưu lịch sử cấm
        /// </summary>
        /// <param name="vehicleBannedList"></param>
        /// <param name="actionType"></param>
        private void SaveHistory(VehicleBannedList vehicleBannedList, EBANNED_HISTORY_ACTION_TYPE actionType, int bannedHistoryId, VehicleBannedListHistory bannedHistory = null, bool isSendAlarmMsg = true)
        {
            try
            {
                int loginUserId = int.Parse(this.HttpContext.Session.GetString("SessionUserSystemId"));

                VehicleBannedListHistory history = new VehicleBannedListHistory();
                history.ActionType = (int)actionType;
                history.CreatedDate = DateTime.Now; //là vehicle updated date
                history.CreatedUser = loginUserId;
                history.ActiveTime = bannedHistory == null ? vehicleBannedList.ActiveTime : bannedHistory.ActiveTime;
                history.ExpiryTime = bannedHistory == null ? vehicleBannedList.ExpiryTime : bannedHistory.ExpiryTime;
                history.FoundBy = bannedHistory == null ? vehicleBannedList.FoundBy : bannedHistory.FoundBy;
                history.Note = bannedHistory == null ? vehicleBannedList.Note : bannedHistory.Note;
                history.OwnerName = bannedHistory == null ? vehicleBannedList.OwnerName : bannedHistory.OwnerName;
                history.ReasonClear = vehicleBannedList.ReasonClear;
                history.ReasonViolation = bannedHistory == null ? vehicleBannedList.ReasonViolation : bannedHistory.ReasonViolation;
                history.ViolationTypeId = bannedHistory == null ? vehicleBannedList.ViolationTypeId : bannedHistory.ViolationTypeId;
                history.ViolationType = FindViolationTypeNameById(history.ViolationTypeId);
                history.TruckID = bannedHistory == null ? vehicleBannedList.TruckID : bannedHistory.TruckID;
                history.TruckKey = bannedHistory == null ? vehicleBannedList.TruckKey : bannedHistory.TruckKey;
                history.VehicleBannedListId = vehicleBannedList.Id;
                history.ClearFlag = bannedHistory == null ? vehicleBannedList.ClearFlag : bannedHistory.ClearFlag;
                history.VehicleCreateDate = bannedHistory == null ? vehicleBannedList.CreatedDate : bannedHistory.VehicleCreateDate;
                history.VehicleCreateUser = bannedHistory == null ? vehicleBannedList.CreatedBy : bannedHistory.VehicleCreateUser;
                history.VehicleClearDate = bannedHistory == null ? vehicleBannedList.ClearDate : bannedHistory.VehicleClearDate;
                history.VehicleClearBy = bannedHistory == null ? vehicleBannedList.ClearBy : bannedHistory.VehicleClearBy;
                history.BannedHistoryId = bannedHistoryId;

                _vehicleBannedListHistoryRepository.Insert(history);
                _vehicleBannedListHistoryRepository.SaveChanges();

                if(isSendAlarmMsg)
                    SendAlarmMessage(actionType, vehicleBannedList, history, loginUserId);
            }
            catch (Exception ex)
            {
                _logger.LogError("VehicleBannedList/SaveHistory: " + ex.Message);
                Console.WriteLine(ex.Message);
            }
        }

        private string AddEditVehicleBannedListHistoryLog(VehicleBannedListHistory _vehicleBannedListHistory, string note, string userName)
        {
            var logs = new List<EditLogVehicleBannedListHistoryModel>();
            if (!string.IsNullOrWhiteSpace(_vehicleBannedListHistory.EditHistoryJson))
                logs = JsonConvert.DeserializeObject<List<EditLogVehicleBannedListHistoryModel>>(_vehicleBannedListHistory.EditHistoryJson);
            
            logs.Insert(0, new EditLogVehicleBannedListHistoryModel()
            {
                ExpiryTime = _vehicleBannedListHistory.ExpiryTime.HasValue ? _vehicleBannedListHistory.ExpiryTime.Value.ToString("dd/MM/yyyy HH:mm:ss") : "",
                Note = note,
                UpdatedTime = DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss"),
                UserName = userName,
                ReasonViolation = _vehicleBannedListHistory.ReasonViolation
            });

            return JsonConvert.SerializeObject(logs);
        }
        #endregion

        #region Cache data
        [HttpGet]
        public JsonResult LoadCache()
        {
            VehicleBannedListCache.Instance.Get(_vehicleBannedListRepository, _logger);
            return Json("ok");
        }
        #endregion

        #region Send Alarm Message
        /// <summary>
        /// Lấy danh sách người dùng gửi tin nhắn cảnh báo
        /// </summary>
        /// <returns></returns>
        private List<User> GetUsersSendAlarm()
        {
            try
            {
                List<Role> roles = new List<Role>();
                List<User> users = new List<User>();

                //lấy cấu hình alarm
                var alarmConfigs = _alarmConfigRepository.GetBy(x => x.Type == (int)EALARM.VehicleBannedAlarm).ToList();
                foreach (var alarmConfig in alarmConfigs)
                {
                    if (!string.IsNullOrWhiteSpace(alarmConfig.AlarmLevel1))
                    {
                        string[] selectedRoles = alarmConfig.AlarmLevel1.Split(',');
                        foreach (string role in selectedRoles) //duyệt tất cả nhóm trong cấu hình
                        {
                            if (int.TryParse(role, out int roleId))
                            {
                                var r = _roleRepository.Get(x => x.RoleId == roleId, null, "UserInRoles.Role,UserInRoles.User").FirstOrDefault();
                                if (r != null) //lấy nhóm
                                    roles.Add(r);
                            }
                        }
                    }
                }

                if (roles.Count == 0)
                    return users;

                foreach (var role in roles) //duyệt nhóm lấy danh sách user trong nhóm
                {
                    if (role.UserInRoles == null || role.UserInRoles.Count == 0)
                        continue;

                    foreach (var user in role.UserInRoles)
                    {
                        if (user.User == null || (string.IsNullOrWhiteSpace(user.User.Phone) &&
                            string.IsNullOrWhiteSpace(user.User.Email)))
                            continue;

                        users.Add(user.User);
                    }
                }

                return users;
            }
            catch (Exception ex)
            {
                _logger.LogError("VehicleBannedList/GetUsersSendAlarm: " + ex.Message);
            }

            return new List<User>();
        }

        private void SendAlarmMessage(string message, string title)
        {
            try
            {
                var users = GetUsersSendAlarm();
                if (users.Count > 0)
                {
                    List<string> phones = new List<string>();
                    List<string> emails = new List<string>();
                    foreach (var user in users)
                    {
                        if (!string.IsNullOrWhiteSpace(user.Phone))
                            phones.Add(user.Phone);

                        if (!string.IsNullOrWhiteSpace(user.Email))
                            emails.Add(user.Email);
                    }

                    if (phones.Count > 0)
                        _zaloService.SendTextAsync(phones.ToArray(), message);

                    if (emails.Count > 0)
                        _emailSender.SendEmailAsync(emails.ToArray(), title, message);
                }
            }
            catch(Exception ex)
            {
                _logger.LogError("VehicleBannedList/SendAlarmMessage: " + ex.Message);
            }
        }

        private void SendAlarmMessage(EBANNED_HISTORY_ACTION_TYPE actionType, VehicleBannedList vehicleBannedList, VehicleBannedListHistory history, int execUser, bool sendToThirdParty = true)
        {
            switch (actionType)
            {
                case EBANNED_HISTORY_ACTION_TYPE.CAM:
                    {
                        StringBuilder messageContent = new StringBuilder();
                        messageContent.Append("Ngày thực hiện cấm: ");
                        messageContent.Append(DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss"));
                        messageContent.Append("\n");
                        messageContent.Append("Biển số xe cấm: ");
                        messageContent.Append(vehicleBannedList.TruckID);
                        messageContent.Append("\n");
                        messageContent.Append("Loại vi phạm: ");
                        messageContent.Append(history.ViolationType != null ? history.ViolationType : string.Empty);
                        messageContent.Append("\n");
                        messageContent.Append("Người cấm: ");
                        messageContent.Append(GetUserNameOrDefault(execUser == -1 ? history.VehicleCreateUser : execUser));

                        SendAlarmMessage(messageContent.ToString(), "Cấm xe " + vehicleBannedList.TruckID + " vào ra");

                        //send to ThirdPartyAPI
                        if (sendToThirdParty)
                        {
                            var group = GetBannedGroupUserByUserId(execUser == -1 ? history.VehicleCreateUser : execUser, vehicleBannedList.ViolationTypeId);
                            var user = GetUserBannedInfoById(execUser == -1 ? history.VehicleCreateUser : execUser);
                            EportVehicleBannedListAPI.Banned(vehicleBannedList, group, user, GetSiteViolationDict(), _ePortSender);
                        }
                    }
                    break;
                case EBANNED_HISTORY_ACTION_TYPE.HUY_CAM:
                    {
                        StringBuilder messageContent = new StringBuilder();
                        messageContent.Append("Ngày hủy cấm: ");
                        messageContent.Append(DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss"));
                        messageContent.Append("\n");
                        messageContent.Append("Biển số xe được hủy cấm: ");
                        messageContent.Append(vehicleBannedList.TruckID);
                        messageContent.Append("\n");
                        messageContent.Append("Lý do hủy: ");
                        messageContent.Append(history.ReasonClear != null ? history.ReasonClear : string.Empty);
                        messageContent.Append("\n");
                        messageContent.Append("Người hủy: ");
                        messageContent.Append(GetUserNameOrDefault(execUser == -1 ? history.VehicleClearBy : execUser));

                        SendAlarmMessage(messageContent.ToString(), "Hủy cấm xe " + vehicleBannedList.TruckID + " vào ra");

                        //send to ThirdPartyAPI 
                        if (sendToThirdParty)
                        {
                            var group = GetBannedGroupUserByUserId(execUser == -1 ? history.VehicleCreateUser : execUser, history.ViolationTypeId);
                            var user = GetUserBannedInfoById(execUser == -1 ? history.VehicleCreateUser : execUser);
                            EportVehicleBannedListAPI.Clear(history, group, user, GetSiteViolationDict(), _ePortSender);
                        }
                    }
                    break;
                case EBANNED_HISTORY_ACTION_TYPE.DELETED:
                    {
                        var user = GetCurrentUserLogin();
                        if (user == null) return;
                        StringBuilder messageContent = new StringBuilder();
                        messageContent.Append("Ngày xóa: ");
                        messageContent.Append(DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss"));
                        messageContent.Append("\n");
                        messageContent.Append("Biển số xe cấm: ");
                        messageContent.Append(vehicleBannedList.TruckID);
                        messageContent.Append("\n");
                        messageContent.Append("Ngày cấm: ");
                        messageContent.Append(history.VehicleCreateDate.HasValue ? history.VehicleCreateDate.Value.ToString("dd/MM/yyyy HH:mm:ss") : string.Empty);
                        messageContent.Append("\n");
                        messageContent.Append("Người xóa: ");
                        messageContent.Append(user != null ? (string.IsNullOrEmpty(user.FullName) ? user.UserName : user.FullName) : string.Empty);

                        SendAlarmMessage(messageContent.ToString(), "Xóa xe " + vehicleBannedList.TruckID + " khỏi danh sách cấm vào ra");

                        //send to ThirdPartyAPI
                        if (sendToThirdParty && IsInValidBanned(vehicleBannedList))
                        {
                            var group = GetBannedGroupUserByUserId(user.Id, history.ViolationTypeId);
                            var userInfo = GetUserBannedInfoById(user.Id);
                            EportVehicleBannedListAPI.Delete(history, group, userInfo, GetSiteViolationDict(), _ePortSender);
                        }
                    }
                    break;
            }
        }

        private void SendEditHistoryAlarmMessage(int userId, string userName, string oldExpiryTime, string oldReasonViolation, string note, VehicleBannedListHistory newVehicleBannedListHistory)
        {
            StringBuilder messageContent = new StringBuilder();
            messageContent.Append("Thời gian sửa: ");
            messageContent.Append(DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss"));
            messageContent.Append("\n");

            messageContent.Append("Người sửa: ");
            messageContent.Append(userName);
            messageContent.Append("\n");

            messageContent.Append("Biển số xe: ");
            messageContent.Append(newVehicleBannedListHistory.TruckID);
            messageContent.Append("\n");

            messageContent.Append("Lý do vi phạm(mới): ");
            messageContent.Append(newVehicleBannedListHistory.ReasonViolation != null ? newVehicleBannedListHistory.ReasonViolation : string.Empty);
            messageContent.Append("\n");

            messageContent.Append("Ngày hết hạn cấm(mới): ");
            messageContent.Append(newVehicleBannedListHistory.ExpiryTime.Value.ToString("dd/MM/yyyy HH:mm:ss"));
            messageContent.Append("\n");

            messageContent.Append("Lý do vi phạm(cũ): ");
            messageContent.Append(oldReasonViolation);
            messageContent.Append("\n");

            messageContent.Append("Ngày hết hạn cấm(cũ): ");
            messageContent.Append(oldExpiryTime);
            messageContent.Append("\n");

            messageContent.Append("Ghi chú: ");
            messageContent.Append(note);

            SendAlarmMessage(messageContent.ToString(), "Sửa cấm xe " + newVehicleBannedListHistory.TruckID + " vào ra");

            //send to ThirdPartyAPI 
            var group = GetBannedGroupUserByUserId(userId, newVehicleBannedListHistory.ViolationTypeId);
            var userInfo = GetUserBannedInfoById(userId);
            EportVehicleBannedListAPI.Update(newVehicleBannedListHistory, group, userInfo, GetSiteViolationDict(), oldExpiryTime, note, _ePortSender);
        }

        private Dictionary<string, string> GetSiteViolationDict()
        {
            var siteViolation = _generalConfigRepository.GetBy(x => x.ConfigKey.Trim() == "SiteSetViolation").FirstOrDefault();
            if(siteViolation == null)
            {
                _logger.LogError("SiteSetViolation is not found on generalConfig");
                return DefaultSiteViolationDict();
            }

            string[] siteIds = siteViolation.ConfigValue.Split(',');
            var siteDict = new Dictionary<string, string>();
            var sites = _siteRepository.GetAll();

            foreach (var siteId in siteIds)
            {
                string _siteId = siteId.Trim();
                if (_siteId == String.Empty)
                    continue;

                var site = sites.Where(x => x.SiteId.Trim().ToLower() == _siteId.ToLower()).FirstOrDefault();
                if (site == null)
                    continue;

                if(!siteDict.ContainsKey(site.SiteId.Trim())) siteDict.Add(site.SiteId.Trim(), site.SiteName);
            }

            if (siteDict.Count == 0) return DefaultSiteViolationDict();
            return siteDict;
        }

        private Dictionary<string, string> DefaultSiteViolationDict()
        {
            var siteDict = new Dictionary<string, string>();
            siteDict.Add("CTL", "Cảng Cát Lái");
            siteDict.Add("GNL", "Cảng Giang Nam");
            return siteDict;
        }

        #endregion

        #region Thêm xe cấm

        [HttpPost("VehicleBannedList/CreateVehicleBannedList")]
        public ActionResult CreateVehicleBannedList(VehicleBannedList vehicleBannedList)
        {
            var user = GetUserRole();
            if (user == null)
            {
                Response.StatusCode = 401;
                return Json("Security/Login"); //yêu cầu đăng nhập lại
            }

            if (!IsHasUserRole(user, vehicleBannedList.ViolationTypeId))
            {
                return Json(new
                {
                    success = false,
                    message = "Bạn không có quyền cấm xe với loại vi phạm này"
                });
            }

            if (!IsValid(vehicleBannedList, true))
                return Json(new
                {
                    success = false,
                    message = "Vui lòng nhập đầy đủ thông tin"
                });

            if (!IsValidBannedTime(vehicleBannedList))
            {
                return Json(new
                {
                    success = false,
                    message = "Thời gian cấm không hợp lệ"
                });
            }

            var truckID = RemoveSpecialCharactersFromTruckId(vehicleBannedList.TruckID);
            var vehicleBannedListDb = _vehicleBannedListRepository.GetBy(x => x.TruckID == truckID).FirstOrDefault();
            if (vehicleBannedListDb != null)
            {
                return Json(new
                {
                    success = false,
                    message = "Biển số xe đã tồn tại"
                });
            }
            
            _logger.LogInformation(string.Format("VehicleBannedList/CreateVehicleBannedList 1: nguoi dung {0}, cam xe {1} vao ra", user.UserName, vehicleBannedList.TruckID));

            try
            {
                string reasonViolation = FindViolationTypeNameById(vehicleBannedList.ViolationTypeId);
                if (CreateVehicleBannedListProc(vehicleBannedList, reasonViolation, vehicleBannedList.DataType))
                {
                    _logger.LogInformation(string.Format("VehicleBannedList/CreateVehicleBannedList 2: nguoi dung {0}, cam xe {1} vao ra thanh cong", user.UserName, vehicleBannedList.TruckID));
                    InsertHistorySystem(user.Id, EACTION_TYPE.BANNED, EnumControllerName.BANNED_LIST, string.Format("Cấm xe {0} vào ra", vehicleBannedList.TruckID), vehicleBannedList.Id);
                    return Json(new
                    {
                        success = true
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("VehicleBannedList/CreateVehicleBannedList: " + "loi khi cam by user " + user.UserName + " bien so cam " + (vehicleBannedList.TruckID ?? "") + "\n" + ex.Message);
            }

            return Json(new
            {
                success = false,
                message = "Thêm thất bại"
            });
        }

        /// <summary>
        /// Lưu cấm xe vào database
        /// </summary>
        /// <param name="vehicleBannedList"></param>
        /// <param name="violationType"></param>
        /// <param name="dataType"></param>
        /// <returns></returns>
        private bool CreateVehicleBannedListProc(VehicleBannedList vehicleBannedList, string violationType, int dataType)
        {
            try
            {
                vehicleBannedList = CharacterLimit(vehicleBannedList);
                int loginUserId = int.Parse(this.HttpContext.Session.GetString("SessionUserSystemId"));
                vehicleBannedList.TruckID = RemoveSpecialCharactersFromTruckId(vehicleBannedList.TruckID);
                vehicleBannedList.CreatedDate = DateTime.Now;
                vehicleBannedList.UpdatedDate = vehicleBannedList.CreatedDate;
                vehicleBannedList.CreatedBy = loginUserId;
                vehicleBannedList.DataType = dataType;
                vehicleBannedList.ViolationType = string.IsNullOrWhiteSpace(violationType) ? "Khác" : violationType;
                _vehicleBannedListRepository.Insert(vehicleBannedList);
                _vehicleBannedListRepository.SaveChanges();

                SaveHistory(vehicleBannedList, EBANNED_HISTORY_ACTION_TYPE.CAM, 0);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("VehicleBannedList/SaveVehicleBannedListProc: " + ex.Message);
                return false;
            }
        }

        /// <summary>
        /// Giới hạn số ký tự cho xe cấm
        /// </summary>
        /// <param name="vehicleBannedList"></param>
        /// <returns></returns>
        private VehicleBannedList CharacterLimit(VehicleBannedList vehicleBannedList)
        {
            if (vehicleBannedList.TruckID != null)
            {
                vehicleBannedList.TruckID = vehicleBannedList.TruckID.Trim();
                if (vehicleBannedList.TruckID.Length > 50)
                {
                    vehicleBannedList.TruckID = vehicleBannedList.TruckID.Replace(".", "");
                    if (vehicleBannedList.TruckID.Length > 50)
                    {
                        vehicleBannedList.TruckID = vehicleBannedList.TruckID.Replace("-", "");

                        if (vehicleBannedList.TruckID.Length > 50)
                            vehicleBannedList.TruckID = vehicleBannedList.TruckID.Substring(0, 50);
                    }
                }
            }

            if (vehicleBannedList.ReasonViolation != null)
            {
                vehicleBannedList.ReasonViolation = vehicleBannedList.ReasonViolation.Trim();
                if (vehicleBannedList.ReasonViolation.Length > 4000)
                    vehicleBannedList.ReasonViolation = vehicleBannedList.ReasonViolation.Substring(0, 4000);
            }

            if (vehicleBannedList.FoundBy != null)
            {
                vehicleBannedList.FoundBy = vehicleBannedList.FoundBy.Trim();
                if (vehicleBannedList.FoundBy.Length > 256)
                    vehicleBannedList.FoundBy = vehicleBannedList.FoundBy.Substring(0, 256);
            }

            if (vehicleBannedList.TruckKey != null)
            {
                vehicleBannedList.TruckKey = vehicleBannedList.TruckKey.Trim();
                if (vehicleBannedList.TruckKey.Length > 50)
                {
                    vehicleBannedList.TruckKey = vehicleBannedList.TruckKey.Replace(".", "");
                    if (vehicleBannedList.TruckKey.Length > 50)
                    {
                        vehicleBannedList.TruckKey = vehicleBannedList.TruckKey.Replace("-", "");

                        if (vehicleBannedList.TruckKey.Length > 50)
                            vehicleBannedList.TruckKey = vehicleBannedList.TruckKey.Substring(0, 50);
                    }
                }
            }

            return vehicleBannedList;
        }

        /// <summary>
        /// Valid VehicleBannedList
        /// </summary>
        /// <param name="vehicleBannedList"></param>
        /// <param name="isCheckBannedTypeExist">
        /// Có kiểm tra loại vi phạm có tồn tại không?
        /// </param>
        /// <returns></returns>
        private bool IsValid(VehicleBannedList vehicleBannedList, bool isCheckBannedTypeExist)
        {
            if (vehicleBannedList.ViolationTypeId == 0 ||
                string.IsNullOrEmpty(vehicleBannedList.TruckID))
                return false;

            if (isCheckBannedTypeExist)
            {
                if (_bannedTypeRepository.GetBy(x => x.Id == vehicleBannedList.ViolationTypeId) == null)
                    return false;
            }

            return true;
        }

        /// <summary>
        /// Kiểm tra thời gian cấm có hợp lệ
        /// </summary>
        /// <param name="vehicleBannedList"></param>
        /// <returns></returns>
        private bool IsValidBannedTime(VehicleBannedList vehicleBannedList)
        {
            if (vehicleBannedList.ExpiryTime.HasValue && vehicleBannedList.ExpiryTime < vehicleBannedList.ActiveTime)
                return false;

            return true;
        }

        //Kiểm tra còn đang bị cấm không
        private bool IsInValidBanned(VehicleBannedList vehicleBannedList)
        {
            List<VehicleBannedListHistory> historys = VehicleBannedListCache.Instance.GetHistorys(vehicleBannedList.Id, _vehicleBannedListHistoryRepository);
            var availableBanned = FindAvailableBannedHistory(historys);
            return availableBanned != null;
        }

        #endregion

        #region Cập nhật thông tin xe cấm
       
        [HttpPost("VehicleBannedList/UpdateVehicleBannedList")]
        public JsonResult UpdateVehicleBannedList(VehicleBannedList vehicleBannedList, bool? haveBannedAgain)
        {
            var user = GetUserRole();
            if (user == null)
            {
                Response.StatusCode = 401;
                return Json("Security/Login"); //yêu cầu đăng nhập lại
            }

            if (!IsValid(vehicleBannedList, true))
                return Json(new
                {
                    success = false,
                    message = "Vui lòng nhập đầy đủ thông tin"
                });

            var vehicleBannedListDb = _vehicleBannedListRepository.GetById(vehicleBannedList.Id);
            if (vehicleBannedListDb == null)
                return Json(new
                {
                    success = false,
                    message = "Không tìm thấy thông tin cấm"
                });

            if(haveBannedAgain.HasValue)
            {
                if(!IsValidBannedTime(vehicleBannedList))
                {
                    return Json(new
                    {
                        success = false,
                        message = "Thời gian không hợp lệ"
                    });
                }

                if (!IsHasUserRole(user, vehicleBannedList.ViolationTypeId))
                {
                    return Json(new
                    {
                        success = false,
                        message = "Bạn không có quyền cấm xe với loại vi phạm này"
                    });
                }
            }
            else
            {
                if (!IsHasUserRole(user, (int)EPERMISSIONS_AM.EditBlackList))
                {
                    return Json(new
                    {
                        success = false,
                        message = "Bạn không có quyền sửa!"
                    });
                }
            }

            if(haveBannedAgain.HasValue && haveBannedAgain.Value)
                _logger.LogInformation(string.Format("VehicleBannedList/UpdateVehicleBannedList 1: nguoi dung {0}, cam xe {1}", user.UserName, vehicleBannedList.TruckID));
            else
                _logger.LogInformation(string.Format("VehicleBannedList/UpdateVehicleBannedList 2: nguoi dung {0}, cap nhat xe {1}", user.UserName, vehicleBannedList.TruckID));

            if (UpdateVehicleBannedListProc(vehicleBannedListDb, vehicleBannedList, haveBannedAgain))
            {
                if (haveBannedAgain.HasValue && haveBannedAgain.Value)
                {
                    _logger.LogInformation(string.Format("VehicleBannedList/UpdateVehicleBannedList 3: nguoi dung {0}, cam xe {1} thanh cong", user.UserName, vehicleBannedList.TruckID));
                    InsertHistorySystem(user.Id, EACTION_TYPE.BANNED, EnumControllerName.BANNED_LIST, string.Format("Cấm xe {0} vào ra", vehicleBannedList.TruckID), vehicleBannedListDb.Id);
                }
                else
                {
                    _logger.LogInformation(string.Format("VehicleBannedList/UpdateVehicleBannedList 4: nguoi dung {0}, cap nhat xe {1} thanh cong", user.UserName, vehicleBannedList.TruckID));
                    InsertHistorySystem(user.Id, EACTION_TYPE.EDIT, EnumControllerName.BANNED_LIST, string.Format("Cập nhật xe cấm {0} vào ra", vehicleBannedList.TruckID), vehicleBannedListDb.Id);
                }

                return Json(new
                {
                    success = true
                });
            }

            return Json(new
            {
                success = false,
                message = "Lưu thất bại"
            });

        }

        /// <summary>
        /// Lưu cập nhật cấm vào ra vào db
        /// </summary>
        /// <param name="vehicleBannedListDb">
        /// Thông tin xe cấm hiện tại trong db
        /// </param>
        /// <param name="vehicleBannedList">
        /// Thông tin xe cấm mới
        /// </param>
        /// <param name="haveBannedAgain">
        /// Có cấm lại xe?
        /// </param>
        /// <param name="violationType">
        /// Loại vi phạm
        /// </param>
        /// <param name="isSaveHistory">
        /// Có lưu lịch sử cấm xe
        /// </param>
        /// <returns></returns>
        private bool UpdateVehicleBannedListProc(VehicleBannedList vehicleBannedListDb, VehicleBannedList vehicleBannedList, bool? haveBannedAgain, string violationType = null, bool isSaveHistory = true)
        {
            vehicleBannedList = CharacterLimit(vehicleBannedList);
            vehicleBannedListDb.TruckID = RemoveSpecialCharactersFromTruckId(vehicleBannedList.TruckID);
            vehicleBannedListDb.TruckKey = vehicleBannedList.TruckKey;
            vehicleBannedListDb.OwnerName = vehicleBannedList.OwnerName;
            if (haveBannedAgain.HasValue)
            {
                vehicleBannedListDb.ActiveTime = vehicleBannedList.ActiveTime;
                vehicleBannedListDb.ExpiryTime = vehicleBannedList.ExpiryTime;
                vehicleBannedListDb.FoundBy = vehicleBannedList.FoundBy;
                vehicleBannedListDb.Note = vehicleBannedList.Note;
                vehicleBannedListDb.ViolationTypeId = vehicleBannedList.ViolationTypeId;
                vehicleBannedListDb.ReasonViolation = vehicleBannedList.ReasonViolation;
                vehicleBannedListDb.ViolationType = violationType == null ? FindViolationTypeNameById(vehicleBannedList.ViolationTypeId) : violationType;
            }
            vehicleBannedListDb.DataType = (int)EBANNED_DATA_TYPE.BANNED;
            vehicleBannedListDb.UpdatedDate = DateTime.Now;

            if (haveBannedAgain.HasValue && haveBannedAgain.Value)
                vehicleBannedListDb.ClearFlag = false;

            try
            {
                _vehicleBannedListRepository.Update(vehicleBannedListDb);
                _vehicleBannedListRepository.SaveChanges();

                if (isSaveHistory)
                {
                    if (haveBannedAgain.HasValue && haveBannedAgain.Value)
                        SaveHistory(vehicleBannedListDb, EBANNED_HISTORY_ACTION_TYPE.CAM, 0);
                    else
                        SaveHistory(vehicleBannedListDb, EBANNED_HISTORY_ACTION_TYPE.UPDATED, 0);
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("VehicleBannedList/OnUpdateVehicleBannedList: " + ex.Message);

                return false;
            }
        }

        #endregion

        #region Hủy cấm xe vào ra
        [HttpPost("VehicleBannedList/ClearVehicleBannedList")]
        public JsonResult ClearVehicleBannedList(int vehicleBannedListId, string ReasonClear, int? bannedHistoryId)
        {
            var user = GetUserRole();
            if (user == null)
            {
                Response.StatusCode = 401;
                return Json("Security/Login"); //yêu cầu đăng nhập lại
            }

            var vehicleBannedListInfo = _vehicleBannedListRepository.Get(x => x.Id == vehicleBannedListId, null, "VehicleBannedListHistorys").FirstOrDefault();
            if (vehicleBannedListInfo == null)
                return Json(new
                {
                    success = false,
                    message = "Không tìm thấy thông tin cấm vào ra"
                });


            if (!IsHasUserRole(user, vehicleBannedListInfo.ViolationTypeId + 1))
            {
                return Json(new
                {
                    success = false,
                    message = "Bạn không có quyền hủy cấm xe với loại vi phạm này"
                });
            }

            if (string.IsNullOrEmpty(ReasonClear))
                return Json(new
                {
                    success = false,
                    message = "Vui lòng nhập đầy đủ thông tin"
                });


            try
            {
                _logger.LogInformation(string.Format("VehicleBannedList/ClearVehicleBannedList 1: nguoi dung {0}, huy cam xe {1} vao ra", user.UserName, vehicleBannedListInfo.TruckID));

                var historys = vehicleBannedListInfo.VehicleBannedListHistorys.OrderByDescending(x => x.Id).ToList();
                var historyNotClear = GetBannedListHistoryNotClear(historys, bannedHistoryId);
                var firstHistory = GetBannedListHistoryNotClear(historys, null, true);
                if (bannedHistoryId.HasValue)
                    firstHistory = historys.Find(x => x.Id == bannedHistoryId.Value);

                int loginUserId = int.Parse(this.HttpContext.Session.GetString("SessionUserSystemId"));

                if (historyNotClear == null) //hết lệnh cấm
                {
                    vehicleBannedListInfo.ReasonClear = ReasonClear;
                    vehicleBannedListInfo.ClearBy = loginUserId;
                    vehicleBannedListInfo.ClearFlag = true;
                    vehicleBannedListInfo.ClearDate = DateTime.Now;
                    vehicleBannedListInfo.UpdatedDate = DateTime.Now;
                }
                else
                {
                    vehicleBannedListInfo.ExpiryTime = historyNotClear.ExpiryTime;
                    vehicleBannedListInfo.ActiveTime = historyNotClear.ActiveTime;
                    vehicleBannedListInfo.ViolationType = historyNotClear.ViolationType;
                    vehicleBannedListInfo.ViolationTypeId = historyNotClear.ViolationTypeId;
                    vehicleBannedListInfo.ReasonViolation = historyNotClear.ReasonViolation;
                    vehicleBannedListInfo.FoundBy = historyNotClear.FoundBy;
                    vehicleBannedListInfo.ReasonClear = ReasonClear;

                    vehicleBannedListInfo.ClearFlag = false;
                    vehicleBannedListInfo.UpdatedDate = DateTime.Now;
                }

                _vehicleBannedListRepository.Update(vehicleBannedListInfo);
                _vehicleBannedListRepository.SaveChanges();
                SaveHistory(vehicleBannedListInfo, EBANNED_HISTORY_ACTION_TYPE.HUY_CAM, bannedHistoryId.HasValue ? bannedHistoryId.Value : (firstHistory == null ? 0 : firstHistory.Id), firstHistory);

                _logger.LogInformation(string.Format("VehicleBannedList/ClearVehicleBannedList 2: nguoi dung {0}, huy cam xe {1} vao ra thanh cong", user.UserName, vehicleBannedListInfo.TruckID));
                InsertHistorySystem(user.Id, EACTION_TYPE.CLEAR_BANNED, 
                    EnumControllerName.BANNED_LIST, 
                    string.Format("Hủy cấm xe {0} vào ra", vehicleBannedListInfo.TruckID), 
                    vehicleBannedListInfo.Id);
                return Json(new
                {
                    success = true
                });
            }
            catch(Exception ex)
            {
                _logger.LogError("VehicleBannedList/ClearVehicleBannedList: " + "loi khi huy cam by user " + user.UserName + " id cam " + vehicleBannedListId + "\n" + ex.Message);
                return Json(new
                {
                    success = false,
                    message = "Lưu thất bại"
                });
            }
        }

        private VehicleBannedListHistory GetBannedListHistoryNotClear(List<VehicleBannedListHistory> historys, int? bannedHistoryId, bool isReturnFirst = false)
        {
            if (historys == null || historys.Count == 0)
                return null;

            List<VehicleBannedListHistory> historysNotClear = new List<VehicleBannedListHistory>();
            foreach(var history in historys)
            {
                if(history.ActionType == (int)EBANNED_HISTORY_ACTION_TYPE.CAM)
                {
                    var clearHistory = historys.Count(x => x.BannedHistoryId == history.Id);
                    if (clearHistory == 0)
                    {
                        if (isReturnFirst)
                            return history;
                        historysNotClear.Add(history);
                    }
                }
            }

            if (!bannedHistoryId.HasValue)
            {
                if (historysNotClear.Count < 2)
                    return null;
                return historysNotClear[1];
            }

            foreach(var hisotry in historysNotClear)
            {
                if (hisotry.Id == bannedHistoryId.Value)
                    continue;

                return hisotry;
            }

            return null;
        }

        private List<VehicleBannedListHistory> GetBannedListHistoriesNotClear(List<VehicleBannedListHistory> historys)
        {
            if (historys == null || historys.Count == 0)
                return null;

            DateTime now = DateTime.Now;
            List<VehicleBannedListHistory> historysNotClear = new List<VehicleBannedListHistory>();
            foreach (var history in historys)
            {
                if (history.ActionType == (int)EBANNED_HISTORY_ACTION_TYPE.CAM && now >= history.ActiveTime && now <= history.ExpiryTime.Value)
                {
                    var clearHistory = historys.Count(x => x.BannedHistoryId == history.Id);
                    if (clearHistory == 0)
                        historysNotClear.Add(history);
                }
            }

            return historysNotClear;
        }

        #endregion

        #region Xóa xe cấm vào ra
        [HttpPost("VehicleBannedList/DeleteVehicleBannedList")]
        public JsonResult DeleteVehicleBannedList(int id)
        {
            var user = GetUserRole();
            if(user == null)
            {
                Response.StatusCode = 401;
                return Json("Security/Login"); //yêu cầu đăng nhập lại
            }

            if(!IsHasUserRole(user, (int)EPERMISSIONS_AM.DeleteBlackList))
            {
                return Json(new
                {
                    success = false,
                    message = "Bạn không có quyền xóa xe cấm"
                });
            }

            var vehicleBannedListInfo = _vehicleBannedListRepository.Get(x=>x.Id == id, null, "VehicleBannedListHistorys").FirstOrDefault();
            if (vehicleBannedListInfo == null)
                return Json(new
                {
                    success = false,
                    message = "Not found vehicle banned list: " + id
                });

            try
            {
                _logger.LogInformation(string.Format("VehicleBannedList/DeleteVehicleBannedList 1: nguoi dung {0}, xoa xe cam xe {1} vao ra", user.UserName, vehicleBannedListInfo.TruckID));

                vehicleBannedListInfo.DataType = (int)EBANNED_DATA_TYPE.DELTED_BANNED;
                vehicleBannedListInfo.UpdatedDate = DateTime.Now;
                _vehicleBannedListRepository.SaveChanges();

                var historiesNotClears = GetBannedListHistoriesNotClear(vehicleBannedListInfo.VehicleBannedListHistorys.OrderByDescending(x => x.Id).ToList());
                
                if(historiesNotClears.Count > 0)
                {
                    for (int i = 1; i < historiesNotClears.Count; i++)
                    {
                        var h = historiesNotClears[i];
                        SaveHistory(vehicleBannedListInfo, EBANNED_HISTORY_ACTION_TYPE.HUY_CAM, h.Id, h, false);
                    }

                    SaveHistory(vehicleBannedListInfo, EBANNED_HISTORY_ACTION_TYPE.DELETED, historiesNotClears[0].Id, historiesNotClears[0]);
                }
                else
                {
                    SaveHistory(vehicleBannedListInfo, EBANNED_HISTORY_ACTION_TYPE.DELETED, 0);
                }

                

                _logger.LogInformation(string.Format("VehicleBannedList/DeleteVehicleBannedList 2: nguoi dung {0}, xoa xe cam xe {1} vao ra thanh cong", user.UserName, vehicleBannedListInfo.TruckID));
                
                InsertHistorySystem(user.Id, EACTION_TYPE.DELETE, 
                    EnumControllerName.BANNED_LIST, 
                    string.Format("Xóa cấm xe {0} vào ra", vehicleBannedListInfo.TruckID),
                    vehicleBannedListInfo.Id,
                    new 
                    {
                        ViolationType = vehicleBannedListInfo.ViolationType,
                        Id = vehicleBannedListInfo.Id,
                        ClearBy = vehicleBannedListInfo.ClearBy,
                        ClearDate = vehicleBannedListInfo.ClearDate,
                        ClearFlag = vehicleBannedListInfo.ClearFlag,
                        TruckID = vehicleBannedListInfo.TruckID,
                        ActiveTime = vehicleBannedListInfo.ActiveTime,
                        ExpiryTime = vehicleBannedListInfo.ExpiryTime,
                        ReasonViolation = vehicleBannedListInfo.ReasonViolation,
                        CreatedDate = vehicleBannedListInfo.CreatedDate,
                        CreatedBy = vehicleBannedListInfo.CreatedBy,
                        FoundBy = vehicleBannedListInfo.FoundBy,
                        TruckKey = vehicleBannedListInfo.TruckKey,
                        OwnerName = vehicleBannedListInfo.OwnerName,
                        ReasonClear = vehicleBannedListInfo.ReasonClear
                    });

                return Json(new
                {
                    success = true
                });
            }
            catch (Exception ex)
            {
                _logger.LogError("VehicleBannedList/DeleteVehicleBannedList: " + "loi khi xoa xe cam by user " + user.UserName + " id cam " + id + "\n" + ex.Message);
                return Json(new
                {
                    success = false,
                    message = "Xóa thất bại"
                });
            }
        }
        #endregion

        #region Autocomplete plate number
        [HttpPost("VehicleBannedList/SearchPlateNumber")]
        public JsonResult SearchPlateNumber(string plateNumber)
        {
            if (string.IsNullOrEmpty(plateNumber))
                return Json(new string[0]);

            plateNumber = plateNumber.ToUpper();
            var plateNumberList = _vehicleBannedListRepository.GetBy(x => x.TruckID.Replace("-", "").Replace(".", "").Replace(" ", "").Contains(plateNumber)).Select(x => new
            {
                PlateNumber = x.TruckID,
                OwnerName = x.OwnerName,
                Id = x.Id
            }).ToList();

            return Json(plateNumberList);
        }

        [HttpPost("VehicleBannedList/FindBannedIdByPlateNumber")]
        public JsonResult FindBannedIdByPlateNumber(string plateNumber)
        {
            if (string.IsNullOrEmpty(plateNumber))
                return Json(-1);

            plateNumber = plateNumber.ToUpper();
            var bannedInfo = _vehicleBannedListRepository.GetBy(x => x.TruckID.Replace("-", "").Replace(".", "").Replace(" ", "") == plateNumber).Select(x => new
            {
                Id = x.Id
            }).FirstOrDefault();

            if (bannedInfo != null)
            {
                return Json(bannedInfo.Id);
            }

            return Json(-1);
        }

        /// <summary>
        /// Lấy thông tin của lệnh cấm theo id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost("VehicleBannedList/GetVehicleBannedListInfo")]
        public VehicleBannedListModel GetVehicleBannedListInfo(int id)
        {
            IQueryable<VehicleBannedList> query = _vehicleBannedListRepository.GetIQueryable();
            query = query.Where(x => x.Id == id);

            DateTime curDateTime = DateTime.Now.Date;
            var vehicleBannedLists = query.Select(x => new VehicleBannedListModel
            {
                Id = x.Id,
                TruckID = x.TruckID,
                ActiveTime = x.ActiveTime.ToString("dd/MM/yyyy"),
                ExpiryTime = x.ExpiryTime.HasValue ? x.ExpiryTime.Value.ToString("dd/MM/yyyy") : string.Empty,
                ReasonViolation = x.ReasonViolation == null ? string.Empty : x.ReasonViolation,
                CreatedDate = x.CreatedDate.ToString("dd/MM/yyyy HH:mm"),
                TruckKey = x.TruckKey,
                UpdatedDate = x.UpdatedDate.ToString("dd/MM/yyyy HH:mm"),
                FoundBy = x.FoundBy,
                ClearFlag = x.ClearFlag ? "Y" : string.Empty,
                ClearByVal = x.ClearBy,
                ClearBy = (x.ClearFlag && x.ClearBy > 0) ? GetUserNameById(x.ClearBy) : string.Empty,
                ClearDate = x.ClearFlag ? (x.ClearDate.HasValue ? x.ClearDate.Value.ToString("dd/MM/yyyy") : string.Empty) : string.Empty,
                ReasonClear = x.ClearFlag ? x.ReasonClear : "",
                Department = string.Empty,
                UserTopoId = string.Empty,
                IsValidBanned = !x.ClearFlag && x.ActiveTime.Date <= curDateTime && x.ExpiryTime.Value >= curDateTime,
                CreatedByVal = x.CreatedBy,
                CreatedBy = x.CreatedBy > 0 ? GetUserNameById(x.CreatedBy) : string.Empty,
                Note = x.Note,
                OwnerName = x.OwnerName,
                ViolationTypeId = x.ViolationTypeId,
                ViolationType = x.ViolationType
            }).AsEnumerable().ToList();

            if (vehicleBannedLists.Count > 0)
                return vehicleBannedLists[0];

            return null;
        }

        #endregion

        #region Import
        /// <summary>
        /// Đọc dữ liệu file excel
        /// </summary>
        /// <param name="file"></param>
        /// <returns></returns>
        private async Task<Dictionary<string, List<VehicleBannedList>>> ReadExcelFile(IFormFile file)
        {
            var users = _userRepository.GetAll().ToList();
            Dictionary<string, List<VehicleBannedList>> vehicleBannedDicts = new Dictionary<string, List<VehicleBannedList>>();
            try
            {
                int loginUserId = int.Parse(this.HttpContext.Session.GetString("SessionUserSystemId"));
                using (var stream = new MemoryStream())
                {
                    await file.CopyToAsync(stream);

                    using (var package = new ExcelPackage(stream))
                    {
                        ExcelWorksheet worksheet = package.Workbook.Worksheets[0];
                        var rowCount = worksheet.Dimension.End.Row;

                        const int START_ROW = 5;
                        for (int row = START_ROW; row <= rowCount; row++)
                        {
                            var vehicleBanned = ReadVehicleBannedListFromExcel(worksheet, row, loginUserId, users);
                            if (vehicleBanned != null)
                            {
                                vehicleBanned.TruckID = RemoveSpecialCharactersFromTruckId(vehicleBanned.TruckID);
                                if (!vehicleBannedDicts.ContainsKey(vehicleBanned.TruckID))
                                    vehicleBannedDicts.Add(vehicleBanned.TruckID, new List<VehicleBannedList>());

                                vehicleBannedDicts[vehicleBanned.TruckID].Add(vehicleBanned);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("VehicleBannedList/ImportVehicleList: " + ex.Message);
                Console.WriteLine(ex.Message);
            }
            return vehicleBannedDicts;
        }

        /// <summary>
        /// ReadVehicleBannedListFromExcel
        /// </summary>
        /// <param name="worksheet"></param>
        /// <param name="row"></param>
        /// <param name="loginUserId"></param>
        /// <param name="users"></param>
        /// <returns></returns>
        private VehicleBannedList ReadVehicleBannedListFromExcel(ExcelWorksheet worksheet, int row, int loginUserId, List<User> users)
        {
            if (worksheet.Cells[row, (int)VehicleExcelColumnIndex.TRUCK_ID].Value == null)
                return null;

            var vehicleBannedList = new VehicleBannedList();
            vehicleBannedList.TruckID = GetExcelCellStringValue(worksheet, row, (int)VehicleExcelColumnIndex.TRUCK_ID);
            if (string.IsNullOrEmpty(vehicleBannedList.TruckID))
                return null;

            vehicleBannedList.ActiveTime = GetExcelCellDateTimeValue(worksheet, row, (int)VehicleExcelColumnIndex.ACTIVE_TIME, DateTime.Now);
            vehicleBannedList.ExpiryTime = GetExcelCellDateTimeValue(worksheet, row, (int)VehicleExcelColumnIndex.EXPIRY_TIME, DateTime.Now.Date.AddSeconds(-1).AddDays(8));

            vehicleBannedList.ViolationType = GetExcelCellStringValue(worksheet, row, (int)VehicleExcelColumnIndex.BANNED_TYPE);
            vehicleBannedList.ReasonViolation = GetExcelCellStringValue(worksheet, row, (int)VehicleExcelColumnIndex.REASON_VIOLATION);
            vehicleBannedList.TruckKey = GetExcelCellStringValue(worksheet, row, (int)VehicleExcelColumnIndex.TRUCK_KEY);
            vehicleBannedList.OwnerName = GetExcelCellStringValue(worksheet, row, (int)VehicleExcelColumnIndex.OWNER);
            vehicleBannedList.FoundBy = GetExcelCellStringValue(worksheet, row, (int)VehicleExcelColumnIndex.FOUND_BY);
            vehicleBannedList.ClearFlag = GetExcelCellStringValue(worksheet, row, (int)VehicleExcelColumnIndex.CLEAR_FLAG) != "";

            string clearBy = GetExcelCellStringValue(worksheet, row, (int)VehicleExcelColumnIndex.CLEAR_BY);
            if (!string.IsNullOrEmpty(clearBy))
                vehicleBannedList.ClearBy = FindUserIdByName(clearBy, users);
            else
                vehicleBannedList.ClearBy = loginUserId;

            if (!string.IsNullOrEmpty(vehicleBannedList.FoundBy))
                vehicleBannedList.CreatedBy = FindUserIdByName(vehicleBannedList.FoundBy, users);
            else
                vehicleBannedList.CreatedBy = loginUserId;

            string clearDateValue = GetExcelCellStringValue(worksheet, row, (int)VehicleExcelColumnIndex.CLEAR_DATE);
            if (!string.IsNullOrEmpty(clearDateValue))
                vehicleBannedList.ClearDate = GetExcelCellDateTimeValue(worksheet, row, (int)VehicleExcelColumnIndex.CLEAR_DATE, DateTime.Now);

            vehicleBannedList.ReasonClear = GetExcelCellStringValue(worksheet, row, (int)VehicleExcelColumnIndex.REASON_CLEAR);
            vehicleBannedList.Note = GetExcelCellStringValue(worksheet, row, (int)VehicleExcelColumnIndex.NOTE);
            vehicleBannedList.ViolationTypeId = 2810;

            return vehicleBannedList;
        }

        /// <summary>
        /// Cập nhật lại cấm xe bằng import
        /// </summary>
        /// <param name="vehicleBannedListDb"></param>
        /// <param name="vehicleBannedList"></param>
        /// <returns></returns>
        private bool UpdateImportVehicleBannedListProc(VehicleBannedList vehicleBannedListDb, VehicleBannedList vehicleBannedList)
        {
            //cập nhật lại clear flag
            vehicleBannedListDb.ClearFlag = vehicleBannedList.ClearFlag;
            vehicleBannedListDb.ClearDate = vehicleBannedList.ClearDate;
            vehicleBannedListDb.ClearBy = vehicleBannedList.ClearBy;
            vehicleBannedListDb.ReasonClear = vehicleBannedList.ReasonClear;
            if (string.IsNullOrEmpty(vehicleBannedList.Note))
                vehicleBannedListDb.Note = vehicleBannedList.Note;
            return UpdateVehicleBannedListProc(vehicleBannedListDb, vehicleBannedList, !vehicleBannedList.ClearFlag, vehicleBannedList.ViolationType, false);
        }

        /// <summary>
        /// Lưu cấm xe bằng import
        /// </summary>
        /// <param name="vehicleBannedList"></param>
        /// <param name="violationType"></param>
        /// <param name="createdBy"></param>
        /// <param name="dataType"></param>
        /// <returns></returns>
        private int SaveImportVehicleBannedListProc(VehicleBannedList vehicleBannedList, string violationType, int createdBy, int dataType)
        {
            try
            {
                vehicleBannedList = CharacterLimit(vehicleBannedList);
                int loginUserId = int.Parse(this.HttpContext.Session.GetString("SessionUserSystemId"));
                vehicleBannedList.TruckID = vehicleBannedList.TruckID.ToUpper();
                vehicleBannedList.CreatedDate = DateTime.Now;
                vehicleBannedList.UpdatedDate = vehicleBannedList.CreatedDate;
                vehicleBannedList.CreatedBy = createdBy;
                vehicleBannedList.DataType = dataType;
                vehicleBannedList.ViolationType = string.IsNullOrWhiteSpace(violationType) ? "Khác" : violationType;
                _vehicleBannedListRepository.Insert(vehicleBannedList);
                _vehicleBannedListRepository.SaveChanges();

                return vehicleBannedList.Id;
            }
            catch (Exception ex)
            {
                _logger.LogError("VehicleBannedList/SaveImportVehicleBannedListProc: " + ex.Message);
                return -1;
            }
        }

        /// <summary>
        /// Lưu lịch sử cấm bằng import
        /// </summary>
        /// <param name="vehicleBannedList"></param>
        /// <param name="actionType"></param>
        /// <param name="VehicleCreateDate"></param>
        private void SaveImportHistory(VehicleBannedList vehicleBannedList, EBANNED_HISTORY_ACTION_TYPE actionType, DateTime VehicleCreateDate)
        {
            try
            {
                bool isSendToThirdParty = (actionType == EBANNED_HISTORY_ACTION_TYPE.CAM ||
                                        (actionType == EBANNED_HISTORY_ACTION_TYPE.HUY_CAM &&
                                        IsInValidBanned(vehicleBannedList)));

                int loginUserId = int.Parse(this.HttpContext.Session.GetString("SessionUserSystemId"));

                _logger.LogInformation(string.Format("VehicleBannedList/SaveImportHistory 1: nguoi dung {0} import xe cam {1} vao ra, loai {2}", loginUserId, vehicleBannedList.TruckID, actionType));

                VehicleBannedListHistory history = new VehicleBannedListHistory();
                history.ActionType = (int)actionType;
                history.ActiveTime = vehicleBannedList.ActiveTime;
                history.CreatedDate = (actionType == EBANNED_HISTORY_ACTION_TYPE.HUY_CAM && vehicleBannedList.ClearDate.HasValue) ? vehicleBannedList.ClearDate.Value : vehicleBannedList.CreatedDate; //là vehicle updated date
                history.CreatedUser = (actionType == EBANNED_HISTORY_ACTION_TYPE.HUY_CAM && vehicleBannedList.ClearBy != 0) ?
                    vehicleBannedList.ClearBy : (actionType == EBANNED_HISTORY_ACTION_TYPE.CAM ? vehicleBannedList.CreatedBy : loginUserId);

                history.ExpiryTime = vehicleBannedList.ExpiryTime;
                history.FoundBy = vehicleBannedList.FoundBy;
                history.Note = vehicleBannedList.Note;
                history.OwnerName = vehicleBannedList.OwnerName;
                history.ReasonClear = vehicleBannedList.ReasonClear;
                history.ReasonViolation = vehicleBannedList.ReasonViolation;
                history.ViolationTypeId = vehicleBannedList.ViolationTypeId;
                history.ViolationType = FindViolationTypeNameById(vehicleBannedList.ViolationTypeId);
                history.TruckID = vehicleBannedList.TruckID;
                history.TruckKey = vehicleBannedList.TruckKey;
                history.VehicleBannedListId = vehicleBannedList.Id;
                history.ClearFlag = vehicleBannedList.ClearFlag;
                history.VehicleCreateDate = VehicleCreateDate;
                history.VehicleCreateUser = vehicleBannedList.CreatedBy;
                history.VehicleClearDate = vehicleBannedList.ClearDate;
                history.VehicleClearBy = vehicleBannedList.ClearBy;

                if(actionType == EBANNED_HISTORY_ACTION_TYPE.HUY_CAM)
                {
                    var historys = _vehicleBannedListHistoryRepository.GetBy(x => x.VehicleBannedListId == vehicleBannedList.Id).OrderByDescending(x => x.Id).ToList();
                    var firstBannedHistory = GetBannedListHistoryNotClear(historys, null, true);
                    if (firstBannedHistory != null)
                        history.BannedHistoryId = firstBannedHistory.Id;
                }

                _vehicleBannedListHistoryRepository.Insert(history);
                _vehicleBannedListHistoryRepository.SaveChanges();

                _logger.LogInformation(string.Format("VehicleBannedList/SaveImportHistory 2: nguoi dung {0} import xe cam {1} vao ra, loai {2} thanh cong", loginUserId, vehicleBannedList.TruckID, actionType));
                string logActionType = (actionType == EBANNED_HISTORY_ACTION_TYPE.CAM) ? "Cấm xe" : (actionType == EBANNED_HISTORY_ACTION_TYPE.HUY_CAM ? "Hủy cấm xe" : "Cập nhật xe cấm");
                InsertHistorySystem(loginUserId, 
                    EACTION_TYPE.IMPORT, 
                    EnumControllerName.BANNED_LIST, 
                    string.Format("{0} {1} vào ra", logActionType, vehicleBannedList.TruckID),
                    vehicleBannedList.Id);
                SendAlarmMessage(actionType, vehicleBannedList, history, -1, isSendToThirdParty);
            }
            catch (Exception ex)
            {
                _logger.LogError("VehicleBannedList/SaveImportHistory: " + ex.Message);
                Console.WriteLine(ex.Message);
            }
        }


        [HttpPost("VehicleBannedList/ImportVehicleList")]
        public JsonResult ImportVehicleList(IFormFile file, int dataType)
        {
            if (!IsValidExcelFile(file))
            {
                return Json(new
                {
                    success = false,
                    message = "Vui lòng chọn file"
                });
            }

            var vehicleBannedDicts = ReadExcelFile(file).Result;
            var bannedTypeList = _bannedTypeRepository.GetAll().ToList();

            int totalImport = vehicleBannedDicts.Count;
            int numOfImportSuccess = 0;
            foreach (var item in vehicleBannedDicts)
            {
                bool? lastClearFlag = null;
                var vehicleBannedList = item.Value.OrderBy(x => x.ActiveTime).ToList();
                var firstVehicleBanned = vehicleBannedList.FirstOrDefault();
                if (firstVehicleBanned == null)
                    continue;

                var lastVehicleBanned = vehicleBannedList.LastOrDefault();
                if (lastVehicleBanned == null)
                    continue;

                if (!IsValid(lastVehicleBanned, false))
                    continue;

                if (!IsValidBannedTime(lastVehicleBanned))
                    continue;

                if (firstVehicleBanned.ActiveTime.Year == 0)
                    firstVehicleBanned.ActiveTime = DateTime.Now;

                lastVehicleBanned.CreatedDate = firstVehicleBanned.ActiveTime;
                lastVehicleBanned.ViolationTypeId = FindViolationTypeIdByName(lastVehicleBanned.ViolationType, bannedTypeList);
                lastVehicleBanned.ViolationType = FindViolationTypeNameById(lastVehicleBanned.ViolationTypeId, bannedTypeList);
                int vehicleBannedId = -1;
                var vehicelBannedDb = GetVehicleBannedListByTrunkID(lastVehicleBanned.TruckID);
                if (vehicelBannedDb == null) //chưa từng bị cấm
                {
                    vehicleBannedId = SaveImportVehicleBannedListProc(lastVehicleBanned, lastVehicleBanned.ViolationType, firstVehicleBanned.CreatedBy, dataType);
                }
                else
                {
                    //cập nhật lại thông tin
                    if (UpdateImportVehicleBannedListProc(vehicelBannedDb, lastVehicleBanned))
                    {
                        //SaveImportHistory(vehicelBannedDb, BannedHistoryActionType.UPDATED, firstVehicleBanned.ActiveTime);
                        vehicleBannedId = vehicelBannedDb.Id;
                        //lastClearFlag = vehicelBannedDb.ClearFlag;
                        if (vehicleBannedList.Count > 1) firstVehicleBanned.ActiveTime = vehicelBannedDb.CreatedDate;
                    }
                    else
                    {
                        continue;
                    }
                }

                if (vehicleBannedId != -1)
                    numOfImportSuccess++;
                else
                    continue;

                //save history
                foreach (var vehicleBanned in vehicleBannedList)
                {
                    if (!IsValid(vehicleBanned, false))
                        continue;

                    vehicleBanned.Id = vehicleBannedId;
                    vehicleBanned.ViolationTypeId = FindViolationTypeIdByName(vehicleBanned.ViolationType, bannedTypeList);
                    vehicleBanned.ViolationType = FindViolationTypeNameById(vehicleBanned.ViolationTypeId, bannedTypeList);
                    vehicleBanned.CreatedDate = vehicleBanned.ActiveTime;
                    vehicleBanned.UpdatedDate = DateTime.Now;

                    if (lastClearFlag == null)
                    {
                        lastClearFlag = vehicleBanned.ClearFlag;
                        SaveImportHistory(vehicleBanned, vehicleBanned.ClearFlag ? EBANNED_HISTORY_ACTION_TYPE.HUY_CAM : EBANNED_HISTORY_ACTION_TYPE.CAM, firstVehicleBanned.ActiveTime);
                    }
                    else
                    {
                        if (lastClearFlag.Value != vehicleBanned.ClearFlag)
                            SaveImportHistory(vehicleBanned, vehicleBanned.ClearFlag ? EBANNED_HISTORY_ACTION_TYPE.HUY_CAM : EBANNED_HISTORY_ACTION_TYPE.CAM, firstVehicleBanned.ActiveTime);
                        else
                            SaveImportHistory(vehicleBanned, EBANNED_HISTORY_ACTION_TYPE.UPDATED, firstVehicleBanned.ActiveTime);
                        lastClearFlag = vehicleBanned.ClearFlag;
                    }
                }

            }

            return Json(new
            {
                success = numOfImportSuccess > 0,
                numOfImportSuccess = numOfImportSuccess,
                totalImport = totalImport
            });
        }

        /// <summary>
        /// Tải excel template khi import
        /// </summary>
        /// <param name="f"></param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult DownloadExcelTemplate(string f)
        {
            try
            {
                string fileName = System.IO.Path.Combine(_hostingEnvironment.ContentRootPath, "ExcelTemplate", f);
                var net = new System.Net.WebClient();
                if (Utils.IsSafeUrl(fileName) == false)
                {
                    throw new Exception("URL is not safe");
                }
                var data = net.DownloadData(fileName);
                var content = new System.IO.MemoryStream(data);
                var contentType = "APPLICATION/octet-stream";
                return File(content, contentType, f);
            }
            catch
            {
                return NotFound();
            }
        }

        /// <summary>
        /// Lấy tên loại vi phạm theo id
        /// </summary>
        /// <param name="bannedType"></param>
        /// <param name="bannedTypeList"></param>
        /// <returns></returns>
        private string FindViolationTypeNameById(int id, List<BannedType> bannedTypeList)
        {
            string violationType = bannedTypeList.Where(x => x.Id == id).Select(x => x.Description).FirstOrDefault();
            return violationType == null ? "Khác" : violationType;
        }

        /// <summary>
        /// Lấy id loại vi phạm theo tên
        /// </summary>
        /// <param name="violationType"></param>
        /// <param name="bannedTypeList"></param>
        /// <returns></returns>
        private int FindViolationTypeIdByName(string violationType, List<BannedType> bannedTypeList)
        {
            BannedType bannedType = bannedTypeList.Where(x => x.Description.ToLower() == violationType.ToLower()).FirstOrDefault();
            if (bannedType == null)
                bannedType = bannedTypeList.Where(x => x.Description.ToLower() == "khác").FirstOrDefault();

            if (bannedType == null)
                return -1;

            return bannedType.Id;
        }

        /// <summary>
        /// Tìm xe vi phạm theo biển số xe
        /// </summary>
        /// <param name="truckID"></param>
        /// <returns></returns>
        private VehicleBannedList GetVehicleBannedListByTrunkID(string truckID)
        {
            truckID = RemoveSpecialCharactersFromTruckId(truckID.Trim());
            var vehicleBannedList = _vehicleBannedListRepository.GetBy(x => RemoveSpecialCharactersFromTruckId(x.TruckID) == truckID).FirstOrDefault();
            return vehicleBannedList;
        }

        /// <summary>
        /// Kiểm tra file excel có hợp lệ
        /// </summary>
        /// <param name="file"></param>
        /// <returns></returns>
        private bool IsValidExcelFile(IFormFile file)
        {
            if (file == null || !Path.GetExtension(file.FileName).Equals(".xlsx", StringComparison.OrdinalIgnoreCase))
                return false;

            return true;
        }

        /// <summary>
        /// Convert datetime string to DateTime
        /// </summary>
        /// <param name="dateTime"></param>
        /// <param name="format"></param>
        /// <param name="dt"></param>
        /// <returns></returns>
        private bool ConvertStringToDateTime(string dateTime, string format, out DateTime dt)
        {
            if (string.IsNullOrEmpty(dateTime))
            {
                dt = DateTime.Now;
                return false;
            }
            try
            {
                dateTime = dateTime.Split(' ')[0];
                dt = DateTime.ParseExact(dateTime, format, System.Globalization.CultureInfo.InvariantCulture);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("VehicleBannedList/ParseDateTime: " + ex.Message);
                dt = DateTime.Now;
                return false;
            }
        }

        /// <summary>
        /// Lấy dữ liệu của excel cell
        /// </summary>
        /// <param name="worksheet"></param>
        /// <param name="row"></param>
        /// <param name="column"></param>
        /// <returns></returns>
        private string GetExcelCellStringValue(ExcelWorksheet worksheet, int row, int column)
        {
            if (worksheet.Cells[row, column].Value != null)
                return worksheet.Cells[row, column].Value.ToString().Trim();

            return string.Empty;
        }

        private DateTime GetExcelCellDateTimeValue(ExcelWorksheet worksheet, int row, int column, DateTime defaultDate)
        {
            try
            {
                if (worksheet.Cells[row, column].Value != null)
                {
                    if (double.TryParse(worksheet.Cells[row, column].Value.ToString().Trim(), out double value))
                    {
                        return DateTime.FromOADate(value);
                    }
                }
            }
            catch
            {

            }

            return defaultDate;
        }

        /// <summary>
        /// Lấy user id theo tên user
        /// </summary>
        /// <param name="name"></param>
        /// <param name="users"></param>
        /// <returns></returns>
        private int FindUserIdByName(string name, List<User> users)
        {
            name = name.ToLower().Trim();
            User user = users.Where(x => (x.FullName != null && x.FullName.ToLower().Trim() == name) || x.UserName.ToLower() == name).FirstOrDefault();
            if (user != null)
                return user.Id;

            user = users.Where(x => (x.FullName != null && x.FullName.ToLower().Trim().StartsWith(name))).FirstOrDefault();
            if (user != null)
                return user.Id;

            name = Utils.ToUnsignedLowerText(name);
            user = users.Where(x => (x.FullName != null && Utils.ToUnsignedLowerText(x.FullName.Trim()).StartsWith(name))).FirstOrDefault();
            if (user != null)
                return user.Id;

            if (HttpContext.Session.GetString("SessionUserSystemId") != null)
            {
                int userId;
                if (int.TryParse(HttpContext.Session.GetString("SessionUserSystemId"), out userId))
                    return userId;
            }

            return 0;
        }

        private enum VehicleExcelColumnIndex
        {
            TRUCK_ID = 1,
            ACTIVE_TIME,
            EXPIRY_TIME,
            BANNED_TYPE,
            REASON_VIOLATION,
            TRUCK_KEY,
            OWNER,
            FOUND_BY,
            CLEAR_FLAG,
            CLEAR_BY,
            CLEAR_DATE,
            REASON_CLEAR,
            NOTE
        }

        #endregion

        #region Common
        /// <summary>
        /// Lấy tên người dùng theo id
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        private string GetUserNameById(int userId)
        {
            if (userId <= 0) return string.Empty;

            lock (_lock)
            {
                if (UserBannedInfos.ContainsKey(userId))
                    return UserBannedInfos[userId].Name;

                return string.Empty;
            }
        }

        /// <summary>
        /// Lấy tên site người dùng theo id
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        private string GetUserSiteNameById(int userId)
        {
            if (userId <= 0) return string.Empty;

            lock (_lock)
            {
                if (UserBannedInfos.ContainsKey(userId))
                    return UserBannedInfos[userId].SiteName;

                return string.Empty;
            }
        }

        /// <summary>
        /// Lấy id site người dùng theo id
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        private string GetUserSiteIdById(int userId)
        {
            if (userId <= 0) return string.Empty;

            lock (_lock)
            {
                if (UserBannedInfos.ContainsKey(userId))
                    return UserBannedInfos[userId].SiteId;

                return string.Empty;
            }
        }

        /// <summary>
        /// Lấy người dùng theo id
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        private UserBannedInfo GetUserBannedInfoById(int userId)
        {
            if (userId <= 0) return null;

            lock (_lock)
            {
                if (UserBannedInfos.ContainsKey(userId))
                    return UserBannedInfos[userId];

                return null;
            }
        }

        /// <summary>
        /// Kiểm trả người dùng có quyền ko
        /// </summary>
        /// <param name="user"></param>
        /// <param name="roleId"></param>
        /// <returns></returns>
        private bool IsHasUserRole(User user, int roleId)
        {
            string _roleId = roleId.ToString();
            foreach (var userInRole in user.UserInRoles)
            {
                if (userInRole.Role.ListFunction_AM != null)
                {
                    var listFunctionC3 = userInRole.Role.ListFunction_AM.Trim().Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
                    if (listFunctionC3.Contains(_roleId))
                        return true;
                }
            }

            return false;
        }

        /// <summary>
        /// Convert string qua list int
        /// </summary>
        /// <param name="value">
        /// Format: 1,2,3,4,...
        /// </param>
        /// <returns></returns>
        private List<int> ConvertStringToListInt(string value)
        {
            if (string.IsNullOrWhiteSpace(value))
                return null;

            string[] parts = value.Trim().Split(',');
            if (parts.Length == 0)
                return null;

            var listInt = new List<int>();
            foreach (string part in parts)
            {
                if (string.IsNullOrWhiteSpace(part))
                    continue;

                if (int.TryParse(part.Trim(), out int val))
                {
                    if (val > 0)
                        listInt.Add(val);
                }
            }

            if (listInt.Count == 0)
                return null;

            return listInt;
        }

        /// <summary>
        /// Lấy id người dùng đang đăng nhập
        /// </summary>
        /// <returns></returns>
        private int GetLoginUserId()
        {
            try
            {
                return int.Parse(this.HttpContext.Session.GetString("SessionUserSystemId"));
            }
            catch
            {
                return -1;
            }
        }

        /// <summary>
        /// Lấy tên user theo id hoặc lấy tên user đang đăng nhập
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        private string GetUserNameOrDefault(int userId)
        {
            if (userId != -1)
            {
                var user = _userRepository.GetById(userId);
                if (user != null)
                    return string.IsNullOrEmpty(user.FullName) ? user.UserName : user.FullName;
            }

            try
            {
                int loginUserId = int.Parse(this.HttpContext.Session.GetString("SessionUserSystemId"));
                var _user = _userRepository.GetById(loginUserId);
                if (_user != null)
                    return string.IsNullOrEmpty(_user.FullName) ? _user.UserName : _user.FullName;
            }
            catch { }

            return string.Empty;
        }

        /// <summary>
        /// Lấy thông tin người dùng đang đăng nhập
        /// </summary>
        /// <returns></returns>
        public User GetCurrentUserLogin()
        {
            var SessionUserSystem = HttpContext.Session.GetComplexData<User>("SessionUserSystemId_" + HttpContext.Session.GetString("SessionUserSystemId"));
            if (SessionUserSystem != null)
            {
                var _user = _userRepository.GetById(SessionUserSystem.Id);

                if (_user != null)
                {
                    if (!_user.Actived)
                    {
                        HttpContext.Session.Clear();
                        return null;
                    }
                    return _user;
                }
            }
            return null;
        }

        /// <summary>
        /// Lấy quyền của người dùng đang đăng nhập
        /// </summary>
        /// <returns></returns>
        public User GetUserRole()
        {
            var SessionUserSystem = HttpContext.Session.GetComplexData<User>("SessionUserSystemId_" + HttpContext.Session.GetString("SessionUserSystemId"));
            if (SessionUserSystem != null)
            {
                var _user = _userRepository.Get(x => x.Id == SessionUserSystem.Id, null, "UserInRoles.Role").SingleOrDefault();

                if (_user != null && _user.UserInRoles != null)
                    return _user;
            }
            return null;
        }

        /// <summary>
        /// Lưu history system
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="action_type"></param>
        /// <param name="controllerName"></param>
        /// <param name="description"></param>
        private void InsertHistorySystem(int userId, EACTION_TYPE action_type, EnumControllerName controllerName, string description, int vehicleBannedListId, object oldObject = null, object newObject = null)
        {
            try
            {
                if (action_type == EACTION_TYPE.DELETE)
                    VehicleBannedListCache.Instance.Delete(vehicleBannedListId, _logger);
                else
                    VehicleBannedListCache.Instance.Update(vehicleBannedListId, _vehicleBannedListRepository, _logger);

                var ipAddress = HttpContext.Connection.RemoteIpAddress.ToString();

                string _oldObject = string.Empty;
                if(oldObject != null)
                {
                    if (oldObject.GetType() == typeof(String))
                        _oldObject = (string)oldObject;
                    else
                        _oldObject = JsonConvert.SerializeObject(oldObject);
                }

                string _newObject = string.Empty;
                if (newObject != null)
                {
                    if (newObject.GetType() == typeof(String))
                        _newObject = (string)newObject;
                    else
                        _newObject = JsonConvert.SerializeObject(newObject);
                }

                HistorySystem history = new HistorySystem
                {
                    ActionType = (int)action_type,
                    ActionTime = DateTime.Now,
                    Description = description,
                    OldObject = _oldObject,
                    NewObject = _newObject,
                    UserId = userId,
                    IpAddress = ipAddress,
                    ControllerName = (int)controllerName
                };

                _historySystemRepository.Insert(history);

                _historySystemRepository.SaveChanges();
            }
            catch (Exception ex)
            {
                _logger.LogError("VehicleBannedList/InsertHistorySystem: " + ex.Message);
            }
        }

        /// <summary>
        /// Tìm tên loại vi phạm theo id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        private string FindViolationTypeNameById(int id)
        {
            string violationType = _bannedTypeRepository.GetBy(x => x.Id == id).Select(x => x.Description).FirstOrDefault();
            return violationType == null ? "Khác" : violationType;
        }

        /// <summary>
        /// Xóa ký tự đặc biệt trong biển số xe
        /// </summary>
        /// <param name="truckID"></param>
        /// <returns></returns>
        private string RemoveSpecialCharactersFromTruckId(string truckID)
        {
            if (truckID == null) throw new Exception("Vui lòng nhập biển số xe");
            truckID = truckID.Trim().ToUpper();
            return System.Text.RegularExpressions.Regex.Replace(truckID, @"[^A-Z0-9]+", string.Empty);
        }

        #endregion
    }

}