﻿using Microsoft.AspNetCore.Mvc;
using PSafe.AM.Common;
using PSafe.Core.Domains;
using PSafe.Core.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.AspNetCore.Http;
using PSafe.AM.Models;
using System.IO;
using System.Threading.Tasks;
using OfficeOpenXml;
using PSafe.Common.UserEnums;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Configuration;
using PSafe.Common;
using System.Net;
using System.Security.Cryptography.X509Certificates;
using System.Net.Security;
using System.Net.Http;
using Newtonsoft.Json;
using PSafe.AM.ACS;
using System.Globalization;
using System.Text;
using Microsoft.AspNetCore.Hosting;
using PSafe.Common.Enums;
using static PSafe.Common.CommonEnums;

namespace PSafe.AM.Controllers
{
    [AuthorizeActionFilter(EPERMISSIONS_AM.AccessControlForPedestrians)]
    public class HumanBannedListController : Controller
    {
        private readonly IUserRepository _userRepository;
        private readonly IBannedTypeRepository _bannedTypeRepository;
        private readonly IUserInRoleRepository _userInRoleRepository;
        private readonly IRoleRepository _roleRepository;
        private readonly IAlarmConfigRepository _alarmConfigRepository;
        private readonly Core.Services.IZaloService _zaloService;
        private readonly Core.Services.IEmailSender _emailSender;
        private readonly IHistorySystemRepository _historySystemRepository;

        private readonly ILogger<HumanBannedListController> _logger;
        private readonly IHostingEnvironment _hostingEnvironment;

        public HumanBannedListController(IUserRepository userRepository,
            IBannedTypeRepository bannedTypeRepository,
            IUserInRoleRepository userInRoleRepository,
            IRoleRepository roleRepository,
            ILogger<HumanBannedListController> logger,
            IHostingEnvironment hostingEnvironment,
            IAlarmConfigRepository alarmConfigRepository,
            Core.Services.IZaloService zaloService,
            Core.Services.IEmailSender emailSender,
            IHistorySystemRepository historySystemRepository)
        {
            _userRepository = userRepository;
            _bannedTypeRepository = bannedTypeRepository;
            _userInRoleRepository = userInRoleRepository;
            _roleRepository = roleRepository;
            _logger = logger;
            _hostingEnvironment = hostingEnvironment;
            _alarmConfigRepository = alarmConfigRepository;
            _zaloService = zaloService;
            _emailSender = emailSender;
            _historySystemRepository = historySystemRepository;

        }

        public ActionResult Index()
        {
            var acsAPIInfo = GetACSAPIInfo();

            string acsAPIUrl = acsAPIInfo.Item1;
            string userName = acsAPIInfo.Item2;
            string passWord = acsAPIInfo.Item3;

            ViewBag.acsAPIUrl = acsAPIUrl;
            ViewBag.userName = userName;
            ViewBag.passWord = passWord;

            ViewBag.reasonForbidList = GetReasonForbid();
            ViewBag.reasonCancelForbidList = GetReasonCancelForbid();

            return View();
        }

        private List<SelectListItem> GetReasonForbid()
        {
            List<SelectListItem> reasonForbidList = new List<SelectListItem>();
            var response = SendHttpRequest("get_reason_forbid", null);
            if (response.retCode == 0)
            {
                try
                {
                    var reasonAPIList = JsonConvert.DeserializeObject<List<ReasonAPI>>(response.result.ToString());
                    foreach (var reason in reasonAPIList)
                    {
                        reasonForbidList.Add(new SelectListItem()
                        {
                            Value = reason.Id,
                            Text = reason.NoiDung
                        });
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("HUmanBannedList/GetReasonForbid: " + ex.Message);
                }
            }

            return reasonForbidList;
        }

        private List<SelectListItem> GetReasonCancelForbid()
        {
            List<SelectListItem> reasonCancelForbidList = new List<SelectListItem>();
            var response = SendHttpRequest("get_reason_cancel_forbid", null);
            if (response.retCode == 0)
            {
                try
                {
                    var reasonAPIList = JsonConvert.DeserializeObject<List<ReasonAPI>>(response.result.ToString());
                    foreach (var reason in reasonAPIList)
                    {
                        reasonCancelForbidList.Add(new SelectListItem()
                        {
                            Value = reason.Id,
                            Text = reason.NoiDung
                        });
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("HUmanBannedList/GetReasonCancelForbid: " + ex.Message);
                }
            }

            return reasonCancelForbidList;
        }

        /// <summary>
        /// Lấy thông tin acs api với url, username, password trong file setting
        /// </summary>
        /// <returns> Tuple<string, string, string>
        /// Item1: url
        /// Item2: username
        /// Item3: password
        /// </returns>
        private Tuple<string, string, string> GetACSAPIInfo()
        {
            var builder = new ConfigurationBuilder()
                                        .SetBasePath(Directory.GetCurrentDirectory())
                                        .AddJsonFile("appsettings.json");

            var configuration = builder.Build();

            string acsAPIUrl = configuration.GetSection("ACSAPI:BaseUrl").Value;
            string userName = Utils.DecodePassword(configuration.GetSection("ACSAPI:Username").Value, Utils.EncodeType.SHA_256);
            string passWord = Utils.DecodePassword(configuration.GetSection("ACSAPI:Password").Value, Utils.EncodeType.SHA_256);

            if (!acsAPIUrl.EndsWith("/"))
                acsAPIUrl += "/";

            return Tuple.Create(acsAPIUrl, userName, passWord);
        }

        /// <summary>
        /// Send http request đến ACS API
        /// </summary>
        /// <param name="command">VD: get_forbid_list, get_reason_forbid, ...</param>
        /// <param name="queryString">Danh sách cách query string, để null nếu ko có</param>
        /// <returns>ACSResponseData</returns>
        private ACSResponseData SendHttpRequest(string command, Dictionary<string, string> queryString)
        {
            try
            {
                var acsAPIInfo = GetACSAPIInfo();

                string acsAPIUrl = acsAPIInfo.Item1;
                string userName = acsAPIInfo.Item2;
                string passWord = acsAPIInfo.Item3;

                if (!acsAPIUrl.EndsWith("/"))
                    acsAPIUrl += "/";

                if (command.StartsWith("/"))
                    command = command.Substring(1);

                acsAPIUrl += command;

                if (queryString == null)
                    queryString = new Dictionary<string, string>();

                //pass https
                ServicePointManager.ServerCertificateValidationCallback +=
                    delegate (
                        Object sender1,
                        X509Certificate certificate,
                        X509Chain chain,
                        SslPolicyErrors sslPolicyErrors)
                    {
                        return true;
                    };

                using (var client = new HttpClient())
                {

                    userName = C3CryptoAesAPI.C3CryptoAes.EncryptStringToString256(userName);
                    passWord = C3CryptoAesAPI.C3CryptoAes.EncryptStringToString256(passWord);

                    queryString.Add("usr", userName);
                    queryString.Add("pw", passWord);

                    acsAPIUrl += BuildHttpQueryString(queryString); //format ?key=value&key2=value
                    if (Utils.IsSafeUrl(acsAPIUrl) == false)
                    {
                        return new ACSResponseData()
                        {
                            retCode = 2,
                            result = "URL is not safe!"
                        };
                    }
                    HttpResponseMessage resp = client.GetAsync(acsAPIUrl).Result;
                    var resutl = resp.Content.ReadAsStringAsync().Result;

                    string data = C3CryptoAesAPI.C3CryptoAes.DecryptStringFromString256(resutl);

                    var responseData = JsonConvert.DeserializeObject<ACSResponseData>(data);
                    return responseData;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("HUmanBannedList/SendHttpRequest: " + ex.Message);
                return new ACSResponseData()
                {
                    retCode = 2,
                    result = ex.Message
                };
            }
        }

        private string BuildHttpQueryString(Dictionary<string, string> queryString)
        {
            string queryStringUrl = "";
            foreach (var query in queryString)
            {
                if (queryStringUrl == "")
                    queryStringUrl += "?";
                else
                    queryStringUrl += "&";

                queryStringUrl += query.Key + "=" + query.Value;
            }

            return queryStringUrl;
        }

        [HttpPost]
        public ActionResult Index(
            int draw,
            string paperCode,
            string fullName,
            string reasonViolation,
            string fromActiveTime,
            string toActiveTime,
            string fromExpiryTime,
            string toExpiryTime)
        {
            if (string.IsNullOrWhiteSpace(reasonViolation))
                reasonViolation = Guid.Empty.ToString();
            else
                reasonViolation = reasonViolation.Replace(",", "|") + "|" + Guid.Empty.ToString();
            Dictionary<string, string> queryString = new Dictionary<string, string>();
            queryString.Add("hoTen", fullName == null ? string.Empty : fullName);
            queryString.Add("maGiayTo", paperCode == null ? string.Empty : paperCode);
            queryString.Add("lyDoCam", reasonViolation);
            queryString.Add("thoiGianCamStart", CheckAndConvertDateTimeStringToString(fromActiveTime, new DateTime(2019, 1, 1, 0, 0, 0), "yyyy-MM-dd"));
            queryString.Add("thoiGianCamEnd", CheckAndConvertDateTimeStringToString(toActiveTime, DateTime.Now, "yyyy-MM-dd"));
            queryString.Add("thoiHanCamStart", CheckAndConvertDateTimeStringToString(fromExpiryTime, new DateTime(2019, 1, 1, 0, 0, 0), "yyyy-MM-dd"));
            queryString.Add("thoiHanCamEnd", CheckAndConvertDateTimeStringToString(toExpiryTime, DateTime.Now, "yyyy-MM-dd"));

            var humanBannedListModels = GetForbidList(queryString);

            return Json(new
            {
                draw = draw,
                recordsFiltered = humanBannedListModels.Count,
                recordsTotal = humanBannedListModels.Count,
                data = humanBannedListModels
            });
        }

        private List<HumanBannedListModel> GetForbidList(Dictionary<string, string> queryString)
        {
            var humanBannedListModels = new List<HumanBannedListModel>();
            var response = SendHttpRequest("get_forbid_list", queryString);
            if (response.retCode == 0)
            {
                try
                {
                    var prohibitedCustomerList = JsonConvert.DeserializeObject<List<ProhibitedCustomerAPI>>(response.result.ToString());
                    int index = 1;
                    foreach (var prohibitedCustomer in prohibitedCustomerList)
                    {
                        var humanBannedListModel = new HumanBannedListModel();
                        humanBannedListModel.Index = index++;
                        humanBannedListModel.IdKhachHang = prohibitedCustomer.IdKhachHang;
                        humanBannedListModel.FullName = prohibitedCustomer.HoTen;
                        humanBannedListModel.PaperCode = prohibitedCustomer.MaGiayTo;
                        humanBannedListModel.BirthDay = CheckAndConvertDateTimeStringToString(prohibitedCustomer.NgaySinh, null, "dd/MM/yyyy HH:mm:ss");
                        humanBannedListModel.PlaceOfBirth = prohibitedCustomer.QueQuan;
                        humanBannedListModel.Company = prohibitedCustomer.CongTy;
                        humanBannedListModel.PhoneNumber = prohibitedCustomer.Sdt;
                        humanBannedListModel.ForbidType = prohibitedCustomer.LoaiCam == "1" ? "Cấm" : "Hủy cấm";
                        humanBannedListModel.Note = prohibitedCustomer.GhiChu == null ? string.Empty : prohibitedCustomer.GhiChu;

                        string ngayCam = prohibitedCustomer.NgayCam == null ? string.Empty : prohibitedCustomer.NgayCam;

                        humanBannedListModel.ActiveTime = prohibitedCustomer.ThoiHanCamBatDau == null ? string.Empty : prohibitedCustomer.ThoiHanCamBatDau;
                        if (string.IsNullOrEmpty(humanBannedListModel.ActiveTime))
                            humanBannedListModel.ActiveTime = ngayCam;
                        humanBannedListModel.ExpiryTime = prohibitedCustomer.ThoiHanCamKetThuc == null ? string.Empty : prohibitedCustomer.ThoiHanCamKetThuc;
                        if (string.IsNullOrEmpty(humanBannedListModel.ExpiryTime))
                            humanBannedListModel.ExpiryTime = "Vĩnh viễn";

                        humanBannedListModel.IsValidBanned = prohibitedCustomer.LoaiCam == "1";
                        if (humanBannedListModel.IsValidBanned)
                        {
                            humanBannedListModel.CreatedBy = prohibitedCustomer.UserCam;
                            humanBannedListModel.ReasonViolation = prohibitedCustomer.LyDoCam;
                            humanBannedListModel.CreatedDate = ngayCam;
                        }
                        else
                        {
                            humanBannedListModel.ClearBy = prohibitedCustomer.UserCam;
                            humanBannedListModel.ReasonClear = prohibitedCustomer.LyDoCam;
                            humanBannedListModel.ClearDate = ngayCam;
                        }
                        humanBannedListModel.ClearFlag = humanBannedListModel.IsValidBanned ? string.Empty : "Y";
                        humanBannedListModels.Add(humanBannedListModel);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("HUmanBannedList/GetForbidList: " + ex.Message);
                }
            }

            return humanBannedListModels;
        }

        private string CheckAndConvertDateTimeStringToString(string dateTime, DateTime defaultDateTime)
        {
            if (string.IsNullOrWhiteSpace(dateTime))
            {
                return defaultDateTime.ToString("dd/MM/yyyy HH:mm:ss");
            }

            try
            {
                var _dateTime = DateTime.ParseExact(dateTime, "yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture);
                return _dateTime.ToString("dd/MM/yyyy HH:mm:ss");
            }
            catch
            {
                return defaultDateTime.ToString("dd/MM/yyyy HH:mm:ss");
            }
        }

        private string CheckAndConvertDateTimeStringToString(string dateTime, DateTime? defaultDateTime = null, string format = "yyyy/MM/dd", string returnFormat = "dd/MM/yyyy")
        {
            if (string.IsNullOrWhiteSpace(dateTime))
            {
                if (defaultDateTime.HasValue)
                    return defaultDateTime.Value.ToString("dd/MM/yyyy HH:mm:ss");
                return string.Empty;
            }

            try
            {
                var _dateTime = DateTime.ParseExact(dateTime, format, CultureInfo.InvariantCulture);
                if (defaultDateTime.HasValue)
                    return _dateTime.ToString("dd/MM/yyyy") + " " + defaultDateTime.Value.ToString("HH:mm:ss");
                return _dateTime.ToString(returnFormat);
            }
            catch
            {
                if (defaultDateTime.HasValue)
                    return defaultDateTime.Value.ToString("dd/MM/yyyy HH:mm:ss");
                return string.Empty;
            }
        }

        [HttpPost("HumanBannedList/GetHumanListHistory")]
        public JsonResult GetHumanListHistory(int draw, int start, int length, string keyword, string id)
        {
            List<HumanBannedListHistoryModel> humanBannedListHistoryModels = new List<HumanBannedListHistoryModel>();
            Dictionary<string, string> queryString = new Dictionary<string, string>();
            queryString.Add("idCustomer", id);
            var response = SendHttpRequest("get_forbid_history", queryString);
            if (response.retCode == 0)
            {
                try
                {
                    var historyList = JsonConvert.DeserializeObject<List<RightsHistoryAPI>>(response.result.ToString());
                    foreach (var history in historyList)
                    {
                        var humanBannedListModel = new HumanBannedListHistoryModel();
                        humanBannedListModel.ActionType = GetHistoryActionTypeText(history.LoaiCam);

                        string ngayCam = history.NgayCam == null ? string.Empty : history.NgayCam;
                        humanBannedListModel.ActiveTime = history.ThoiHanCamBatDau;
                        humanBannedListModel.ExpiryTime = history.ThoiHanCamKetThuc;
                        if (string.IsNullOrEmpty(humanBannedListModel.ActiveTime))
                            humanBannedListModel.ActiveTime = ngayCam;
                        if (string.IsNullOrEmpty(humanBannedListModel.ExpiryTime))
                            humanBannedListModel.ExpiryTime = "Vĩnh viễn";
                        humanBannedListModel.CreatedBy = history.UserCam;
                        humanBannedListModel.ClearFlag = history.LoaiCam == "1" ? "" : "Y";
                        humanBannedListModel.CreatedDate = ngayCam;

                        if (history.LoaiCam == "1")
                        {
                            humanBannedListModel.ReasonViolation = history.LyDoCam;
                        }
                        else
                        {
                            humanBannedListModel.ReasonClear = history.LyDoCam;
                            humanBannedListModel.ClearDate = ngayCam;
                        }

                        humanBannedListHistoryModels.Add(humanBannedListModel);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("HUmanBannedList/GetHumanListHistory: " + ex.Message);
                }
            }

            return Json(new
            {
                draw = draw,
                recordsFiltered = humanBannedListHistoryModels.Count,
                recordsTotal = humanBannedListHistoryModels.Count,
                data = humanBannedListHistoryModels
            });
        }

        private string GetHistoryActionTypeText(string type)
        {
            switch (type)
            {
                case "1":
                    return "Cấm";
                case "0":
                    return "Hủy cấm";
            }

            return "";
        }



        private string ConvertDateTimeToString(DateTime? dateTime, string format = "dd/MM/yyyy HH:mm:ss")
        {
            if (dateTime.HasValue)
                return dateTime.Value.ToString(format);

            return string.Empty;
        }

        /// <summary>
        /// Lấy danh sách số điện thoại gửi tin nhắn cảnh báo
        /// </summary>
        /// <returns></returns>
        private List<User> GetUsersSendAlarm()
        {
            try
            {
                List<Role> roles = new List<Role>();
                List<User> users = new List<User>();

                //lấy cấu hình alarm
                var alarmConfigs = _alarmConfigRepository.GetBy(x => x.Type == (int)EALARM.HumanBannedAlarm).ToList();
                foreach (var alarmConfig in alarmConfigs)
                {
                    if (!string.IsNullOrWhiteSpace(alarmConfig.AlarmLevel1))
                    {
                        string[] selectedRoles = alarmConfig.AlarmLevel1.Split(',');
                        foreach (string role in selectedRoles) //duyệt tất cả nhóm trong cấu hình
                        {
                            if (int.TryParse(role, out int roleId))
                            {
                                var r = _roleRepository.Get(x => x.RoleId == roleId, null, "UserInRoles.Role,UserInRoles.User").FirstOrDefault();
                                if (r != null) //lấy nhóm
                                    roles.Add(r);
                            }
                        }
                    }
                }

                if (roles.Count == 0)
                    return users;

                foreach (var role in roles) //duyệt nhóm lấy danh sách user trong nhóm
                {
                    if (role.UserInRoles == null || role.UserInRoles.Count == 0)
                        continue;

                    foreach (var user in role.UserInRoles)
                    {
                        if (user.User == null || (string.IsNullOrWhiteSpace(user.User.Phone) &&
                            string.IsNullOrWhiteSpace(user.User.Email)))
                            continue;

                        users.Add(user.User);
                    }
                }

                return users;
            }
            catch (Exception ex)
            {
                _logger.LogError("HumanBannedList/GetUsersSendAlarm: " + ex.Message);
            }

            return new List<User>();
        }

        private void SendAlarmMessage(string message, string title)
        {
            var users = GetUsersSendAlarm();
            if (users.Count > 0)
            {
                List<string> phones = new List<string>();
                List<string> emails = new List<string>();
                foreach (var user in users)
                {
                    if (!string.IsNullOrWhiteSpace(user.Phone))
                        phones.Add(user.Phone);

                    if (!string.IsNullOrWhiteSpace(user.Email))
                        emails.Add(user.Email);
                }

                if (phones.Count > 0)
                    _zaloService.SendTextAsync(phones.ToArray(), message);

                if (emails.Count > 0)
                    _emailSender.SendEmailAsync(emails.ToArray(), title, message);
            }
        }

        private void SendAlarmMessage(EBANNED_HISTORY_ACTION_TYPE action, HumanBannedListModel humanBannedList)
        {
            if (string.IsNullOrEmpty(humanBannedList.PaperCode) ||
                string.IsNullOrEmpty(humanBannedList.FullName))
                return;

            switch (action)
            {
                case EBANNED_HISTORY_ACTION_TYPE.CAM:
                    {
                        StringBuilder messageContent = new StringBuilder();
                        messageContent.Append("Ngày thực hiện cấm: ");
                        messageContent.Append(humanBannedList.CreatedDate);
                        messageContent.Append("\n");
                        messageContent.Append("Mã giấy tờ: ");
                        messageContent.Append(humanBannedList.PaperCode);
                        messageContent.Append("\n");
                        messageContent.Append("Họ và tên: ");
                        messageContent.Append(humanBannedList.FullName);
                        messageContent.Append("\n");
                        messageContent.Append("Lý do cấm: ");
                        messageContent.Append(humanBannedList.ViolationType != null ? humanBannedList.ViolationType : string.Empty);
                        messageContent.Append("\n");
                        messageContent.Append("Người cấm: ");
                        messageContent.Append(humanBannedList.CreatedBy);

                        //_logger.LogError(Utils.NonUnicode(messageContent.ToString()));

                        string msgTexg = "Cấm khách hàng " + humanBannedList.FullName + " vào ra";
                        SendAlarmMessage(messageContent.ToString(), msgTexg);
                        InsertHistorySystem(EACTION_TYPE.BANNED, EnumControllerName.PROHIBIT_LIST, msgTexg);
                    }
                    break;
                case EBANNED_HISTORY_ACTION_TYPE.HUY_CAM:
                    {
                        //tu day
                        StringBuilder messageContent = new StringBuilder();
                        messageContent.Append("Ngày hủy cấm: ");
                        messageContent.Append(DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss"));
                        messageContent.Append("\n");
                        messageContent.Append("Mã giấy tờ: ");
                        messageContent.Append(humanBannedList.PaperCode);
                        messageContent.Append("\n");
                        messageContent.Append("Họ và tên: ");
                        messageContent.Append(humanBannedList.FullName);
                        messageContent.Append("\n");
                        messageContent.Append("Lý do hủy: ");
                        messageContent.Append(humanBannedList.ReasonClear);
                        messageContent.Append("\n");
                        messageContent.Append("Người hủy: ");
                        messageContent.Append(humanBannedList.ClearBy);

                        //_logger.LogError(Utils.NonUnicode(messageContent.ToString()));

                        string msgText = "Hủy cấm khách hàng " + humanBannedList.FullName + " vào ra";
                        SendAlarmMessage(messageContent.ToString(), msgText);
                        InsertHistorySystem(EACTION_TYPE.CLEAR_BANNED, EnumControllerName.PROHIBIT_LIST, msgText);
                    }
                    break;
            }
        }

        private bool OnSaveHumanBannedList(HumanBannedListModel humanBannedList, bool isSendAlarm = true)
        {
            Dictionary<string, string> queryString = new Dictionary<string, string>();
            queryString.Add("hoTen", humanBannedList.FullName);
            queryString.Add("loaiGiayTo", humanBannedList.PaperType.ToString());
            queryString.Add("maGiayTo", humanBannedList.PaperCode);
            queryString.Add("ngayCap", ConvertDateTimeToString(humanBannedList.DateOfIssueDate));
            queryString.Add("noiCap", humanBannedList.PlaceOfIssue == null ? string.Empty : humanBannedList.PlaceOfIssue);
            queryString.Add("queQuan", humanBannedList.ProvincialOfBirth == null ? string.Empty : humanBannedList.ProvincialOfBirth);
            queryString.Add("ngaySinh", ConvertDateTimeToString(humanBannedList.BirthDayDate));
            queryString.Add("congTy", humanBannedList.Company == null ? string.Empty : humanBannedList.Company);
            queryString.Add("soDT", humanBannedList.PhoneNumber == null ? string.Empty : humanBannedList.PhoneNumber);
            queryString.Add("diaChi", humanBannedList.Address == null ? string.Empty : humanBannedList.Address);
            queryString.Add("ngayGioCam", CheckAndConvertDateTimeStringToString(humanBannedList.ActiveTime, DateTime.Now));
            queryString.Add("thoiGianCam", ConvertDateTimeToString(humanBannedList.ExpiryTimeDate));
            queryString.Add("userCam", humanBannedList.CreatedBy);
            queryString.Add("lyDoCam", humanBannedList.ReasonViolation);
            queryString.Add("ghiChu", humanBannedList.Note == null ? string.Empty : humanBannedList.Note);
            queryString.Add("idKhachHang", Guid.NewGuid().ToString());
            var response = SendHttpRequest("add_forbid_list", queryString);

            if (isSendAlarm && response.retCode == 0)
            {
                humanBannedList.CreatedDate = queryString["ngayGioCam"];
                SendAlarmMessage(EBANNED_HISTORY_ACTION_TYPE.CAM, humanBannedList);
            }

            return response.retCode == 0;
        }

        [HttpPost("HumanBannedList/AddHumanBannedList")]
        public ActionResult AddHumanBannedList(HumanBannedListModel humanBannedList)
        {
            if (!IsValid(humanBannedList))
                return Json(new
                {
                    success = false,
                    message = "Vui lòng nhập đầy đủ thông tin"
                });

            var user = GetSesson();
            if (user == null)
            {
                return Json(new
                {
                    success = false,
                    message = "Vui lòng đăng nhập để tiếp tục"
                });
            }

            _logger.LogInformation(string.Format("HumanBannedList/AddHumanBannedList 1: nguoi dung {0}, cam khach {1} vao ra", user.UserName, humanBannedList.FullName));

            humanBannedList.CreatedBy = user.UserName;
            if (OnSaveHumanBannedList(humanBannedList))
            {
                _logger.LogInformation(string.Format("HumanBannedList/AddHumanBannedList 2: nguoi dung {0}, cam khach {1} vao ra thanh cong", user.UserName, humanBannedList.FullName));
                return Json(new
                {
                    success = true
                });
            }

            return Json(new
            {
                success = false,
                message = "Thêm thất bại. Vui lòng kiểm tra kết nối đến ACS"
            });
        }

        private bool ClearHumanBannedList(string idKhachHang, string ngayGioHuy, string nguoiHuy, string lyDoHuy, string ghiChu)
        {
            Dictionary<string, string> queryString = new Dictionary<string, string>();
            queryString.Add("idKhachHang", idKhachHang);
            queryString.Add("ngayGioHuyCam", ngayGioHuy);
            queryString.Add("userHuyCam", nguoiHuy);
            queryString.Add("lyDoHuyCam", string.IsNullOrWhiteSpace(lyDoHuy) ? Guid.Empty.ToString() : lyDoHuy);
            queryString.Add("ghiChu", ghiChu == null ? String.Empty : ghiChu);
            var response = SendHttpRequest("remove_forbid_list", queryString);
            return response.retCode == 0;
        }


        [HttpPost("HumanBannedList/ClearHumanList")]
        public JsonResult ClearHumanList(string id, string ReasonClearId,
            HumanBannedListModel humanBannedList)
        {
            var user = GetSesson();
            if (user == null)
            {
                return Json(new
                {
                    success = false,
                    message = "Vui lòng đăng nhập để tiếp tục"
                });
            }

            _logger.LogInformation(string.Format("HumanBannedList/ClearHumanList 1: nguoi dung {0}, xoa cam khach {1} vao ra", user.UserName, humanBannedList.FullName ?? id));

            if (ClearHumanBannedList(id, DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss"), user.UserName, ReasonClearId, humanBannedList.Note))
            {
                humanBannedList.ClearBy = user.UserName;
                SendAlarmMessage(EBANNED_HISTORY_ACTION_TYPE.HUY_CAM, humanBannedList);
                _logger.LogInformation(string.Format("HumanBannedList/ClearHumanList 2: nguoi dung {0}, xoa cam khach {1} vao ra thanh cong", user.UserName, humanBannedList.FullName ?? id));
                return Json(new
                {
                    success = true
                });
            }

            return Json(new
            {
                success = false,
                message = "Lưu thất bại. Vui lòng kiểm tra kết nối đến ACS!"
            });
        }

        [HttpPost("HumanBannedList/ReBanned")]
        public JsonResult ReBanned(string id, string reasonViolationId, DateTime? thoiGianCam,
            HumanBannedListModel humanBannedList)
        {
            var user = GetSesson();
            if (user == null)
            {
                return Json(new
                {
                    success = false,
                    message = "Vui lòng đăng nhập để tiếp tục"
                });
            }

            _logger.LogInformation(string.Format("HumanBannedList/ReBanned 1: nguoi dung {0}, cam khach {1} vao ra", user.UserName, humanBannedList.FullName ?? id));

            Dictionary<string, string> queryString = new Dictionary<string, string>();
            queryString.Add("idKhachHang", id);
            queryString.Add("ngayGioCam", DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss"));
            queryString.Add("userCam", user.UserName);
            queryString.Add("lyDoCam", reasonViolationId);
            queryString.Add("ghiChu", humanBannedList.Note == null ? string.Empty : humanBannedList.Note);
            queryString.Add("thoiGianCam", thoiGianCam.HasValue ? thoiGianCam.Value.ToString("dd/MM/yyyy HH:mm:ss") : string.Empty);
            var response = SendHttpRequest("edit_forbid_list", queryString);
            if (response.retCode == 0)
            {
                humanBannedList.CreatedBy = user.UserName;
                humanBannedList.CreatedDate = queryString["ngayGioCam"];
                SendAlarmMessage(EBANNED_HISTORY_ACTION_TYPE.CAM, humanBannedList);
                _logger.LogInformation(string.Format("HumanBannedList/ReBanned 1: nguoi dung {0}, cam khach {1} vao ra thanh cong", user.UserName, humanBannedList.FullName ?? id));
                return Json(new
                {
                    success = true
                });
            }

            return Json(new
            {
                success = false,
                message = "Lưu thất bại. Vui lòng kiểm tra kết nối đến ACS!"
            });
        }


        private bool IsValidExcelFile(IFormFile file)
        {
            if (file == null || !Path.GetExtension(file.FileName).Equals(".xlsx", StringComparison.OrdinalIgnoreCase))
                return false;

            return true;
        }

        private bool ParseDateTime(string dateTime, string format, out DateTime dt)
        {
            if (string.IsNullOrEmpty(dateTime))
            {
                dt = DateTime.Now;
                return false;
            }
            try
            {
                dt = DateTime.ParseExact(dateTime, format, System.Globalization.CultureInfo.InvariantCulture);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("HumanBannedList/ParseDateTime: " + ex.Message);
                dt = DateTime.Now;
                return false;
            }
        }

        private string GetExcelCellStringValue(ExcelWorksheet worksheet, int row, int column)
        {
            if (worksheet.Cells[row, column].Value != null)
                return worksheet.Cells[row, column].Value.ToString().Trim();

            return string.Empty;
        }

        private string GetIdKhachHangTheoMaGiayTo(string maGiayTo, string reasonForbidString)
        {
            Dictionary<string, string> queryString = new Dictionary<string, string>();
            queryString.Add("hoTen", string.Empty);
            queryString.Add("maGiayTo", maGiayTo);
            queryString.Add("lyDoCam", reasonForbidString);
            queryString.Add("thoiGianCamStart", new DateTime(2018, 1, 1, 0, 0, 0).ToString("dd/MM/yyyy HH:mm:ss"));
            queryString.Add("thoiGianCamEnd", DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss"));
            queryString.Add("thoiHanCamStart", new DateTime(2018, 1, 1, 0, 0, 0).ToString("dd/MM/yyyy HH:mm:ss"));
            queryString.Add("thoiHanCamEnd", DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss"));

            var humanBannedListModels = GetForbidList(queryString);
            if (humanBannedListModels != null && humanBannedListModels.Count > 0)
                return humanBannedListModels[0].IdKhachHang;

            return null;
        }

        [HttpGet]
        public IActionResult DownloadExcelTemplate(string f)
        {
            try
            {
                string fileName = System.IO.Path.Combine(_hostingEnvironment.ContentRootPath, "ExcelTemplate", f);
                var net = new System.Net.WebClient();

                if (Utils.IsSafeUrl(fileName) == false)
                {
                    throw new Exception("URL is not safe!");
                }

                var data = net.DownloadData(fileName);
                var content = new System.IO.MemoryStream(data);
                var contentType = "APPLICATION/octet-stream";
                return File(content, contentType, f);
            }
            catch
            {
                return NotFound();
            }
        }

        private string ConvertReasonForbidListToString(List<SelectListItem> reasonForbidList)
        {
            StringBuilder sb = new StringBuilder();
            foreach (var reasonForbid in reasonForbidList)
            {
                sb.Append(reasonForbid.Value);
                sb.Append("|");
            }
            sb.Append(Guid.Empty.ToString());
            return sb.ToString();
        }

        private void ClearHumanBannedList(HumanBannedListModel humanBannedList, string reasonForbidString, List<SelectListItem> reasonCancelForbidList, User user)
        {
            string idKhachHang = GetIdKhachHangTheoMaGiayTo(humanBannedList.PaperCode, reasonForbidString);
            if (idKhachHang != null)
            {
                if (string.IsNullOrEmpty(humanBannedList.ClearBy))
                    humanBannedList.ClearBy = user.UserName;

                if (string.IsNullOrWhiteSpace(humanBannedList.ReasonClear))
                {
                    humanBannedList.ReasonClear = Guid.Empty.ToString();
                }
                else
                {
                    humanBannedList.ReasonViolation = humanBannedList.ReasonClear.ToLower();
                    humanBannedList.ReasonClear = humanBannedList.ReasonClear.ToLower();
                    var reasonCancelForbid = reasonCancelForbidList.Where(x => x.Text.ToLower() == humanBannedList.ReasonClear).FirstOrDefault();
                    if (reasonCancelForbid != null)
                        humanBannedList.ReasonClear = reasonCancelForbid.Value;
                    else
                        humanBannedList.ReasonClear = Guid.Empty.ToString();
                }

                humanBannedList.ClearDate = DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss");
                if (ClearHumanBannedList(idKhachHang, humanBannedList.ClearDate, humanBannedList.ClearBy, humanBannedList.ReasonClear, string.Empty))
                {
                    humanBannedList.ClearBy = user.UserName;
                    humanBannedList.ReasonClear = humanBannedList.ReasonViolation;
                    SendAlarmMessage(EBANNED_HISTORY_ACTION_TYPE.HUY_CAM, humanBannedList);
                }
            }
        }

        [HttpPost("HumanBannedList/ImportHumanList")]
        public async Task<JsonResult> ImportHumanList(IFormFile file, int bannedType)
        {
            if (!IsValidExcelFile(file))
            {
                return Json(new
                {
                    success = false,
                    message = "Vui lòng chọn file"
                });
            }

            var user = GetSesson();
            if (user == null)
            {
                return Json(new
                {
                    success = false,
                    message = "Vui lòng đăng nhập để tiếp tục"
                });
            }

            int totalImport = 0;
            List<HumanBannedListModel> humanBannedLists = new List<HumanBannedListModel>();
            try
            {
                int loginUserId = int.Parse(this.HttpContext.Session.GetString("SessionUserSystemId"));

                using (var stream = new MemoryStream())
                {
                    await file.CopyToAsync(stream);

                    using (var package = new ExcelPackage(stream))
                    {
                        ExcelWorksheet worksheet = package.Workbook.Worksheets[0];
                        var rowCount = worksheet.Dimension.End.Row;

                        const int START_ROW = 5;
                        for (int row = START_ROW; row <= rowCount; row++)
                        {
                            var humanBannedList = ReadHumanBannedListFromExcel(worksheet, row, loginUserId, ref totalImport);
                            if (humanBannedList != null)
                                humanBannedLists.Add(humanBannedList);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("HumanBannedList/ImportHumanList: " + ex.Message);
                Console.WriteLine(ex.Message);
            }

            var reasonForbidList = GetReasonForbid();
            var reasonCancelForbidList = GetReasonCancelForbid();
            string reasonForbidString = ConvertReasonForbidListToString(reasonForbidList);

            int numOfImportSuccess = 0;

            foreach (var humanBannedList in humanBannedLists)
            {
                if (string.IsNullOrWhiteSpace(humanBannedList.ReasonViolation))
                {
                    humanBannedList.ReasonViolation = Guid.Empty.ToString();
                }
                else
                {
                    humanBannedList.ViolationType = humanBannedList.ReasonViolation;
                    var reasonForbid = reasonForbidList.Where(x => x.Text.ToLower() == humanBannedList.ViolationType.ToLower()).FirstOrDefault();
                    if (reasonForbid != null)
                        humanBannedList.ReasonViolation = reasonForbid.Value;
                    else
                        humanBannedList.ReasonViolation = Guid.Empty.ToString();
                }

                if (!IsValid(humanBannedList))
                    continue;

                bool isClearBanned = !string.IsNullOrWhiteSpace(humanBannedList.ClearFlag) && humanBannedList.ClearFlag.ToLower() == "y";
                humanBannedList.CreatedBy = user.UserName;
                if (OnSaveHumanBannedList(humanBannedList, !isClearBanned))
                {
                    _logger.LogInformation(string.Format("HumanBannedList/ImportHumanList 1: nguoi dung {0}, cam khach {1} vao ra thanh cong", user.UserName, humanBannedList.FullName));
                    numOfImportSuccess++;
                }

                //Hủy cấm đối tượng
                if (isClearBanned)
                {
                    ClearHumanBannedList(humanBannedList, reasonForbidString, reasonCancelForbidList, user);
                    if(!string.IsNullOrEmpty(humanBannedList.PaperCode))
                        _logger.LogInformation(string.Format("HumanBannedList/ImportHumanList 2: nguoi dung {0}, huy cam khach {1} vao ra thanh cong", user.UserName, humanBannedList.PaperCode));
                }
            }

            return Json(new
            {
                success = numOfImportSuccess > 0,
                numOfImportSuccess = numOfImportSuccess,
                totalImport = totalImport
            });
        }

        private HumanBannedListModel ReadHumanBannedListFromExcel(ExcelWorksheet worksheet, int row, int loginUserId, ref int totalImport)
        {
            if (string.IsNullOrEmpty(GetExcelCellStringValue(worksheet, row, (int)HumanExcelColumnIndex.LOAI_GIAY_TO)) ||
                string.IsNullOrEmpty(GetExcelCellStringValue(worksheet, row, (int)HumanExcelColumnIndex.MA_GIAY_TO)) ||
                string.IsNullOrEmpty(GetExcelCellStringValue(worksheet, row, (int)HumanExcelColumnIndex.HO_TEN)))
                return null;

            totalImport++;

            string PaperType = GetExcelCellStringValue(worksheet, row, (int)HumanExcelColumnIndex.LOAI_GIAY_TO);
            if (string.IsNullOrEmpty(PaperType))
                return null;

            var humanBannedList = new HumanBannedListModel();
            if (PaperType.ToLower().Equals("cmnd"))
                humanBannedList.PaperType = (int)EnumDocType.ID_CARD;
            else if (PaperType.ToLower().Equals("passport"))
                humanBannedList.PaperType = (int)EnumDocType.PASSPORT;
            else if (PaperType.ToLower().Equals("bằng lái xe") || PaperType.ToLower().Equals("blx"))
                humanBannedList.PaperType = (int)EnumDocType.DRIVER_LICENSE;
            else if (PaperType.ToLower().Equals("cccd") || PaperType.ToLower().Equals("căn cước công dân"))
                humanBannedList.PaperType = (int)EnumDocType.CCCD;
            else
                humanBannedList.PaperType = (int)EnumDocType.NONE;

            humanBannedList.PaperCode = GetExcelCellStringValue(worksheet, row, (int)HumanExcelColumnIndex.MA_GIAY_TO);
            if (string.IsNullOrWhiteSpace(humanBannedList.PaperCode))
                return null;

            humanBannedList.FullName = GetExcelCellStringValue(worksheet, row, (int)HumanExcelColumnIndex.HO_TEN);
            humanBannedList.ActiveTime = GetExcelCellDateTimeValueToString(worksheet, row, (int)HumanExcelColumnIndex.ACTIVE_TIME, "dd/MM/yyyy HH:mm:ss", DateTime.Now);
            humanBannedList.ExpiryTime = GetExcelCellDateTimeValueToString(worksheet, row, (int)HumanExcelColumnIndex.EXPIRY_TIME, "dd/MM/yyyy HH:mm:ss", null);

            humanBannedList.ReasonViolation = GetExcelCellStringValue(worksheet, row, (int)HumanExcelColumnIndex.LY_DO_VI_PHAM);
            humanBannedList.PhoneNumber = GetExcelCellStringValue(worksheet, row, (int)HumanExcelColumnIndex.SO_DIEN_THOAI);
            humanBannedList.Company = GetExcelCellStringValue(worksheet, row, (int)HumanExcelColumnIndex.CONG_TY);
            humanBannedList.BirthDay = GetExcelCellDateTimeValueToString(worksheet, row, (int)HumanExcelColumnIndex.NGAY_SINH, "dd/MM/yyyy", null);
            humanBannedList.ProvincialOfBirth = GetExcelCellStringValue(worksheet, row, (int)HumanExcelColumnIndex.QUE_QUAN);
            humanBannedList.Address = GetExcelCellStringValue(worksheet, row, (int)HumanExcelColumnIndex.DIA_CHI_THUONG_CHU);
            humanBannedList.PlaceOfIssue = GetExcelCellStringValue(worksheet, row, (int)HumanExcelColumnIndex.NOI_CAP_GIAY_TO);
            humanBannedList.DateOfIssue = GetExcelCellDateTimeValueToString(worksheet, row, (int)HumanExcelColumnIndex.NGAY_CAP_GIAY_TO, "dd/MM/yyyy", null);
            humanBannedList.ClearFlag = GetExcelCellStringValue(worksheet, row, (int)HumanExcelColumnIndex.HUY_VI_PHAM);
            humanBannedList.ClearBy = GetExcelCellStringValue(worksheet, row, (int)HumanExcelColumnIndex.NGUOI_HUY);
            humanBannedList.ClearDate = GetExcelCellDateTimeValueToString(worksheet, row, (int)HumanExcelColumnIndex.NGAY_HUY, "dd/MM/yyyy HH:mm:ss", DateTime.Now);
            humanBannedList.ReasonClear = GetExcelCellStringValue(worksheet, row, (int)HumanExcelColumnIndex.LY_DO_HUY);

            return humanBannedList;
        }

        private string GetExcelCellDateTimeValueToString(ExcelWorksheet worksheet, int row, int column, string format, DateTime? defaultDate)
        {
            try
            {
                if (worksheet.Cells[row, column].Value != null)
                {
                    if (double.TryParse(worksheet.Cells[row, column].Value.ToString().Trim(), out double value))
                    {
                        return DateTime.FromOADate(value).ToString(format);
                    }
                }
            }
            catch
            {

            }

            if (defaultDate.HasValue)
                return defaultDate.Value.ToString(format);

            return string.Empty;
        }

        private int FindUserIdByName(string name)
        {
            name = name.ToLower();
            var user = _userRepository.GetBy(x => x.UserName.ToLower() == name).FirstOrDefault();
            if (user != null)
                return user.Id;

            if (HttpContext.Session.GetString("SessionUserSystemId") != null)
            {
                int userId;
                if (int.TryParse(HttpContext.Session.GetString("SessionUserSystemId"), out userId))
                    return userId;
            }

            return 0;
        }


        private bool IsValid(HumanBannedListModel humanBannedList)
        {
            if (string.IsNullOrEmpty(humanBannedList.PaperCode) ||
                string.IsNullOrEmpty(humanBannedList.FullName))
                return false;

            return true;
        }

        public User GetSesson()
        {
            var SessionUserSystem = HttpContext.Session.GetComplexData<User>("SessionUserSystemId_" + HttpContext.Session.GetString("SessionUserSystemId"));
            if (SessionUserSystem != null)
            {
                var _user = _userRepository.GetById(SessionUserSystem.Id);

                if (_user != null)
                {
                    if (!_user.Actived)
                    {
                        HttpContext.Session.Clear();
                        return null;
                    }
                    return _user;
                }
            }
            return null;
        }

        private void InsertHistorySystem( EACTION_TYPE action_type, EnumControllerName controllerName, string description)
        {
            try
            {
                var ipAddress = HttpContext.Connection.RemoteIpAddress.ToString();

                HistorySystem history = new HistorySystem
                {
                    ActionType = (int)action_type,
                    ActionTime = DateTime.Now,
                    Description = description,
                    OldObject = string.Empty,
                    NewObject = string.Empty,
                    UserId = int.Parse(HttpContext.Session.GetString("SessionUserSystemId")),
                    IpAddress = ipAddress,
                    ControllerName = (int)controllerName
                };

                _historySystemRepository.Insert(history);

                _historySystemRepository.SaveChanges();
            }
            catch (Exception ex)
            {
                _logger.LogError("BannedList/InsertHistorySystem: " + ex.Message);
            }
        }


        private enum HumanExcelColumnIndex
        {
            LOAI_GIAY_TO = 1,
            MA_GIAY_TO,
            HO_TEN,
            ACTIVE_TIME,
            EXPIRY_TIME,
            LY_DO_VI_PHAM,
            SO_DIEN_THOAI,
            CONG_TY,
            NGAY_SINH,
            QUE_QUAN,
            DIA_CHI_THUONG_CHU,
            NOI_CAP_GIAY_TO,
            NGAY_CAP_GIAY_TO,
            HUY_VI_PHAM,
            NGUOI_HUY,
            NGAY_HUY,
            LY_DO_HUY
        }

    }
}