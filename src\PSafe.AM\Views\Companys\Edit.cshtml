﻿@model PSafe.AM.Models.CompanyModel

@{
    ViewBag.Title = "Sửa";
}
<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-lg-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>@PSafe.AM.Resources.Resource.Edit @PSafe.AM.Resources.Resource.Company</h5>
                    <div class="ibox-tools">
                        @Html.ActionLink(PSafe.AM.Resources.Resource.BackToList, "Index", "Companys", null, new { @class = "btn btn-primary btn-xs" })
                    </div>
                </div>
                <div class="ibox-content">
                    @using (Html.BeginForm())
                    {
                        @Html.AntiForgeryToken()
                        @Html.HiddenFor(model => model.Id)

                    <div class="row">
                        <div class="col-lg-6">
                            <div class="form-group row">
                                <label class="control-label col-md-4">@Html.LabelFor(model => model.Name) (*)</label>
                                <div class="col-md-8">
                                    @Html.EditorFor(model => model.Name)
                                    @Html.ValidationMessageFor(model => model.Name, null, new { @class = "text-danger" })
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6" id="hiddenUID">
                            <div class="form-group row">
                                <label class="control-label col-md-4">@Html.LabelFor(model => model.NickName)</label>
                                <div class="col-md-8">
                                    @Html.EditorFor(model => model.NickName)
                                    @Html.ValidationMessageFor(model => model.NickName, null, new { @class = "text-danger" })
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                <label class="control-label col-md-4">@Html.LabelFor(model => model.Email)</label>
                                <div class="col-md-8">
                                    @Html.EditorFor(model => model.Email)
                                    @Html.ValidationMessageFor(model => model.Email, null, new { @class = "text-danger" })
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                <label class="control-label col-md-4">@Html.LabelFor(model => model.Address)</label>
                                <div class="col-md-8">
                                    @Html.EditorFor(model => model.Address)
                                    @Html.ValidationMessageFor(model => model.Address, null, new { @class = "text-danger" })
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                <label class="control-label col-md-4">@Html.LabelFor(model => model.PhoneNumber)</label>
                                <div class="col-md-8">
                                    @Html.EditorFor(model => model.PhoneNumber)
                                    @Html.ValidationMessageFor(model => model.PhoneNumber, null, new { @class = "text-danger" })
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                <label class="control-label col-md-4">@Html.LabelFor(model => model.ContactPerson)</label>
                                <div class="col-md-8">
                                    @Html.EditorFor(model => model.ContactPerson)
                                    @Html.ValidationMessageFor(model => model.ContactPerson, null, new { @class = "text-danger" })
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                @Html.LabelFor(model => model.ParentId, new { @class = "control-label col-md-4" })
                                <div class="col-md-8">
                                    @Html.DropDownListFor(model => model.ParentId, Model.ListParentId as SelectList, new { @class = "form-control" })
                                    @Html.ValidationMessageFor(model => model.ParentId, null, new { @class = "text-danger" })
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                @Html.LabelFor(model => model.FormOfFeeCollection, new { @class = "control-label col-md-4" })
                                <div class="col-md-8">
                                    @Html.DropDownListFor(model => model.FormOfFeeCollection, Model.ListFormOfFeeCollection as SelectList, new { @class = "form-control" })
                                    @Html.ValidationMessageFor(model => model.FormOfFeeCollection, null, new { @class = "text-danger" })
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                @Html.LabelFor(model => model.Description, new { @class = "control-label col-md-4" })
                                <div class="col-md-8">
                                    @Html.TextAreaFor(model => model.Description, new { @class = "form-control" })
                                    @Html.ValidationMessageFor(model => model.Description, null, new { @class = "text-danger" })
                                </div>
                            </div>
                        </div>
                    </div>
                        <div class="row">
                            <div class="col-md-offset-2 col-md-10">
                                <input type="submit" value="@PSafe.AM.Resources.Resource.Save" class="btn btn-primary" />
                                @Html.ActionLink(@PSafe.AM.Resources.Resource.Cancel, "Index", "Companys", null, new { @class = "btn btn-white" })
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <environment names="Development,Staging,Production">
        <link rel="stylesheet" href="~/lib/toastr/toastr.min.css" />
    </environment>
}

@section Scripts {
    <environment names="Development,Staging,Production">
        <script src="~/lib/validate/jquery.validate.min.js"></script>
        <script src="~/lib/toastr/toastr.js"></script>
        <script src="~/lib/toastr/toastr.min.js"></script>
    </environment>

    @if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "warning")
    {
        <script type="text/javascript">
            toastr.warning('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "error")
    {
        <script type="text/javascript">
            toastr.error('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "success")
    {
        <script type="text/javascript">
            toastr.success('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
}