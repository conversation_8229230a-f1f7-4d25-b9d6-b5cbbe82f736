﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Routing;
using PSafe.Common.UserEnums;
using PSafe.Core.Domains;
using PSafe.Core.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PSafe.AM.Common
{
    public class AuthorizeActionFilterAttribute : ActionFilterAttribute
    {
        private readonly EPERMISSIONS_AM[] roles = null;
        public AuthorizeActionFilterAttribute(params EPERMISSIONS_AM[] _roles)
        {
            roles = _roles;
        }
        public override void OnActionExecuted(ActionExecutedContext context)
        {
            if ((context.Controller is Controller controller) && context.HttpContext.Session != null && context.HttpContext.Session.GetComplexData<SystemUser>("SessionUserSystemId_" + context.HttpContext.Session.GetString("SessionUserSystemId")) == null)
            {
                string url = Microsoft.AspNetCore.Http.Extensions.UriHelper.GetEncodedPathAndQuery(context.HttpContext.Request);
                if (url != "/")
                {
                    context.Result =
                           new RedirectToRouteResult(
                               new RouteValueDictionary{
                                       { "controller", "Security" },
                                              { "action", "Login" },
                                                { "returnUrl", url },
                                                    { "SessionEnd", true }
                                                             });
                }
                else
                {
                    context.Result =
                           new RedirectToRouteResult(
                               new RouteValueDictionary{
                                       { "controller", "Security" },
                                              { "action", "Login" },
                                                { "returnUrl", url },
                                                    { "SessionEnd", false }
                                                             });
                }
            }

            var contextResult = context.ActionDescriptor.RouteValues.Values.ToList();

            if(contextResult[1].ToString() + "/" + contextResult[0].ToString() != "Users/ChangePassword")
            {
                if (context.Controller is Controller && context.HttpContext.Session != null &&
                context.HttpContext.Session.GetString("SessionUserSystemId") != null &&
                !IsHasPermission(context.HttpContext))
                {
                    context.HttpContext.Response.StatusCode = 401;
                    context.HttpContext.Response.WriteAsync("<h1>401 - You do not have permission to access this page</h1>");
                    context.Result = new UnauthorizedResult();
                }
            }

            base.OnActionExecuted(context);
        }

        private bool IsHasPermission(HttpContext context)
        {
            if (roles.Length == 0)
                return true;

            string _userId = context.Session.GetString("SessionUserSystemId");
            if (_userId == null)
                return false;

            if (!int.TryParse(_userId, out int userId))
                return false;

            var userRepository = context.RequestServices.GetService(typeof(IUserRepository)) as IUserRepository;
            if (userRepository == null)
                return false;

            var user = userRepository.Get(x => x.Id == userId, null, "UserInRoles.Role").FirstOrDefault();
            if (user == null)
                return false;

            if (user.UserInRoles == null || user.UserInRoles.Count == 0)
                return false;

            var userFunctionSb = new StringBuilder();
            foreach (var role in user.UserInRoles)
            {
                if (string.IsNullOrWhiteSpace(role.Role.ListFunction_AM))
                    continue;

                userFunctionSb.Append(role.Role.ListFunction_AM.Trim());
                userFunctionSb.Append(",");
            }

            string _userFunctions = userFunctionSb.ToString();

            if (string.IsNullOrWhiteSpace(_userFunctions))
                return false;

            _userFunctions = _userFunctions.Substring(0, _userFunctions.Length - 1);

            if (roles.Contains(EPERMISSIONS_AM.ViewBlackList))
                return IsHasViewBackListPermission(_userFunctions);

            string userFunctions = string.Format(",{0},", _userFunctions);
            foreach(EPERMISSIONS_AM role in roles)
            {
                string roleStr = string.Format(",{0},", ((int)role).ToString());
                if (userFunctions.Contains(roleStr))
                    return true;
            }
            return false;
        }

        private bool IsHasViewBackListPermission(string userFunctions)
        {
            var functions = userFunctions.Split(',');
            if (functions.Contains(((int)EPERMISSIONS_AM.ForbidViolatingLoadControl).ToString()) ||
                        functions.Contains(((int)EPERMISSIONS_AM.CancelViolatingLoadControl).ToString()) ||
                        functions.Contains(((int)EPERMISSIONS_AM.ForbidViolatingTrafficSafety).ToString()) ||
                        functions.Contains(((int)EPERMISSIONS_AM.CancelViolatingTrafficSafety).ToString()) ||
                        functions.Contains(((int)EPERMISSIONS_AM.ForbidViolationOfBehavioralCulture).ToString()) ||
                        functions.Contains(((int)EPERMISSIONS_AM.CancelViolationOfBehavioralCulture).ToString()) ||
                        functions.Contains(((int)EPERMISSIONS_AM.ForbidViolationOfPortSecurity).ToString()) ||
                        functions.Contains(((int)EPERMISSIONS_AM.CancelViolationOfPortSecurity).ToString()) ||
                        functions.Contains(((int)EPERMISSIONS_AM.ForbidCarInOutImproperly).ToString()) ||
                        functions.Contains(((int)EPERMISSIONS_AM.CancelCarInOutImproperly).ToString()) ||
                        functions.Contains(((int)EPERMISSIONS_AM.ForbidOther).ToString()) ||
                        functions.Contains(((int)EPERMISSIONS_AM.CancelOther).ToString()) ||
                        functions.Contains(((int)EPERMISSIONS_AM.DeleteBlackList).ToString()) ||
                        functions.Contains(((int)EPERMISSIONS_AM.EditBlackList).ToString()) ||
                        functions.Contains(((int)EPERMISSIONS_AM.ViewBlackList).ToString()))
            {
                return true;
            }

            return false;
        }

        public static bool IsHasViewBackListPermission(List<EPERMISSIONS_AM> userFunctions)
        {
            if (userFunctions.Contains(EPERMISSIONS_AM.ForbidViolatingLoadControl) ||
                        userFunctions.Contains(EPERMISSIONS_AM.CancelViolatingLoadControl) ||
                        userFunctions.Contains(EPERMISSIONS_AM.ForbidViolatingTrafficSafety) ||
                        userFunctions.Contains(EPERMISSIONS_AM.CancelViolatingTrafficSafety) ||
                        userFunctions.Contains(EPERMISSIONS_AM.ForbidViolationOfBehavioralCulture) ||
                        userFunctions.Contains(EPERMISSIONS_AM.CancelViolationOfBehavioralCulture) ||
                        userFunctions.Contains(EPERMISSIONS_AM.ForbidViolationOfPortSecurity) ||
                        userFunctions.Contains(EPERMISSIONS_AM.CancelViolationOfPortSecurity) ||
                        userFunctions.Contains(EPERMISSIONS_AM.ForbidCarInOutImproperly) ||
                        userFunctions.Contains(EPERMISSIONS_AM.CancelCarInOutImproperly) ||
                        userFunctions.Contains(EPERMISSIONS_AM.ForbidOther) ||
                        userFunctions.Contains(EPERMISSIONS_AM.CancelOther) ||
                        userFunctions.Contains(EPERMISSIONS_AM.DeleteBlackList) ||
                        userFunctions.Contains(EPERMISSIONS_AM.EditBlackList) ||
                        userFunctions.Contains(EPERMISSIONS_AM.ViewBlackList))
            {
                return true;
            }

            return false;
        }

        public override void OnResultExecuting(ResultExecutingContext context)
        {
            base.OnResultExecuting(context);
            if(context.Controller is Controller && context.HttpContext.Session != null && context.HttpContext.Session.GetString("UserFunctions") != null)
            {
                ((Controller)context.Controller).ViewBag.userFunctions = context.HttpContext.Session.GetString("UserFunctions");
            }
            var generalConfig = context.HttpContext.RequestServices.GetService(typeof(IGeneralConfigRepository)) as IGeneralConfigRepository;
            if (generalConfig != null)
            {
                ((Controller)context.Controller).ViewBag.uiConfigs = generalConfig.GetSiteUIConfigs();
            }

            ((Controller)context.Controller).ViewBag.winform = context.HttpContext.Request.Query.ContainsKey("winform");
        }

    }
}
