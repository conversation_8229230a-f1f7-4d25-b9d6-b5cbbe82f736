﻿using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.AspNetCore.Mvc;
using PSafe.Core.Domains;
using PSafe.Core.Interface;
using PSafe.AM.Models;
using PSafe.Common;
using AutoMapper;
using PSafe.AM.Common;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.AspNetCore.Http;
using System.Net;
using static PSafe.Common.CommonEnums;
using PSafe.Common.UserEnums;

namespace PSafe.AM.Controllers
{
    [AuthorizeActionFilter(EPERMISSIONS_AM.ConfigurationGeneralManage)]
    [AutoValidateAntiforgeryToken]
    public class SystemUsersController : Controller
    {
        private readonly ISystemUserRepository _systemUserRepository;
        private readonly IGroupRepository _groupRepository;
        private readonly IDepartmentRepository _departmentRepository;
        private readonly IPositionRepository _positionRepository;
        private readonly IMapper _mapper;
        private readonly IHistorySystemRepository _historySystemRepository;

        public SystemUsersController(ISystemUserRepository systemUserRepository, IGroupRepository groupRepository, IMapper mapper, 
            IHistorySystemRepository historySystemRepository, IDepartmentRepository departmentRepository, IPositionRepository positionRepository)
        {
            _systemUserRepository = systemUserRepository;
            _groupRepository = groupRepository;
            _mapper = mapper;
            _historySystemRepository = historySystemRepository;
            _departmentRepository = departmentRepository;
            _positionRepository = positionRepository;
        }

        public ActionResult Index()
        {
            StatusQuery Notification;
            List<SystemUserModel> _listSystemModel = new List<SystemUserModel>();
            try
            {
                var _listUser = _systemUserRepository.GetAll().ToList();

                _listSystemModel = _mapper.Map<List<SystemUser>, List<SystemUserModel>>(_listUser);

                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }
            }
            catch
            {
                Notification = new StatusQuery("error", "Thất bại!", "Xem danh sách");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;
            }

            return View(_listSystemModel);
        }

        // GET: /user/Details/
        public ActionResult Details(int id)
        {
            try
            {
                var _systemUser = _systemUserRepository.GetById(id);

                var _systemModel = _mapper.Map<SystemUser, SystemUserModel>(_systemUser);

                try
                {
                    if (TempDataHelper.Get<StatusQuery>(TempData, "Notification") is StatusQuery Notification)
                    {
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;
                    }

                    if (_systemModel.GroupID != null && _systemModel.GroupID != -1)
                    {
                        ViewBag.GroupName = _groupRepository.GetById(_systemModel.GroupID).GroupName;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex.Message);
                }

                if (_systemModel != null)
                {
                    return View(_systemModel);
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "Thất bại", "Xem chi tiết"));

                    return RedirectToAction("Index");
                }
            }
            catch
            {
                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "Thất bại", "Xem chi tiết"));

                return RedirectToAction("Index");
            }
        }

        // GET: /users/Create
        public ActionResult Create()
        {
            SystemUserModel systemUser = new SystemUserModel();
            systemUser = GetDropdownListGroup(systemUser);

            return View(systemUser);
        }

        // POST: /a/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create([Bind("UserName, Password, Email, IsApproved, IsOnline, GroupID")] SystemUserModel systemUserModel)
        {
            StatusQuery Notification;
            if (systemUserModel.Password == null)
            {
                ModelState.AddModelError("Password", "Mật khẩu không được trống!");
            }
            else if (systemUserModel.Password.Length < 8)
            {
                ModelState.AddModelError("Password", "Độ dài từ 8-255 ký tự!");
            }

            if (ModelState.IsValid)
            {
                try
                {
                    var _systemUserSession = GetSesson();

                    if (_systemUserSession == null)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại!"));
                        return RedirectToAction("Logout", "Security");
                    }

                    var obj = _systemUserRepository.GetAll().SingleOrDefault(item => item.UserName.ToLower() == systemUserModel.UserName.ToLower());

                    if(obj != null)
                    {
                        Notification = new StatusQuery("error", "Thất bại!", "Tên người dùng đã tồn tại!");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        systemUserModel = GetDropdownListGroup(systemUserModel);

                        ModelState.AddModelError("UserName", "Vui lòng nhập tên người dùng khác!");

                        return View(systemUserModel);
                    }

                    HashEncoding hashEncoding = new HashEncoding();

                    if (systemUserModel.GroupID == -1)
                    {
                        systemUserModel.GroupID = null;
                    }

                    //systemUserModel.Password = hashEncoding.HashPasswordBCrypt(systemUserModel.Password);
                    systemUserModel.Password = hashEncoding.HashPassword(systemUserModel.Password, systemUserModel.UserName);
                    systemUserModel.CreationDate = DateTime.Now;
                    systemUserModel.CreationDate = DateTime.Now;
                    systemUserModel.LastActivityDate = DateTime.Now;
                    systemUserModel.LastLoginDate = DateTime.Now;
                    systemUserModel.LastPasswordChangeDate = DateTime.Now;

                    var _system = _mapper.Map<SystemUserModel, SystemUser>(systemUserModel);

                    _systemUserRepository.Insert(_system);

                    var addStatus = _systemUserRepository.SaveChanges();

                    if (addStatus > 0)
                    {
                        string StringDescription = new GetStringHistorySystem().Get(_systemUserSession.UserName, Resources.Resource.Create, _system.UserName.ToString(), Resources.Resource.SystemUsers);

                        InsertHistorySystem((int)EACTION_TYPE.CREATE, (int)EnumControllerName.SYSTEMUSERS, StringDescription, null, _system);

                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "Thành công", "Thêm mới"));

                        return RedirectToAction("Index");
                    }
                    else
                    {
                        Notification = new StatusQuery("error", "Thất bại!", "Thêm mới");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        systemUserModel = GetDropdownListGroup(systemUserModel);

                        return View(systemUserModel);
                    }
                }
                catch
                {
                    Notification = new StatusQuery("error", "Thất bại!", "Thêm mới");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    systemUserModel = GetDropdownListGroup(systemUserModel);

                    return View(systemUserModel);
                }

            }
            Notification = new StatusQuery("warning", "Giá trị nhập vào chưa đúng!", "Thêm mới");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;

            systemUserModel = GetDropdownListGroup(systemUserModel);

            return View(systemUserModel);
        }

        // GET: /user/Edit/5
        public ActionResult Edit(int id)
        {
            try
            {
                var _systemUserSession = GetSesson();

                if (_systemUserSession == null)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại!"));
                    return RedirectToAction("Logout", "Security");
                }

                var _systemUser = _systemUserRepository.GetById(id);

                if (_systemUser.Id == _systemUserSession.Id)
                {
                    ViewBag.HidenButtonChanger = true;
                }
                else
                {
                    ViewBag.HidenButtonChanger = false;
                }

                var _systemModel = _mapper.Map<SystemUser, SystemUserModel>(_systemUser);

                _systemModel = GetDropdownListGroup(_systemModel);

                if (_systemModel != null)
                {
                    return View(_systemModel);
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "Thất bại", "Xem chi tiết"));

                    return RedirectToAction("Index");
                }
            }
            catch
            {
                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "Thất bại", "Xem chi tiết"));

                return RedirectToAction("Index");
            }
        }

        // POST: /a/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit([Bind("SystemUserId, UserName, Email, IsApproved, IsOnline, GroupID")] SystemUserModel systemUserModel)
        {
            StatusQuery Notification;

            var sessionUser = GetSesson();

            if (sessionUser == null)
            {
                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại!"));
                return RedirectToAction("Logout", "Security");
            }

            if (sessionUser == GetSesson())
            {
                ViewBag.HidenButtonChanger = true;
            }
            else
            {
                ViewBag.HidenButtonChanger = false;
            }

            if (ModelState.IsValid)
            {
                try
                {
                    var systemUser = _systemUserRepository.GetById(systemUserModel.SystemUserId);

                    var _systemUserTemp = _mapper.Map<SystemUser, SystemUserModel>(systemUser);

                    var systemUserSession = GetSesson();

                    if (systemUserSession == null)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại!"));
                        return RedirectToAction("Logout", "Security");
                    }

                    if (systemUser.UserName.ToLower() != systemUserModel.UserName.ToLower())
                    {
                        var obj = _systemUserRepository.GetAll().SingleOrDefault(item => item.UserName.ToLower() == systemUserModel.UserName.ToLower());

                        if (obj != null)
                        {
                            Notification = new StatusQuery("error", "Thất bại!", "Tên người dùng đã tồn tại!");
                            ViewBag.Status = Notification.Status;
                            ViewBag.Value = Notification.Value;
                            ViewBag.Type = Notification.Type;

                            systemUserModel = GetDropdownListGroup(systemUserModel);

                            ModelState.AddModelError("UserName", "Vui lòng nhập tên người dùng khác!");

                            return View(systemUserModel);
                        }
                        else
                        {
                            systemUser.UserName = systemUserModel.UserName;
                            systemUser.Email = systemUserModel.Email;
                            systemUser.IsOnline = systemUserModel.IsOnline;
                            systemUser.IsApproved = systemUserModel.IsApproved;

                            // Nếu có chọn nhóm thì update nhóm, nếu không chọn thì update nhóm = -1
                            if (systemUserModel.GroupID != null)
                            {
                                systemUser.GroupId = (int)systemUserModel.GroupID;
                            }

                            _systemUserRepository.Update(systemUser);

                            var updateStatusNow = _systemUserRepository.SaveChanges();

                            if(updateStatusNow > 0)
                            {
                                string StringDescription = new GetStringHistorySystem().Get(systemUserSession.UserName, Resources.Resource.Edit, systemUser.UserName.ToString(), Resources.Resource.SystemUsers);

                                InsertHistorySystem((int)EACTION_TYPE.EDIT, (int)EnumControllerName.SYSTEMUSERS, StringDescription, systemUser, systemUser);

                                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "Thành công", "Sửa"));

                                return RedirectToAction("Logout", "Security"); 
                            }
                            else
                            {
                                Notification = new StatusQuery("error", "Thất bại!", "Sửa");
                                ViewBag.Status = Notification.Status;
                                ViewBag.Value = Notification.Value;
                                ViewBag.Type = Notification.Type;

                                systemUserModel = GetDropdownListGroup(systemUserModel);

                                return View(systemUserModel);
                            }
                        }
                    }
                    else
                    {
                        systemUser.Email = systemUserModel.Email;
                        systemUser.IsOnline = systemUserModel.IsOnline;
                        systemUser.IsApproved = systemUserModel.IsApproved;

                        // Nếu có chọn nhóm thì update nhóm, nếu không chọn thì update nhóm = -1
                        if (systemUserModel.GroupID != null)
                        {
                            systemUser.GroupId = (int)systemUserModel.GroupID;
                        }

                        _systemUserRepository.Update(systemUser);

                        var updateStatus = _systemUserRepository.SaveChanges();

                        if (updateStatus > 0)
                        {
                            string StringDescription = new GetStringHistorySystem().Get(systemUserSession.UserName, Resources.Resource.Edit, systemUser.UserName.ToString(), Resources.Resource.SystemUsers);

                            InsertHistorySystem((int)EACTION_TYPE.EDIT, (int)EnumControllerName.SYSTEMUSERS, StringDescription, systemUser, systemUser);

                            TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "Thành công", "Sửa"));

                            return RedirectToAction("Index");
                        }
                        else
                        {
                            Notification = new StatusQuery("error", "Thất bại!", "Sửa");
                            ViewBag.Status = Notification.Status;
                            ViewBag.Value = Notification.Value;
                            ViewBag.Type = Notification.Type;

                            systemUserModel = GetDropdownListGroup(systemUserModel);

                            return View(systemUserModel);
                        }
                    }
                }
                catch
                {
                    Notification = new StatusQuery("error", "Thất bại!", "Sửa");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    systemUserModel = GetDropdownListGroup(systemUserModel);

                    return View(systemUserModel);
                }
            }

            Notification = new StatusQuery("warning", "Giá trị nhập vào chưa đúng!", "Sửa");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;

            systemUserModel = GetDropdownListGroup(systemUserModel);

            return View(systemUserModel);
        }

        // GET: /user/Edit/5
        public ActionResult ChangePassword(int? id)
        {
            StatusQuery Notification;
            try
            {
                var user = GetSesson();

                if (user == null)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại!"));
                    return RedirectToAction("Logout", "Security");
                }

                SystemUser _systemUser;
                if(id != null)
                {
                    _systemUser = _systemUserRepository.GetById(id);

                    if (_systemUser != null)
                    {
                        ChangePassword _changePassword = new ChangePassword
                        {
                            UserName = _systemUser.UserName,
                            UsesIsLogging = false
                        };

                        return View(_changePassword);
                    }
                }
                else
                {
                    _systemUser = _systemUserRepository.GetById(user.Id);

                    if (_systemUser != null)
                    {
                        ChangePassword _changePassword = new ChangePassword
                        {
                            UserName = _systemUser.UserName,
                            // UsesIsLogging = true -> đăng xuất -> đăng nhập lại
                            UsesIsLogging = true
                        };

                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại!"));
                        return View(_changePassword);
                    }
                }

                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }
            }
            catch
            {
                Notification = new StatusQuery("error", "Thất bại!", "Đổi mật khẩu");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;
            }
            return View();
        }

        // POST: /user/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult ChangePassword([Bind("NewPassword, Repeatpassword, UserName, OldPassword")] ChangePassword _changePassword)
        {
            StatusQuery Notification;
            if (ModelState.IsValid)
            {
                try
                {
                    var systemUserSession = GetSesson();

                    if (systemUserSession == null)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại!"));
                        return RedirectToAction("Logout", "Security");
                    }

                    var systemUser = _systemUserRepository.GetAll().SingleOrDefault(p => p.UserName == _changePassword.UserName);
                    // kiểm tra user đang login -> đổi mật khẩu chính mình
                    if (systemUser.Id == systemUserSession.Id)
                    {
                        var _passwordUser = systemUser.Password;
                        var has = new HashEncoding();
                        var checkPasswordOld = has.VerifyPassword(_changePassword.OldPassword,_changePassword.UserName, _passwordUser);

                        if (!checkPasswordOld)
                        {
                            Notification = new StatusQuery("warning", "Mật khẩu cũ không đúng!", "Thất bại");
                            ViewBag.Status = Notification.Status;
                            ViewBag.Value = Notification.Value;
                            ViewBag.Type = Notification.Type;

                            return View(_changePassword);
                        }
                    }

                    var userIsChanger = _systemUserRepository.HashPassword(_changePassword.UserName, _changePassword.NewPassword);

                    _systemUserRepository.Update(userIsChanger);

                    var updateStatus = _systemUserRepository.SaveChanges();

                    if (updateStatus > 0)
                    {
                        string StringDescription = new GetStringHistorySystem().Get(systemUserSession.UserName, Resources.Resource.ChangePassword, userIsChanger.UserName.ToString(), Resources.Resource.SystemUsers);

                        InsertHistorySystem((int)EACTION_TYPE.CHANGE_PASSWORD, (int)EnumControllerName.SYSTEMUSERS, StringDescription, systemUser, userIsChanger);

                        if (systemUser.Id == systemUserSession.Id)
                        {
                            TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "Đổi mật khẩu", "Vui lòng đăng nhập lại!"));
                            return RedirectToAction("Logout", "Security");
                        }

                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "Thành công", "Đổi mật khẩu"));

                        return RedirectToAction("Index");
                    }
                    else
                    {
                        Notification = new StatusQuery("error", "Thất bại!", "Đổi mật khẩu");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        return View(_changePassword);
                    }
                }
                catch
                {
                    Notification = new StatusQuery("error", "Thất bại!", "Đổi mật khẩu");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return View(_changePassword);
                }
            }

            Notification = new StatusQuery("warning", "Giá trị nhập vào chưa đúng!", "Đổi mật khẩu");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;

            return View(_changePassword);
        }

        // GET: /user/Delete/5

        public ActionResult Delete(int id)
        {
            StatusQuery Notification;
            try
            {
                var _SystemUserOnLogin = GetSesson();

                if (_SystemUserOnLogin == null)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại!"));
                    return RedirectToAction("Logout", "Security");
                }

                if (_SystemUserOnLogin.Id == id)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("warning", "Người dùng đang hoạt động, Không thể xóa!", "Xóa"));

                    return RedirectToAction("Index");
                }

                var _systemUser = _systemUserRepository.GetById(id);

                var _systemUserModel = _mapper.Map<SystemUser, SystemUserModel>(_systemUser);

                try
                {
                    if (_systemUserModel.GroupID != null && _systemUserModel.GroupID != -1)
                    {
                        ViewBag.GroupName = _groupRepository.GetById(_systemUserModel.GroupID).GroupName;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex.Message);
                }

                if (_systemUserModel == null)
                {
                    Notification = new StatusQuery("warning", "Không tìm thấy đối tương xóa", "Xóa");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return RedirectToAction("Index");
                }

                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }

                return View(_systemUserModel);
            }
            catch
            {
                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "Thất bại!", "Xóa"));

                return RedirectToAction("Index");
            }
        }

        // POST: /CommandCenters/Delete/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int id)
        {
            try
            {
                var systemUserSession = GetSesson();

                if (systemUserSession == null)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại!"));
                    return RedirectToAction("Logout", "Security");
                }

                var systemUser = _systemUserRepository.GetById(id);

                _systemUserRepository.Delete(systemUser);

                var deleteStatus = _systemUserRepository.SaveChanges();

                if (deleteStatus > 0)
                {
                    HttpContext.Session.Remove("SessionUserSystemId_" + id);

                    string StringDescription = new GetStringHistorySystem().Get(systemUserSession.UserName, Resources.Resource.Delete, systemUser.UserName.ToString(), Resources.Resource.SystemUsers);

                    InsertHistorySystem((int)EACTION_TYPE.DELETE, (int)EnumControllerName.SYSTEMUSERS, StringDescription, systemUser, null);

                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "Thành công", "Xóa"));
                    return RedirectToAction("Index");
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "Thất bại!", "Xóa"));
                    return RedirectToAction("Delete", "SystemUsers", new { id });
                }
            }
            catch
            {
                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "Thất bại!", "Xóa"));
                return RedirectToAction("Delete", "SystemUsers", new { id });
            }
        }

        public SystemUser GetSesson()
        {
            var SessionUserSystem = HttpContext.Session.GetString("SessionUserSystemId");
            if (SessionUserSystem != null)
            {
                var _user = _systemUserRepository.GetById(int.Parse(SessionUserSystem));

                if (_user != null)
                {
                    return _user;
                }
            }
            return null;
        }

        private SystemUserModel GetDropdownListGroup(SystemUserModel systemUser)
        {
            List<DropDownList> _listGroup = new List<DropDownList>();
            List<DropDownList> _listPosition = new List<DropDownList>();
            List<DropDownList> _listdepartment = new List<DropDownList>();
            try
            {
                DropDownList drop = new DropDownList
                {
                    Id = -1,
                    Name = "Chọn"
                };

                _listGroup.Add(drop);
                _listPosition.Add(drop);
                _listdepartment.Add(drop);

                var listgroup = _groupRepository.GetAll().Select(p => new { p.Id, Name = p.GroupName }).ToList();

                var listPosition = _positionRepository.GetAll().ToList();

                var listDepartment = _departmentRepository.GetAll().ToList();

                foreach (var item in listgroup)
                {
                    DropDownList dropItem = new DropDownList
                    {
                        Id = item.Id,
                        Name = item.Name
                    };
                    _listGroup.Add(dropItem);
                }

                foreach (var item in listPosition)
                {
                    DropDownList dropItem = new DropDownList
                    {
                        Id = item.Id,
                        Name = item.PositionName
                    };
                    _listPosition.Add(dropItem);
                }

                foreach (var item in listDepartment)
                {
                    DropDownList dropItem = new DropDownList
                    {
                        Id = item.Id,
                        Name = item.DepartmentName
                    };
                    _listdepartment.Add(dropItem);
                }

                systemUser.ListGroups = ToSelectList(_listGroup);
                systemUser.ListpositionId = ToSelectList(_listPosition);
                systemUser.ListDepartmentId = ToSelectList(_listdepartment);
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
            }
            return (systemUser);
        }

        [NonAction]
        private SelectList ToSelectList(List<DropDownList> DropDownList)
        {
            List<SelectListItem> list = new List<SelectListItem>();

            foreach (var item in DropDownList)
            {
                list.Add(new SelectListItem()
                {
                    Text = item.Name,
                    Value = item.Id.ToString()
                });
            }

            return new SelectList(list, "Value", "Text");
        }

        private void InsertHistorySystem(int action_type, int controllerName, string description, object OldModel, object NewModel)
        {
            IPHostEntry heserver = Dns.GetHostEntry(Dns.GetHostName());
            var ipAddress = heserver.AddressList.FirstOrDefault(p => p.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork).ToString();

            string jsonOldObject = Newtonsoft.Json.JsonConvert.SerializeObject(OldModel);
            string jsonNewObject = Newtonsoft.Json.JsonConvert.SerializeObject(NewModel);

            HistorySystem history = new HistorySystem
            {
                ActionType = action_type,
                ActionTime = DateTime.Now,
                Description = description,
                OldObject = jsonOldObject,
                NewObject = jsonNewObject,
                UserId = int.Parse(HttpContext.Session.GetString("SessionUserSystemId")),
                IpAddress = ipAddress,
                ControllerName = controllerName
            };

            _historySystemRepository.Insert(history);

            _historySystemRepository.SaveChanges();
        }
    }
}