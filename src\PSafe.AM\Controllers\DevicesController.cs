﻿using AutoMapper;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using PSafe.AM.Common;
using PSafe.AM.Models;
using PSafe.Common;
using PSafe.Common.UserEnums;
using PSafe.Core.Domains;
using PSafe.Core.Interface;
using PSafe.Core.Models.Device;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using static PSafe.Common.CommonEnums;

namespace PSafe.AM.Controllers
{
    [AuthorizeActionFilter(EPERMISSIONS_AM.DeviceManage)]
    public class DevicesController : Controller
    {
        private readonly IProvinceRepository _provinceRepository;
        private readonly IWardRepository _wardRepository;
        private readonly IDeviceRepository _deviceRepository;
        private readonly ITypeOfDeviceRepository _typeOfDeviceRepository;
        private readonly IUserRepository _userRepository;
        private readonly IAreaRepository _areaRepository;
        private readonly ISupplierRepository _supplierRepository;
        private readonly ILocationRepository _locationRepository;
        private readonly IMapper _mapper;
        private readonly IHistorySystemRepository _historySystemRepository;
        private readonly IHttpContextAccessor _accessor;
        private readonly IConfiguration _configuration;
        private readonly ILogger<DevicesController> _logger;
        private readonly IHostingEnvironment _env;
        private readonly ITypeOfDeviceRepository _typeOfDevice;

        public DevicesController(IDeviceRepository deviceRepository, 
            ITypeOfDeviceRepository typeOfDeviceRepository, 
            IUserRepository userRepository, 
            IAreaRepository areaRepository, 
            ISupplierRepository supplierRepository, 
            ILocationRepository locationRepository, 
            IMapper mapper, 
            IHistorySystemRepository historySystemRepository, 
            ITypeOfDeviceRepository typeOfDevice, 
            IHttpContextAccessor accessor, 
            ILogger<DevicesController> logger, IHostingEnvironment env, 
            IConfiguration configuration, 
            IProvinceRepository provinceRepository,
            IWardRepository wardRepository)
        {
            _deviceRepository = deviceRepository;
            _typeOfDeviceRepository = typeOfDeviceRepository;
            _userRepository = userRepository;
            _areaRepository = areaRepository;
            _typeOfDevice = typeOfDevice;
            _supplierRepository = supplierRepository;
            _locationRepository = locationRepository;
            _mapper = mapper;
            _historySystemRepository = historySystemRepository;
            _accessor = accessor;
            _configuration = configuration;
            _logger = logger;
            _env = env;
            _provinceRepository = provinceRepository;
            _wardRepository = wardRepository;
        }

        public ActionResult Index()
        {
            StatusQuery Notification;
            try
            {
                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Devices/Index: " + ex.Message);

                Notification = new StatusQuery("error", "", "Xem danh sách thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;
            }

            return View();
        }

        public JsonResult GetIndex()
        {
            try
            {
                var listTypeOfSingnal = new ListTypeOfSingnal().List;

                var query = from d in _deviceRepository.GetAll().ToList()
                            let lo = (from l in _locationRepository.GetAll().ToList()
                                      where d.LocationId == l.Id
                                      select new { l.LocationName, l.AreaId }).DefaultIfEmpty()
                            from location in lo
                            let ty = (from t in _typeOfDevice.GetAll().ToList()
                                      where d.TypeOfDeviceId == t.Id
                                      select new { t.TypeName, t.Id }).DefaultIfEmpty()
                            from typeofdevice in ty
                            let ar = (from a in _areaRepository.GetAll().ToList()
                                      where (location == null ? -2 : location.AreaId) == a.Id
                                      select new { a.AreaName }).DefaultIfEmpty()
                            from area in ar
                            select new
                            {
                                Description = d.Description ?? string.Empty,
                                d.Id,
                                DeviceName = d.DeviceName ?? string.Empty,
                                SerialNumber = d.SerialNumber ?? string.Empty,
                                TypeOfSingnal = listTypeOfSingnal.Where(p => p.Id == d.TypeOfSignal).Select(p => p.Name),
                                LocationName = location?.LocationName ?? string.Empty,
                                AreaName = area?.AreaName ?? string.Empty,
                                Ip = d.Ip ?? string.Empty,
                                DriverName = d.DriverName ?? string.Empty,
                                MacAddress = d.MacAddress ?? string.Empty,
                                TypeOfDevice = typeofdevice?.TypeName ?? string.Empty,
                                FromSync = typeofdevice?.Id != 6 || d.FromSync == true
                            };

                return Json(query);
            }
            catch (Exception ex)
            {
                _logger.LogError("Devices/GetIndex: " + ex.Message);

                return Json(null);
            }
        }

        public ActionResult Details(int id)
        {
            try
            {
                var device = _deviceRepository.GetById(id);

                if (device == null)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                    return RedirectToAction("Index");
                }

                var _listSystemUser = _userRepository.GetAll();

                var _deviceModel = _mapper.Map<Device, DeviceModel>(device);

                try
                {
                    if (TempDataHelper.Get<StatusQuery>(TempData, "Notification") is StatusQuery Notification)
                    {
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;
                    }

                    if (_deviceModel.LOCATIONID != null)
                    {
                        ViewBag.LOCATIONID = _locationRepository.GetById(_deviceModel.LOCATIONID).LocationName;
                    }
                    if (_deviceModel.TYPEOFDEVICEID != null)
                    {
                        ViewBag.TYPEOFDEVICEID = _typeOfDeviceRepository.GetById(_deviceModel.TYPEOFDEVICEID).TypeName;
                    }
                    if (_deviceModel.SUPPLIERID != null && _deviceModel.SUPPLIERID != -1)
                    {
                        ViewBag.SUPPLIERID = _supplierRepository.GetById(_deviceModel.SUPPLIERID).Name;
                    }

                    ViewBag.TypeOfSingnal = new ListTypeOfSingnal().List.SingleOrDefault(p => p.Id == _deviceModel.TYPEOFSINGNAL).Name;
                    ViewBag.UPDATEDUSER = _listSystemUser.SingleOrDefault(p => p.Id == _deviceModel.UPDATEDUSER).UserName;
                    ViewBag.CREATEDUSER = _listSystemUser.SingleOrDefault(p => p.Id == _deviceModel.CREATEDUSER).UserName;
                    ViewBag.ProvinceName = _provinceRepository.GetById(device.ProvinceId)?.Name;
                    ViewBag.WardName = _wardRepository.GetById(device.WardId)?.Name;
                }
                catch (Exception ex)
                {
                    _logger.LogError("Devices/Details: " + ex.Message);
                    Console.WriteLine(ex.Message);
                }

                return View(_deviceModel);
            }
            catch (Exception ex)
            {
                _logger.LogError("Devices/Details: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                return RedirectToAction("Index");
            }
        }

        public async Task<ActionResult> Create()
        {
            DeviceModel deviceModel = new DeviceModel();
            try
            {
                deviceModel.ACTIVED = true;
                deviceModel.TrafficCamera = false;
                deviceModel = GetListDropdown(deviceModel);

                var builder = new ConfigurationBuilder()
                                     .SetBasePath(Directory.GetCurrentDirectory())
                                     .AddJsonFile("appsettings.json");
                var configuration = builder.Build();

                deviceModel.ListAllPreset = await GetListPreset(_deviceRepository.GetAll().ToList());

                ViewBag.IDTypeOfDevice = configuration["IDTypeOfDevice"];
                ViewBag.TYPEOFSINGNALEnum = (int)PSafe.Common.EventEnums.ETYPE_OF_SIGNAL.FireSafetyAlarm;
                ViewBag.SpeedLimitVehicleType = new List<DynamicField>();
            }
            catch (Exception ex)
            {
                _logger.LogError("Devices/Create: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Tạo mới thất bại"));

                return RedirectToAction("Index");
            }

            return View(deviceModel);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> Create(
            [Bind("TYPEOFDEVICEID, TYPEOFSINGNAL,LOCATIONID, DEVICENAME, SERIALNUMBER, ACTIVED, SERVER, PORT, URL, USERNAME, PASSWORD, DESCRIPTION, LASTCONNECTTIME, SUPPLIERID, areas, ALLOWUSERREMOTEACCESS, IPLOCAL, VIDEOLINK, QUANTITYCHANNEL, CameraMonitorId, Zone, Ip, MacAddress, DriverName, Preset, TrafficCamera,ProvinceId,DistrictId,WardId")] DeviceModel deviceModel, IFormCollection form)
        {
            StatusQuery Notification;

            var builder = new ConfigurationBuilder()
                                     .SetBasePath(Directory.GetCurrentDirectory())
                                     .AddJsonFile("appsettings.json");
            var configuration = builder.Build();

            ViewBag.IDTypeOfDevice = configuration["IDTypeOfDevice"];

            var vehicleTypes = form.Keys.Where(kv => kv.EndsWith("VehicleType"))
                        .Select(kv => new
                        {
                            Id = kv.Split('_')[0],
                            Type = form[kv]
                        }).ToList();

            var speeds = form.Keys.Where(kv => kv.EndsWith("SpeedLimited"))
                                .Select(kv => new
                                {
                                    Id = kv.Split('_')[0],
                                    Speed = form[kv]
                                })
                                .ToList();

            var dynamicField = vehicleTypes.Join(speeds,
                vehicle => vehicle.Id,
                speed => speed.Id,
                (vehicle, speed) => new DynamicField()
                {
                    VehicleType = vehicle.Type,
                    SpeedLimited = int.Parse(speed.Speed)
                }).ToList();

            if (ModelState.IsValid)
            {
                try
                {
                    var _systemUser = GetSesson();

                    if (_systemUser == null)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                        return RedirectToAction("Logout", "Security");
                    }

                    var _deviceDuplicate = _deviceRepository.GetAll().FirstOrDefault(p => p.DeviceName.ToLower() == deviceModel.DEVICENAME.ToLower());

                    if (_deviceDuplicate != null)
                    {
                        Notification = new StatusQuery("error", "", "Vui lòng kiểm tra lại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        ModelState.AddModelError("DEVICENAME", "Tên thiết bị đã tồn tại");

                        deviceModel = GetListDropdown(deviceModel);
                        deviceModel.ListAllPreset = await GetListPreset(_deviceRepository.GetAll().ToList());

                        return View(deviceModel);
                    }
                    if (deviceModel.ProvinceId == 0)
                    {
                        ModelState.AddModelError("ProvinceId", "Vui lòng chọn tỉnh");
                        return View(deviceModel);
                    }

                    //if (deviceModel.DistrictId == 0)
                    //{
                    //    ModelState.AddModelError("DistrictId", "Vui lòng chọn quận/huyện");
                    //    return View(deviceModel);
                    //}
                    if (deviceModel.WardId == 0)
                    {
                        ModelState.AddModelError("WardId", "Vui lòng chọn phường/xã");
                        return View(deviceModel);
                    }

                    deviceModel.CREATEDDATE = DateTime.Now;
                    deviceModel.CREATEDUSER = _systemUser.Id;
                    deviceModel.UPDATEDDATE = DateTime.Now;
                    deviceModel.UPDATEDUSER = _systemUser.Id;
                    deviceModel.RENEWDAY = DateTime.Now;
                    deviceModel.EXPDAY = DateTime.Now.AddMonths(1);
                    deviceModel.CONNECTSTATUS = 1;
                    deviceModel.Preset = deviceModel.Preset == "-1" ? null : deviceModel.Preset;
                    //deviceModel.SpeedLimit = deviceModel.SpeedLimit <= 0 ? null : deviceModel.SpeedLimit;

                    var _device = _mapper.Map<DeviceModel, Device>(deviceModel);

                    _device.SpeedLimitVehicleType = JsonConvert.SerializeObject(dynamicField);

                    try
                    {
                        var list = new ListTypeOfSingnal().List;

                        var TYPEOFSINGNAL = list.FirstOrDefault(p => p.Id == deviceModel.TYPEOFSINGNAL);

                        if (TYPEOFSINGNAL == null)
                        {
                            Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                            ViewBag.Status = Notification.Status;
                            ViewBag.Value = Notification.Value;
                            ViewBag.Type = Notification.Type;

                            deviceModel = GetListDropdown(deviceModel);
                            ViewBag.TYPEOFSINGNALEnum = (int)PSafe.Common.EventEnums.ETYPE_OF_SIGNAL.FireSafetyAlarm;
                            deviceModel.ListAllPreset = await GetListPreset(_deviceRepository.GetAll().ToList());
                            ViewBag.SpeedLimitVehicleType = dynamicField;

                            return View(deviceModel);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError("Devices/Create: " + ex.Message);

                        Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        deviceModel = GetListDropdown(deviceModel);
                        ViewBag.TYPEOFSINGNALEnum = (int)PSafe.Common.EventEnums.ETYPE_OF_SIGNAL.FireSafetyAlarm;
                        deviceModel.ListAllPreset = await GetListPreset(_deviceRepository.GetAll().ToList());
                        ViewBag.SpeedLimitVehicleType = dynamicField;

                        return View(deviceModel);
                    }
                    _device.ProvinceId = deviceModel.ProvinceId.Value;
                    //_device.DistrictId = deviceModel.DistrictId.Value;
                    _device.WardId = deviceModel.WardId.Value;
                    _device.LocationId = _device.LocationId != -1 ? _device.LocationId : null;
                    _device.SupplierId = _device.SupplierId != -1 ? _device.SupplierId : null;
                    _device.CameraMonitorId = _device.CameraMonitorId == -1 ? null : _device.CameraMonitorId;
                    _device.TrafficCamera = deviceModel.TrafficCamera == null ? false : deviceModel.TrafficCamera;

                    if (deviceModel.areas != -1)
                    {
                        try
                        {
                            var areaName = _areaRepository.GetById(int.Parse(deviceModel.areas.ToString()));
                            _device.AreaNameNonUnicode = areaName.AreaName.NonUnicode();

                            if (deviceModel.LOCATIONID != -1)
                            {
                                var areaLocation = _locationRepository.GetById(int.Parse(deviceModel.LOCATIONID.ToString()));
                                _device.LocationNameNonUnicode = areaLocation.LocationName.NonUnicode();
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError("Devices/Create: " + ex.Message);

                            Notification = new StatusQuery("error", "", "Chọn khu vực và vị trí không chính xác");
                            ViewBag.Status = Notification.Status;
                            ViewBag.Value = Notification.Value;
                            ViewBag.Type = Notification.Type;

                            deviceModel = GetListDropdown(deviceModel);
                            ViewBag.TYPEOFSINGNALEnum = (int)PSafe.Common.EventEnums.ETYPE_OF_SIGNAL.FireSafetyAlarm;
                            deviceModel.ListAllPreset = await GetListPreset(_deviceRepository.GetAll().ToList());
                            ViewBag.SpeedLimitVehicleType = dynamicField;

                            return View(deviceModel);
                        }
                    }

                    _deviceRepository.Insert(_device);

                    var statusInsert = _deviceRepository.SaveChanges();

                    if (statusInsert > 0)
                    {
                        string StringDescription = new GetStringHistorySystem().Get(_systemUser.UserName, Resources.Resource.Create, _device.DeviceName.ToString(), Resources.Resource.Device);

                        InsertHistorySystem((int)EACTION_TYPE.CREATE, (int)EnumControllerName.DEVICES, StringDescription, null, _device);

                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Thêm mới thành công"));
                        return RedirectToAction("Index");
                    }
                    else
                    {
                        Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        deviceModel = GetListDropdown(deviceModel);
                        ViewBag.TYPEOFSINGNALEnum = (int)PSafe.Common.EventEnums.ETYPE_OF_SIGNAL.FireSafetyAlarm;
                        deviceModel.ListAllPreset = await GetListPreset(_deviceRepository.GetAll().ToList());
                        ViewBag.SpeedLimitVehicleType = dynamicField;

                        return View(deviceModel);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("Devices/Create: " + ex.Message);

                    Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    deviceModel = GetListDropdown(deviceModel);
                    ViewBag.TYPEOFSINGNALEnum = (int)PSafe.Common.EventEnums.ETYPE_OF_SIGNAL.FireSafetyAlarm;
                    deviceModel.ListAllPreset = await GetListPreset(_deviceRepository.GetAll().ToList());
                    ViewBag.SpeedLimitVehicleType = dynamicField;

                    return View(deviceModel);
                }
            }
            Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;

            deviceModel = GetListDropdown(deviceModel);
            ViewBag.TYPEOFSINGNALEnum = (int)PSafe.Common.EventEnums.ETYPE_OF_SIGNAL.FireSafetyAlarm;
            deviceModel.ListAllPreset = await GetListPreset(_deviceRepository.GetAll().ToList());
            ViewBag.SpeedLimitVehicleType = dynamicField;

            return View(deviceModel);
        }

        public async Task<ActionResult> Edit(int id)
        {
            try
            {
                var _device = await _deviceRepository.GetOneAsync(p => p.Id == id, null, "Location.Area");
                if (_device == null)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                    return RedirectToAction("Index");
                }

                var _deviceModel = _mapper.Map<Device, DeviceModel>(_device);

                var builder = new ConfigurationBuilder()
                                     .SetBasePath(Directory.GetCurrentDirectory())
                                     .AddJsonFile("appsettings.json");
                var configuration = builder.Build();

                ViewBag.IDTypeOfDevice = configuration["IDTypeOfDevice"];
                ViewBag.TYPEOFSINGNALEnum = (int)PSafe.Common.EventEnums.ETYPE_OF_SIGNAL.FireSafetyAlarm;

                _deviceModel.ListAllPreset = await GetListPreset(new List<Device>() { _device });
                _deviceModel.TrafficCamera = _device.TrafficCamera ?? false;

                if (_device.SpeedLimitVehicleType != null)
                {
                    try
                    {
                        ViewBag.SpeedLimitVehicleType = JsonConvert.DeserializeObject<List<DynamicField>>(_device.SpeedLimitVehicleType);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError("SpeedLimitVehicleType Failed: " + ex.Message);
                        ViewBag.SpeedLimitVehicleType = new List<DynamicField>();
                    }
                }
                else
                {
                    ViewBag.SpeedLimitVehicleType = new List<DynamicField>();
                }

                _deviceModel = await GetListDropdownAsync(_deviceModel);

                return View(_deviceModel);
            }
            catch (Exception ex)
            {
                _logger.LogError("Devices/Edit: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                return RedirectToAction("Index");
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> Edit([Bind("DEVICEID, areas,TYPEOFDEVICEID, TYPEOFSINGNAL, LOCATIONID, USERID, DEVICENAME, SERIALNUMBER, ACTIVED, SERVER, PORT, URL, USERNAME, PASSWORD, DESCRIPTION, LASTCONNECTTIME, SUPPLIERID, DEVICESTATUS, ALLOWUSERREMOTEACCESS, IPLOCAL, VIDEOLINK, QUANTITYCHANNEL, CameraMonitorId, Zone, Ip, MacAddress, DriverName, Preset, TrafficCamera, SpeedLimit,ProvinceId,DistrictId,WardId")] DeviceModel deviceModel, IFormCollection form)
        {
            StatusQuery Notification;

            var builder = new ConfigurationBuilder()
                                     .SetBasePath(Directory.GetCurrentDirectory())
                                     .AddJsonFile("appsettings.json");
            var configuration = builder.Build();

            //if (deviceModel.TYPEOFDEVICEID == int.Parse(configuration["IDTypeOfDevice"]))
            //{
            //    ModelState.Remove("CameraMonitorId");
            //}

            ViewBag.IDTypeOfDevice = configuration["IDTypeOfDevice"];

            var vehicleTypes = form.Keys.Where(kv => kv.EndsWith("VehicleType"))
                        .Select(kv => new
                        {
                            Id = kv.Split('_')[0],
                            Type = form[kv]
                        }).ToList();

            var speeds = form.Keys.Where(kv => kv.EndsWith("SpeedLimited"))
                                .Select(kv => new
                                {
                                    Id = kv.Split('_')[0],
                                    Speed = form[kv]
                                })
                                .ToList();

            var dynamicField = vehicleTypes.Join(speeds,
                vehicle => vehicle.Id,
                speed => speed.Id,
                (vehicle, speed) => new DynamicField()
                {
                    VehicleType = vehicle.Type,
                    SpeedLimited = int.Parse(speed.Speed)
                }).ToList();

            if (ModelState.IsValid)
            {
                try
                {
                    var _systemUser = GetSesson();

                    if (_systemUser == null)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                        return RedirectToAction("Logout", "Security");
                    }

                    var device = _deviceRepository.GetById(deviceModel.DEVICEID);

                    var _DeviceModelTemp = _mapper.Map<Device, DeviceModel>(device);
                    var deviceOld = _mapper.Map<DeviceModel, Device>(_DeviceModelTemp);

                    device.UpdatedDate = DateTime.Now;
                    device.UpdatedBy = _systemUser.Id;
                    device.TypeOfDeviceId = deviceModel.TYPEOFDEVICEID;
                    device.CameraMonitorId = deviceModel.CameraMonitorId;
                    device.Zone = deviceModel.Zone;
                    device.Ip = deviceModel.Ip;
                    device.Preset = deviceModel.Preset == "-1" ? null : deviceModel.Preset;
                    device.TrafficCamera = deviceModel.TrafficCamera != null && deviceModel.TrafficCamera;
                    device.SpeedLimit = deviceModel.SpeedLimit <= 0 ? null : deviceModel.SpeedLimit;
                    device.SpeedLimitVehicleType = JsonConvert.SerializeObject(dynamicField);

                    try
                    {
                        var list = new ListTypeOfSingnal().List;
                        var TYPEOFSINGNAL = list.FirstOrDefault(p => p.Id == deviceModel.TYPEOFSINGNAL);
                        if (TYPEOFSINGNAL == null)
                        {
                            Notification = new StatusQuery("error", "", "Sửa thất bại");
                            ViewBag.Status = Notification.Status;
                            ViewBag.Value = Notification.Value;
                            ViewBag.Type = Notification.Type;
                            deviceModel = GetListDropdown(deviceModel);
                            deviceModel.ListAllPreset = await GetListPreset(_deviceRepository.GetAll().ToList());
                            ViewBag.SpeedLimitVehicleType = dynamicField;

                            return View(deviceModel);
                        }
                        else
                        {
                            device.TypeOfSignal = TYPEOFSINGNAL.Id;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError("Devices/Edit: " + ex.Message);

                        Notification = new StatusQuery("error", "", "Sửa thất bại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;
                        deviceModel = GetListDropdown(deviceModel);
                        ViewBag.SpeedLimitVehicleType = dynamicField;

                        return View(deviceModel);
                    }

                    if (deviceModel.areas != -1)
                    {
                        try
                        {
                            var areaName = _areaRepository.GetById(int.Parse(deviceModel.areas.ToString()));
                            device.AreaNameNonUnicode = areaName.AreaName.NonUnicode();

                            if (deviceModel.LOCATIONID != -1)
                            {
                                var areaLocation = _locationRepository.GetById(int.Parse(deviceModel.LOCATIONID.ToString()));
                                device.LocationNameNonUnicode = areaLocation.LocationName.NonUnicode();
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError("Devices/Edit: " + ex.Message);

                            Notification = new StatusQuery("error", "", "Chọn khu vực và vị trí không chính xác");
                            ViewBag.Status = Notification.Status;
                            ViewBag.Value = Notification.Value;
                            ViewBag.Type = Notification.Type;

                            deviceModel = GetListDropdown(deviceModel);
                            deviceModel.ListAllPreset = await GetListPreset(_deviceRepository.GetAll().ToList());
                            ViewBag.SpeedLimitVehicleType = dynamicField;

                            return View(deviceModel);
                        }
                    }

                    device.LocationId = deviceModel.LOCATIONID;
                    device.DeviceName = deviceModel.DEVICENAME;
                    device.SerialNumber = deviceModel.SERIALNUMBER;
                    device.QuantityChannel = deviceModel.QUANTITYCHANNEL;
                    device.Actived = deviceModel.ACTIVED;
                    device.Server = deviceModel.SERVER;
                    device.Port = deviceModel.PORT;
                    device.Url = deviceModel.URL;
                    device.Username = deviceModel.USERNAME;
                    device.Password = deviceModel.PASSWORD;
                    device.LastConnectTime = deviceModel.LASTCONNECTTIME;
                    device.SupplierId = deviceModel.SUPPLIERID;
                    device.DeviceStatus = deviceModel.DEVICESTATUS;
                    device.AllowUserRemoteAccess = deviceModel.ALLOWUSERREMOTEACCESS;
                    device.IpLocal = deviceModel.IPLOCAL;
                    device.VideoLink = deviceModel.VIDEOLINK;
                    device.Description = deviceModel.DESCRIPTION;
                    device.LocationId = deviceModel.LOCATIONID != -1 ? deviceModel.LOCATIONID : null;
                    device.MacAddress = deviceModel.MacAddress;
                    device.DriverName = deviceModel.DriverName;
                    device.ProvinceId = deviceModel.ProvinceId.Value;
                    //device.DistrictId = deviceModel.DistrictId.Value;
                    device.WardId = deviceModel.WardId.Value;
                    _deviceRepository.Update(device);

                    var updateStatus = _deviceRepository.SaveChanges();

                    if (updateStatus > 0)
                    {
                        string StringDescription = new GetStringHistorySystem().Get(_systemUser.UserName, Resources.Resource.Edit, device.DeviceName.ToString(), Resources.Resource.Device);

                        InsertHistorySystem((int)EACTION_TYPE.EDIT, (int)EnumControllerName.DEVICES, StringDescription, deviceOld, device);

                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Sửa thành công"));

                        return RedirectToAction("Index");
                    }
                    else
                    {
                        Notification = new StatusQuery("error", "", "Sửa thất bại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        deviceModel = GetListDropdown(deviceModel);
                        ViewBag.TYPEOFSINGNALEnum = (int)PSafe.Common.EventEnums.ETYPE_OF_SIGNAL.FireSafetyAlarm;
                        deviceModel.ListAllPreset = await GetListPreset(_deviceRepository.GetAll().ToList());
                        ViewBag.SpeedLimitVehicleType = dynamicField;

                        return View(deviceModel);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("Devices/Edit: " + ex.Message);

                    Notification = new StatusQuery("error", "", "Sửa thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    deviceModel = GetListDropdown(deviceModel);
                    ViewBag.TYPEOFSINGNALEnum = (int)PSafe.Common.EventEnums.ETYPE_OF_SIGNAL.FireSafetyAlarm;
                    deviceModel.ListAllPreset = await GetListPreset(_deviceRepository.GetAll().ToList());
                    ViewBag.SpeedLimitVehicleType = dynamicField;

                    return View(deviceModel);
                }
            }

            Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;

            deviceModel = GetListDropdown(deviceModel);
            ViewBag.TYPEOFSINGNALEnum = (int)PSafe.Common.EventEnums.ETYPE_OF_SIGNAL.FireSafetyAlarm;
            deviceModel.ListAllPreset = await GetListPreset(_deviceRepository.GetAll().ToList());
            ViewBag.SpeedLimitVehicleType = dynamicField;

            return View(deviceModel);
        }

        public ActionResult Delete(int id)
        {
            StatusQuery Notification;
            try
            {
                var _device = _deviceRepository.GetById(id);

                var _deviceModel = _mapper.Map<Device, DeviceModel>(_device);

                try
                {
                    if (_deviceModel.LOCATIONID != null)
                    {
                        ViewBag.LOCATIONID = _locationRepository.GetById(_deviceModel.LOCATIONID).LocationName;
                    }
                    if (_deviceModel.TYPEOFDEVICEID != null)
                    {
                        ViewBag.TYPEOFDEVICEID = _typeOfDeviceRepository.GetById(_deviceModel.TYPEOFDEVICEID).TypeName;
                    }
                    if (_deviceModel.SUPPLIERID != null && _deviceModel.SUPPLIERID != -1)
                    {
                        ViewBag.SUPPLIERID = _supplierRepository.GetById(_deviceModel.SUPPLIERID).Name;
                    }

                    ViewBag.TypeOfSingnal = new ListTypeOfSingnal().List.SingleOrDefault(p => p.Id == _deviceModel.TYPEOFSINGNAL).Name;
                    ViewBag.UPDATEDUSER = _userRepository.GetById(_deviceModel.UPDATEDUSER).UserName;
                    ViewBag.CREATEDUSER = _userRepository.GetById(_deviceModel.CREATEDUSER).UserName;
                }
                catch (Exception ex)
                {
                    _logger.LogError("Devices/Delete: " + ex.Message);

                    Console.WriteLine(ex.Message);
                }

                if (_deviceModel == null)
                {
                    Notification = new StatusQuery("warning", "", "Không tìm thấy thiết bị");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return RedirectToAction("Index");
                }

                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }

                return View(_deviceModel);
            }
            catch (Exception ex)
            {
                _logger.LogError("Devices/Delete: " + ex.Message);

                Notification = new StatusQuery("warning", "", "Xóa thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;

                return RedirectToAction("Index");
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteList(List<int> listId)
        {
            try
            {
                var systemUser = GetSesson();

                if (systemUser == null)
                {
                    return Ok(new
                    {
                        Result = 1,
                        message = "Phiên làm việc hết hạn, vui lòng đăng nhập lại"
                    });
                }

                int count = 0;
                foreach (var item in listId)
                {
                    var device = _deviceRepository.GetById(item);
                    if (device == null)
                        continue;
                    _deviceRepository.Delete(device);

                    var deleteStatus = _deviceRepository.SaveChanges();

                    if (deleteStatus > 0)
                    {
                        count++;
                    }
                }

                return Ok(new
                {
                    Result = 0,
                    message = "Xóa thành công " + count + " thiết bị"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError("Devices/Delete: " + ex.Message);

                return Ok(new
                {
                    Result = 1,
                    message = "Xóa thiết bị thất bại"
                });
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int id)
        {
            try
            {
                var systemUser = GetSesson();

                if (systemUser == null)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                    return RedirectToAction("Logout", "Security");
                }

                var device = _deviceRepository.GetById(id);

                _deviceRepository.Delete(device);

                var deleteStatus = _deviceRepository.SaveChanges();

                if (deleteStatus > 0)
                {

                    string StringDescription = new GetStringHistorySystem().Get(systemUser.UserName, Resources.Resource.Delete, device.DeviceName.ToString(), Resources.Resource.Device);

                    InsertHistorySystem((int)EACTION_TYPE.DELETE, (int)EnumControllerName.DEVICES, StringDescription, device, null);

                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Xóa thành công"));
                    return RedirectToAction("Index");
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));
                    return RedirectToAction("Delete", "Devices", new { id });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Devices/DeleteConfirmed: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));
                return RedirectToAction("Delete", "Devices", new { id });
            }
        }

        public async Task<ActionResult> Sync()
        {
            try
            {
                var systemUser = GetSesson();
                if (systemUser == null)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                    return RedirectToAction("Logout", "Security");
                }

                int SumInsert = 0, SumUpdate = 0;

                string baseURL = _configuration.GetSection("linkGetAllCameraMilestone:BaseUrl").Value + "/api/cameras";

                try
                {
                    string token = await GetToken();

                    //var handler = new HttpClientHandler thêm dòng này để tắt SSL
                    //{ thêm dòng này để tắt SSL
                    //    ClientCertificateOptions = ClientCertificateOption.Automatic, thêm dòng này để tắt SSL
                    //    ServerCertificateCustomValidationCallback = (httpRequestMessage, cert, cetChain, policyErrors) => thêm dòng này để tắt SSL
                    //    { thêm dòng này để tắt SSL
                    //        return true; thêm dòng này để tắt SSL
                    //    } thêm dòng này để tắt SSL
                    //}; thêm dòng này để tắt SSL

                    using (HttpClient client = new HttpClient())
                    {
                        client.DefaultRequestHeaders.Authorization
                        = new AuthenticationHeaderValue("Bearer", token);

                        //using (HttpResponseMessage res = await client.GetAsync("https://localhost:44376/home/<USER>")) test
                        using (HttpResponseMessage res = await client.GetAsync(baseURL))
                        {
                            using (HttpContent content = res.Content)
                            {
                                string data = await content.ReadAsStringAsync();
                                var dataObj = JObject.Parse(data);

                                if (data != null && data != string.Empty && JObject.Parse(data)["result"].ToString() != "2")
                                {
                                    var ListDevices = _deviceRepository.GetAll().ToList();

                                    foreach (var item in dataObj["message"])
                                    {
                                        var milestoneGuid = Guid.Parse(item["guid"].ToString());
                                        var deviceName = item["name"].ToString() == "" ? null : item["name"].ToString();
                                        var driverName = item["driverName"].ToString() == "" ? null : item["driverName"].ToString();
                                        var macAddress = item["macAddress"].ToString() == "" ? null : item["macAddress"].ToString();
                                        var ip = item["ip"].ToString() == "" ? null : item["ip"].ToString();
                                        var description = item["description"].ToString() == "" ? null : item["description"].ToString();

                                        Device check = null;

                                        if (milestoneGuid != null)
                                        {
                                            check = ListDevices.FirstOrDefault(p => p.MilestoneId == milestoneGuid);

                                            if (check == null)
                                            {
                                                Location location = new Location
                                                {
                                                    LocationName = deviceName,
                                                    CreatedBy = systemUser.Id,
                                                    CreatedDate = DateTime.Now,
                                                    UpdatedBy = systemUser.Id,
                                                    Longitude = 0,
                                                    Latitude = 0,
                                                    Actived = true
                                                };

                                                _locationRepository.Insert(location);
                                                var statusLocation = _locationRepository.SaveChanges();

                                                if (statusLocation > 0)
                                                {
                                                    Device device = new Device
                                                    {
                                                        DeviceName = deviceName,
                                                        SerialNumber = deviceName,
                                                        TypeOfDeviceId = int.Parse(_configuration.GetSection("IDTypeOfDevice").Value),
                                                        TypeOfSignal = (int)PSafe.Common.EventEnums.ETYPE_OF_SIGNAL.SecurityAlarm,
                                                        CreatedBy = systemUser.Id,
                                                        CreatedDate = DateTime.Now,
                                                        UpdatedBy = systemUser.Id,
                                                        UpdatedDate = DateTime.Now,
                                                        Expday = DateTime.Now.AddYears(1),
                                                        Renewday = DateTime.Now,
                                                        Ip = ip,
                                                        Actived = true,
                                                        MilestoneId = milestoneGuid,
                                                        ConnectStatus = 1,
                                                        DriverName = driverName,
                                                        MacAddress = macAddress,
                                                        Description = description,
                                                        LocationId = location.Id,
                                                        LocationNameNonUnicode = location.LocationName.NonUnicode(),
                                                        FromSync = true
                                                    };

                                                    _logger.LogInformation("Devices/GetAsync: Sync New Device: " + milestoneGuid + "; Ip: " + item["ip"].ToString());

                                                    _deviceRepository.Insert(device);
                                                    var status = _deviceRepository.SaveChanges();

                                                    if (status > 0)
                                                    {
                                                        SumInsert += status;
                                                    }
                                                }
                                            }
                                            else
                                            {
                                                bool checkUpdate = false;

                                                if (deviceName != null)
                                                {
                                                    if (check.DeviceName == null)
                                                    {
                                                        check.DeviceName = deviceName;
                                                        check.SerialNumber = deviceName;
                                                        checkUpdate = true;
                                                    }
                                                    else if (String.Compare(check.DeviceName.Trim(), deviceName.Trim(), false) != 0)
                                                    {
                                                        check.DeviceName = deviceName;
                                                        check.SerialNumber = deviceName;
                                                        checkUpdate = true;
                                                    }

                                                    if (check.LocationId != null && check.LocationId > 0)
                                                    {
                                                        var loc = _locationRepository.GetById(check.LocationId);
                                                        if (loc != null)
                                                        {
                                                            if (String.Compare(loc.LocationName.Trim(), deviceName.Trim(), false) != 0)
                                                            {
                                                                loc.LocationName = deviceName;
                                                                _locationRepository.Update(loc);
                                                                _locationRepository.SaveChanges();
                                                            }
                                                        }
                                                    }
                                                    else
                                                    {
                                                        Location location = new Location
                                                        {
                                                            LocationName = deviceName,
                                                            CreatedBy = systemUser.Id,
                                                            CreatedDate = DateTime.Now,
                                                            UpdatedBy = systemUser.Id,
                                                            Longitude = 0,
                                                            Latitude = 0,
                                                            Actived = true
                                                        };

                                                        _locationRepository.Insert(location);
                                                        var statusLocation = _locationRepository.SaveChanges();

                                                        if (statusLocation > 0)
                                                        {
                                                            check.LocationId = location.Id;
                                                            checkUpdate = true;
                                                        }
                                                    }
                                                }

                                                if (driverName != null)
                                                {
                                                    if (check.DriverName == null)
                                                    {
                                                        check.DriverName = driverName;
                                                        checkUpdate = true;
                                                    }
                                                    else if (String.Compare(check.DriverName.Trim(), driverName, false) != 0)
                                                    {
                                                        check.DriverName = driverName;
                                                        checkUpdate = true;
                                                    }
                                                }

                                                if (macAddress != null)
                                                {
                                                    if (check.MacAddress == null)
                                                    {
                                                        check.MacAddress = macAddress;
                                                        checkUpdate = true;
                                                    }
                                                    else if (String.Compare(check.MacAddress.Trim(), macAddress, false) != 0)
                                                    {
                                                        check.MacAddress = macAddress;
                                                        checkUpdate = true;
                                                    }
                                                }

                                                if (ip != null)
                                                {
                                                    if (check.Ip == null)
                                                    {
                                                        check.Ip = ip;
                                                        checkUpdate = true;
                                                    }
                                                    else if (String.Compare(check.Ip.Trim(), ip, false) != 0)
                                                    {
                                                        check.Ip = ip;
                                                        checkUpdate = true;
                                                    }

                                                }

                                                if (description != null)
                                                {
                                                    if (check.Description == null)
                                                    {
                                                        check.Description = description;
                                                        checkUpdate = true;
                                                    }
                                                    else if (String.Compare(check.Description.Trim(), description, false) != 0)
                                                    {
                                                        check.Description = description;
                                                        checkUpdate = true;
                                                    }
                                                }

                                                if (check.FromSync != true)
                                                {
                                                    check.FromSync = true;
                                                    checkUpdate = true;
                                                }

                                                if (checkUpdate)
                                                {
                                                    check.UpdatedBy = systemUser.Id;
                                                    check.UpdatedDate = DateTime.Now;

                                                    _logger.LogInformation("Devices/GetAsync: Sync update Device: Guid: " + milestoneGuid + "; Ip: " + item["ip"].ToString());

                                                    _deviceRepository.Update(check);
                                                    var status = _deviceRepository.SaveChanges();

                                                    if (status > 0)
                                                    {
                                                        SumUpdate += status;
                                                    }
                                                    else
                                                    {
                                                        _logger.LogError("Sync error Device Guid: " + milestoneGuid);
                                                    }
                                                }
                                            }
                                        }
                                    }

                                    if (SumInsert == 0 && SumUpdate == 0)
                                    {
                                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Không có cập nhật nào được thay đổi"));
                                        return RedirectToAction("Index", "Devices");
                                    }
                                    else
                                    {
                                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Đã thêm: " + SumInsert + " và cập nhật: " + SumUpdate + " thiết bị"));
                                        return RedirectToAction("Index", "Devices");
                                    }
                                }
                                else
                                {
                                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("warning", "", "Không tìm thấy thiết bị"));
                                    return RedirectToAction("Index", "Devices");
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("Devices/Sync: " + ex.Message);

                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", ex.Message));
                    return RedirectToAction("Index", "Devices");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Devices/Sync: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", ex.Message));
                return RedirectToAction("Index", "Devices");
            }
        }

        public async Task<string> GetToken()
        {
            var handler = new HttpClientHandler
            {
                //ClientCertificateOptions = ClientCertificateOption.Automatic,  thêm dòng này để tắt SSL
                //ServerCertificateCustomValidationCallback = (httpRequestMessage, cert, cetChain, policyErrors) =>  thêm dòng này để tắt SSL
                //{  thêm dòng này để tắt SSL
                //    return true;  thêm dòng này để tắt SSL
                //},  thêm dòng này để tắt SSL
                UseProxy = false
            };

            string data = string.Empty;

            try
            {
                string baseGetTokenURL = _configuration.GetSection("linkGetAllCameraMilestone:BaseUrl").Value + "/auth/getToken";

                try
                {
                    using (HttpClient client = new HttpClient(handler))
                    {
                        var UsernameEncode = Utils.DecodePassword(_configuration.GetSection("linkGetAllCameraMilestone:Username").Value, Utils.EncodeType.SHA_256);
                        var PasswordEncode = Utils.DecodePassword(_configuration.GetSection("linkGetAllCameraMilestone:Password").Value, Utils.EncodeType.SHA_256);

                        var requestData = new Dictionary<string, string>();
                        requestData["Username"] = UsernameEncode;
                        requestData["Password"] = PasswordEncode;

                        using (HttpRequestMessage res = new HttpRequestMessage(HttpMethod.Post, baseGetTokenURL))
                        {
                            var json = JsonConvert.SerializeObject(requestData);
                            using (var stringContent = new StringContent(json, Encoding.UTF8, "application/json"))
                            {
                                res.Content = stringContent;

                                using (var response = await client.SendAsync(res, CancellationToken.None))
                                {
                                    response.EnsureSuccessStatusCode();
                                    var responseBody = await response.Content.ReadAsStringAsync();

                                    var dataObj = JObject.Parse(responseBody);

                                    data = dataObj["message"].ToString();

                                    _logger.LogInformation("Devices/GetToken: ok");

                                    client.Dispose();
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("Devices/GetToken: " + ex.Message);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Devices/GetToken: " + ex.Message);
            }

            return data;
        }

        [NonAction]
        private SelectList ToSelectList(List<DropDownList> DropDownList)
        {
            List<SelectListItem> list = new List<SelectListItem>();

            foreach (var item in DropDownList)
            {
                list.Add(new SelectListItem()
                {
                    Text = item.Name,
                    Value = item.Id.ToString()
                });
            }

            return new SelectList(list, "Value", "Text");
        }

        public DeviceModel GetListDropdown(DeviceModel deviceModel)
        {
            List<DropDownList> listDropDownListTypeOfDevice = new List<DropDownList>();
            List<DropDownList> listDropDownListArea = new List<DropDownList>();
            List<DropDownList> listDropDownListSupplier = new List<DropDownList>();
            List<DropDownList> listDropDownListLocation = new List<DropDownList>();
            List<DropDownList> listDropDownListTyoeOfSingnal = null;
            List<DropDownList> listDropDownListCameraMonitorId = new List<DropDownList>();

            DropDownList Default = new DropDownList
            {
                Id = -1,
                Name = "Chọn"
            };

            listDropDownListTypeOfDevice.Add(Default);
            listDropDownListArea.Add(Default);
            listDropDownListSupplier.Add(Default);
            listDropDownListLocation.Add(Default);
            listDropDownListCameraMonitorId.Add(Default);
            listDropDownListTyoeOfSingnal = new ListTypeOfSingnal().List;

            var typeOfDevices = _typeOfDeviceRepository.GetAll();
            var areas = _areaRepository.GetAll();
            var supplier = _supplierRepository.GetAll();

            var builder = new ConfigurationBuilder()
                                     .SetBasePath(Directory.GetCurrentDirectory())
                                     .AddJsonFile("appsettings.json");
            var configuration = builder.Build();

            List<Device> ListCameraMonitorId = _deviceRepository.GetAll().Where(p => p.TypeOfDeviceId == int.Parse(configuration["IDTypeOfDevice"]) && p.Id != deviceModel.DEVICEID).ToList(); ;

            foreach (var item in ListCameraMonitorId)
            {
                DropDownList drop = new DropDownList
                {
                    Id = item.Id,
                    Name = item.DeviceName
                };
                listDropDownListCameraMonitorId.Add(drop);
            }

            if (deviceModel.areas != -1)
            {
                var location = _locationRepository.GetAll().Where(p => p.AreaId == deviceModel.areas);

                foreach (var item in location)
                {
                    DropDownList drop = new DropDownList
                    {
                        Id = item.Id,
                        Name = item.LocationName
                    };
                    listDropDownListLocation.Add(drop);
                }
            }

            foreach (var item in typeOfDevices)
            {
                DropDownList drop = new DropDownList
                {
                    Id = item.Id,
                    Name = item.TypeName
                };
                listDropDownListTypeOfDevice.Add(drop);
            }

            foreach (var item in areas)
            {
                DropDownList drop = new DropDownList
                {
                    Id = item.Id,
                    Name = item.AreaName
                };
                listDropDownListArea.Add(drop);
            }

            foreach (var item in supplier)
            {
                DropDownList drop = new DropDownList
                {
                    Id = item.Id,
                    Name = item.Name
                };
                listDropDownListSupplier.Add(drop);
            }

            deviceModel.ListTypeOfDevices = ToSelectList(listDropDownListTypeOfDevice);
            deviceModel.ListSuppliers = ToSelectList(listDropDownListSupplier);
            deviceModel.ListAreas = ToSelectList(listDropDownListArea);
            deviceModel.ListLocations = ToSelectList(listDropDownListLocation);
            deviceModel.ListTYPEOFSINGNAL = ToSelectList(listDropDownListTyoeOfSingnal);
            deviceModel.ListCameraMonitorId = ToSelectList(listDropDownListCameraMonitorId.OrderBy(p => p.Name).ToList());

            var provinceList = _provinceRepository.GetAll().ToList();
            deviceModel.ProvinceSelectableList = ToSelectList(provinceList.Select(i => new DropDownList() { Id = i.Id, Name = i.Name }).ToList());
            deviceModel.WardSelectableList = new SelectList(new List<DropDownList>());
            if (deviceModel.ProvinceId != 0)
            {
                deviceModel.WardSelectableList = ToSelectList(_wardRepository.Get(p => p.ProvinceId == deviceModel.ProvinceId).Select(i => new DropDownList() { Id = i.Id, Name = i.Name }).ToList());
            }
            return deviceModel;
        }

        public async Task<DeviceModel> GetListDropdownAsync(DeviceModel deviceModel)
        {
            List<DropDownList> listDropDownListTypeOfDevice = new List<DropDownList>();
            List<DropDownList> listDropDownListArea = new List<DropDownList>();
            List<DropDownList> listDropDownListSupplier = new List<DropDownList>();
            List<DropDownList> listDropDownListLocation = new List<DropDownList>();
            List<DropDownList> listDropDownListTyoeOfSingnal = null;
            List<DropDownList> listDropDownListCameraMonitorId = new List<DropDownList>();

            DropDownList Default = new DropDownList
            {
                Id = -1,
                Name = "Chọn"
            };

            listDropDownListTypeOfDevice.Add(Default);
            listDropDownListArea.Add(Default);
            listDropDownListSupplier.Add(Default);
            listDropDownListLocation.Add(Default);
            listDropDownListCameraMonitorId.Add(Default);
            listDropDownListTyoeOfSingnal = new ListTypeOfSingnal().List;

            var typeOfDevices = await _typeOfDeviceRepository.GetAllAsync();
            var areas = await _areaRepository.GetAllAsync();
            var supplier = await _supplierRepository.GetAllAsync();

            var builder = new ConfigurationBuilder()
                                     .SetBasePath(Directory.GetCurrentDirectory())
                                     .AddJsonFile("appsettings.json");
            var configuration = builder.Build();

            var listCameraMonitor = await _deviceRepository.GetByAsync(p => p.TypeOfDeviceId == int.Parse(configuration["IDTypeOfDevice"]) && p.Id != deviceModel.DEVICEID);
            List<Device> ListCameraMonitorId = listCameraMonitor.ToList();

            foreach (var item in ListCameraMonitorId)
            {
                DropDownList drop = new DropDownList
                {
                    Id = item.Id,
                    Name = item.DeviceName
                };
                listDropDownListCameraMonitorId.Add(drop);
            }

            if (deviceModel.areas != -1)
            {
                var location = await _locationRepository.GetByAsync(p => p.AreaId == deviceModel.areas);

                foreach (var item in location)
                {
                    DropDownList drop = new DropDownList
                    {
                        Id = item.Id,
                        Name = item.LocationName
                    };
                    listDropDownListLocation.Add(drop);
                }
            }

            foreach (var item in typeOfDevices)
            {
                DropDownList drop = new DropDownList
                {
                    Id = item.Id,
                    Name = item.TypeName
                };
                listDropDownListTypeOfDevice.Add(drop);
            }

            foreach (var item in areas)
            {
                DropDownList drop = new DropDownList
                {
                    Id = item.Id,
                    Name = item.AreaName
                };
                listDropDownListArea.Add(drop);
            }

            foreach (var item in supplier)
            {
                DropDownList drop = new DropDownList
                {
                    Id = item.Id,
                    Name = item.Name
                };
                listDropDownListSupplier.Add(drop);
            }

            deviceModel.ListTypeOfDevices = ToSelectList(listDropDownListTypeOfDevice);
            deviceModel.ListSuppliers = ToSelectList(listDropDownListSupplier);
            deviceModel.ListAreas = ToSelectList(listDropDownListArea);
            deviceModel.ListLocations = ToSelectList(listDropDownListLocation);
            deviceModel.ListTYPEOFSINGNAL = ToSelectList(listDropDownListTyoeOfSingnal);
            deviceModel.ListCameraMonitorId = ToSelectList(listDropDownListCameraMonitorId.OrderBy(p => p.Name).ToList());

            var provinceList = await _provinceRepository.GetAllAsync();
            deviceModel.ProvinceSelectableList = ToSelectList(provinceList.Select(i => new DropDownList() { Id = i.Id, Name = i.Name }).ToList());
            deviceModel.WardSelectableList = new SelectList(new List<DropDownList>());
            if (deviceModel.ProvinceId != 0)
            {
                var wards = await _wardRepository.GetByAsync(p => p.ProvinceId == deviceModel.ProvinceId);
                if(wards != null)
                    deviceModel.WardSelectableList = ToSelectList(wards.Select(i => new DropDownList() { Id = i.Id, Name = i.Name }).ToList());
            }
            return deviceModel;
        }

        [HttpGet]
        public JsonResult CountDeviceOfLocation(int id)
        {
            try
            {
                var count = _deviceRepository.GetAll().Count(p => p.LocationId == id);

                return Json(count);
            }
            catch (Exception ex)
            {
                _logger.LogError("Devices/CountDeviceOfLocation: " + ex.Message);

                return Json(0);
            }
        }

        [HttpGet]
        public JsonResult GetListDropdownWards(int provinceId)
        {
            try
            {
                var districtList = _wardRepository.Get(p => p.ProvinceId == provinceId).Select(i => new DropDownList() { Id = i.Id, Name = i.Name }).ToList();

                return Json(districtList);
            }
            catch (Exception ex)
            {
                _logger.LogError("Devices/GetListDropdownWards: " + ex.Message);

                return Json(0);
            }
        }
        private async Task<List<PatrolCameraPresetModel>> GetListPreset(List<Device> listDevice)
        {
            List<PatrolCameraPresetModel> patrolCameraPreset = new List<PatrolCameraPresetModel>();
            try
            {
                string baseURL = _configuration.GetSection("linkGetAllCameraMilestone:BaseUrl").Value + "/api/cameras";

                string token = await GetToken();

                //var handler = new HttpClientHandler thêm dòng này để tắt SSL
                //{ thêm dòng này để tắt SSL
                //    ClientCertificateOptions = ClientCertificateOption.Automatic, thêm dòng này để tắt SSL
                //    ServerCertificateCustomValidationCallback = (httpRequestMessage, cert, cetChain, policyErrors) => thêm dòng này để tắt SSL
                //    { thêm dòng này để tắt SSL
                //        return true; thêm dòng này để tắt SSL
                //    } thêm dòng này để tắt SSL
                //}; thêm dòng này để tắt SSL

                using (HttpClient client = new HttpClient())
                {
                    client.Timeout = new TimeSpan(0, 0, 50);
                    client.DefaultRequestHeaders.Authorization
                    = new AuthenticationHeaderValue("Bearer", token);

                    using (HttpResponseMessage res = await client.GetAsync(baseURL))
                    {
                        using (HttpContent content = res.Content)
                        {
                            string data = await content.ReadAsStringAsync();
                            var dataObj = JObject.Parse(data);

                            if (data != null && data != string.Empty && JObject.Parse(data)["result"].ToString() != "2")
                            {
                                foreach (var item in listDevice)
                                {
                                    var listPreset = new List<string>();
                                    var camera = dataObj["message"].Where(p => p["guid"].ToString().Trim() == item.MilestoneId.ToString()).FirstOrDefault();

                                    if (camera != null && camera["presetList"].ToList().Count > 0)
                                    {
                                        foreach (var preset in camera["presetList"].ToList())
                                        {
                                            listPreset.Add(preset.ToString());
                                        }
                                    }
                                    PatrolCameraPresetModel patrolCamera = new PatrolCameraPresetModel
                                    {
                                        Id = item.Id,
                                        CameraName = item.DeviceName,
                                        MilestoneId = item.MilestoneId.Value == null ? new Guid() : item.MilestoneId.Value,
                                        ListPreset = listPreset
                                    };
                                    patrolCameraPreset.Add(patrolCamera);
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Devices/Sync: " + ex.Message);
            }

            return patrolCameraPreset;
        }

        public ActionResult RecycleBin()
        {
            StatusQuery Notification;
            try
            {
                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Devices/Index: " + ex.Message);

                Notification = new StatusQuery("error", "", "Xem danh sách thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;
            }

            return View();
        }

        public JsonResult GetRecycleBin()
        {
            try
            {
                var listTypeOfSingnal = new ListTypeOfSingnal().List;

                var query = from d in _deviceRepository.GetBy(p => p.FromSync == false)
                            let lo = (from l in _locationRepository.GetAll()
                                      where d.LocationId == l.Id
                                      select new { l.LocationName, l.AreaId }).DefaultIfEmpty()
                            from location in lo.DefaultIfEmpty()
                            let ty = (from t in _typeOfDevice.GetAll()
                                      where d.TypeOfDeviceId == t.Id
                                      select new { t.TypeName, t.Id }).DefaultIfEmpty()
                            from typeofdevice in ty.DefaultIfEmpty()
                            let ar = (from a in _areaRepository.GetAll()
                                      where (location == null ? -2 : location.AreaId) == a.Id
                                      select new { a.AreaName }).DefaultIfEmpty()
                            from area in ar.DefaultIfEmpty()
                            select new
                            {
                                Description = d.Description ?? string.Empty,
                                d.Id,
                                DeviceName = d.DeviceName ?? string.Empty,
                                SerialNumber = d.SerialNumber ?? string.Empty,
                                TypeOfSingnal = listTypeOfSingnal.Where(p => p.Id == d.TypeOfSignal).Select(p => p.Name),
                                LocationName = location.LocationName ?? string.Empty,
                                AreaName = area.AreaName ?? string.Empty,
                                Ip = d.Ip ?? string.Empty,
                                DriverName = d.DriverName ?? string.Empty,
                                MacAddress = d.MacAddress ?? string.Empty,
                                TypeOfDevice = typeofdevice.TypeName ?? string.Empty,
                                FromSync = typeofdevice.Id == 6 && d.FromSync == false
                            };

                return Json(query);
            }
            catch (Exception ex)
            {
                _logger.LogError("Devices/GetIndex: " + ex.Message);

                return Json(null);
            }
        }

        public async Task<JsonResult> GetCameras()

        {

            List<PatrolCameraPresetModel> retVal = new List<PatrolCameraPresetModel>();

            var typeOfDeviceCamera = _typeOfDeviceRepository.GetBy(x => x.TypeName.ToLower().Equals("camera")).FirstOrDefault();

            if (typeOfDeviceCamera != null)

            {

                var deviceCameraList = _deviceRepository.GetBy(x => x.TypeOfDeviceId == typeOfDeviceCamera.Id).OrderBy(x => x.DeviceName).ToList();

                foreach (var camera in deviceCameraList)

                {

                    retVal.Add(new PatrolCameraPresetModel()

                    {

                        CameraName = camera.DeviceName,

                        MilestoneId = camera.MilestoneId ?? Guid.Empty,

                        ListPreset = new List<string>()

                    });

                }

            }


            var cameraPatrolByLocation = await GetListPreset(retVal);


            return Json(cameraPatrolByLocation);

        }



        private async Task<List<PatrolCameraPresetModel>> GetListPreset(List<PatrolCameraPresetModel> patrolCameraPresetModel)

        {

            List<PatrolCameraPresetModel> patrolCameraPreset = new List<PatrolCameraPresetModel>();

            try

            {

                string baseURL = _configuration.GetSection("linkGetAllCameraMilestone:BaseUrl").Value + "/api/cameras";


                string token = await GetToken();


                //var handler = new HttpClientHandler thêm dòng này để tắt SSL

                //{ thêm dòng này để tắt SSL

                //    ClientCertificateOptions = ClientCertificateOption.Automatic, thêm dòng này để tắt SSL

                //    ServerCertificateCustomValidationCallback = (httpRequestMessage, cert, cetChain, policyErrors) => thêm dòng này để tắt SSL

                //    { thêm dòng này để tắt SSL

                //        return true; thêm dòng này để tắt SSL

                //    } thêm dòng này để tắt SSL

                //}; thêm dòng này để tắt SSL


                using (HttpClient client = new HttpClient())

                {

                    client.DefaultRequestHeaders.Authorization

                    = new AuthenticationHeaderValue("Bearer", token);


                    using (HttpResponseMessage res = await client.GetAsync(baseURL))

                    {

                        using (HttpContent content = res.Content)

                        {

                            string data = await content.ReadAsStringAsync();

                            var dataObj = JObject.Parse(data);


                            if (data != null && data != string.Empty && JObject.Parse(data)["result"].ToString() != "2")

                            {

                                foreach (var item in patrolCameraPresetModel)

                                {

                                    var listPreset = new List<string>();

                                    var camera = dataObj["message"].Where(p => p["guid"].ToString().Trim() == item.MilestoneId.ToString()).FirstOrDefault();


                                    if (camera != null && camera["presetList"].ToList().Count > 0)

                                    {

                                        foreach (var preset in camera["presetList"].ToList())

                                        {

                                            listPreset.Add(preset.ToString());

                                        }

                                    }

                                    PatrolCameraPresetModel patrolCamera = new PatrolCameraPresetModel

                                    {

                                        CameraName = item.CameraName,

                                        MilestoneId = item.MilestoneId,

                                        ListPreset = listPreset

                                    };

                                    patrolCameraPreset.Add(patrolCamera);

                                }

                            }

                        }

                    }

                }

            }

            catch (Exception ex)

            {

                _logger.LogError("Devices/Sync: " + ex.Message);

            }


            return patrolCameraPreset;

        }

        public User GetSesson()
        {
            var SessionUserSystem = HttpContext.Session.GetComplexData<User>("SessionUserSystemId_" + HttpContext.Session.GetString("SessionUserSystemId"));
            if (SessionUserSystem != null)
            {
                var _user = _userRepository.GetById(SessionUserSystem.Id);

                if (_user != null)
                {
                    if (!_user.Actived)
                    {
                        HttpContext.Session.Clear();
                        return null;
                    }
                    return _user;
                }
            }
            return null;
        }

        private void InsertHistorySystem(int action_type, int controllerName, string description, object OldModel, object NewModel)
        {
            try
            {
                var ipAddress = _accessor.HttpContext.Connection.RemoteIpAddress.ToString();

                string jsonOldObject = Newtonsoft.Json.JsonConvert.SerializeObject(OldModel);
                string jsonNewObject = Newtonsoft.Json.JsonConvert.SerializeObject(NewModel);

                HistorySystem history = new HistorySystem
                {
                    ActionType = action_type,
                    ActionTime = DateTime.Now,
                    Description = description,
                    OldObject = jsonOldObject,
                    NewObject = jsonNewObject,
                    UserId = int.Parse(HttpContext.Session.GetString("SessionUserSystemId")),
                    IpAddress = ipAddress,
                    ControllerName = controllerName
                };

                _historySystemRepository.Insert(history);

                _historySystemRepository.SaveChanges();
            }
            catch (Exception ex)
            {
                _logger.LogError("Device/InsertHistorySystem: " + ex.Message);
            }
        }
    }
}