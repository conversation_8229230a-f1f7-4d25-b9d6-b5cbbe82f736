﻿@model PSafe.AM.Models.BlackVehicleListModel

@{
    ViewBag.Title = "Xóa";
}
<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-lg-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>@PSafe.AM.Resources.Resource.Delete @PSafe.AM.Resources.Resource.BlackList.ToLower()</h5>
                    <div class="ibox-tools">
                        @Html.ActionLink(PSafe.AM.Resources.Resource.BackToList, "Index", "BlackVehicleLists", null, new { @class = "btn btn-primary btn-xs" })
                    </div>
                </div>
                <div class="ibox-content">
                    @using (Html.BeginForm("DeleteConfirmed", "BlackVehicleLists", new { id = Model.Id }, FormMethod.Post))
                    {

                        @Html.AntiForgeryToken()
                        <div class="row">
                            <div class="col-lg-6">
                                <div class="form-group row">
                                    <div class="col-md-4">
                                        <strong>
                                            @Html.DisplayNameFor(model => model.VehicleNumber)
                                        </strong>
                                    </div>
                                    <div class="col-md-8">
                                        @Html.DisplayFor(model => model.VehicleNumber)
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group row">
                                    <div class="col-md-4">
                                        <strong>
                                            @Html.DisplayNameFor(model => model.FullName)
                                        </strong>
                                    </div>
                                    <div class="col-md-8">
                                        @Html.DisplayFor(model => model.FullName)
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group row">
                                    <div class="col-md-4">
                                        <strong>
                                            @Html.DisplayNameFor(model => model.Description)
                                        </strong>
                                    </div>
                                    <div class="col-md-8">
                                        @Html.DisplayFor(model => model.Description)
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group row">
                                    <div class="col-md-4">
                                        <strong>
                                            @Html.DisplayNameFor(model => model.Active)
                                        </strong>
                                    </div>
                                    <div class="col-md-8">
                                        @Html.DisplayFor(model => model.Active)
                                    </div>
                                </div>
                            </div>
                        </div>
                        <input type="submit" value="@PSafe.AM.Resources.Resource.Delete" class="btn btn-danger" />
                        @Html.ActionLink(@PSafe.AM.Resources.Resource.Cancel, "Index", "BlackVehicleLists", null, new { @class = "btn btn-white" })
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <environment names="Development,Staging,Production">
        <link rel="stylesheet" href="~/lib/toastr/toastr.min.css" />
    </environment>
}

@section Scripts {
    <environment names="Development,Staging,Production">
        <script src="~/lib/validate/jquery.validate.min.js"></script>
        <script src="~/lib/toastr/toastr.js"></script>
        <script src="~/lib/toastr/toastr.min.js"></script>
    </environment>

    @if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "warning")
    {
        <script type="text/javascript">
            toastr.warning('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "error")
    {
        <script type="text/javascript">
            toastr.error('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "success")
    {
        <script type="text/javascript">
            toastr.success('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
}