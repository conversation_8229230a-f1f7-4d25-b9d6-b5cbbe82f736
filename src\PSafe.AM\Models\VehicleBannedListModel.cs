﻿using PSafe.Core.Domains;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace PSafe.AM.Models
{
    public class VehicleBannedListModel : VehicleBannedList
    {
        public new string ActiveTime { get; set; }
        public new string ExpiryTime { get; set; }
        public new string CreatedDate { get; set; }
        public new string UpdatedDate { get; set; }
        public new string ClearFlag { get; set; }
        public new string ClearBy { get; set; }
        public new string CreatedBy { get; set; }
        public new string ClearDate { get; set; }
        public DateTime? ClearDateVal { get; set; }
        public new string UserTopoId { get; set; }
        public int ClearByVal { get; set; }
        public bool IsValidBanned { get; set; }
        public string Department { get; set; }
        public int CreatedByVal { get; set; }
        public bool IsHasClearRole { get; set; }
        public bool IsHasForbidRole { get; set; }
        public string BannedBy { get; set; }
        public string BannedTime { get; set; }

        public int NumOfBannedTimes { get; set; }

        public string SiteId { get; set; }
        public string SiteName { get; set; }


        public new List<VehicleBannedListHistory> VehicleBannedListHistorys { get; set; }

        public static VehicleBannedListModel Parse(VehicleBannedList vehicleBannedList)
        {
            var vehicleBannedListModel = new VehicleBannedListModel();
            vehicleBannedListModel.Id = vehicleBannedList.Id;
            vehicleBannedListModel.TruckID = vehicleBannedList.TruckID;
            vehicleBannedListModel.CreatedDate = vehicleBannedList.CreatedDate.ToString("dd/MM/yyyy HH:mm");
            vehicleBannedListModel.TruckKey = vehicleBannedList.TruckKey == null ? string.Empty : vehicleBannedList.TruckKey;
            vehicleBannedListModel.UpdatedDate = vehicleBannedList.UpdatedDate.ToString("dd/MM/yyyy HH:mm");
            vehicleBannedListModel.ClearByVal = vehicleBannedList.ClearBy;
            vehicleBannedListModel.Department = string.Empty;
            vehicleBannedListModel.UserTopoId = string.Empty;
            vehicleBannedListModel.CreatedByVal = vehicleBannedList.CreatedBy;
            vehicleBannedListModel.ClearDateVal = vehicleBannedList.ClearDate;
            //vehicleBannedListModel.CreatedBy = vehicleBannedList.CreatedBy > 0 ? GetUserNameById(x.CreatedBy) : string.Empty,
            vehicleBannedListModel.Note = vehicleBannedList.Note;
            vehicleBannedListModel.OwnerName = vehicleBannedList.OwnerName == null ? string.Empty : vehicleBannedList.OwnerName;
            //IsHasClearRole = IsHasUserRole(user, x.BannedType + 1),
            //IsHasForbidRole = IsHasUserRole(user, x.BannedType),

            vehicleBannedListModel.ActiveTime = vehicleBannedList.ActiveTime.ToString("dd/MM/yyyy HH:mm");
            vehicleBannedListModel.ExpiryTime = vehicleBannedList.ExpiryTime.HasValue ? vehicleBannedList.ExpiryTime.Value.ToString("dd/MM/yyyy HH:mm") : string.Empty;
            vehicleBannedListModel.ReasonViolation = vehicleBannedList.ReasonViolation == null ? string.Empty : vehicleBannedList.ReasonViolation;
            vehicleBannedListModel.FoundBy = vehicleBannedList.FoundBy == null ? string.Empty : vehicleBannedList.FoundBy;
            //vehicleBannedListModel.ClearFlag = string.Empty;
            //vehicleBannedListModel.ClearBy = (x.ClearFlag && x.ClearBy > 0) ? GetUserNameById(x.ClearBy) : string.Empty,
            //vehicleBannedListModel.ClearDate = x.ClearFlag ? (x.ClearDate.HasValue ? x.ClearDate.Value.ToString("dd/MM/yyyy HH:mm") : string.Empty) : string.Empty,
            //vehicleBannedListModel.ReasonClear = x.ClearFlag ? x.ReasonClear : string.Empty,
            vehicleBannedListModel.ViolationTypeId = vehicleBannedList.ViolationTypeId;
            vehicleBannedListModel.ViolationType = vehicleBannedList.ViolationType == null ? string.Empty : vehicleBannedList.ViolationType;

            vehicleBannedListModel.VehicleBannedListHistorys = null;
            return vehicleBannedListModel;
        }
    }
}
