﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;

namespace PSafe.AM.Controllers
{
    public class ViewLogController : Controller
    {
        public IActionResult Index()
        {
            string logPath = System.IO.Path.Combine(System.IO.Path.GetDirectoryName(System.Reflection.Assembly.GetEntryAssembly().Location), "logs", "test." + System.DateTime.Now.ToString("dd-MM-yyyy") + ".log.txt");

            var logs = new Dictionary<string, List<Infrastructure.Services.EPort.TestLog>>();
            if (System.IO.File.Exists(logPath))
            {
                string[] contents = System.IO.File.ReadAllLines(logPath);

                foreach (var content in contents)
                {
                    if (string.IsNullOrWhiteSpace(content)) continue;
                    try
                    {
                        var obj = Newtonsoft.Json.JsonConvert.DeserializeObject<Infrastructure.Services.EPort.TestLog>(content);
                        if (obj == null) continue;

                        int startIndex = obj.Message.IndexOf(":");
                        if (startIndex != -1)
                        {
                            startIndex = obj.Message.IndexOf(":", startIndex + 2);
                            if (startIndex != -1)
                            {
                                string k = obj.Message.Substring(0, startIndex);
                                obj.Message = obj.Message.Substring(startIndex + 1);
                                if (!logs.ContainsKey(k))
                                    logs.Add(k, new List<Infrastructure.Services.EPort.TestLog>());

                                logs[k].Add(obj);
                            }
                        }
                    }
                    catch { }
                }
            }
            ViewBag.logs = logs;

            return View();
        }
    }
}