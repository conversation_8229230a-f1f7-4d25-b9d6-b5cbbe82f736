﻿using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace PSafe.AM.Models
{
    public class ScheduleTimeModel
    {
        [Key]
        [Display(Name = "Id", ResourceType = typeof(Resources.Resource_scheduleTime))]
        public int Id { get; set; }

        [Required(ErrorMessage = "Giá trị không được trống!")]
        [StringLength(50, MinimumLength = 1, ErrorMessage = "Độ dài tối đa 50 ký tự!")]
        [Display(Name = "Name", ResourceType = typeof(Resources.Resource_scheduleTime))]
        public string Name { get; set; } = string.Empty;

        [DataType(DataType.Date)]
        [Required(ErrorMessage = "Giá trị không được trống!")]
        [DisplayFormat(DataFormatString = "{0:HH:mm}", ApplyFormatInEditMode = true)]
        [Display(Name = "TimeBegin", ResourceType = typeof(Resources.Resource_scheduleTime))]
        public DateTime TimeBegin { get; set; }

        [DataType(DataType.Date)]
        [Required(ErrorMessage = "Giá trị không được trống!")]
        [DisplayFormat(DataFormatString = "{0:HH:mm}", ApplyFormatInEditMode = true)]
        [Display(Name = "TimeEnd", ResourceType = typeof(Resources.Resource_scheduleTime))]
        public DateTime TimeEnd { get; set; }

        [Display(Name = "Description", ResourceType = typeof(Resources.Resource_scheduleTime))]
        public string Description { get; set; } = string.Empty;
    }
}
