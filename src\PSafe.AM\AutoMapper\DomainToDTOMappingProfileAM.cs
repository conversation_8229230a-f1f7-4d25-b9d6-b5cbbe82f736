﻿using System;
using System.Linq;
using AutoMapper;
using PSafe.AM.Models;
using PSafe.Core.Domains;

namespace PSafe.AM.AutoMapper
{
    public class DomainToDtoMappingProfileAm : Profile
    {
        public DomainToDtoMappingProfileAm()
        {
            CreateMap<Device, DeviceModel>()
                .ForMember(dest => dest.DEVICEID, opts => opts.MapFrom(src => src.Id))
                .ForMember(dest => dest.TYPEOFDEVICEID, opts => opts.MapFrom(src => src.TypeOfDeviceId))
                .ForMember(dest => dest.USERID, opts => opts.MapFrom(src => src.UserId))
                .ForMember(dest => dest.SUPPLIERID, opts => opts.MapFrom(src => src.SupplierId))
                .ForMember(dest => dest.DEVICENAME, opts => opts.MapFrom(src => src.DeviceName))
                .ForMember(dest => dest.SERIALNUMBER, opts => opts.MapFrom(src => src.SerialNumber))
                .ForMember(dest => dest.ACTIVED, opts => opts.MapFrom(src => src.Actived))
                .ForMember(dest => dest.SERVER, opts => opts.MapFrom(src => src.Server))
                .ForMember(dest => dest.PORT, opts => opts.MapFrom(src => src.Port))
                .ForMember(dest => dest.URL, opts => opts.MapFrom(src => src.Url))
                .ForMember(dest => dest.USERNAME, opts => opts.MapFrom(src => src.Username))
                .ForMember(dest => dest.PASSWORD, opts => opts.MapFrom(src => src.Password))
                .ForMember(dest => dest.DESCRIPTION, opts => opts.MapFrom(src => src.Description))
                .ForMember(dest => dest.LASTCONNECTTIME, opts => opts.MapFrom(src => src.LastConnectTime))
                .ForMember(dest => dest.DEVICESTATUS, opts => opts.MapFrom(src => src.DeviceStatus))
                .ForMember(dest => dest.ALLOWUSERREMOTEACCESS, opts => opts.MapFrom(src => src.AllowUserRemoteAccess))
                .ForMember(dest => dest.CONNECTSTATUS, opts => opts.MapFrom(src => src.ConnectStatus))
                .ForMember(dest => dest.IPLOCAL, opts => opts.MapFrom(src => src.IpLocal))
                .ForMember(dest => dest.VIDEOLINK, opts => opts.MapFrom(src => src.VideoLink))
                .ForMember(dest => dest.QUANTITYCHANNEL, opts => opts.MapFrom(src => src.QuantityChannel))
                .ForMember(dest => dest.RENEWDAY, opts => opts.MapFrom(src => src.Renewday))
                .ForMember(dest => dest.EXPDAY, opts => opts.MapFrom(src => src.Expday))
                .ForMember(dest => dest.LOCATIONID, opts => opts.MapFrom(src => src.LocationId))
                .ForMember(dest => dest.UPDATEDUSER, opts => opts.MapFrom(src => src.UpdatedBy))
                .ForMember(dest => dest.UPDATEDDATE, opts => opts.MapFrom(src => src.UpdatedDate))
                .ForMember(dest => dest.CREATEDUSER, opts => opts.MapFrom(src => src.CreatedBy))
                .ForMember(dest => dest.CREATEDDATE, opts => opts.MapFrom(src => src.CreatedDate))
                .ForMember(dest => dest.TYPEOFSINGNAL, opts => opts.MapFrom(src => src.TypeOfSignal))
                .ForMember(dest => dest.Ip, opts => opts.MapFrom(src => src.Ip));

            CreateMap<DeviceModel, Device>()
            .ForMember(dest => dest.Id, opts => opts.MapFrom(src => src.DEVICEID))
            .ForMember(dest => dest.TypeOfDeviceId, opts => opts.MapFrom(src => src.TYPEOFDEVICEID))
            .ForMember(dest => dest.UserId, opts => opts.MapFrom(src => src.USERID))
            .ForMember(dest => dest.SupplierId, opts => opts.MapFrom(src => src.SUPPLIERID))
            .ForMember(dest => dest.DeviceName, opts => opts.MapFrom(src => src.DEVICENAME))
            .ForMember(dest => dest.SerialNumber, opts => opts.MapFrom(src => src.SERIALNUMBER))
            .ForMember(dest => dest.Actived, opts => opts.MapFrom(src => src.ACTIVED))
            .ForMember(dest => dest.Server, opts => opts.MapFrom(src => src.SERVER))
            .ForMember(dest => dest.Port, opts => opts.MapFrom(src => src.PORT))
            .ForMember(dest => dest.Url, opts => opts.MapFrom(src => src.URL))
            .ForMember(dest => dest.Username, opts => opts.MapFrom(src => src.USERNAME))
            .ForMember(dest => dest.Password, opts => opts.MapFrom(src => src.PASSWORD))
            .ForMember(dest => dest.Description, opts => opts.MapFrom(src => src.DESCRIPTION))
            .ForMember(dest => dest.LastConnectTime, opts => opts.MapFrom(src => src.LASTCONNECTTIME))
            .ForMember(dest => dest.DeviceStatus, opts => opts.MapFrom(src => src.DEVICESTATUS))
            .ForMember(dest => dest.AllowUserRemoteAccess, opts => opts.MapFrom(src => src.ALLOWUSERREMOTEACCESS))
            .ForMember(dest => dest.ConnectStatus, opts => opts.MapFrom(src => src.CONNECTSTATUS))
            .ForMember(dest => dest.IpLocal, opts => opts.MapFrom(src => src.IPLOCAL))
            .ForMember(dest => dest.VideoLink, opts => opts.MapFrom(src => src.VIDEOLINK))
            .ForMember(dest => dest.QuantityChannel, opts => opts.MapFrom(src => src.QUANTITYCHANNEL))
            .ForMember(dest => dest.Renewday, opts => opts.MapFrom(src => src.RENEWDAY))
            .ForMember(dest => dest.Expday, opts => opts.MapFrom(src => src.EXPDAY))
            .ForMember(dest => dest.LocationId, opts => opts.MapFrom(src => src.LOCATIONID))
            .ForMember(dest => dest.UpdatedBy, opts => opts.MapFrom(src => src.UPDATEDUSER))
            .ForMember(dest => dest.UpdatedDate, opts => opts.MapFrom(src => src.UPDATEDDATE))
            .ForMember(dest => dest.CreatedBy, opts => opts.MapFrom(src => src.CREATEDUSER))
            .ForMember(dest => dest.CreatedDate, opts => opts.MapFrom(src => src.CREATEDDATE))
            .ForMember(dest => dest.TypeOfSignal, opts => opts.MapFrom(src => src.TYPEOFSINGNAL))
            .ForMember(dest => dest.Ip, opts => opts.MapFrom(src => src.Ip));

            CreateMap<Area, AreaModel>()
            .ForMember(dest => dest.AREAID, opts => opts.MapFrom(src => src.Id))
            .ForMember(dest => dest.ACTIVED, opts => opts.MapFrom(src => src.Actived))
            .ForMember(dest => dest.ACTIVEEMAIL, opts => opts.MapFrom(src => src.ActiveEmail))
            .ForMember(dest => dest.ACTIVEMESSAGEPHONE, opts => opts.MapFrom(src => src.ActiveMessagePhone))
            .ForMember(dest => dest.ADDRESS, opts => opts.MapFrom(src => src.Address))
            .ForMember(dest => dest.BRANCHNAME, opts => opts.MapFrom(src => src.AreaName))
            .ForMember(dest => dest.COMMANDCENTREID, opts => opts.MapFrom(src => src.CommandCentreId))
            .ForMember(dest => dest.CONTACTPERSON, opts => opts.MapFrom(src => src.ContactPerson))
            .ForMember(dest => dest.CONTACTPHONE, opts => opts.MapFrom(src => src.ContactPhone))
            .ForMember(dest => dest.CREATEDDATE, opts => opts.MapFrom(src => src.CreatedDate))
            .ForMember(dest => dest.CREATEDUSER, opts => opts.MapFrom(src => src.CreatedBy))
            .ForMember(dest => dest.DESCRIPTION, opts => opts.MapFrom(src => src.Description))
            .ForMember(dest => dest.EMAIL, opts => opts.MapFrom(src => src.Email))
            .ForMember(dest => dest.FAX, opts => opts.MapFrom(src => src.Fax))
            .ForMember(dest => dest.LATITUDE, opts => opts.MapFrom(src => src.Latitude))
            .ForMember(dest => dest.LONGITUDE, opts => opts.MapFrom(src => src.Longitude))
            .ForMember(dest => dest.MESSAGEPHONE, opts => opts.MapFrom(src => src.MessagePhone))
            .ForMember(dest => dest.REGISTEREDDATE, opts => opts.MapFrom(src => src.RegisteredDate))
            .ForMember(dest => dest.REPRESENTATIVE, opts => opts.MapFrom(src => src.Representative))
            .ForMember(dest => dest.UPDATEDDATE, opts => opts.MapFrom(src => src.UpdatedDate))
            .ForMember(dest => dest.UPDATEDUSER, opts => opts.MapFrom(src => src.UpdatedBy));

            CreateMap<AreaModel, Area>()
            .ForMember(dest => dest.Id, opts => opts.MapFrom(src => src.AREAID))
            .ForMember(dest => dest.Actived, opts => opts.MapFrom(src => src.ACTIVED))
            .ForMember(dest => dest.ActiveEmail, opts => opts.MapFrom(src => src.ACTIVEEMAIL))
            .ForMember(dest => dest.ActiveMessagePhone, opts => opts.MapFrom(src => src.ACTIVEMESSAGEPHONE))
            .ForMember(dest => dest.Address, opts => opts.MapFrom(src => src.ADDRESS))
            .ForMember(dest => dest.AreaName, opts => opts.MapFrom(src => src.BRANCHNAME))
            .ForMember(dest => dest.CommandCentreId, opts => opts.MapFrom(src => src.COMMANDCENTREID))
            .ForMember(dest => dest.ContactPerson, opts => opts.MapFrom(src => src.CONTACTPERSON))
            .ForMember(dest => dest.ContactPhone, opts => opts.MapFrom(src => src.CONTACTPHONE))
            .ForMember(dest => dest.CreatedDate, opts => opts.MapFrom(src => src.CREATEDDATE))
            .ForMember(dest => dest.CreatedBy, opts => opts.MapFrom(src => src.CREATEDUSER))
            .ForMember(dest => dest.Description, opts => opts.MapFrom(src => src.DESCRIPTION))
            .ForMember(dest => dest.Email, opts => opts.MapFrom(src => src.EMAIL))
            .ForMember(dest => dest.Fax, opts => opts.MapFrom(src => src.FAX))
            .ForMember(dest => dest.Latitude, opts => opts.MapFrom(src => src.LATITUDE))
            .ForMember(dest => dest.Longitude, opts => opts.MapFrom(src => src.LONGITUDE))
            .ForMember(dest => dest.MessagePhone, opts => opts.MapFrom(src => src.MESSAGEPHONE))
            .ForMember(dest => dest.RegisteredDate, opts => opts.MapFrom(src => src.REGISTEREDDATE))
            .ForMember(dest => dest.Representative, opts => opts.MapFrom(src => src.REPRESENTATIVE))
            .ForMember(dest => dest.UpdatedDate, opts => opts.MapFrom(src => src.UPDATEDDATE))
            .ForMember(dest => dest.UpdatedBy, opts => opts.MapFrom(src => src.UPDATEDUSER));

            CreateMap<Group, GroupModel>()
            .ForMember(dest => dest.GroupID, opts => opts.MapFrom(src => src.Id))
            .ForMember(dest => dest.GroupDescription, opts => opts.MapFrom(src => src.Description));

            CreateMap<GroupModel, Group>()
            .ForMember(dest => dest.Id, opts => opts.MapFrom(src => src.GroupID))
            .ForMember(dest => dest.Description, opts => opts.MapFrom(src => src.GroupDescription));

            CreateMap<CommandCenter, CommandCenterModel>()
            .ForMember(dest => dest.CommandCenterID, opts => opts.MapFrom(src => src.Id));

            CreateMap<CommandCenterModel, CommandCenter>()
            .ForMember(dest => dest.Id, opts => opts.MapFrom(src => src.CommandCenterID));

            CreateMap<Location, LocationModel>()
            .ForMember(dest => dest.LocationId, opts => opts.MapFrom(src => src.Id))
            .ForMember(dest => dest.LocationName, opts => opts.MapFrom(src => src.LocationName))
            .ForMember(dest => dest.Map, opts => opts.MapFrom(src => src.Map))
            .ForMember(dest => dest.Description, opts => opts.MapFrom(src => src.Description))
            .ForMember(dest => dest.Actived, opts => opts.MapFrom(src => src.Actived))
            .ForMember(dest => dest.AreaId, opts => opts.MapFrom(src => src.AreaId))
            .ForMember(dest => dest.LATITUDE, opts => opts.MapFrom(src => src.Latitude))
            .ForMember(dest => dest.LONGITUDE, opts => opts.MapFrom(src => src.Longitude));

            CreateMap<LocationModel, Location>()
            .ForMember(dest => dest.Id, opts => opts.MapFrom(src => src.LocationId))
            .ForMember(dest => dest.LocationName, opts => opts.MapFrom(src => src.LocationName))
            .ForMember(dest => dest.Map, opts => opts.MapFrom(src => src.Map))
            .ForMember(dest => dest.Description, opts => opts.MapFrom(src => src.Description))
            .ForMember(dest => dest.Actived, opts => opts.MapFrom(src => src.Actived))
            .ForMember(dest => dest.AreaId, opts => opts.MapFrom(src => src.AreaId))
            .ForMember(dest => dest.Latitude, opts => opts.MapFrom(src => src.LATITUDE))
            .ForMember(dest => dest.Longitude, opts => opts.MapFrom(src => src.LONGITUDE));

            CreateMap<Role, RoleModel>()
            .ForMember(dest => dest.ListFunction_ACS, opts => opts.MapFrom(src => src.ListFunction_ACS))
            .ForMember(dest => dest.ListFunction_REPORT, opts => opts.MapFrom(src => src.ListFunction_REPORT));

            CreateMap<RoleModel, Role>()
            .ForMember(dest => dest.ListFunction_ACS, opts => opts.MapFrom(src => src.ListFunction_ACS))
            .ForMember(dest => dest.ListFunction_REPORT, opts => opts.MapFrom(src => src.ListFunction_REPORT));

            CreateMap<Supplier, SupplierModel>()
            .ForMember(dest => dest.SUPPLIERID, opts => opts.MapFrom(src => src.Id))
            .ForMember(dest => dest.CREATEDDATE, opts => opts.MapFrom(src => src.CreatedDate))
            .ForMember(dest => dest.UPDATEDDATE, opts => opts.MapFrom(src => src.UpdatedDate))
            .ForMember(dest => dest.CREATEDUSER, opts => opts.MapFrom(src => src.CreatedBy))
            .ForMember(dest => dest.UPDATEDUSER, opts => opts.MapFrom(src => src.UpdatedBy))
            .ForMember(dest => dest.DESCRIPTION, opts => opts.MapFrom(src => src.Description))
            .ForMember(dest => dest.NAME, opts => opts.MapFrom(src => src.Name));

            CreateMap<SupplierModel, Supplier>()
            .ForMember(dest => dest.Id, opts => opts.MapFrom(src => src.SUPPLIERID))
            .ForMember(dest => dest.CreatedBy, opts => opts.MapFrom(src => src.CREATEDUSER))
            .ForMember(dest => dest.UpdatedBy, opts => opts.MapFrom(src => src.UPDATEDUSER))
            .ForMember(dest => dest.CreatedDate, opts => opts.MapFrom(src => src.CREATEDDATE))
            .ForMember(dest => dest.UpdatedDate, opts => opts.MapFrom(src => src.UPDATEDDATE))
            .ForMember(dest => dest.Description, opts => opts.MapFrom(src => src.DESCRIPTION))
            .ForMember(dest => dest.Name, opts => opts.MapFrom(src => src.NAME));

            CreateMap<SystemUser, SystemUserModel>()
            .ForMember(dest => dest.SystemUserId, opts => opts.MapFrom(src => src.Id))
            .ForMember(dest => dest.GroupID, opts => opts.MapFrom(src => src.GroupId));

            CreateMap<SystemUserModel, SystemUser>()
            .ForMember(dest => dest.Id, opts => opts.MapFrom(src => src.SystemUserId))
            .ForMember(dest => dest.GroupId, opts => opts.MapFrom(src => src.GroupID));


            CreateMap<TypeOfDevice, TypeOfDeviceModel>()
            .ForMember(dest => dest.TYPEOFDEVICEID, opts => opts.MapFrom(src => src.Id))
            .ForMember(dest => dest.CREATEDDATE, opts => opts.MapFrom(src => src.CreatedDate))
            .ForMember(dest => dest.CREATEDUSER, opts => opts.MapFrom(src => src.CreatedBy))
            .ForMember(dest => dest.UPDATEDDATE, opts => opts.MapFrom(src => src.UpdatedDate))
            .ForMember(dest => dest.UPDATEDUSER, opts => opts.MapFrom(src => src.UpdatedBy))
            .ForMember(dest => dest.DESCRIPTION, opts => opts.MapFrom(src => src.Description))
            .ForMember(dest => dest.TYPENAME, opts => opts.MapFrom(src => src.TypeName))
            .ForMember(dest => dest.ICON, opts => opts.MapFrom(src => src.Icon));

            CreateMap<TypeOfDeviceModel, TypeOfDevice>()
            .ForMember(dest => dest.Id, opts => opts.MapFrom(src => src.TYPEOFDEVICEID))
            .ForMember(dest => dest.CreatedDate, opts => opts.MapFrom(src => src.CREATEDDATE))
            .ForMember(dest => dest.CreatedBy, opts => opts.MapFrom(src => src.CREATEDUSER))
            .ForMember(dest => dest.UpdatedDate, opts => opts.MapFrom(src => src.UPDATEDDATE))
            .ForMember(dest => dest.UpdatedBy, opts => opts.MapFrom(src => src.UPDATEDUSER))
            .ForMember(dest => dest.Description, opts => opts.MapFrom(src => src.DESCRIPTION))
            .ForMember(dest => dest.TypeName, opts => opts.MapFrom(src => src.TYPENAME))
            .ForMember(dest => dest.Icon, opts => opts.MapFrom(src => src.ICON));

            CreateMap<User, UserModel>()
            .ForMember(dest => dest.UserID, opts => opts.MapFrom(src => src.Id))
            .ForMember(dest => dest.CreatedUser, opts => opts.MapFrom(src => src.CreatedBy))
            .ForMember(dest => dest.SiteID, opts => opts.MapFrom(src => src.SiteId))
            .ForMember(dest => dest.UpdatedUser, opts => opts.MapFrom(src => src.UpdatedBy));

            CreateMap<UserModel, User>()
            .ForMember(dest => dest.Id, opts => opts.MapFrom(src => src.UserID))
            .ForMember(dest => dest.CreatedBy, opts => opts.MapFrom(src => src.CreatedUser))
            .ForMember(dest => dest.SiteId, opts => opts.MapFrom(src => src.SiteID))
            .ForMember(dest => dest.UpdatedBy, opts => opts.MapFrom(src => src.UpdatedUser));

            CreateMap<Document, DocumentModel>()
            .ForMember(dest => dest.Id, opts => opts.MapFrom(src => src.Id))
            .ForMember(dest => dest.CreatedBy, opts => opts.MapFrom(src => src.CreatedBy))
            .ForMember(dest => dest.UpdatedBy, opts => opts.MapFrom(src => src.UpdatedBy))
            .ForMember(dest => dest.CreatedDate, opts => opts.MapFrom(src => src.CreatedDate))
            .ForMember(dest => dest.UpdatedDate, opts => opts.MapFrom(src => src.UpdatedDate))
            .ForMember(dest => dest.DocumentName, opts => opts.MapFrom(src => src.DocumentName))
            .ForMember(dest => dest.FileName, opts => opts.MapFrom(src => src.FileName))
            .ForMember(dest => dest.Description, opts => opts.MapFrom(src => src.Description));

            CreateMap<DocumentModel, Document>()
            .ForMember(dest => dest.Id, opts => opts.MapFrom(src => src.Id))
            .ForMember(dest => dest.CreatedBy, opts => opts.MapFrom(src => src.CreatedBy))
            .ForMember(dest => dest.UpdatedBy, opts => opts.MapFrom(src => src.UpdatedBy))
            .ForMember(dest => dest.CreatedDate, opts => opts.MapFrom(src => src.CreatedDate))
            .ForMember(dest => dest.UpdatedDate, opts => opts.MapFrom(src => src.UpdatedDate))
            .ForMember(dest => dest.DocumentName, opts => opts.MapFrom(src => src.DocumentName))
            .ForMember(dest => dest.FileName, opts => opts.MapFrom(src => src.FileName))
            .ForMember(dest => dest.Description, opts => opts.MapFrom(src => src.Description));

            CreateMap<HistorySystem, HistorySystemModel>()
            .ForMember(dest => dest.Id, opts => opts.MapFrom(src => src.Id));

            CreateMap<HistorySystemModel, HistorySystem>()
            .ForMember(dest => dest.Id, opts => opts.MapFrom(src => src.Id));

            CreateMap<Position, PositionModel>()
            .ForMember(dest => dest.Id, opts => opts.MapFrom(src => src.Id));

            CreateMap<PositionModel, Position>()
            .ForMember(dest => dest.Id, opts => opts.MapFrom(src => src.Id));

            CreateMap<DepartmentModel, Department>()
            .ForMember(dest => dest.Id, opts => opts.MapFrom(src => src.Id));

            CreateMap<Department, DepartmentModel>()
            .ForMember(dest => dest.Id, opts => opts.MapFrom(src => src.Id));

            CreateMap<Marker, MarkerModel>()
            .ForMember(dest => dest.MarkerID, opts => opts.MapFrom(src => src.MarkerID))
            .ForMember(dest => dest.LATITUDE, opts => opts.MapFrom(src => src.Latitude))
            .ForMember(dest => dest.LONGITUDE, opts => opts.MapFrom(src => src.Longitude))
            .ForMember(dest => dest.MarkerTypeID, opts => opts.MapFrom(src => src.TypeOfMarkerID));

            CreateMap<MarkerModel, Marker>()
            .ForMember(dest => dest.MarkerID, opts => opts.MapFrom(src => src.MarkerID))
            .ForMember(dest => dest.Latitude, opts => opts.MapFrom(src => src.LATITUDE))
            .ForMember(dest => dest.Longitude, opts => opts.MapFrom(src => src.LONGITUDE))
            .ForMember(dest => dest.TypeOfMarkerID, opts => opts.MapFrom(src => src.MarkerTypeID));

            CreateMap<TypeOfMarkerModel, TypeOfMarker>()
            .ForMember(dest => dest.Id, opts => opts.MapFrom(src => src.Id));

            CreateMap<TypeOfMarker, TypeOfMarkerModel>()
            .ForMember(dest => dest.Id, opts => opts.MapFrom(src => src.Id));

            CreateMap<Site, SiteModel>()
            .ForMember(dest => dest.SiteId, opts => opts.MapFrom(src => src.SiteId))
            .ForMember(dest => dest.SiteName, opts => opts.MapFrom(src => src.SiteName));

            CreateMap<SiteModel, Site>()
            .ForMember(dest => dest.SiteId, opts => opts.MapFrom(src => src.SiteId))
            .ForMember(dest => dest.SiteName, opts => opts.MapFrom(src => src.SiteName));

            CreateMap<ContactPlace, ContactPlaceModel>()
            .ForMember(dest => dest.Id, opts => opts.MapFrom(src => src.Id))
            .ForMember(dest => dest.Name, opts => opts.MapFrom(src => src.Name))
            .ForMember(dest => dest.Index, opts => opts.MapFrom(src => src.Index));

            CreateMap<ContactPlaceModel, ContactPlace>()
            .ForMember(dest => dest.Id, opts => opts.MapFrom(src => src.Id))
            .ForMember(dest => dest.Name, opts => opts.MapFrom(src => src.Name))
            .ForMember(dest => dest.Index, opts => opts.MapFrom(src => src.Index));

            CreateMap<ContactUnit, ContactUnitModel>()
            .ForMember(dest => dest.Id, opts => opts.MapFrom(src => src.Id))
            .ForMember(dest => dest.Name, opts => opts.MapFrom(src => src.Name))
            .ForMember(dest => dest.ContactPlaceName, opts => opts.MapFrom(src => src.ContactUnitPlaces != null ? string.Join(", ", src.ContactUnitPlaces.Select(x => x.ContactPlace.Name).ToList()) : string.Empty))
            .ForMember(dest => dest.Description, opts => opts.MapFrom(src => src.Description))
            .ForMember(dest => dest.Index, opts => opts.MapFrom(src => src.Index));

            CreateMap<ContactUnitModel, ContactUnit>()
            .ForMember(dest => dest.Id, opts => opts.MapFrom(src => src.Id))
            .ForMember(dest => dest.Name, opts => opts.MapFrom(src => src.Name))
            .ForMember(dest => dest.Description, opts => opts.MapFrom(src => src.Description))
            .ForMember(dest => dest.Index, opts => opts.MapFrom(src => src.Index));

            CreateMap<CompanyModel, Company>()
            .ForMember(dest => dest.Id, opts => opts.MapFrom(src => src.Id))
            .ForMember(dest => dest.Name, opts => opts.MapFrom(src => src.Name));

            CreateMap<Company, CompanyModel>()
            .ForMember(dest => dest.Id, opts => opts.MapFrom(src => src.Id))
            .ForMember(dest => dest.Name, opts => opts.MapFrom(src => src.Name));


            CreateMap<ReasonModel, Reason>()
            .ForMember(dest => dest.Id, opts => opts.MapFrom(src => src.Id))
            .ForMember(dest => dest.Name, opts => opts.MapFrom(src => src.Name))
            .ForMember(dest => dest.Type, opts => opts.MapFrom(src => src.Type))
            .ForMember(dest => dest.Description, opts => opts.MapFrom(src => src.Description));

            CreateMap<Reason, ReasonModel>()
            .ForMember(dest => dest.Id, opts => opts.MapFrom(src => src.Id))
            .ForMember(dest => dest.Name, opts => opts.MapFrom(src => src.Name))
            .ForMember(dest => dest.Type, opts => opts.MapFrom(src => src.Type))
            .ForMember(dest => dest.Description, opts => opts.MapFrom(src => src.Description));

            CreateMap<VehicleType, VehicleTypeModel>()
          .ForMember(dest => dest.Id, opts => opts.MapFrom(src => src.Id))
          .ForMember(dest => dest.Name, opts => opts.MapFrom(src => src.Name));

            CreateMap<VehicleTypeModel, VehicleType>()
           .ForMember(dest => dest.Id, opts => opts.MapFrom(src => src.Id))
           .ForMember(dest => dest.Name, opts => opts.MapFrom(src => src.Name));

            CreateMap<Weight, WeightModel>()
           .ForMember(dest => dest.Id, opts => opts.MapFrom(src => src.Id))
           .ForMember(dest => dest.Name, opts => opts.MapFrom(src => src.Name));

            CreateMap<WeightModel, Weight>()
           .ForMember(dest => dest.Id, opts => opts.MapFrom(src => src.Id))
           .ForMember(dest => dest.Name, opts => opts.MapFrom(src => src.Name));

            CreateMap<PurposeInPort, PurposeInPortModel>()
            .ForMember(dest => dest.Id, opts => opts.MapFrom(src => src.Id))
            .ForMember(dest => dest.Name, opts => opts.MapFrom(src => src.Name));

            CreateMap<PurposeInPortModel, PurposeInPort>()
            .ForMember(dest => dest.Id, opts => opts.MapFrom(src => src.Id))
            .ForMember(dest => dest.Name, opts => opts.MapFrom(src => src.Name));

            CreateMap<BlackVehicleList, BlackVehicleListModel>()
            .ForMember(dest => dest.Id, opts => opts.MapFrom(src => src.Id));

            CreateMap<BlackVehicleListModel, BlackVehicleList>()
            .ForMember(dest => dest.Id, opts => opts.MapFrom(src => src.Id));

            CreateMap<District, DistrictModel>()
            .ForMember(dest => dest.Id, opts => opts.MapFrom(src => src.Id));


            CreateMap<DistrictModel, District>()
            .ForMember(dest => dest.Id, opts => opts.MapFrom(src => src.Id));


            CreateMap<Province, ProvinceModel>()
            .ForMember(dest => dest.Id, opts => opts.MapFrom(src => src.Id))
            .ForMember(dest => dest.ProvinceName, opts => opts.MapFrom(src => src.Name));

            CreateMap<ProvinceModel, Province>()
            .ForMember(dest => dest.Id, opts => opts.MapFrom(src => src.Id))
            .ForMember(dest => dest.Name, opts => opts.MapFrom(src => src.ProvinceName));

            CreateMap<UserCBCS, UserCBCSModel>()
            .ForMember(dest => dest.Id, opts => opts.MapFrom(src => src.Id))
            .ForMember(dest => dest.Name, opts => opts.MapFrom(src => src.FullName));

            CreateMap<UserCBCSModel, UserCBCS>()
            .ForMember(dest => dest.Id, opts => opts.MapFrom(src => src.Id))
            .ForMember(dest => dest.FullName, opts => opts.MapFrom(src => src.Name));

            CreateMap<RoleCBCS, RoleCBCSModel>()
            .ForMember(dest => dest.RoleId, opts => opts.MapFrom(src => src.Id))
            .ForMember(dest => dest.RoleName, opts => opts.MapFrom(src => src.RoleName))
            .ForMember(dest => dest.RoleDescription, opts => opts.MapFrom(src => src.RoleDescription));

            CreateMap<RoleCBCSModel, RoleCBCS>()
            .ForMember(dest => dest.Id, opts => opts.MapFrom(src => src.RoleId));
        }
    }
}
