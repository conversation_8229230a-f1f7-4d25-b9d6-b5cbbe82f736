﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.DirectoryServices.AccountManagement;
using System.Linq;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PSafe.AM.Common;
using PSafe.AM.Models;
using PSafe.Common;
using PSafe.Core.Domains;
using PSafe.Core.Interface;
using PSafeAM.Controllers;
using static PSafe.Common.CommonEnums;

namespace PSafe.AM.Controllers
{
    public class SecurityController : BaseController
    {
        private readonly IUserRepository _userRepository;
        private readonly IUserInRoleRepository _userInRoleRepository;
        private readonly IRoleRepository _roleRepository;
        private readonly IHistorySystemRepository _historySystemRepository;
        private readonly IHttpContextAccessor _accessor;
        private readonly IConfiguration _configuration;
        private readonly ILogger<SecurityController> _logger;

        public SecurityController(
            IUserRepository userRepository,
            IUserInRoleRepository userInRoleRepository,
            IRoleRepository roleRepository,
            IHistorySystemRepository historySystemRepository,
            IHttpContextAccessor accessor,
            ILogger<SecurityController> logger,
            IGeneralConfigRepository generalConfigRepo,
            IConfiguration configuration) : base(generalConfigRepo)
        {
            _logger = logger;
            _userRepository = userRepository;
            _userInRoleRepository = userInRoleRepository;
            _roleRepository = roleRepository;
            _historySystemRepository = historySystemRepository;
            _accessor = accessor;
            _configuration = configuration;
        }

        public ActionResult Login(string returnUrl, bool? SessionEnd)
        {
            UserModel systemUser = new UserModel();
            try
            {
                StatusQuery Notification;
                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                ViewBag.uiConfigs = _uiConfigs;

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }
                else if (SessionEnd != null && SessionEnd.Value)
                {
                    Notification = new StatusQuery("warning", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
                _logger.LogError("Security/Login: " + ex.Message);
            }

            ViewBag.ReturnUrl = returnUrl;

            return View(systemUser);
        }

        public ActionResult LoginApp(string username, string password)
        {
            return Login(new UserModel()
            {
                UserName = username,
                Password = password,
                Type = 0
            }, "/LoginSuccess", true);
        }

        public ActionResult Logout()
        {
            StatusQuery Notification;
            Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

            if (Notification != null)
            {
                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery(Notification.Type, Notification.Status, Notification.Value));
            }

            HttpContext.Session.Clear();
            return RedirectToAction("Login");
        }

        private ActionResult RedirectToLocal(string returnUrl)
        {
            StatusQuery Notification;
            Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

            if (Notification != null)
            {
                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery(Notification.Type, Notification.Status, Notification.Value));
            }

            if (Url.IsLocalUrl(returnUrl))
            {
                return Redirect(returnUrl);
            }
            else
            {
                return RedirectToAction("Index", "Home");
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Login(UserModel objUser, string returnUrl, bool winform = false)
        {
            ViewBag.uiConfigs = _uiConfigs;
            _logger.LogInformation("loging");
            StatusQuery Notification;
            if (objUser.Password == null)
            {
                ModelState.AddModelError("Password", "Vui lòng nhập mật khẩu");
            }
            else if (objUser.Password.Length < 3)
            {
                ModelState.AddModelError("Password", "Mật khẩu không đúng");
                objUser.Password = string.Empty;
            }

            ModelState.Remove("Email");
            ModelState.Remove("Phone");

            objUser.UserName = objUser.UserName ?? "";
            objUser.Password = objUser.Password ?? "";
            if (ModelState.IsValid)
            {
                try
                {
                    var obj = _userRepository.GetBy(item => item.UserName.ToLower() == objUser.UserName.ToLower() && item.Type == objUser.Type).SingleOrDefault();
                    var has = new HashEncoding();
                    if (obj != null)
                    {
                        if (!obj.Actived)
                        {
                            Notification = new StatusQuery("warning", "", "Tài khoản chưa kích hoạt, vui lòng kiểm tra lại");
                            ViewBag.Status = Notification.Status;
                            ViewBag.Value = Notification.Value;
                            ViewBag.Type = Notification.Type;
                            ModelState.AddModelError("authenticationError", "Tài khoản chưa kích hoạt");
                            objUser.Password = string.Empty;
                            _logger.LogInformation("Securiry/Login: " + obj.UserName + " chưa kích hoạt");
                            return View(objUser);
                        }
                        else if (obj.Type == 1)
                        {
                            var listDomain = _configuration.GetSection("DomainAD").Value.Split(',');
                            foreach (var item in listDomain)
                            {
                                Stopwatch stopWatch = new Stopwatch();
                                stopWatch.Start();

                                PrincipalContext context = GetContext(item);

                                stopWatch.Stop();
                                TimeSpan ts = stopWatch.Elapsed;
                                string elapsedTime_GetContext = String.Format("{0:00}:{1:00}:{2:00}.{3:00}", ts.Hours, ts.Minutes, ts.Seconds, ts.Milliseconds / 10);
                                _logger.LogInformation("Time_GetContext: " + elapsedTime_GetContext);

                                if (context != null)
                                {
                                    stopWatch.Start();

                                    var checkValidate = FnValidateUser(context, objUser.UserName, objUser.Password);

                                    stopWatch.Stop();
                                    string elapsedTime_FnValidateUser = String.Format("{0:00}:{1:00}:{2:00}.{3:00}", ts.Hours, ts.Minutes, ts.Seconds, ts.Milliseconds / 10);
                                    _logger.LogInformation("Time_FnValidateUser: " + elapsedTime_FnValidateUser);

                                    if (checkValidate)
                                    {
                                        string userFunctions = GetUserFunctions(obj);

                                        HttpContext.Session.SetString("SessionUserSystem", obj.UserName);
                                        HttpContext.Session.SetString("SessionUserSystemId", obj.Id.ToString());
                                        HttpContext.Session.SetString("UserFunctions", userFunctions);
                                        HttpContext.Session.SetString("winform", winform.ToString());
                                        HttpContext.Session.SetString("SessionUserSystemType", obj.Type.ToString());
                                        HttpContext.Session.SetComplexData("SessionUserSystemId_" + obj.Id, new User() { Id = obj.Id, UserName = obj.UserName, Actived = obj.Actived, Phone = obj.Phone });

                                        string StringDescription = new GetStringHistorySystem().Get(obj.UserName, Resources.Resource.Login, "", "");
                                        InsertHistorySystem((int)EACTION_TYPE.LOGIN, (int)EnumControllerName.SECURITYS, StringDescription, obj.Id);
                                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Đăng nhập thành công"));

                                        _logger.LogInformation("Security/Login: Xin CHAO " + obj.UserName);

                                        ViewBag.uiConfigs = _uiConfigs;

                                        if (returnUrl != null)
                                        {
                                            return RedirectToLocal(returnUrl);
                                        }

                                        return RedirectToAction("Index", "Home");
                                    }
                                    else
                                    {
                                        Notification = new StatusQuery("warning", "", "Tên đăng nhập hoặc mật khẩu không đúng");
                                        ViewBag.Status = Notification.Status;
                                        ViewBag.Value = Notification.Value;
                                        ViewBag.Type = Notification.Type;
                                        ModelState.AddModelError("authenticationError", "");
                                        objUser.Password = string.Empty;

                                        _logger.LogError("Security/Login: AD-FnValidateUser = " + checkValidate);

                                        return View(objUser);
                                    }
                                }
                            }

                            Notification = new StatusQuery("warning", "", "Không thể kết nối tới server");
                            ViewBag.Status = Notification.Status;
                            ViewBag.Value = Notification.Value;
                            ViewBag.Type = Notification.Type;

                            objUser.Password = string.Empty;
                            _logger.LogError("Security/Login: " + "Không thể kết nối tới server_" + listDomain);

                            ModelState.AddModelError("authenticationError", "Không thể kết nối tới server");
                        }
                         
                        
                        else if (has.VerifyPassword(objUser.Password,obj.UserName, obj.Password))
                        {
                            string userFunctions = GetUserFunctions(obj);

                            HttpContext.Session.SetString("SessionUserSystem", obj.UserName);
                            HttpContext.Session.SetString("SessionUserSystemId", obj.Id.ToString());
                            HttpContext.Session.SetString("UserFunctions", userFunctions);
                            HttpContext.Session.SetString("winform", winform.ToString());
                            HttpContext.Session.SetComplexData("SessionUserSystemId_" + obj.Id, new User() { Id = obj.Id, UserName = obj.UserName, Actived = obj.Actived, Phone = obj.Phone });

                            var CookieOptions = new CookieOptions
                            {
                                Expires = DateTime.Now.AddDays(15)
                            };

                            Response.Cookies.Append("UserName", obj.UserName, CookieOptions);

                            string StringDescription = new GetStringHistorySystem().Get(obj.UserName, Resources.Resource.Login, "", "");

                            InsertHistorySystem((int)EACTION_TYPE.LOGIN, (int)EnumControllerName.SECURITYS, StringDescription, obj.Id);

                            TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Đăng nhập thành công"));

                            _logger.LogInformation("Securiry/Login: Xin Chao: " + obj.UserName);

                            if (returnUrl != null)
                            {
                                return RedirectToLocal(returnUrl);
                            }

                            return RedirectToAction("Index", "Home");
                        }
                        else
                        {
                            Notification = new StatusQuery("warning", "", "Mật khẩu không đúng");
                            ViewBag.Status = Notification.Status;
                            ViewBag.Value = Notification.Value;
                            ViewBag.Type = Notification.Type;
                            ModelState.AddModelError("Password", "Mật khẩu không đúng");
                            objUser.Password = string.Empty;

                            _logger.LogError("Security/Login: Thường-Mật khẩu không đúng");

                            return View(objUser);
                        }
                    }
                    else
                    {
                        Notification = new StatusQuery("warning", "", "Tài khoản không tồn tại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;
                        ModelState.AddModelError("authenticationError", "Tài khoản không tồn tại");
                        objUser.Password = string.Empty;

                        _logger.LogError("Security/Login: Tài khoản không tồn tại");

                        return View(objUser);
                    }
                }
                catch(Exception ex)
                {
                    _logger.LogError("Security/Login: " + ex.Message);

                    Notification = new StatusQuery("error", "", "Kết nối tới server thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                    ModelState.AddModelError("authenticationError", "Kết nối tới server thất bại");
                    objUser.Password = string.Empty;
                    return View(objUser);
                }
            }
            Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;
            objUser.Password = string.Empty;

            return View(objUser);
        }


        private string GetUserFunctions(User user)
        {
            List<int> userFunctions = new List<int>();
            try
            {
                var userInRoles = _userInRoleRepository.GetBy(x => x.UserId == user.Id).Select(x => x.RoleId).ToList();
                
                if (userInRoles != null)
                {
                    List<string> roles = _roleRepository.GetBy(x => userInRoles.Contains(x.RoleId)).Select(x => x.ListFunction_AM).ToList();

                    foreach (var role in roles)
                    {
                        if (role != null)
                        {
                            string[] functionArray = role.Split(',');
                            foreach (string functionItem in functionArray)
                            {
                                int function;
                                if (int.TryParse(functionItem, out function))
                                {
                                    if (!userFunctions.Contains(function))
                                        userFunctions.Add(function);
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.Message);
            }
            return string.Join(",", userFunctions);
        }

        public PrincipalContext GetContext(string domain)
        {
            try
            {
                PrincipalContext context = new PrincipalContext(ContextType.Domain, domain);
                return context;

            }
            catch(Exception ex)
            {
                _logger.LogError("Security/GetContext: " + ex.Message);
                return null;
            }
        }

        public bool FnValidateUser(PrincipalContext context, string username, string password)
        {
            bool validation;
            try
            {
                validation = context.ValidateCredentials(username, password);
            }
            catch(Exception ex)
            {
                _logger.LogError("FnValidateUser: " + ex.Message);
                validation = false;
            }
            return validation;
        }

        private void InsertHistorySystem(int action_type, int controllerName, string description, int UserId)
        {
            try
            {
                var ipAddress = _accessor.HttpContext.Connection.RemoteIpAddress.ToString();
                HistorySystem history = new HistorySystem
                {
                    ActionType = action_type,
                    ActionTime = DateTime.Now,
                    Description = description,
                    OldObject = null,
                    NewObject = null,
                    UserId = UserId,
                    IpAddress = ipAddress,
                    ControllerName = controllerName
                };

                _historySystemRepository.Insert(history);

                _historySystemRepository.SaveChanges();
            }
            catch (Exception ex)
            {
                _logger.LogError("Security/InsertHistorySystem: " + ex.Message);
            } 
        }
    }
}