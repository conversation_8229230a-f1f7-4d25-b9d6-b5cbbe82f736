﻿@model PSafe.AM.Models.LocationModel
@{
    ViewBag.Title = "Tạo mới";
}

<environment names="Development">
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/validate/jquery.validate.min.js"></script>
    <script src="~/js/script.js" asp-append-version="true"></script>
</environment>
<environment names="Staging,Production">
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/validate/jquery.validate.min.js"></script>
    <script src="~/js/script.js" asp-append-version="true"></script>
</environment>
<link href="~/css/searchmap.css" rel="stylesheet" asp-append-version="true" />


<script type="text/javascript" src="https://maps.googleapis.com/maps/api/js?key=AIzaSyAKIiNg5Bi53czOoPvLuEz_Tv6BywCafyY"></script>
<script type="text/javascript">
    var mapTypeIds = [];
</script>

<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-lg-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>@PSafe.AM.Resources.Resource.AddItem @PSafe.AM.Resources.Resource.Location</h5>
                    <div class="ibox-tools">
                        @Html.ActionLink(PSafe.AM.Resources.Resource.BackToList, "Index", "Locations", null, new { @class = "btn btn-primary btn-xs" })
                    </div>
                </div>
                <div class="ibox-content">
                    @using (Html.BeginForm("Create", "Locations", FormMethod.Post, new { enctype = "multipart/form-data" }))
                    {
                        @Html.AntiForgeryToken()

                        <div class="form-horizontal">
                            @Html.ValidationSummary(true)
                            <div class="form-group">
                                <label class="control-label col-md-2">@Html.LabelFor(model => model.AreaId) (*)</label>
                                <div class="col-md-10">
                                    @Html.DropDownListFor(model => model.AreaId, Model.ListAreas as SelectList, new { @class = "form-control" })
                                    @Html.ValidationMessageFor(model => model.AreaId, null, new { @class = "text-danger" })
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="control-label col-md-2">@Html.LabelFor(model => model.LocationName) (*)</label>
                                <div class="col-md-10">
                                    @Html.EditorFor(model => model.LocationName)
                                    @Html.ValidationMessageFor(model => model.LocationName, null, new { @class = "text-danger" })
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="control-label col-md-2">@Html.LabelFor(model => model.Map) (*)</label>
                                <div class="col-md-10">
                                    @Html.TextBoxFor(model => model.ImageFile, new { @type = "file", @class = "btn btn-primary btn-file", accept = ".xlsx,.xls,image/*,.doc, .docx,.ppt, .pptx,.txt,.pdf" })
                                    @Html.ValidationMessageFor(model => model.Map, null, new { @class = "text-danger" })
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="control-label col-md-2">@Html.LabelFor(model => model.LONGITUDE) (*)</label>
                                <div class="col-md-10">
                                    @Html.EditorFor(model => model.LONGITUDE)
                                    @Html.ValidationMessageFor(model => model.LONGITUDE, null, new { @class = "text-danger" })
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="control-label col-md-2">@Html.LabelFor(model => model.LATITUDE) (*)</label>
                                <div class="col-md-8">
                                    @Html.EditorFor(model => model.LATITUDE)
                                    @Html.ValidationMessageFor(model => model.LATITUDE, null, new { @class = "text-danger" })
                                </div>
                                <div class="col-md-2">
                                    <input type="button" class="btn btn-primary btn-xs" btn- value="Lấy tọa độ" data-toggle="modal" data-target="#LoadMapLocal" />
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="control-label col-md-2">@Html.LabelFor(model => model.Address) (*)</label>
                                <div class="col-md-10">
                                    @Html.EditorFor(model => model.Address)
                                    @Html.ValidationMessageFor(model => model.Address, null, new { @class = "text-danger" })
                                </div>
                            </div>

                            <div class="form-group">
                                @Html.LabelFor(model => model.Document, new { @class = "control-label col-md-2" })
                                <div class="col-md-10">
                                    <select data-placeholder="Chọn tài liệu..." class="chosen-select" name="document_select" multiple tabindex="4">
                                        @foreach (var item in Model.ListDocumentNotOnLocation)
                                        {
                                            <option value="@item.Id">@Html.DisplayFor(modelItem => item.FileName)</option>
                                        }
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                @Html.LabelFor(model => model.Description, new { @class = "control-label col-md-2" })
                                <div class="col-md-10">
                                    @Html.TextAreaFor(model => model.Description, new { @class = "form-control" })
                                    @Html.ValidationMessageFor(model => model.Description, null, new { @class = "text-danger" })
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-md-offset-2 col-md-10">
                                    <input type="submit" value="@PSafe.AM.Resources.Resource.Save" class="btn btn-primary" />
                                    @Html.ActionLink(PSafe.AM.Resources.Resource.Cancel, "Index", "Locations", null, new { @class = "btn btn-white" })
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal -->
<div class="modal fade bd-example-modal-xl" id="LoadMapLocal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog-centered modal-xl" style="margin: 1.75rem auto; max-width: 900px;" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title text-warning" id="exampleModalLabel"><strong>Chọn vị trí</strong></h2>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" style="width: 100%; height: 100%; padding: 0px;">
                <div id="searchAdress">
               
                    <div class="groupSearch">
                        <div id="suggestions"></div>
                        <input id="location-input" class="form-control" type="text" placeholder="Tìm kiếm địa điểm" />
                    </div>
                </div>

                <div id="map-canvas" style="width: 100%; height: 50vh;"></div>
            </div>
            <div class="modal-footer">
                <div class="col-md-10">
                   <h4>Thông tin</h4>
                    <p id="latitude-select">Thông tin vĩ độ</p>
                    <p id="longitude-select">Thông tin kinh độ</p>
                    <p id="address-select">Thông tin địa chỉ</p>
                </div>
                <div class="col-md-2">
                    <button type="button" class="btn btn-white" data-dismiss="modal">@PSafe.AM.Resources.Resource.Cancel</button>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <environment names="Development,Staging,Production">
        <link rel="stylesheet" href="~/lib/toastr/toastr.min.css" />
    </environment>

    <environment names="Development">
        <link rel="stylesheet" href="~/lib/chosen/bootstrap-chosen.css" />

    </environment>
    <environment names="Staging,Production">
        <link rel="stylesheet" href="~/lib/chosen/bootstrap-chosen.css" />
    </environment>
}

@section Scripts {
    <environment names="Development">
        <script src="~/lib/chosen/chosen.jquery.js"></script>
    </environment>

    <environment names="Development,Staging,Production">
        <script src="~/lib/validate/jquery.validate.min.js"></script>
        <script src="~/lib/toastr/toastr.js"></script>
        <script src="~/lib/toastr/toastr.min.js"></script>
        <script src="~/lib/chosen/chosen.jquery.js"></script>
    </environment>

    @if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "warning")
    {
        <script type="text/javascript">
            toastr.warning('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "error")
    {
        <script type="text/javascript">
            toastr.error('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "success")
    {
        <script type="text/javascript">
            toastr.success('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }

    <script type="text/javascript">
        $('.chosen-select').chosen({ width: "100%" });

        $("#AreaId").change(function () {
            var areasId = $(this).val();
            $.ajax({
                url: '@Url.Action("GetArea", "Areas")',
                type: 'GET',
                data: { id: areasId },
                dataType: 'json',
                success: function (response) {
                    $("#LONGITUDE").empty();
                    $("#LATITUDE").empty();

                    $("#LONGITUDE").val(response.longitude);
                    $("#LATITUDE").val(response.latitude);
                }
            });
        });
    </script>

    <script type="text/javascript">
        var marker;
        var map;

        async function updateLatLngInputs(position) {
            console.log(position.lat() + " - " + position.lng());
            $("#LONGITUDE").val(position.lng());
            $("#LATITUDE").val(position.lat());
            map.setCenter(position);

            $("#latitude-select").text(position.lat());
            $("#longitude-select").text(position.lng());

            let address = await getAddressByPosition(position.lat(), position.lng());

            $("#address-select").text(address);
        }

        async function getAddressByPosition(lat, lon)
        {
            try {
                const response = await fetch(`/Maps/GetReverseGeocoding?lat=${lat}&lon=${lon}`,
                {
                    method: 'GET'
                });

                const locations = await response.json();
                if(locations.result != 0){
                    return "Không tìm thấy địa chỉ";
                }

                return locations.message;
            } catch (err) {
                console.error("Lỗi khi gọi API:", err);
            }
        }

        $(function() {
            var link_map_ots = function (a, c) { return "https://maps.ots.vn/api/v1/tiles/basic/" + c + "/" + a.x + "/" + a.y + ".png?apikey=NkJ7pLZ3rA5tXvW2cE0KdQ9oIi4CVY8Ox" };
            mapTypeIds.push("GTEL Map");
            let myOptions = {
                zoom: 17,
                center: new google.maps.LatLng(10.535202, 106.416929),
                mapTypeId: "GTEL Map",
                zoomControl: true
            };
            map = new google.maps.Map(document.getElementById("map-canvas"), myOptions);

            const osmMapType = new google.maps.ImageMapType({
                getTileUrl: link_map_ots,
                tileSize: new google.maps.Size(256, 256),
                name: "GTEL Map",
                maxZoom: 19,
            });

            map.mapTypes.set("GTEL", osmMapType);
            map.setMapTypeId("GTEL");

            var divsearch = document.getElementById("searchAdress");
            map.controls[google.maps.ControlPosition.LEFT_BOTTOM].push(divsearch);

            map.addListener("click", function (e) {
                const clickedLatLng = e.latLng;

                if (marker) {
                    marker.setPosition(clickedLatLng);
                } else {
                    marker = new google.maps.Marker({
                        position: clickedLatLng,
                        map: map,
                        draggable: true,
                    });

                    marker.addListener("dragend", function () {
                        updateLatLngInputs(marker.getPosition());
                    });
                }

                updateLatLngInputs(clickedLatLng);
            });
        });
    </script>
    <script>
        const input = document.getElementById("location-input");
        const suggestionsBox = document.getElementById("suggestions");

        input.addEventListener("input", async () => {
            const query = input.value.trim();
            if (query.length < 3) {
                suggestionsBox.innerHTML = "";
                return;
            }

            if (query.length < 5) {
                suggestionsBox.innerHTML = "<div class='suggestion-item empty'>Nhập ít nhất 5 ký tự để tìm kiếm</div>";
                return;
            }

            // Show loading state
            suggestionsBox.innerHTML = "<div class='suggestion-item loading'>Đang tìm kiếm...</div>";

            try {
                const response = await fetch("@Url.Action("GetAutocompleteGeocoding", "Maps")" + "?search=" + query,
                {
                    method: 'GET'
                });
                const locations = await response.json();

                if(locations.result != 0){
                    suggestionsBox.innerHTML = "<div class='suggestion-item empty'>Không tìm thấy địa chỉ</div>";
                    return;
                }

                suggestionsBox.innerHTML = "";

                if (locations.message && locations.message.length > 0) {
                    locations.message.forEach((loc, index) => {
                        const div = document.createElement("div");
                        div.className = "suggestion-item";
                        div.textContent = loc.properties.address;
                        div.setAttribute("data-index", index);

                        div.addEventListener("click", () => {
                            selectLocation(loc);
                        });

                        // Add keyboard navigation support
                        div.addEventListener("mouseenter", () => {
                            // Remove active class from all items
                            document.querySelectorAll(".suggestion-item").forEach(item => {
                                item.classList.remove("active");
                            });
                            // Add active class to current item
                            div.classList.add("active");
                        });

                        suggestionsBox.appendChild(div);
                    });
                } else {
                    suggestionsBox.innerHTML = "<div class='suggestion-item empty'>Không tìm thấy địa chỉ</div>";
                }
            } catch (err) {
                console.error("Lỗi khi gọi API:", err);
                suggestionsBox.innerHTML = "<div class='suggestion-item error'>Lỗi khi tải dữ liệu</div>";
            }
        });

        // Function to select a location
        function selectLocation(loc) {
            input.value = loc.properties.address;
            suggestionsBox.innerHTML = "";

            console.log("Đã chọn địa điểm:", loc);

            const clickedLatLng = new google.maps.LatLng(loc.geometry.coordinates[1], loc.geometry.coordinates[0]);

            if (marker) {
                marker.setPosition(clickedLatLng);
            } else {
                marker = new google.maps.Marker({
                    position: clickedLatLng,
                    map: map,
                    draggable: true,
                });

                marker.addListener("dragend", function () {
                    updateLatLngInputs(marker.getPosition());
                });
            }

            updateLatLngInputs(clickedLatLng);
            map.setCenter(clickedLatLng);
            map.setZoom(16);
        }

        // Keyboard navigation support
        let currentActiveIndex = -1;

        input.addEventListener("keydown", (e) => {
            const suggestions = document.querySelectorAll(".suggestion-item:not(.loading):not(.empty):not(.error)");

            if (suggestions.length === 0) return;

            switch(e.key) {
                case "ArrowDown":
                    e.preventDefault();
                    currentActiveIndex = Math.min(currentActiveIndex + 1, suggestions.length - 1);
                    updateActiveItem(suggestions);
                    break;

                case "ArrowUp":
                    e.preventDefault();
                    currentActiveIndex = Math.max(currentActiveIndex - 1, -1);
                    updateActiveItem(suggestions);
                    break;

                case "Enter":
                    e.preventDefault();
                    if (currentActiveIndex >= 0 && suggestions[currentActiveIndex]) {
                        suggestions[currentActiveIndex].click();
                    }
                    break;

                case "Escape":
                    suggestionsBox.innerHTML = "";
                    currentActiveIndex = -1;
                    break;
            }
        });

        function updateActiveItem(suggestions) {
            // Remove active class from all items
            suggestions.forEach(item => item.classList.remove("active"));

            // Add active class to current item
            if (currentActiveIndex >= 0 && suggestions[currentActiveIndex]) {
                suggestions[currentActiveIndex].classList.add("active");
                // Scroll into view if needed
                suggestions[currentActiveIndex].scrollIntoView({
                    block: "nearest",
                    behavior: "smooth"
                });
            }
        }

        // Hide suggestions when clicking outside
        document.addEventListener("click", (e) => {
            if (!e.target.closest(".groupSearch")) {
                suggestionsBox.innerHTML = "";
                currentActiveIndex = -1;
            }
        });

        // Reset active index when new search starts
        input.addEventListener("focus", () => {
            currentActiveIndex = -1;
        });
    </script>
}