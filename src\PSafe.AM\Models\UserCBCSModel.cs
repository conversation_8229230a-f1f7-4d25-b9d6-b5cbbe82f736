﻿using System;
using System.ComponentModel.DataAnnotations;

namespace PSafe.AM.Models
{
    public class UserCBCSModel
    {
        [Key]
        public int Id { get; set; }

        [Required(ErrorMessage = "<PERSON>ui lòng nhập tên đăng nhập")]
        [StringLength(255, MinimumLength = 3, ErrorMessage = "Độ dài từ 3-255 ký tự")]
        [Display(Name = "UserName", ResourceType = typeof(Resources.Resource_userCBCS))]
        public string UserName { get; set; } = string.Empty;

        [DataType(DataType.Password)]
        [Display(Name = "Password", ResourceType = typeof(Resources.Resource_userCBCS))]
        public string Password { get; set; } = string.Empty;

        [DataType(DataType.PhoneNumber)]
        [Required(ErrorMessage = "Vui lòng nhập số điện thoại")]
        [Display(Name = "Phone", ResourceType = typeof(Resources.Resource_userCBCS))]
        public string Phone { get; set; } = string.Empty;

        [Required(ErrorMessage = "Vui lòng nhập tên")]
        [StringLength(255, MinimumLength = 3, ErrorMessage = "Độ dài từ 3-255 ký tự")]
        [Display(Name = "Name", ResourceType = typeof(Resources.Resource_userCBCS))]
        public string Name { get; set; } = string.Empty;

        [RegularExpression("^([a-zA-Z0-9_\\-\\.]+)@((\\[[0-9]{1,3}" +
                  @"\.[0-9]{1,3}\.[0-9]{1,3}\.)|(([a-zA-Z0-9\-]+\" +
                  @".)+))([a-zA-Z]{2,4}|[0-9]{1,3})(\]?)$", ErrorMessage = "Địa chỉ Email không hợp lệ")]
        [Display(Name = "Email", ResourceType = typeof(Resources.Resource_userCBCS))]
        public string Email { get; set; } = string.Empty;

        [Display(Name = "Actived", ResourceType = typeof(Resources.Resource_userCBCS))]
        public bool Actived { get; set; }

        [DataType(DataType.Date)]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd}", ApplyFormatInEditMode = true)]
        [Display(Name = "CreatedDate", ResourceType = typeof(Resources.Resource_userCBCS))]
        public DateTime CreatedDate { get; set; }

        [Display(Name = "CreatedUser", ResourceType = typeof(Resources.Resource_userCBCS))]
        public int CreatedUser { get; set; }

        [DataType(DataType.Date)]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd}", ApplyFormatInEditMode = true)]
        [Display(Name = "UpdatedDate", ResourceType = typeof(Resources.Resource_userCBCS))]
        public DateTime UpdatedDate { get; set; }

        [Display(Name = "UpdatedUser", ResourceType = typeof(Resources.Resource_userCBCS))]
        public int UpdatedUser { get; set; }

        [Display(Name = "ListRole", ResourceType = typeof(Resources.Resource_userCBCS))]
        public string ListRole { get; set; } = string.Empty;
    }
}