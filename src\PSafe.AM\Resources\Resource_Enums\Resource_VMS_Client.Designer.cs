﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace PSafe.AM.Resources.Resource_Enums {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class Resource_VMS_Client {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resource_VMS_Client() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("PSafe.AM.Resources.Resource_Enums.Resource_VMS_Client", typeof(Resource_VMS_Client).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Trích xuất, sao lưu.
        /// </summary>
        public static string Audio_Export {
            get {
                return ResourceManager.GetString("Audio_Export", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Toàn quyền.
        /// </summary>
        public static string Audio_FullControl {
            get {
                return ResourceManager.GetString("Audio_FullControl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cho phép bật tắt thủ công.
        /// </summary>
        public static string Audio_Manual {
            get {
                return ResourceManager.GetString("Audio_Manual", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cho phép nghe từ live.
        /// </summary>
        public static string Audio_SpeakLive {
            get {
                return ResourceManager.GetString("Audio_SpeakLive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cho phép nghe từ playback.
        /// </summary>
        public static string Audio_SpeakPlayback {
            get {
                return ResourceManager.GetString("Audio_SpeakPlayback", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cho phép sử dụng bộ nhớ đệm.
        /// </summary>
        public static string Camera_Buffer {
            get {
                return ResourceManager.GetString("Camera_Buffer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Toàn quyền.
        /// </summary>
        public static string Camera_FullControl {
            get {
                return ResourceManager.GetString("Camera_FullControl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cho phép chọn luồng xem.
        /// </summary>
        public static string Camera_Threards {
            get {
                return ResourceManager.GetString("Camera_Threards", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cho phép cấu hình xử lý ảnh.
        /// </summary>
        public static string Camera_VideoAnalysis {
            get {
                return ResourceManager.GetString("Camera_VideoAnalysis", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cho phép xem trực tiếp.
        /// </summary>
        public static string Camera_ViewLive {
            get {
                return ResourceManager.GetString("Camera_ViewLive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cho phép xuất được tất cả các file hỗ trợ.
        /// </summary>
        public static string Export_All {
            get {
                return ResourceManager.GetString("Export_All", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Chỉ xuất được file MP4.
        /// </summary>
        public static string Export_MP4 {
            get {
                return ResourceManager.GetString("Export_MP4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Chỉ xuất được file VMS.
        /// </summary>
        public static string Export_VMS {
            get {
                return ResourceManager.GetString("Export_VMS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cho phép điều khiển PTZ.
        /// </summary>
        public static string ManualPTZ_LockUnlockPTZ {
            get {
                return ResourceManager.GetString("ManualPTZ_LockUnlockPTZ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quản lý các điểm đặt trước (preset).
        /// </summary>
        public static string ManualPTZ_ManagePreset {
            get {
                return ResourceManager.GetString("ManualPTZ_ManagePreset", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cài đặt điểm đặt trước và cấu hình tuần tra.
        /// </summary>
        public static string ManualPTZ_PatrolCamera {
            get {
                return ResourceManager.GetString("ManualPTZ_PatrolCamera", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cho phép xem lại thời gian tối đa cho phép.
        /// </summary>
        public static string Playback_Time {
            get {
                return ResourceManager.GetString("Playback_Time", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cho phép xem lại Không giới hạn.
        /// </summary>
        public static string Playback_Unlimited {
            get {
                return ResourceManager.GetString("Playback_Unlimited", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cho phép xem lại từ database.
        /// </summary>
        public static string PlaybackDatabase {
            get {
                return ResourceManager.GetString("PlaybackDatabase", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cho phép xem lại từ vùng lưu trữ.
        /// </summary>
        public static string PlaybackStorage {
            get {
                return ResourceManager.GetString("PlaybackStorage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cho phép vào chế độ cấu hình.
        /// </summary>
        public static string SetupControl {
            get {
                return ResourceManager.GetString("SetupControl", resourceCulture);
            }
        }
    }
}
