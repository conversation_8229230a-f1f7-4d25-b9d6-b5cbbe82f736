﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Net;
using System.Security.Claims;
using System.Web;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using PSafe.AM.Common;
using PSafe.AM.Models;
using PSafe.Core.Domains;
using PSafe.Core.Interface;
using PSafe.Core.SharedKernel;
using static PSafe.Common.CommonEnums;

namespace PSafe.AM.Controllers
{
    [AuthorizeActionFilter]
    public class CommandCentersController : Controller
    {
        private readonly ICommandCenterRepository _commandCenterRepository;
        private readonly IUserRepository _userRepository;
        private readonly IMapper _mapper;
        private readonly IHistorySystemRepository _historySystemRepository;

        public CommandCentersController(IUnitOfWork unitOfWork, IMapper mapper)
        {
            _commandCenterRepository = unitOfWork.CommandCenterRepository;
            _userRepository = unitOfWork.UserRepository;
            _mapper = mapper;
            _historySystemRepository = unitOfWork.HistorySystemRepository;
    }

        public ActionResult Index()
        {
            List<CommandCenterModel> _listCommandCentersModel = new List<CommandCenterModel>();
            try
            {
                var _listCommandCenters = _commandCenterRepository.GetAll().ToList();

                _listCommandCentersModel = _mapper.Map<List<CommandCenter>, List<CommandCenterModel>>(_listCommandCenters);

                StatusQuery Notification;
                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }
            }
            catch
            {
                StatusQuery Notification = new StatusQuery("error", "Thất bại", "Xem danh sách");

                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;
            }

            return View(_listCommandCentersModel);
        }

        public ActionResult Details(int id)
        {
            try
            {
                var _commandCenterView = _commandCenterRepository.GetById(id);

                var _commandCentersModel = _mapper.Map<CommandCenter, CommandCenterModel>(_commandCenterView);

                try
                {
                    if (TempDataHelper.Get<StatusQuery>(TempData, "Notification") is StatusQuery Notification)
                    {
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;
                    }

                    ViewBag.UpdatedUser = _userRepository.GetById(_commandCentersModel.UpdatedUser).UserName;
                    ViewBag.CreatedUser = _userRepository.GetById(_commandCentersModel.CreatedUser).UserName;
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex.Message);
                }

                if (_commandCentersModel != null)
                {
                    return View(_commandCentersModel);
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "Thất bại", "Xem chi tiết"));

                    return RedirectToAction("Index");
                }
            }
            catch
            {
                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "Thất bại", "Xem chi tiết"));

                return RedirectToAction("Index");
            }
        }

        public ActionResult Create()
        {
            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create([Bind("CenterName, Representative, Address, Email, ContactPhone, Fax, Actived, Description")] CommandCenterModel commandCenterModel)
        {
            StatusQuery Notification;
            if (ModelState.IsValid)
            {
                try
                {
                    var _systemUser = GetSesson();

                    if (_systemUser == null)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại!"));
                        return RedirectToAction("Logout", "Security");
                    }

                    commandCenterModel.CreatedDate = DateTime.Now;
                    commandCenterModel.CreatedUser = _systemUser.Id;
                    commandCenterModel.UpdatedDate = DateTime.Now;
                    commandCenterModel.UpdatedUser = _systemUser.Id;

                    var _commandCentersModel = _mapper.Map<CommandCenterModel, CommandCenter>(commandCenterModel);

                    _commandCenterRepository.Insert(_commandCentersModel);

                    var addStatus = _commandCenterRepository.SaveChanges();

                    if (addStatus > 0)
                    {
                        string StringDescription = new GetStringHistorySystem().Get(_systemUser.UserName, Resources.Resource.Create, _commandCentersModel.CenterName.ToString(), Resources.Resource.CommandCenter);

                        InsertHistorySystem((int)EACTION_TYPE.CREATE, (int)EnumControllerName.COMMANDCENTERS, StringDescription, null, _commandCentersModel);

                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "Thành công", "Thêm mới"));

                        return RedirectToAction("Index");
                    }
                    else
                    {
                        Notification = new StatusQuery("error", "Thất bại!", "Thêm mới");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        return View(commandCenterModel);
                    }
                }
                catch
                {
                    Notification = new StatusQuery("error", "Thất bại!", "Thêm mới");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return View(commandCenterModel);
                }

            }
            Notification = new StatusQuery("warning", "Giá trị nhập vào chưa đúng!", "Thêm mới");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;

            return View(commandCenterModel);
        }

        public ActionResult Edit(int id)
        {
            try
            {
                var _commandCenter = _commandCenterRepository.GetById(id);

                var _commandCentersModel = _mapper.Map<CommandCenter, CommandCenterModel>(_commandCenter);

                if (_commandCentersModel != null)
                {
                    return View(_commandCentersModel);
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "Thất bại", "Xem chi tiết"));

                    return RedirectToAction("Index");
                }
            }
            catch
            {
                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "Thất bại", "Xem chi tiết"));

                return RedirectToAction("Index");
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit([Bind("CommandCenterID, CenterName, Representative, Address, Email, ContactPhone, Fax, Actived, Description")] CommandCenterModel commandCenterModel)
        {
            StatusQuery Notification;
            if (ModelState.IsValid)
            {
                try
                {
                    var _systemUser = GetSesson();

                    if (_systemUser == null)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại!"));
                        return RedirectToAction("Logout", "Security");
                    }

                    var commanCenter = _commandCenterRepository.GetById(commandCenterModel.CommandCenterID);

                    var _commanCenterTemp = _mapper.Map<CommandCenter, CommandCenterModel>(commanCenter);
                    var commanCenterOld = _mapper.Map<CommandCenterModel, CommandCenter>(_commanCenterTemp);

                    commanCenter.UpdatedDate = DateTime.Now;
                    commanCenter.UpdatedUser = _systemUser.Id;
                    commanCenter.CenterName = commandCenterModel.CenterName;
                    commanCenter.Representative = commandCenterModel.Representative;
                    commanCenter.Address = commandCenterModel.Address;
                    commanCenter.Email = commandCenterModel.Email;
                    commanCenter.ContactPhone = commandCenterModel.ContactPhone;
                    commanCenter.Fax = commandCenterModel.Fax;
                    commanCenter.Description = commandCenterModel.Description;
                    commanCenter.Actived = commandCenterModel.Actived;

                    _commandCenterRepository.Update(commanCenter);

                    var updateStatus = _commandCenterRepository.SaveChanges();

                    if (updateStatus > 0)
                    {
                        string StringDescription = new GetStringHistorySystem().Get(_systemUser.UserName, Resources.Resource.Edit, commanCenter.CenterName.ToString(), Resources.Resource.CommandCenter);

                        InsertHistorySystem((int)EACTION_TYPE.EDIT, (int)EnumControllerName.COMMANDCENTERS, StringDescription, commanCenterOld, commanCenter);

                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "Thành công", "Sửa"));

                        return RedirectToAction("Index");
                    }
                    else
                    {
                        Notification = new StatusQuery("error", "Thất bại!", "Sửa");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        return View(commandCenterModel);
                    }
                }
                catch
                {
                    Notification = new StatusQuery("error", "Thất bại!", "Sửa");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return View(commandCenterModel);
                }
            }

            Notification = new StatusQuery("warning", "Giá trị nhập vào chưa đúng!", "Sửa");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;

            return View(commandCenterModel);
        }

        public ActionResult Delete(int id)
        {
            StatusQuery Notification;
            try
            {
                var _commandCenter = _commandCenterRepository.GetById(id);

                var _commandCentersModel = _mapper.Map<CommandCenter, CommandCenterModel>(_commandCenter);

                try
                {
                    ViewBag.UpdatedUser = _userRepository.GetById(_commandCentersModel.UpdatedUser).UserName;
                    ViewBag.CreatedUser = _userRepository.GetById(_commandCentersModel.CreatedUser).UserName; 
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex.Message);
                }

                if (_commandCentersModel == null)
                {
                    Notification = new StatusQuery("warning", "Không tìm thấy đối tương xóa", "Xóa");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return RedirectToAction("Index");
                }

                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }

                return View(_commandCentersModel);
            }
            catch
            {
                Notification = new StatusQuery("warning", "Thất bại", "Xóa");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;

                return RedirectToAction("Index");
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int id)
        {
            try
            {
                var systemUser = GetSesson();

                if (systemUser == null)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại!"));
                    return RedirectToAction("Logout", "Security");
                }

                var commandCenter = _commandCenterRepository.GetById(id);
                _commandCenterRepository.Delete(commandCenter);
                var deleteStatus = _commandCenterRepository.SaveChanges();

                if (deleteStatus > 0)
                {
                    string StringDescription = new GetStringHistorySystem().Get(systemUser.UserName, Resources.Resource.Delete, commandCenter.CenterName.ToString(), Resources.Resource.CommandCenter);

                    InsertHistorySystem((int)EACTION_TYPE.DELETE, (int)EnumControllerName.COMMANDCENTERS, StringDescription, commandCenter, null);

                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "Thành công", "Xóa"));
                    return RedirectToAction("Index");
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "Thất bại!", "Xóa"));
                    return RedirectToAction("Delete", "CommandCenters", new { id });
                }
            }
            catch
            {
                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "Thất bại!", "Xóa"));
                return RedirectToAction("Delete", "CommandCenters", new { id });
            }
        }

        public User GetSesson()
        {
            var SessionUserSystem = HttpContext.Session.GetComplexData<User>("SessionUserSystemId_" + HttpContext.Session.GetString("SessionUserSystemId"));
            if (SessionUserSystem != null)
            {
                var _user = _userRepository.GetById(SessionUserSystem.Id);

                if (_user != null)
                {
                    if (!_user.Actived)
                    {
                        HttpContext.Session.Clear();
                        return null;
                    }
                    return _user;
                }
            }
            return null;
        }

        private void InsertHistorySystem(int action_type, int controllerName, string description, object OldModel, object NewModel)
        {
            IPHostEntry heserver = Dns.GetHostEntry(Dns.GetHostName());
            var ipAddress = heserver.AddressList.FirstOrDefault(p => p.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork).ToString();

            string jsonOldObject = Newtonsoft.Json.JsonConvert.SerializeObject(OldModel);
            string jsonNewObject = Newtonsoft.Json.JsonConvert.SerializeObject(NewModel);

            HistorySystem history = new HistorySystem
            {
                ActionType = action_type,
                ActionTime = DateTime.Now,
                Description = description,
                OldObject = jsonOldObject,
                NewObject = jsonNewObject,
                UserId = int.Parse(HttpContext.Session.GetString("SessionUserSystemId")),
                IpAddress = ipAddress,
                ControllerName = controllerName
            };

            _historySystemRepository.Insert(history);

            _historySystemRepository.SaveChanges();
        }
    }
}
