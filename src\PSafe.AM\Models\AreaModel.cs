﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.ComponentModel.DataAnnotations;

namespace PSafe.AM.Models
{
    public class AreaModel
    {
        #region Properties


        [Key]
        [Display(Name = "AREAID", ResourceType = typeof(Resources.Resource__area))]
        public int AREAID { get; set; }

        [Required(ErrorMessage = "Vui lòng điền tên khu vực")]
        [StringLength(255, MinimumLength = 1, ErrorMessage = "Độ dài từ 1-255 ký tự")]
        [Display(Name = "BRANCHNAME", ResourceType = typeof(Resources.Resource__area))]
        public string BRANCHNAME { get; set; } = string.Empty;

        [DataType(DataType.Date)]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd}", ApplyFormatInEditMode = true)]
        [Display(Name = "REGISTEREDDATE", ResourceType = typeof(Resources.Resource__area))]
        public DateTime? REGISTEREDDATE { get; set; }

        [Display(Name = "ADDRESS", ResourceType = typeof(Resources.Resource__area))]
        public string ADDRESS { get; set; } = string.Empty;

        [DisplayFormat(DataFormatString = "{0:00.000000000}", ApplyFormatInEditMode = true)]
        [Range(-2147483645.999999999999999, 2147483646.999999999999999, ErrorMessage = "Giá trị không hợp lệ")]
        [Display(Name = "LATITUDE", ResourceType = typeof(Resources.Resource__area))]
        public decimal LATITUDE { get; set; }

        [Range(-2147483645.999999999999999, 2147483646.999999999999999, ErrorMessage = "Giá trị không hợp lệ")]
        [DisplayFormat(DataFormatString = "{0:000.000000000}", ApplyFormatInEditMode = true)]
        [Display(Name = "LONGITUDE", ResourceType = typeof(Resources.Resource__area))]
        public decimal LONGITUDE { get; set; }

        [Display(Name = "REPRESENTATIVE", ResourceType = typeof(Resources.Resource__area))]
        public string REPRESENTATIVE { get; set; } = string.Empty;

        [Display(Name = "CONTACTPERSON", ResourceType = typeof(Resources.Resource__area))]
        public string CONTACTPERSON { get; set; } = string.Empty;

        [RegularExpression("^([a-zA-Z0-9_\\-\\.]+)@((\\[[0-9]{1,3}" +
                  @"\.[0-9]{1,3}\.[0-9]{1,3}\.)|(([a-zA-Z0-9\-]+\" +
                  @".)+))([a-zA-Z]{2,4}|[0-9]{1,3})(\]?)$", ErrorMessage = "Địa chỉ Email không hợp lệ")]
        [Display(Name = "EMAIL", ResourceType = typeof(Resources.Resource__area))]
        public string EMAIL { get; set; } = string.Empty;

        [StringLength(11, MinimumLength = 10, ErrorMessage = "Số điện thoại không hợp lệ")]
        [Display(Name = "CONTACTPHONE", ResourceType = typeof(Resources.Resource__area))]
        public string CONTACTPHONE { get; set; } = string.Empty;

        [StringLength(11, MinimumLength = 10, ErrorMessage = "Số điện thoại không hợp lệ")]
        [Display(Name = "MESSAGEPHONE", ResourceType = typeof(Resources.Resource__area))]
        public string MESSAGEPHONE { get; set; } = string.Empty;

        [Display(Name = "FAX", ResourceType = typeof(Resources.Resource__area))]
        public string FAX { get; set; } = string.Empty;

        [Display(Name = "DESCRIPTION", ResourceType = typeof(Resources.Resource__area))]
        public string DESCRIPTION { get; set; } = string.Empty;

        [Display(Name = "ACTIVED", ResourceType = typeof(Resources.Resource__area))]
        public bool ACTIVED { get; set; }

        [DataType(DataType.Date)]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd}", ApplyFormatInEditMode = true)]
        [Display(Name = "CREATEDDATE", ResourceType = typeof(Resources.Resource__area))]
        public DateTime CREATEDDATE { get; set; }

        [Display(Name = "CREATEDUSER", ResourceType = typeof(Resources.Resource__area))]
        public int CREATEDUSER { get; set; }

        [DataType(DataType.Date)]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd}", ApplyFormatInEditMode = true)]
        [Display(Name = "UPDATEDDATE", ResourceType = typeof(Resources.Resource__area))]
        public DateTime UPDATEDDATE { get; set; }

        [Display(Name = "UPDATEDUSER", ResourceType = typeof(Resources.Resource__area))]
        public int UPDATEDUSER { get; set; }

        [Display(Name = "ACTIVEMESSAGEPHONE", ResourceType = typeof(Resources.Resource__area))]
        public bool ACTIVEMESSAGEPHONE { get; set; }

        [Display(Name = "ACTIVEEMAIL", ResourceType = typeof(Resources.Resource__area))]
        public bool ACTIVEEMAIL { get; set; }

        [Display(Name = "COMMANDCENTREID", ResourceType = typeof(Resources.Resource__area))]
        public int? COMMANDCENTREID { get; set; }

        [Display(Name = "AREABORDER", ResourceType = typeof(Resources.Resource__area))]
        public string AREABORDER { get; set; }

        #endregion
    }
}