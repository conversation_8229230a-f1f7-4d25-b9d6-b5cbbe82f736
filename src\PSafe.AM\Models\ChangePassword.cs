﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace PSafe.AM.Models
{
    public class ChangePassword
    {
        private string _userName = string.Empty;
        [Required(ErrorMessage = "<PERSON>ui lòng nhập tên người dùng")]
        [StringLength(255, MinimumLength = 3, ErrorMessage = "Độ dài từ 3-255 ký tự")]
        [Display(Name = "UserName", ResourceType = typeof(Resources.Resource_changePassword))]
        public string UserName { get; set; } = string.Empty;

        private string _oldPassword = string.Empty;
        [DataType(DataType.Password, ErrorMessage = "Mật khẩu không hợp lệ")]
        [Display(Name = "OldPassword", ResourceType = typeof(Resources.Resource_changePassword))]
        public string OldPassword { get; set; } = string.Empty;

        private string _newPassword = string.Empty;
        [Required(ErrorMessage = "M<PERSON>t khẩu không hợp lệ")]
        [DataType(DataType.Password, ErrorMessage = "Mật khẩu không hợp lệ")]
        [StringLength(255, MinimumLength = 3, ErrorMessage = "Độ dài từ 3-255 ký tự")]
        [Display(Name = "NewPassword", ResourceType = typeof(Resources.Resource_changePassword))]
        public string NewPassword { get; set; } = string.Empty;

        private string _repeatpassword = string.Empty;
        [Compare("NewPassword", ErrorMessage = "'Mật khẩu' và 'Xác nhận mật khẩu' không khớp, Vui lòng nhập lại")]
        [DataType(DataType.Password, ErrorMessage = "Mật khẩu không hợp lệ")]
        [StringLength(255, MinimumLength = 3, ErrorMessage = "Độ dài từ 3-255 ký tự")]
        [Display(Name = "Repeatpassword", ResourceType = typeof(Resources.Resource_changePassword))]
        public string Repeatpassword { get; set; } = string.Empty;

        public bool UsesIsLogging { get; set; }
    }
}
