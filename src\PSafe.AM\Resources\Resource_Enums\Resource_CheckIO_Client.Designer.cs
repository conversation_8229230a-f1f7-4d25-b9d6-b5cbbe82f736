﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace PSafe.AM.Resources.Resource_Enums {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class Resource_CheckIO_Client {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resource_CheckIO_Client() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("PSafe.AM.Resources.Resource_Enums.Resource_CheckIO_Client", typeof(Resource_CheckIO_Client).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kích hoạt thẻ mới.
        /// </summary>
        public static string ACTIVE_NEW_CARD {
            get {
                return ResourceManager.GetString("ACTIVE_NEW_CARD", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thêm thông báo mất thẻ.
        /// </summary>
        public static string ADD_CARD_LOST_INFO {
            get {
                return ResourceManager.GetString("ADD_CARD_LOST_INFO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thêm khách thường xuyên.
        /// </summary>
        public static string ADD_FREQUENT_GUEST {
            get {
                return ResourceManager.GetString("ADD_FREQUENT_GUEST", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thêm nhân viên nội bộ.
        /// </summary>
        public static string ADD_INTERNAL_EMPLOYEE {
            get {
                return ResourceManager.GetString("ADD_INTERNAL_EMPLOYEE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thêm khách vãng lai.
        /// </summary>
        public static string ADD_NONRESIDENT_GUEST {
            get {
                return ResourceManager.GetString("ADD_NONRESIDENT_GUEST", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thêm vi phạm an ninh.
        /// </summary>
        public static string ADD_SECURITY_VIOLATION {
            get {
                return ResourceManager.GetString("ADD_SECURITY_VIOLATION", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cho phép sửa các thông tin trừ thay đổi loại phương tiện, Cổng vào ra.
        /// </summary>
        public static string ALLOW_EDIT_INFORMATION {
            get {
                return ResourceManager.GetString("ALLOW_EDIT_INFORMATION", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cấp phép khách thường xuyên.
        /// </summary>
        public static string ALLOW_LICENSING_FREQUENT_GUEST {
            get {
                return ResourceManager.GetString("ALLOW_LICENSING_FREQUENT_GUEST", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Backup, restore.
        /// </summary>
        public static string BACKUP_RESTORE {
            get {
                return ResourceManager.GetString("BACKUP_RESTORE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hủy cấm vào ra.
        /// </summary>
        public static string CANCEL_FORBID_INOUT {
            get {
                return ResourceManager.GetString("CANCEL_FORBID_INOUT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hủy quyền ra vào nhân viên nội bộ nghỉ việc.
        /// </summary>
        public static string CANCEL_INOUT_INTERNAL_EMPLOYEE {
            get {
                return ResourceManager.GetString("CANCEL_INOUT_INTERNAL_EMPLOYEE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hủy thẻ/vân tay/khuôn mặt khách thường xuyên.
        /// </summary>
        public static string CANCEL_LICENSING_FREQUENT_GUEST {
            get {
                return ResourceManager.GetString("CANCEL_LICENSING_FREQUENT_GUEST", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hủy cấp phép vào ra khu trọng yếu.
        /// </summary>
        public static string CANCEL_LICENSING_INOUT_SIGNIFICANT_AREA {
            get {
                return ResourceManager.GetString("CANCEL_LICENSING_INOUT_SIGNIFICANT_AREA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hủy thẻ/vân tay/khuôn mặt nhân viên nội bộ.
        /// </summary>
        public static string CANCEL_LICENSING_INTERNAL_EMPLOYEE {
            get {
                return ResourceManager.GetString("CANCEL_LICENSING_INTERNAL_EMPLOYEE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hủy thẻ/vân tay/khuôn mặt khách vãng lai.
        /// </summary>
        public static string CANCEL_LICENSING_NONRESIDENT_GUEST {
            get {
                return ResourceManager.GetString("CANCEL_LICENSING_NONRESIDENT_GUEST", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hủy tạm ngưng ra vào.
        /// </summary>
        public static string CANCEL_TEMPORARY_STOP_INOUT {
            get {
                return ResourceManager.GetString("CANCEL_TEMPORARY_STOP_INOUT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thay đổi loại khách hàng.
        /// </summary>
        public static string CHANGE_CUSTOMER_TYPE {
            get {
                return ResourceManager.GetString("CHANGE_CUSTOMER_TYPE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Xóa thẻ.
        /// </summary>
        public static string DELETE_CARD {
            get {
                return ResourceManager.GetString("DELETE_CARD", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Xóa khách thường xuyên.
        /// </summary>
        public static string DELETE_FREQUENT_GUEST {
            get {
                return ResourceManager.GetString("DELETE_FREQUENT_GUEST", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Xóa nhân viên nội bộ.
        /// </summary>
        public static string DELETE_INTERNAL_EMPLOYEE {
            get {
                return ResourceManager.GetString("DELETE_INTERNAL_EMPLOYEE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Xóa khách vãng lai.
        /// </summary>
        public static string DELETE_NONRESIDENT_GUEST {
            get {
                return ResourceManager.GetString("DELETE_NONRESIDENT_GUEST", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Xóa vi phạm an ninh.
        /// </summary>
        public static string DELETE_SECURITY_VIOLATION {
            get {
                return ResourceManager.GetString("DELETE_SECURITY_VIOLATION", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mở xả, đóng hoàn toàn barrier.
        /// </summary>
        public static string DISABLE_BARRIER {
            get {
                return ResourceManager.GetString("DISABLE_BARRIER", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sửa khách thường xuyên.
        /// </summary>
        public static string EDIT_FREQUENT_GUEST {
            get {
                return ResourceManager.GetString("EDIT_FREQUENT_GUEST", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sửa nhân viên nội bộ.
        /// </summary>
        public static string EDIT_INTERNAL_EMPLOYEE {
            get {
                return ResourceManager.GetString("EDIT_INTERNAL_EMPLOYEE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sửa khách vãng lai.
        /// </summary>
        public static string EDIT_NONRESIDENT_GUEST {
            get {
                return ResourceManager.GetString("EDIT_NONRESIDENT_GUEST", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cấm vào ra.
        /// </summary>
        public static string FORBID_INOUT {
            get {
                return ResourceManager.GetString("FORBID_INOUT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mở barier cưỡng bức.
        /// </summary>
        public static string FORCE_OPEN_BARRIER {
            get {
                return ResourceManager.GetString("FORCE_OPEN_BARRIER", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Import danh sách khách vãng lai.
        /// </summary>
        public static string IMPORT_FREQUENT_CURRENT {
            get {
                return ResourceManager.GetString("IMPORT_FREQUENT_CURRENT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Import danh sách khách thường xuyên.
        /// </summary>
        public static string IMPORT_FREQUENT_GUEST {
            get {
                return ResourceManager.GetString("IMPORT_FREQUENT_GUEST", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Import danh sách khách nhân viên.
        /// </summary>
        public static string IMPORT_FREQUENT_REGULAR {
            get {
                return ResourceManager.GetString("IMPORT_FREQUENT_REGULAR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cấp thẻ/vân tay/khuôn mặt khách thường xuyên.
        /// </summary>
        public static string LICENSING_FREQUENT_GUEST {
            get {
                return ResourceManager.GetString("LICENSING_FREQUENT_GUEST", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cấp phép vào ra khu trọng yếu.
        /// </summary>
        public static string LICENSING_INOUT_SIGNIFICANT_AREA {
            get {
                return ResourceManager.GetString("LICENSING_INOUT_SIGNIFICANT_AREA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cấp thẻ/vân tay/khuôn mặt nhân viên nội bộ.
        /// </summary>
        public static string LICENSING_INTERNAL_EMPLOYEE {
            get {
                return ResourceManager.GetString("LICENSING_INTERNAL_EMPLOYEE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cấp thẻ/vân tay/khuôn mặt khách vãng lai.
        /// </summary>
        public static string LICENSING_NONRESIDENT_GUEST {
            get {
                return ResourceManager.GetString("LICENSING_NONRESIDENT_GUEST", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hồi phục thẻ báo mất.
        /// </summary>
        public static string RESTORE_CARD_LOST {
            get {
                return ResourceManager.GetString("RESTORE_CARD_LOST", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cấu hình chung thiết bị.
        /// </summary>
        public static string SETUP_GENERAL_DEVICE {
            get {
                return ResourceManager.GetString("SETUP_GENERAL_DEVICE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cấu hình chung tính năng.
        /// </summary>
        public static string SETUP_GENERAL_FEATURE {
            get {
                return ResourceManager.GetString("SETUP_GENERAL_FEATURE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cấu hình thiết bị từng làn.
        /// </summary>
        public static string SETUP_LANE_DEVICE {
            get {
                return ResourceManager.GetString("SETUP_LANE_DEVICE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cấu hình làn cho phép chọn loại khách vào ra.
        /// </summary>
        public static string SETUP_LANE_INOUT_OBJECT {
            get {
                return ResourceManager.GetString("SETUP_LANE_INOUT_OBJECT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cấu hình hệ thống.
        /// </summary>
        public static string SETUP_SYS {
            get {
                return ResourceManager.GetString("SETUP_SYS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tạm ngưng ra vào.
        /// </summary>
        public static string TEMPORARY_STOP_INOUT {
            get {
                return ResourceManager.GetString("TEMPORARY_STOP_INOUT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cập nhật danh mục.
        /// </summary>
        public static string UPDATE_LIST_INFO {
            get {
                return ResourceManager.GetString("UPDATE_LIST_INFO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Xem danh sách thông báo mất thẻ.
        /// </summary>
        public static string VIEW_LIST_CARD_LOST {
            get {
                return ResourceManager.GetString("VIEW_LIST_CARD_LOST", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Xem log.
        /// </summary>
        public static string VIEW_LOG {
            get {
                return ResourceManager.GetString("VIEW_LOG", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Xem khách hàng vảng lai.
        /// </summary>
        public static string VIEWCUSTOMERSCIRCULATE {
            get {
                return ResourceManager.GetString("VIEWCUSTOMERSCIRCULATE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Xem khách hàng thường xuyên.
        /// </summary>
        public static string VIEWCUSTOMERSREGULAR {
            get {
                return ResourceManager.GetString("VIEWCUSTOMERSREGULAR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Xem nhân viên nội bộ.
        /// </summary>
        public static string VIEWINTERNALSTAFF {
            get {
                return ResourceManager.GetString("VIEWINTERNALSTAFF", resourceCulture);
            }
        }
    }
}
