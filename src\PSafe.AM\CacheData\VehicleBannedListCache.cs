﻿using Microsoft.Extensions.Logging;
using PSafe.Core.Domains;
using PSafe.Core.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;

namespace PSafe.AM.CacheData
{
    public class VehicleBannedListCache
    {
        private object _lock = new object();
        private List<VehicleBannedList> vehicleBannedLists = null;
        public VehicleBannedListCache()
        {
            StartThreadSyncData();
        }

        private void StartThreadSyncData()
        {
            Thread thread = new Thread(StartThreadSyncDataProc);
            thread.IsBackground = true;
            thread.Start();
        }

        private void StartThreadSyncDataProc()
        {
            while(true)
            {
                Thread.Sleep(60000 * 720); //12 hours
                lock (_lock)
                    vehicleBannedLists = null;
            }
        }

        public List<VehicleBannedList> Get(IVehicleBannedListRepository vehicleBannedListRepository, ILogger<object> logger)
        {
            try
            {
                lock (_lock)
                {
                    if (vehicleBannedLists == null)
                        vehicleBannedLists = vehicleBannedListRepository.Get(null, null, "VehicleBannedListHistorys").ToList();
                    return vehicleBannedLists;
                }
            }
            catch(Exception ex)
            {
                logger.LogError("VehicleBannedListCache/Get: " + ex.Message);
                return new List<VehicleBannedList>();
            }
        }

        public void Update(int vehicleBannedListId, IVehicleBannedListRepository vehicleBannedListRepository, ILogger<object> logger)
        {
            lock (_lock)
            {
                try
                {
                    if (vehicleBannedLists == null) return;
                    var vehicleBannedList = vehicleBannedLists.Find(x => x.Id == vehicleBannedListId);
                    if (vehicleBannedList != null) vehicleBannedLists.Remove(vehicleBannedList);
                    var _vehicleBannedList = vehicleBannedListRepository.Get(x => x.Id == vehicleBannedListId, null, "VehicleBannedListHistorys").FirstOrDefault();
                    if (_vehicleBannedList == null) return;
                    vehicleBannedLists.Insert(0, _vehicleBannedList);
                }
                catch (Exception ex)
                {
                    logger.LogError("VehicleBannedListCache/Update: " + ex.Message);
                    vehicleBannedLists = null;
                }
            }
        }

        public void Delete(int vehicleBannedListId, ILogger<object> logger)
        {
            lock (_lock)
            {
                try
                {
                    if (vehicleBannedLists == null) return;
                    var vehicleBannedList = vehicleBannedLists.Find(x => x.Id == vehicleBannedListId);
                    if (vehicleBannedList != null) vehicleBannedLists.Remove(vehicleBannedList);
                }
                catch (Exception ex)
                {
                    logger.LogError("VehicleBannedListCache/Delete: " + ex.Message);
                    vehicleBannedLists = null;
                }
            }
        }

        public List<VehicleBannedListHistory> GetHistorys(int vehicleBannedListId, IVehicleBannedListHistoryRepository _vehicleBannedListHistoryRepository)
        {
            lock (_lock)
            {
                try
                {
                    if (vehicleBannedLists == null) return _vehicleBannedListHistoryRepository.GetBy(x => x.VehicleBannedListId == vehicleBannedListId).OrderByDescending(x => x.Id).ToList();
                    var vehicleBannedList = vehicleBannedLists.Find(x => x.Id == vehicleBannedListId);
                    if (vehicleBannedList == null) return _vehicleBannedListHistoryRepository.GetBy(x => x.VehicleBannedListId == vehicleBannedListId).OrderByDescending(x => x.Id).ToList();
                    return vehicleBannedList.VehicleBannedListHistorys.OrderByDescending(x => x.Id).ToList();
                }
                catch 
                {
                    return _vehicleBannedListHistoryRepository.GetBy(x => x.VehicleBannedListId == vehicleBannedListId).OrderByDescending(x => x.Id).ToList();
                }
            }
        }

        private static VehicleBannedListCache _instance = null;
        public static VehicleBannedListCache Instance
        {
            get 
            {
                if (_instance == null)
                    _instance = new VehicleBannedListCache();
                return _instance; 
            }
            
        }
    }
}
