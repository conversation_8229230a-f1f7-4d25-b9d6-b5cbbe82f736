﻿using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace PSafe.AM.Models
{
    public class TypeOfMarkerModel
    {
        [Key]
        [Display(Name = "Id", ResourceType = typeof(Resources.Resource_typeofmarker))]
        public int Id { get; set; }

        [Required(ErrorMessage = "Vui lòng điền tên")]
        [StringLength(255, MinimumLength = 1, ErrorMessage = "Độ dài từ 1-255 ký tự")]
        [Display(Name = "Name", ResourceType = typeof(Resources.Resource_typeofmarker))]
        public string Name { get; set; }

        [Required(ErrorMessage = "Vui lòng chọn icon")]
        [Display(Name = "Icon", ResourceType = typeof(Resources.Resource_typeofmarker))]
        public string Icon { get; set; }

        [Display(Name = "Description", ResourceType = typeof(Resources.Resource_typeofmarker))]
        public string Description { get; set; }

        public IFormFile ImageFile { get; set; }
    }
}
