﻿@using PSafe.AM.Models
@model PSafe.AM.Models.AreaModel

@{
    ViewBag.Title = "Hiệu chỉnh";
}

<environment names="Development">
    <script src="~/lib/jquery/dist/jquery.js"></script>
    <script src="~/js/script.js" asp-append-version="true"></script>
</environment>
<environment names="Staging,Production">
    <script src="~/lib/jquery/dist/jquery.js"></script>
    <script src="~/js/script.js" asp-append-version="true"></script>
</environment>

<script language="javascript" type="text/javascript">
    function openChild(file, window) {
        childWindow = open(file, window, 'resizable=no,width=700,height=400,scrollbars,resizable,toolbar,status');
        if (childWindow.opener == null) childWindow.opener = self;
    }
</script>

<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-lg-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>@PSafe.AM.Resources.Resource.Edit @PSafe.AM.Resources.Resource.Area</h5>
                    <div class="ibox-tools">
                        @Html.ActionLink(PSafe.AM.Resources.Resource.BackToList, "Index", "Areas", null, new { @class = "btn btn-primary btn-xs" })
                    </div>
                </div>
                <div class="ibox-content">

                    @using (Html.BeginForm())
                    {
                        @Html.AntiForgeryToken()
                        @Html.ValidationSummary(true)

                        @Html.HiddenFor(model => model.AREAID)

                    <div class="row">
                        <div class="col-lg-6">
                            <div class="form-group row">
                                <label class="control-label col-md-4">@Html.LabelFor(model => model.BRANCHNAME) (*)</label>
                                <div class="col-md-8">
                                    @Html.EditorFor(model => model.BRANCHNAME)
                                    @Html.ValidationMessageFor(model => model.BRANCHNAME, null, new { @class = "text-danger" })
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-6">
                            <div class="form-group row">
                                <label class="control-label col-md-4">@Html.LabelFor(model => model.LONGITUDE) (*)</label>
                                <div class="col-md-8">
                                    @Html.EditorFor(model => model.LONGITUDE)
                                    @Html.ValidationMessageFor(model => model.LONGITUDE, null, new { @class = "text-danger" })
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                @Html.LabelFor(model => model.REPRESENTATIVE, new { @class = "control-label col-md-4" })
                                <div class="col-md-8">
                                    @Html.EditorFor(model => model.REPRESENTATIVE)
                                    @Html.ValidationMessageFor(model => model.REPRESENTATIVE, null, new { @class = "text-danger" })
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                <label class="control-label col-md-4">@Html.LabelFor(model => model.LATITUDE) (*)</label>
                                <div class="col-md-5">
                                    @Html.EditorFor(model => model.LATITUDE)
                                    @Html.ValidationMessageFor(model => model.LATITUDE, null, new { @class = "text-danger" })
                                </div>
                                <div class="col-md-3">
                                    <input type="button" class="btn btn-primary btn-xs" btn- value="Lấy tọa độ" onClick="openChild('/static/GetLatlon.htm','win2')" />
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                @Html.LabelFor(model => model.CONTACTPERSON, new { @class = "control-label col-md-4" })
                                <div class="col-md-8">
                                    @Html.EditorFor(model => model.CONTACTPERSON)
                                    @Html.ValidationMessageFor(model => model.CONTACTPERSON, null, new { @class = "text-danger" })
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                @Html.LabelFor(model => model.CONTACTPHONE, new { @class = "control-label col-md-4" })
                                <div class="col-md-8">
                                    @Html.EditorFor(model => model.CONTACTPHONE)
                                    @Html.ValidationMessageFor(model => model.CONTACTPHONE, null, new { @class = "text-danger" })
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                @Html.LabelFor(model => model.EMAIL, new { @class = "control-label col-md-4" })
                                <div class="col-md-8">
                                    @Html.EditorFor(model => model.EMAIL)
                                    @Html.ValidationMessageFor(model => model.EMAIL, null, new { @class = "text-danger" })
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-6">
                            <div class="form-group row">
                                @Html.LabelFor(model => model.MESSAGEPHONE, new { @class = "control-label col-md-4" })
                                <div class="col-md-8">
                                    @Html.EditorFor(model => model.MESSAGEPHONE)
                                    @Html.ValidationMessageFor(model => model.MESSAGEPHONE, null, new { @class = "text-danger" })
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                @Html.LabelFor(model => model.FAX, new { @class = "control-label col-md-4" })
                                <div class="col-md-8">
                                    @Html.EditorFor(model => model.FAX)
                                    @Html.ValidationMessageFor(model => model.FAX, null, new { @class = "text-danger" })
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                @Html.LabelFor(model => model.DESCRIPTION, new { @class = "control-label col-md-4" })
                                <div class="col-md-8">
                                    @Html.TextAreaFor(model => model.DESCRIPTION, new { @class = "form-control" })
                                    @Html.ValidationMessageFor(model => model.DESCRIPTION, null, new { @class = "text-danger" })
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-12">
                            <div class="form-group row">
                                @Html.LabelFor(model => model.AREABORDER, new { @class = "control-label col-md-2" })
                                <div class="col-md-8">
                                    @Html.TextBoxFor(model => model.AREABORDER, new { @class = "form-control", @readonly = true })
                                </div>
                                <div class="col-md-2">
                                    <button class="btn btn-primary btn-xs" type="button" id="drawAreaBorderBtn">Vẽ bản đồ</button>
                                </div>

                            </div>
                        </div>

                    </div>
                        <div class="row">
                            <div class="col-md-offset-2 col-md-10">
                                <input type="submit" value="@PSafe.AM.Resources.Resource.Save" class="btn btn-primary" />
                                @Html.ActionLink(PSafe.AM.Resources.Resource.Cancel, "Index", "Areas", null, new { @class = "btn btn-white" })
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>Quản lý @PSafe.AM.Resources.Resource.Location</h5>
                    <div class="ibox-tools">
                        <button type="button" class="btn btn-primary btn-xs" data-toggle="modal" data-url="@Url.Action("CreateLocation","Areas")" id="btnCreateLocation">
                            <span class="glyphicon glyphicon-new-window" aria-hidden="true"></span>  @PSafe.AM.Resources.Resource.AddItem
                        </button>
                        
                    </div>
                </div>
                <div class="ibox-content table-responsive">
                    <div class="row"><table id="tblLocation" class="table table-striped table-bordered table-hover dataTables-list"></table></div>

                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal inmodal fade" id="createLocationModal" tabindex="-1" role="dialog" aria-labelledby="CreateLocationModal" aria-hidden="true" data-backdrop="static">
    <div id="createLocationContainer">
    </div>
</div>

<div class="modal inmodal fade" id="editLocationModal" tabindex="-1" role="dialog" aria-labelledby="EditLocationModal" aria-hidden="true" data-backdrop="static">
    <div id="editLocationContainer">
    </div>
</div>

<div class="modal inmodal fade" id="deleteLocationModal" tabindex="-1" role="dialog" aria-labelledby="DeleteLocationModal" aria-hidden="true" data-backdrop="static">
    <div id="deleteLocationContainer">
    </div>
</div>

<div class="modal inmodal fade" id="localMapModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header" style="padding:10px 15px">
                <button type="button" class="close" data-dismiss="modal" style="margin: -0.5rem -1rem -1rem auto">&times;</button>
                <h4 class="modal-title">Modal Header</h4>
            </div>
            <div class="modal-body">
                <iframe frameborder="0" title=""></iframe>
                <div>
                    <div class="text-center" style="margin:15% auto">Đang tải...</div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <environment names="Development,Staging,Production">
        <link rel="stylesheet" href="~/lib/datatables/datatables.min.css" />
        <link rel="stylesheet" href="~/lib/toastr/toastr.min.css" />
        <link rel="stylesheet" href="~/lib/datatables/datatables.min.css" />
        <link rel="stylesheet" href="~/lib/toastr/toastr.min.css" />
    </environment>

    <environment names="Development">
        <link rel="stylesheet" href="~/lib/chosen/bootstrap-chosen.css" />
    </environment>
    <environment names="Staging,Production">
        <link rel="stylesheet" href="~/lib/chosen/bootstrap-chosen.css" />
    </environment>
}

@section Scripts {
    <environment names="Development">
        <script src="~/lib/chosen/chosen.jquery.js"></script>
    </environment>

    <environment names="Development,Staging,Production">
        <script src="~/lib/validate/jquery.validate.min.js"></script>
        <script src="~/lib/dataTables/datatables.min.js"></script>
        <script src="~/lib/dataTables/dataTables.bootstrap4.min.js"></script>
        <script src="~/lib/toastr/toastr.js"></script>
        <script src="~/lib/toastr/toastr.min.js"></script>
        <script src="~/lib/chosen/chosen.jquery.js"></script>
    </environment>

    @if (!string.IsNullOrEmpty(ViewBag.Type) && ViewBag.Type == "warning")
    {
        <script type="text/javascript">
            toastr.warning('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Status) && ViewBag.Type == "error")
    {
        <script type="text/javascript">
            toastr.error('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Status) && ViewBag.Type == "success")
    {
        <script type="text/javascript">
            toastr.success('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }

    <script type="text/javascript">
        var locationListVM;
        var localMapUrl = '@ViewBag.linkLocalMaps';
        $(document).ready(function() {
            var areaId = $('#AREAID').val();

         locationListVM = {
                dt: null,

                init: function() {
                    dt = $('#tblLocation').DataTable({
                        "serverSide": false,
                        "ajax": {
                            "url": "@Url.Action("LocationList", "Areas")",
                            "dataType": 'json',
                            "contentType": "application/json; charset=utf-8",
                            "type": "GET",
                            "data": {
                                areaId: areaId
                            },
                            "dataSrc": ''

                        },
                        "autoWidth": false,
                        "columns": [
                            { "title": "Tên", "data": "locationName", "searchable": true },
                            { "title": "Bản đồ", "data": "map", "searchable": true },
                            { "title": "Mô tả", "data": "description", "searchable": true },
                            {
                                "title": "Hành động",
                                "data": "id",
                                "searchable": false,
                                "sortable": false,
                                "render": function (data, type, full, meta) {
                                    return '<a onclick="return openLocalMap(' + data + ',\'' + full.locationName + '\');" href="@ViewBag.linkLocalMaps' + data +'" target="_blank" class="btn btn-primary btn-sm">Bản đồ</a> ' +
                                        '<a href="@Url.Action("EditLocation", "Areas")?id=' +
                                        data +
                                        '" class="btn btn-white btn-sm editLocation">Sửa</a> ' +
                                        '<a href="@Url.Action("DeleteLocation", "Areas")?id=' +
                                        data +
                                        '" class="btn btn-danger btn-sm deleteLocation">Xóa</a> ';
                                }
                            }
                        ],
                        "oLanguage": {
                            "sProcessing": "Đang xử lý...",
                            "sLengthMenu": "Xem _MENU_ mục",
                            "sZeroRecords": "Không tìm thấy dòng nào phù hợp",
                            "sInfo": "Đang xem _START_ đến _END_ trong tổng số _TOTAL_ mục",
                            "sInfoEmpty": "Đang xem 0 đến 0 trong tổng số 0 mục",
                            "sInfoFiltered": "(được lọc từ _MAX_ mục)",
                            "sInfoPostFix": "",
                            "sSearch": "Tìm:",
                            "sUrl": "",
                            "oPaginate": {
                                "sFirst": "Đầu",
                                "sPrevious": "Trước",
                                "sNext": "Tiếp",
                                "sLast": "Cuối"
                            }
                        },
                    });
                },

                refresh: function() {
                    dt.ajax.reload();
                }
            }

            locationListVM.init();

            $("#btnCreateLocation").on("click", function () {
                var url = $(this).data("url") + '?areaId=' + areaId;
                $.get(url, function (data) {
                    $('#createLocationContainer').html(data);

                    $('#createLocationModal').modal('show');
                });
            });

            $('#tblLocation').on("click", ".editLocation", function (event) {
                event.preventDefault();

                var url = $(this).attr("href");

                $.get(url, function (data) {
                    $('#editLocationContainer').html(data);
                    $('#editLocationModal').modal('show');
                });
            });

            $('#tblLocation').on("click", ".deleteLocation", function (event) {
                event.preventDefault();

                var url = $(this).attr("href");

                $.get(url, function (data) {
                    $('#deleteLocationContainer').html(data);
                   $('#deleteLocationModal').modal('show');
                });
            });

            $('#localMapModal').on('hidden.bs.modal', function (e) {
                window.removeEventListener('message', closeLocalMapModalMessage);
                $('#localMapModal .modal-body iframe').attr('src', '');
            });

             $("#drawAreaBorderBtn").on("click", function () {
                 var lon = $.trim($("#LONGITUDE").val());
                 lon = lon || "";
                 var lat = $.trim($("#LATITUDE").val());
                 lat = lat || "";

                 var areaBorderData = $.trim($("#AREABORDER").val());
                 areaBorderData = areaBorderData.replace(/\[/g, "sdfqww");
                 areaBorderData = areaBorderData.replace(/\]/g, "sdfqdd");

                 var title = encodeURI($.trim($("#BRANCHNAME").val()));
                 openChild('/static/DrawArea.htm?lon=' + lon + "&lat=" + lat + "&data=" + areaBorderData + "&title=" + title, 'win3');
            });
        });

        /**** Open - CLose LocalMap Modal ****/
        function openLocalMap(locationId, locationName) {
            $('#localMapModal .modal-dialog').css({
                maxWidth: document.body.offsetWidth - 50
            });
            $('#localMapModal .modal-title').html(locationName);
            $('#localMapModal .modal-body').css("height", document.body.offsetHeight - 130);
            var cssStyle = {
                position: 'absolute',
                width: '100%',
                height: '100%',
                top: 0,
                left: 0
            };
            $('#localMapModal .modal-body > div').css(cssStyle).show();
            $('#localMapModal .modal-body iframe').css(cssStyle).attr("src", localMapUrl + locationId).on("load", function () {
                $('#localMapModal .modal-body > div').hide();
                window.addEventListener('message', closeLocalMapModalMessage);
                this.contentWindow.postMessage('', "*");
            });
            $('#localMapModal').modal('show');
            return false;
        }

        function closeLocalMapModalMessage(e) {
            $('#localMapModal').modal('hide');
        }

        /**** Create Location Ajax Form CallBack ********/

        function CreateLocationSuccess(data) {
            if (data != "success") {
                $('#createLocationContainer').html(data);
                return;
            }

            $('#createLocationModal').modal('hide');
            $('#createLocationContainer').html("");
            locationListVM.refresh();
        }

        /**** Edit Location Ajax Form CallBack ********/

        function UpdateLocationSuccess(data) {

            if (data != "success") {
                $('#editLocationContainer').html(data);
                return;
            }
            $('#editLocationModal').modal('hide');
            $('#editLocationContainer').html("");
            locationListVM.refresh();

        }

        /**** Delet Location Ajax Form CallBack ********/

        function DeleteLocationSuccess(data) {

            if (data != "success") {
                $('#deleteLocationContainer').html(data);
                return;
            }
            $('#deleteLocationModal').modal('hide');
            $('#deleteLocationContainer').html("");
            locationListVM.refresh();

        }

        /**
         * cập nhật lại bản đồ cục bộ
         */
        function updateAreaBorderData(data) {
            $("#AREABORDER").val(data);
        }
    </script>
}
