﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Transactions;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using PSafe.AM.Common;
using PSafe.AM.Models;
using PSafe.Core.Domains;
using PSafe.Core.Interface;
using PSafe.Core.SharedKernel;
using static PSafe.Common.CommonEnums;

namespace PSafe.AM.Controllers
{
    [AuthorizeActionFilter]
    public class GroupsController : Controller
    {
        private readonly IGroupRepository _groupRepository;
        private readonly ISystemUserRepository _systemUserRepository;
        private readonly IMapper _mapper;
        private readonly IHistorySystemRepository _historySystemRepository;

        public GroupsController(IUnitOfWork unitOfWork, IMapper mapper)
        {
            _groupRepository = unitOfWork.GroupRepository;
            _systemUserRepository = unitOfWork.SystemUserRepository;
            _mapper = mapper;
            _historySystemRepository = unitOfWork.HistorySystemRepository;
        }
        // GET: /Groups/

        public ActionResult Index()
        {
            StatusQuery Notification;
            List<GroupModel> _listGroupModel = new List<GroupModel>();
            try
            {
                var _listGroup = _groupRepository.GetAll().ToList();

                _listGroupModel = _mapper.Map<List<Group>, List<GroupModel>>(_listGroup);

                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }
            }
            catch
            {
                Notification = new StatusQuery("error", "", "Xem danh sách thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;
            }

            return View(_listGroupModel);
        }

        // GET: /Groups/Details/5
        public ActionResult Details(int id)
        {
            try
            {
                var _group = _groupRepository.GetById(id);

                var _groupModel = _mapper.Map<Group, GroupModel>(_group);

                if (TempDataHelper.Get<StatusQuery>(TempData, "Notification") is StatusQuery Notification)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }

                if (_groupModel != null)
                {
                    return View(_groupModel);
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                    return RedirectToAction("Index");
                }
            }
            catch
            {
                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                return RedirectToAction("Index");
            }
        }

        // GET: /Groups/Create
        public ActionResult Create()
        {
            return View();
        }

        // POST: /Groups/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create([Bind("GroupID, GroupName, GroupDescription")] GroupModel groupModel)
        {
            StatusQuery Notification;
            if (ModelState.IsValid)
            {
                try
                {
                    var _group = _mapper.Map<GroupModel, Group>(groupModel);

                    _groupRepository.Insert(_group);

                    var addStatus = _groupRepository.SaveChanges();

                    if (addStatus > 0)
                    {
                        var systemUser = GetSesson();

                        if (systemUser == null)
                        {
                            TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                            return RedirectToAction("Logout", "Security");
                        }

                        string StringDescription = new GetStringHistorySystem().Get(systemUser.UserName, Resources.Resource.Create, _group.GroupName.ToString(), Resources.Resource.Group);

                        InsertHistorySystem((int)EACTION_TYPE.CREATE, (int)EnumControllerName.GROUP, StringDescription, null, _group);

                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Thêm mới thành công"));

                        return RedirectToAction("Index");
                    }
                    else
                    {
                        Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        return View(groupModel);
                    }
                }
                catch
                {
                    Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return View(groupModel);
                }

            }
            Notification = new StatusQuery("warning", "", "TVui lòng kiểm tra lại");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;

            return View(groupModel);
        }

        // GET: /Groups/Edit/5
        public ActionResult Edit(int id)
        {
            try
            {
                var _group = _groupRepository.GetById(id);

                var _groupModel = _mapper.Map<Group, GroupModel>(_group);

                if (_groupModel != null)
                {
                    return View(_groupModel);
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                    return RedirectToAction("Index");
                }
            }
            catch
            {
                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                return RedirectToAction("Index");
            }
        }

        // POST: /Groups/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit([Bind("GroupID, GroupName, GroupDescription")] GroupModel groupModel)
        {
            StatusQuery Notification;
            if (ModelState.IsValid)
            {
                try
                {
                    var group = _groupRepository.GetById(groupModel.GroupID);

                    var _groupTemp = _mapper.Map<Group, GroupModel>(group);
                    var groupOld = _mapper.Map<GroupModel, Group>(_groupTemp);

                    if (group == null)
                    {
                        Notification = new StatusQuery("warning", "", "Nhóm không tồn tại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        return View(groupModel);
                    }

                    group.GroupName = groupModel.GroupName;
                    group.Description = groupModel.GroupDescription;

                    _groupRepository.Update(group);

                    var updateStatus = _groupRepository.SaveChanges();

                    if (updateStatus > 0)
                    {
                        var systemUser = GetSesson();

                        if (systemUser == null)
                        {
                            TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                            return RedirectToAction("Logout", "Security");
                        }

                        string StringDescription = new GetStringHistorySystem().Get(systemUser.UserName, Resources.Resource.Edit, group.GroupName.ToString(), Resources.Resource.Group);

                        InsertHistorySystem((int)EACTION_TYPE.EDIT, (int)EnumControllerName.GROUP, StringDescription, groupOld, group);

                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Sửa thành công"));

                        return RedirectToAction("Index");
                    }
                    else
                    {
                        Notification = new StatusQuery("error", "", "Sửa thất bại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        return View(groupModel);
                    }
                }
                catch
                {
                    Notification = new StatusQuery("error", "", "Sửa thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return View(groupModel);
                }
            }

            Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;

            return View(groupModel);
        }

        public ActionResult EditUser(int id)
        {
            try
            {
                SystemUserViewModel view = new SystemUserViewModel();
                List<SystemUser> listViewUserInRole = new List<SystemUser>();
                List<SystemUser> listViewUserNotInRole = new List<SystemUser>();
                List<SystemUser> listUserNotInRole = new List<SystemUser>();

                // Danh sách user thuộc nhóm GroupId
                var listUserInGroup = _systemUserRepository.GetBy(r => r.GroupId == id).ToList();
                //Danh sách tất cả users
                var listUser = _systemUserRepository.GetAll();

                var check = true;
                foreach (var itemUser in listUser)
                {
                    check = true;
                    foreach (var itemUserInGroup in listUserInGroup)
                    {
                        if (itemUser.GroupId == itemUserInGroup.GroupId)
                        {
                            check = false;
                            break;
                        }
                    }

                    if (check)
                    {
                        // lấy user không thuộc nhóm
                        listUserNotInRole.Add(itemUser);
                    }
                }

                foreach (var item in listUserInGroup)
                {
                    listViewUserInRole.Add(item);
                }

                foreach (var item in listUserNotInRole)
                {
                    listViewUserNotInRole.Add(item);
                }

                view.UserInGroup = listViewUserInRole;
                view.UserNotInGroup = listViewUserNotInRole;

                view.GroupId = id;

                StatusQuery Notification;

                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }

                return View(view);
            }
            catch
            {
                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Hiệu chỉnh người dùng thất bại"));

                return RedirectToAction("Index");
            }
        }

        // GET: /role/Details/
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult EditUser(int groupId, List<int> dual_select)
        {
            using (TransactionScope scope = new TransactionScope())
            {
                try
                {
                    var _listuser = _systemUserRepository.GetAll().Where(p => p.GroupId == groupId).ToList();

                    List<int> userOld = new List<int>();

                    if (dual_select != null)
                    {
                        foreach (var item in _listuser)
                        {
                            item.GroupId = -1;
                            userOld.Add(item.Id);
                            _systemUserRepository.Update(item);
                        }

                        foreach (var item in dual_select)
                        {
                            SystemUser user = _systemUserRepository.GetById(item);
                            user.GroupId = groupId;
                            _systemUserRepository.Update(user);
                        }
                    }
                    else
                    {
                        var listUserInGroup = _systemUserRepository.GetBy(r => r.GroupId == groupId).ToList();
                        foreach (var item in listUserInGroup)
                        {
                            // không cho nó thuộc group nào cả
                            item.GroupId = -1;
                            _systemUserRepository.Update(item);
                        }
                    }

                    var StatusEdit = _systemUserRepository.SaveChanges();

                    if(StatusEdit > 0)
                    {
                        // lưu list danh sách id user thay đổi
                        var systemUser = GetSesson();

                        if (systemUser == null)
                        {
                            TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                            return RedirectToAction("Logout", "Security");
                        }

                        string StringDescription = new GetStringHistorySystem().Get(systemUser.UserName, Resources.Resource.Edit, Resources.Resource.EditUser, Resources.Resource.Group);

                        InsertHistorySystem((int)EACTION_TYPE.EDIT, (int)EnumControllerName.EDIT_USER_ON_GROUP, StringDescription, userOld, dual_select);

                        scope.Complete();
                    }
                }
                catch
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Sửa thất bại"));
                    return RedirectToAction("EditUser", "Groups", new { groupId });
                }
            }

            TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Sửa thành công"));
            return RedirectToAction("EditUser", "Groups", new { groupId });
        }

        // GET: /Groups/Delete/5
        public ActionResult Delete(int id)
        {
            StatusQuery Notification;
            try
            {
                var _group = _groupRepository.GetById(id);

                var _groupModel = _mapper.Map<Group, GroupModel>(_group);

                if (_groupModel == null)
                {
                    Notification = new StatusQuery("warning", "", "Không tìm thấy nhóm");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return RedirectToAction("Index");
                }

                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }

                return View(_groupModel);
            }
            catch
            {
                Notification = new StatusQuery("warning", "", "Xóa thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;

                return RedirectToAction("Index");
            }
        }

        // POST: /Groups/Delete/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int id)
        {
            try
            {
                var systemUser = GetSesson();

                if (systemUser == null)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                    return RedirectToAction("Logout", "Security");
                }

                var group = _groupRepository.GetById(id);

                _groupRepository.Delete(group);

                var deleteStatus = _groupRepository.SaveChanges();

                if (deleteStatus > 0)
                {

                    string StringDescription = new GetStringHistorySystem().Get(systemUser.UserName, Resources.Resource.Delete, Resources.Resource.EditUser, Resources.Resource.Group);

                    InsertHistorySystem((int)EACTION_TYPE.DELETE, (int)EnumControllerName.GROUP, StringDescription, group, null);

                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Xóa thành công"));
                    return RedirectToAction("Index");
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));
                    return RedirectToAction("Delete", "Groups", new { id });
                }
            }
            catch
            {
                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));
                return RedirectToAction("Delete", "Groups", new { id });
            }
        }

        public SystemUser GetSesson()
        {
            var SessionUserSystem = HttpContext.Session.GetComplexData<User>("SessionUserSystemId_" + HttpContext.Session.GetString("SessionUserSystemId"));
            if (SessionUserSystem != null)
            {
                var _user = _systemUserRepository.GetById(SessionUserSystem.Id);

                if (_user != null)
                {
                    return _user;
                }
            }
            return null;
        }

        private void InsertHistorySystem(int action_type, int controllerName, string description, object OldModel, object NewModel)
        {
            IPHostEntry heserver = Dns.GetHostEntry(Dns.GetHostName());
            var ipAddress = heserver.AddressList.FirstOrDefault(p => p.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork).ToString();

            string jsonOldObject = Newtonsoft.Json.JsonConvert.SerializeObject(OldModel);
            string jsonNewObject = Newtonsoft.Json.JsonConvert.SerializeObject(NewModel);

            HistorySystem history = new HistorySystem
            {
                ActionType = action_type,
                ActionTime = DateTime.Now,
                Description = description,
                OldObject = jsonOldObject,
                NewObject = jsonNewObject,
                UserId = int.Parse(HttpContext.Session.GetString("SessionUserSystemId")),
                IpAddress = ipAddress,
                ControllerName = controllerName
            };

            _historySystemRepository.Insert(history);

            _historySystemRepository.SaveChanges();
        }
    }
}
