html {
    --blackheadercolorCustom: #17212b;
    --whitecolorCustom: #ffffff;
    --blackcolorCustom: #000000;
    --bluecolorCustom: #007bff;
}

#searchAdress {
    margin: 10px;
    height: 40px;
    display: flex;
    flex-direction: row;
    border-radius: 20px;
    align-items: center;
    justify-content: space-between;
    /*background-color: var(--blackheadercolorCustom);*/
    color: var(--whitecolorCustom);
    bottom: 10px !important;
}

    #searchAdress .k-icon.k-i-search {
        font-size: 18px;
        font-weight: 800;
        cursor: pointer;
    }

    #searchAdress i {
        height: 40px;
        width: 40px;
    }

    #searchAdress #inputSearchAdress {
        /*margin-right: 5px;*/
        border: 0;
        outline: none;
        border-radius: 20px;
        height: 32px;
        /*padding-left: 10px;
                padding-right: 10px;*/
        /*transition: all 0.3s ease;*/
        transition: 0.3s;
        /*width: 270px;*/
        width: 0;
        background: transparent;
    }

    #searchAdress .active#inputSearchAdress {
        margin-right: 5px;
        padding-left: 10px;
        padding-right: 10px;
        width: 270px;
        background-color: var(--whitecolorCustom);
    }

/*---------------------------------------------------*/

.groupSearch {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    position: relative;
}

/* Suggestions dropdown styling */
#suggestions {
    position: absolute;
    bottom: 100%;
    left: 0;
    right: 0;
    z-index: 1050;
    max-height: 300px;
    overflow-y: auto;
    background-color: #ffffff;
    border: 1px solid #e7eaec;
    border-radius: 4px;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
    margin-bottom: 2px;
    display: none;
}

#suggestions:not(:empty) {
    display: block;
}

.suggestion-item {
    padding: 8px 15px;
    cursor: pointer;
    border-bottom: 1px solid #f1f1f1;
    color: #333333;
    font-size: 14px;
    line-height: 1.42857143;
    transition: all 0.2s ease-in-out;
    background-color: #ffffff;
}

.suggestion-item:last-child {
    border-bottom: none;
}

.suggestion-item:hover {
    background-color: #f5f5f5;
    color: #262626;
}

.suggestion-item:active,
.suggestion-item.active {
    background-color: #1ab394;
    color: #ffffff;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    #suggestions {
        max-height: 200px;
    }

    .suggestion-item {
        padding: 10px 12px;
        font-size: 13px;
    }
}

/* Loading state */
.suggestion-item.loading {
    text-align: center;
    color: #999999;
    font-style: italic;
}

/* Error state */
.suggestion-item.error {
    color: #ed5565;
    background-color: #fdf2f2;
}

/* Empty state */
.suggestion-item.empty {
    color: #999999;
    text-align: center;
    font-style: italic;
}

/* Focus and accessibility improvements */
#location-input:focus + #suggestions,
#location-input:focus ~ #suggestions {
    display: block;
}

#location-input{
    width:400px;
}

.suggestion-item:focus {
    outline: 2px solid #1ab394;
    outline-offset: -2px;
}

/* Smooth scrolling for suggestions */
#suggestions {
    scroll-behavior: smooth;
}

/* Custom scrollbar for suggestions */
#suggestions::-webkit-scrollbar {
    width: 6px;
}

#suggestions::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

#suggestions::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

#suggestions::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Animation for suggestions appearance */
@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

#suggestions:not(:empty) {
    animation: fadeInDown 0.2s ease-out;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .suggestion-item {
        border-bottom: 2px solid #000000;
    }

    .suggestion-item:hover {
        background-color: #000000;
        color: #ffffff;
    }

    .suggestion-item.active {
        background-color: #000000;
        color: #ffffff;
        border: 2px solid #ffffff;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .suggestion-item,
    #suggestions {
        transition: none;
        animation: none;
    }
}

/* Utility classes for customization */
.groupSearch.compact #suggestions {
    max-height: 150px;
}

.groupSearch.compact .suggestion-item {
    padding: 6px 12px;
    font-size: 13px;
}

.groupSearch.large #suggestions {
    max-height: 400px;
}

.groupSearch.large .suggestion-item {
    padding: 12px 18px;
    font-size: 15px;
}

/* Dark theme support */
.groupSearch.dark #suggestions {
    background-color: #2f3349;
    border-color: #404553;
    color: #ffffff;
}

.groupSearch.dark .suggestion-item {
    background-color: #2f3349;
    color: #ffffff;
    border-bottom-color: #404553;
}

.groupSearch.dark .suggestion-item:hover {
    background-color: #404553;
    color: #ffffff;
}

.groupSearch.dark .suggestion-item.active {
    background-color: #1ab394;
    color: #ffffff;
}

.groupSearch.dark .suggestion-item.empty,
.groupSearch.dark .suggestion-item.loading {
    color: #a7a9ac;
}

.groupSearch.dark .suggestion-item.error {
    color: #ed5565;
    background-color: #3d2f2f;
}
