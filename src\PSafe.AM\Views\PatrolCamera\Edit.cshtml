﻿@model PSafe.AM.Models.PatrolCameraModel
@{
    ViewBag.Title = "Hiệu chỉnh " + PSafe.AM.Resources.Resource.PatrolCamera;
    var areaListItems = (List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem>)ViewBag.areaListItems;
    var locationListItems = new List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem>();

    var patrolType = new List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem>();
    patrolType.Add(new SelectListItem()
    {
        Text = "Theo khu vực",
        Value = ((int)PSafe.Common.PatrolEnums.EPATROL_TYPE.MainPatrol).ToString()
    });
    patrolType.Add(new SelectListItem()
    {
        Text = "Theo tuyến",
        Selected = true,
        Value = ((int)PSafe.Common.PatrolEnums.EPATROL_TYPE.RoutePatrol).ToString()
    });
}

<environment names="Development">
    <script src="~/lib/jquery/dist/jquery.js"></script>
    <script src="~/lib/validate/jquery.validate.min.js"></script>
    <script src="~/js/script.js" asp-append-version="true"></script>
</environment>
<environment names="Staging,Production">
    <script src="~/lib/jquery/dist/jquery.js"></script>
    <script src="~/lib/validate/jquery.validate.min.js"></script>
    <script src="~/js/script.js" asp-append-version="true"></script>
</environment>
<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-lg-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>@PSafe.AM.Resources.Resource.Edit @PSafe.AM.Resources.Resource.PatrolCamera</h5>
                    <div class="ibox-tools">
                        @Html.ActionLink(PSafe.AM.Resources.Resource.BackToList, "Index", "PatrolCamera", null, new { @class = "btn btn-primary btn-xs" })
                    </div>
                </div>
                <div class="ibox-content">
                    @using (Html.BeginForm("Edit", "PatrolCamera", FormMethod.Post, new { id = "PatrolCameraForm" }))
                    {
                        @Html.AntiForgeryToken()

                        <div class="form-horizontal">
                            @Html.ValidationSummary(true)

                            @Html.HiddenFor(model => model.Id)

                            <div class="row form-group">
                                @Html.LabelFor(model => model.Name, new { @class = "control-label col-md-2" })
                                <div class="col-md-4">
                                    @Html.EditorFor(model => model.Name)
                                    @Html.ValidationMessageFor(model => model.Name, null, new { @class = "text-danger" })
                                </div>

                                @Html.LabelFor(model => model.Type, new { @class = "control-label col-md-2" })
                                <div class="col-md-4">
                                    @Html.DropDownListFor(model => model.Type, patrolType, "-- Chọn loại tuần tra --", new { @class = "form-control" })
                                </div>
                            </div>

                            <div class="row form-group">
                                @Html.LabelFor(model => model.AreaId, new { @class = "control-label col-md-2" })
                                <div class="col-md-4">
                                    @Html.DropDownListFor(model => model.AreaId, areaListItems, "-- Chọn khu vực --", new { @class = "form-control" })
                                </div>
                                @Html.LabelFor(model => model.LocationId, new { @class = "control-label col-md-2" })
                                <div class="col-md-4">
                                    @Html.DropDownListFor(model => model.LocationId, locationListItems, "-- Chọn vị trí --", new { @class = "form-control" })
                                </div>
                            </div>

                            <div class="row form-group">
                                <label class="control-label col-md-2">Danh sách camera</label>

                                <div class="col-md-4">
                                    <ul id="sortable1" class="connectedSortable">
                                    </ul>
                                </div>

                                @Html.LabelFor(model => model.ListCamera, new { @class = "control-label col-md-2" })
                                @Html.HiddenFor(model => model.ListCamera)

                                <div class="col-md-4">
                                    <ul id="sortable2" class="connectedSortable">

                                        @foreach (var camera in Model.ListCameraSelected) {
                                        <li class="ui-state-default" data-value="@camera.MilestoneId.ToString()" data-name="@camera.CameraName">
                                            @camera.CameraName
                                            <a>
                                                <i class="CameraClick float-right fa fa-plus" id="@camera.MilestoneId">
                                                </i>
                                            </a>
                                            <ol class="@camera.MilestoneId" style="display: none;">
                                                ---Chọn Preset---
                                                @{var i = 0; }

                                                @{
                                                        var cameraSelected = Model.ListCameraNoSelected.Where(p => p.MilestoneId == camera.MilestoneId).FirstOrDefault();
                                                        if (cameraSelected != null)
                                                        {
                                                        @foreach (var preset in cameraSelected.ListPreset)
                                                        {
                                                            if (camera.ListPreset.Any(p => p == preset))
                                                            {
                                                                <li style="overflow: hidden; padding: 0px; margin-bottom: 0px;" class="preset">
                                                                    <input type="checkbox" id="@camera.MilestoneId + '_' + @i" name="@camera.MilestoneId" value="@preset" checked />&ensp;
                                                                    <label for="@camera.MilestoneId + '_' + @i">@preset</label>
                                                                </li>
                                                            }
                                                            else
                                                            {
                                                                <li style="overflow: hidden; padding: 0px; margin-bottom: 0px;" class="preset">
                                                                    <input type="checkbox" id="@camera.MilestoneId + '_' + @i" name="@camera.MilestoneId" value="@preset" />&ensp;
                                                                    <label for="@camera.MilestoneId + '_' + @i">@preset</label>
                                                                </li>
                                                            }
                                                            i++;
                                                        }
                                                    }
                                                }
                                            </ol>
                                            </li>
                                        }
                                    </ul>
                                    @Html.ValidationMessageFor(model => model.ListCamera, null, new { @class = "text-danger" })
                                </div>

                            </div>

                            <div class="form-group">
                                <div class="col-md-offset-2 col-md-10">
                                    <input type="submit" value="@PSafe.AM.Resources.Resource.Save" class="btn btn-primary" />
                                    @Html.ActionLink(PSafe.AM.Resources.Resource.Cancel, "Index", "PatrolCamera", null, new { @class = "btn btn-white" })
                                </div>
                            </div>

                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>
@Html.AntiForgeryToken()

@section Styles {
    <environment names="Development,Staging,Production">
        <link rel="stylesheet" href="~/lib/toastr/toastr.min.css" />
        <link rel="stylesheet" href="~/css/jquery-ui.min.css">
    </environment>

    <style>
        #sortable1, #sortable2 {
            list-style: none;
            border: 1px dotted #ccc;
            padding: 10px;
            min-height: 120px;
            background-color: #f0f1f3;
            max-height: 300px;
            overflow: auto
        }

        #sortable1 li, #sortable2 li {
            padding: 10px;
            margin-bottom: 5px;
            background-color: white;
        }

        #sortable2.empty {
            background-image: url("@Url.Content("~/images/drag_drop.jpg")");
            background-repeat: no-repeat;
            background-position: center;
            background-size: contain;
        }

        #sortable2.move {
            border: 1px solid blue;
        }
    </style>
}

@section Scripts {
    <environment names="Development,Staging,Production">
        <script src="~/lib/validate/jquery.validate.min.js"></script>
        <script src="~/lib/toastr/toastr.js"></script>
        <script src="~/lib/toastr/toastr.min.js"></script>
        <script src="~/js/jquery-ui.min.js"></script>
    </environment>

    @if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "warning")
    {
        <script type="text/javascript">
            toastr.warning('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "error")
    {
        <script type="text/javascript">
            toastr.error('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "success")
    {
        <script type="text/javascript">
            toastr.success('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }

    <script type="text/javascript">
        var controllerUrl = '@Url.Action("Index", "PatrolCamera")';

        var curAreaId = '@Model.AreaId';
        var curLocationId = '@Model.LocationId';
        $(document).ready(function () {
            $("#PatrolCameraForm").on('submit', function () {
                var listCamera = [];
                $("#sortable2 li:not(.preset)").each(function () {
                    var milestoneId = this.getAttribute('data-value');
                    var cameraName = this.getAttribute('data-name');
                    if (milestoneId !== null) {
                        var listPreset = [];

                        $("li input:checkbox[name=\'" + milestoneId + "\']:checked").each(function () {
                            listPreset.push($(this).val());
                        });

                        var json = { "CameraName": cameraName, "MilestoneId": milestoneId, "ListPreset": listPreset };
                        listCamera.push(json);
                    }
                });

                $("#ListCamera").val(JSON.stringify(listCamera));
            });
            $("#AreaId").on('change', function () {
                $("#LocationId").val("").html('<option value="">-- Chọn vị trí --</option>');
                clearSortableList();
                $.ajax({
                    url: controllerUrl + "/GetLocationByArea",
                    data: { areaId: this.value },
                    dataType: 'json',
                    method: "POST",
                    headers: {
                        RequestVerificationToken: $("input[name='__RequestVerificationToken']").val()
                    },
                }).done(function (data) {
                    var html = '<option value="">-- Chọn vị trí --</option>';
                    for (var key in data) {
                        html += '<option value="' +data[key].value+ '">' +data[key].text+ '</option>';
                    }
                    $("#LocationId").html(html);
                }).fail(function () {

                });
            });

            $("#LocationId").on('change', function () {
                clearSortableList();
                if (this.value == "")
                    return;

                var locationId = this.value;
                $.ajax({
                    url: controllerUrl + "/GetCameraByLocation",
                    data: { locationId: locationId },
                    dataType: 'json',
                    method: "POST",
                    headers: {
                        RequestVerificationToken: $("input[name='__RequestVerificationToken']").val()
                    },
                }).done(function (data) {

                    var cameraAddeds = [];
                    $("#sortable2 .ui-state-default").each(function () {
                        var id = this.getAttribute("data-value");
                        cameraAddeds.push(id);
                    });
                    var html = '';
                    for (var key in data) {
                        if (cameraAddeds.indexOf(data[key].milestoneId) !== -1)
                            continue;

                        var listPreset = "";
                        if (data[key].listPreset.length > 0) {
                            listPreset += '<ol class="' + data[key].milestoneId + '" style="display: none;">---Chọn Preset---';
                            var i = 0;
                            for (var preset in data[key].listPreset) {
                                listPreset += '<li style="overflow: hidden; padding: 0px; margin-bottom: 0px;" class="preset"><input type="checkbox" id="' + data[key].milestoneId + '_' + i + '" name="' + data[key].milestoneId + '" value="' + data[key].listPreset[preset] + '">&ensp;<label for="' + data[key].milestoneId + '_' + i + '">' + data[key].listPreset[preset] + '</label></li>';
                                i++;
                            }

                            listPreset += '</ol>';
                        } else {
                            listPreset += '<ol class="' + data[key].milestoneId + '" style="display: none;">---Chọn Preset---';

                            listPreset += '</ol>';
                        }

                        html += '<li class="ui-state-default" data-value="' + data[key].milestoneId + '" data-name="' + data[key].cameraName + '">' + data[key].cameraName + '<a><i class="CameraClick fa fa-plus float-right" id="' + data[key].milestoneId +'"></i></a>' + listPreset + '</li>';
                    }
                    $("#sortable1").html(html);
                }).fail(function () {

                });

            });


            $( "#sortable1, #sortable2" ).sortable({
                connectWith: ".connectedSortable",
                update: function (e) {
                    if ($("#sortable2 li").length == 0) {
                        $("#sortable2").addClass("empty");
                    } else {
                        $("#sortable2").removeClass("empty");
                    }
                },
                start: function (e) {
                    if (e.currentTarget.id == "sortable1") {
                        $("#sortable2").addClass("move");
                    }
                },
                stop: function (e) {
                    console.log(e);
                    $("#sortable2").removeClass("move");
                }
            }).disableSelection();

            $("#sortable1").on("click", ".CameraClick", function () {
                var visible = $('.' + this.id).toggle().is(":visible");
                if (visible) {
                    $(this).removeClass("fa fa-plus").addClass("fa fa-minus");
                } else {
                    $(this).removeClass("fa fa-minus").addClass("fa fa-plus");
                }
            });

            $("#sortable2").on("click", ".CameraClick", function () {
                var visible = $('.' + this.id).toggle().is(":visible");
                if (visible) {
                    $(this).removeClass("fa fa-plus").addClass("fa fa-minus");
                } else {
                    $(this).removeClass("fa fa-minus").addClass("fa fa-plus");
                }
            });
        });

        function clearSortableList() {
            $("#sortable1").html("");
        }
    </script>
}