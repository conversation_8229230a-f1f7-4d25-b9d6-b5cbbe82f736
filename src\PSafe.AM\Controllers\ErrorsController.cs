﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;

namespace PSafe.AM.Controllers
{
    public class ErrorsController : Controller
    {
        public ActionResult Index()
        {
            return View();
        }

        public ActionResult NotFoundError(string errorValue)
        {
            ViewBag.ErrorString = errorValue;

            return View();
        }
    }
}