﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace PSafe.AM.Models
{
    public class PatrolCameraCalendarModel
    {
        [Key]
        [Display(Name = "Id", ResourceType = typeof(Resources.Resource_patrolCameraCalendar))]
        public int Id { get; set; }

        [Range(1, int.MaxValue, ErrorMessage = "Vui lòng chọn kịch bản tuần tra")]
        [Display(Name = "PatrolCameraId", ResourceType = typeof(Resources.Resource_patrolCameraCalendar))]
        public int PatrolCameraId { get; set; }

        [DataType(DataType.DateTime)]
        [Required(ErrorMessage = "Gi<PERSON> trị không được trống!")]
        [DisplayFormat(DataFormatString = "{0:HH:mm}", ApplyFormatInEditMode = true)]
        [Display(Name = "BeginTime", ResourceType = typeof(Resources.Resource_patrolCameraCalendar))]
        public DateTime BeginTime { get; set; }

        [DataType(DataType.DateTime)]
        [Required(ErrorMessage = "Giá trị không được trống!")]
        [DisplayFormat(DataFormatString = "{0:HH:mm}", ApplyFormatInEditMode = true)]
        [Display(Name = "EndTime", ResourceType = typeof(Resources.Resource_patrolCameraCalendar))]
        public DateTime EndTime { get; set; }

        //[Range(0, int.MaxValue, ErrorMessage = "Vui lòng chọn nhân viên tuần tra")]
        [Display(Name = "UserId", ResourceType = typeof(Resources.Resource_patrolCameraCalendar))]
        public List<int> UserId { get; set; }

        //[Range(0, int.MaxValue, ErrorMessage = "Vui lòng chọn tổ/nhóm tuần tra")]
        [Display(Name = "RoleId", ResourceType = typeof(Resources.Resource_patrolCameraCalendar))]
        public int? RoleId { get; set; }

        [Display(Name = "RoleName", ResourceType = typeof(Resources.Resource_patrolCameraCalendar))]
        public string RoleName { get; set; }

        [Display(Name = "Description", ResourceType = typeof(Resources.Resource_patrolCameraCalendar))]
        public string Description { get; set; }

        [Display(Name = "Dates", ResourceType = typeof(Resources.Resource_patrolCameraCalendar))]
        public List<DateTime> Dates { get; set; }

        public string DateStrings { get; set; }

        [Display(Name = " ")]
        public string PatrolCameraName { get; set; }

        [Display(Name = " ")]
        public int PatrollingStatus { get; set; } = 0;

    }
}
