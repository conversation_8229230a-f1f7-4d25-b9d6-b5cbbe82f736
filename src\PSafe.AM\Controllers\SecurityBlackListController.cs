﻿using Microsoft.AspNetCore.Mvc;
using PSafe.AM.Common;
using PSafe.Core.Domains;
using PSafe.Core.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.AspNetCore.Http;
using PSafe.AM.Models;
using System.IO;
using System.Threading.Tasks;
using OfficeOpenXml;
using PSafe.Common.UserEnums;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Configuration;
using PSafe.Common;
using System.Net;
using System.Security.Cryptography.X509Certificates;
using System.Net.Security;
using System.Net.Http;
using Newtonsoft.Json;
using PSafe.AM.ACS;
using System.Globalization;
using System.Text;
using Microsoft.AspNetCore.Hosting;
using static PSafe.Common.CommonEnums;

namespace PSafe.AM.Controllers
{
    [AuthorizeActionFilter(EPERMISSIONS_AM.SecurityBlacklist)]
    public class SecurityBlackListController : Controller
    {
        private readonly IUserRepository _userRepository;
        private readonly IBannedTypeRepository _bannedTypeRepository;
        private readonly IUserInRoleRepository _userInRoleRepository;
        private readonly IRoleRepository _roleRepository;
        private readonly IHistorySystemRepository _historySystemRepository;

        private readonly ILogger<SecurityBlackListController> _logger;
        private readonly IHostingEnvironment _hostingEnvironment;

        public SecurityBlackListController(IUserRepository userRepository, 
            IBannedTypeRepository bannedTypeRepository, 
            IUserInRoleRepository userInRoleRepository, 
            IRoleRepository roleRepository, 
            ILogger<SecurityBlackListController> logger, 
            IHostingEnvironment hostingEnvironment,
            IHistorySystemRepository historySystemRepository)
        {
            _userRepository = userRepository;
            _bannedTypeRepository = bannedTypeRepository;
            _userInRoleRepository = userInRoleRepository;
            _roleRepository = roleRepository;
            _logger = logger;
            _hostingEnvironment = hostingEnvironment;
            _historySystemRepository = historySystemRepository;
        }

        public ActionResult Index()
        {
            var acsAPIInfo = GetACSAPIInfo();

            string acsAPIUrl = acsAPIInfo.Item1;
            string userName = acsAPIInfo.Item2;
            string passWord = acsAPIInfo.Item3;

            ViewBag.acsAPIUrl = acsAPIUrl;
            ViewBag.userName = userName;
            ViewBag.passWord = passWord;

            return View();
        }


        /// <summary>
        /// Lấy thông tin acs api với url, username, password trong file setting
        /// </summary>
        /// <returns> Tuple<string, string, string>
        /// Item1: url
        /// Item2: username
        /// Item3: password
        /// </returns>
        private Tuple<string, string, string> GetACSAPIInfo()
        {
            var builder = new ConfigurationBuilder()
                                        .SetBasePath(Directory.GetCurrentDirectory())
                                        .AddJsonFile("appsettings.json");

            var configuration = builder.Build();

            string acsAPIUrl = configuration.GetSection("ACSAPI:BaseUrl").Value;
            string userName = Utils.DecodePassword(configuration.GetSection("ACSAPI:Username").Value, Utils.EncodeType.SHA_256);
            string passWord = Utils.DecodePassword(configuration.GetSection("ACSAPI:Password").Value, Utils.EncodeType.SHA_256);

            if (!acsAPIUrl.EndsWith("/"))
                acsAPIUrl += "/";

            return Tuple.Create(acsAPIUrl, userName, passWord);
        }

        /// <summary>
        /// Send http request đến ACS API
        /// </summary>
        /// <param name="command">VD: get_forbid_list, get_reason_forbid, ...</param>
        /// <param name="queryString">Danh sách cách query string, để null nếu ko có</param>
        /// <returns>ACSResponseData</returns>
        private ACSResponseData SendHttpRequest(string command, Dictionary<string, string> queryString)
        {
            try
            {
                var acsAPIInfo = GetACSAPIInfo();

                string acsAPIUrl = acsAPIInfo.Item1;
                string userName = acsAPIInfo.Item2;
                string passWord = acsAPIInfo.Item3;

                if (!acsAPIUrl.EndsWith("/"))
                    acsAPIUrl += "/";

                if (command.StartsWith("/"))
                    command = command.Substring(1);

                acsAPIUrl += command;

                if (queryString == null)
                    queryString = new Dictionary<string, string>();

                //pass https
                ServicePointManager.ServerCertificateValidationCallback +=
                    delegate (
                        Object sender1,
                        X509Certificate certificate,
                        X509Chain chain,
                        SslPolicyErrors sslPolicyErrors)
                    {
                        return true;
                    };

                using (var client = new HttpClient())
                {
                    userName = C3CryptoAesAPI.C3CryptoAes.EncryptStringToString256(userName);
                    passWord = C3CryptoAesAPI.C3CryptoAes.EncryptStringToString256(passWord);

                    queryString.Add("usr", userName);
                    queryString.Add("pw", passWord);

                    acsAPIUrl += BuildHttpQueryString(queryString); //format ?key=value&key2=value

                    HttpResponseMessage resp = client.GetAsync(acsAPIUrl).Result;
                    var resutl = resp.Content.ReadAsStringAsync().Result;

                    string data = C3CryptoAesAPI.C3CryptoAes.DecryptStringFromString256(resutl);

                    var responseData = JsonConvert.DeserializeObject<ACSResponseData>(data);
                    return responseData;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("SecurityBlackList/SendHttpRequest: " + ex.Message);
                return new ACSResponseData()
                {
                    retCode = 2,
                    result = ex.Message
                };
            }
        }

        private string BuildHttpQueryString(Dictionary<string, string> queryString)
        {
            string queryStringUrl = "";
            foreach (var query in queryString)
            {
                if (queryStringUrl == "")
                    queryStringUrl += "?";
                else
                    queryStringUrl += "&";

                queryStringUrl += query.Key + "=" + query.Value;
            }

            return queryStringUrl;
        }

        [HttpPost]
        public ActionResult Index(
            int draw,
            int start,
            int length,
            string paperCode,
            string fullName,
            string reason,
            string queQuan,
            string birthDay,
            string fromTime,
            string toTime)
        {
            Dictionary<string, string> queryString = new Dictionary<string, string>();
            queryString.Add("hoTen", fullName == null ? string.Empty : fullName);
            queryString.Add("maGiayTo", paperCode == null ? string.Empty : paperCode);
            queryString.Add("queQuan", queQuan == null ? string.Empty : queQuan);
            queryString.Add("ngaySinh", CheckAndConvertDateTimeStringToString(birthDay, null, "yyyy-MM-dd"));
            queryString.Add("thoiGianThemBatDau", CheckAndConvertDateTimeStringToString(fromTime, new DateTime(2019, 1, 1, 0, 0, 0), "yyyy-MM-dd", "dd/MM/yyyy HH:mm:ss"));
            queryString.Add("thoiGianThemketThuc", CheckAndConvertDateTimeStringToString(toTime, DateTime.Now, "yyyy-MM-dd", "dd/MM/yyyy HH:mm:ss"));
            queryString.Add("lyDo", reason == null ? string.Empty : reason);

            var blackList = GetBlackList(queryString);

            return Json(new
            {
                draw = draw,
                recordsFiltered = blackList.Count,
                recordsTotal = blackList.Count,
                data = blackList
            });
        }

        private List<BlackListModel> GetBlackList(Dictionary<string, string> queryString)
        {
            var blackList = new List<BlackListModel>();
            var response = SendHttpRequest("get_black_list", queryString);
            if (response.retCode == 0)
            {
                try
                {
                    blackList = JsonConvert.DeserializeObject<List<BlackListModel>>(response.result.ToString());
                }
                catch (Exception ex)
                {
                    _logger.LogError("SecurityBlackList/GetBlackList: " + ex.Message);
                }
            }

            return blackList;
        }

        private string CheckAndConvertDateTimeStringToString(string dateTime, DateTime defaultDateTime)
        {
            if (string.IsNullOrWhiteSpace(dateTime))
            {
                return defaultDateTime.ToString("dd/MM/yyyy HH:mm:ss");
            }

            try
            {
                var _dateTime = DateTime.ParseExact(dateTime, "yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture);
                return _dateTime.ToString("dd/MM/yyyy HH:mm:ss");
            }
            catch
            {
                return defaultDateTime.ToString("dd/MM/yyyy HH:mm:ss");
            }
        }

        private string CheckAndConvertDateTimeStringToString(string dateTime, DateTime? defaultDateTime = null, string format = "yyyy/MM/dd", string returnFormat = "dd/MM/yyyy")
        {
            if (string.IsNullOrWhiteSpace(dateTime))
            {
                if (defaultDateTime.HasValue)
                    return defaultDateTime.Value.ToString("dd/MM/yyyy HH:mm:ss");
                return string.Empty;
            }

            try
            {
                var _dateTime = DateTime.ParseExact(dateTime, format, CultureInfo.InvariantCulture);
                if (defaultDateTime.HasValue)
                    return _dateTime.ToString("dd/MM/yyyy") + " " + defaultDateTime.Value.ToString("HH:mm:ss");
                return _dateTime.ToString(returnFormat);
            }
            catch
            {
                if (defaultDateTime.HasValue)
                    return defaultDateTime.Value.ToString("dd/MM/yyyy HH:mm:ss");
                return string.Empty;
            }
        }

        [HttpPost("SecurityBlackList/GetHumanListHistory")]
        public JsonResult GetHumanListHistory(int draw, int start, int length, string keyword, string id)
        {
            List<HumanBannedListHistoryModel> humanBannedListHistoryModels = new List<HumanBannedListHistoryModel>();
            Dictionary<string, string> queryString = new Dictionary<string, string>();
            queryString.Add("idCustomer", id);
            var response = SendHttpRequest("get_forbid_history", queryString);
            if (response.retCode == 0)
            {
                try
                {
                    var historyList = JsonConvert.DeserializeObject<List<RightsHistoryAPI>>(response.result.ToString());
                    foreach (var history in historyList)
                    {
                        var humanBannedListModel = new HumanBannedListHistoryModel();
                        humanBannedListModel.ActionType = GetHistoryActionTypeText(history.LoaiCam);

                        string ngayCam = history.NgayCam == null ? string.Empty : history.NgayCam;
                        humanBannedListModel.ActiveTime = history.ThoiHanCamBatDau;
                        humanBannedListModel.ExpiryTime = history.ThoiHanCamKetThuc;
                        if (string.IsNullOrEmpty(humanBannedListModel.ActiveTime))
                            humanBannedListModel.ActiveTime = ngayCam;
                        if (string.IsNullOrEmpty(humanBannedListModel.ExpiryTime))
                            humanBannedListModel.ExpiryTime = "Vĩnh viễn";
                        humanBannedListModel.CreatedBy = history.UserCam;
                        humanBannedListModel.ClearFlag = history.LoaiCam == "1" ? "" : "Y";
                        humanBannedListModel.CreatedDate = ngayCam;

                        if (history.LoaiCam == "1")
                        {
                            humanBannedListModel.ReasonViolation = history.LyDoCam;
                        }
                        else
                        {
                            humanBannedListModel.ReasonClear = history.LyDoCam;
                            humanBannedListModel.ClearDate = ngayCam;
                        }

                        humanBannedListHistoryModels.Add(humanBannedListModel);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("SecurityBlackList/GetHumanListHistory: " + ex.Message);
                }
            }

            return Json(new
            {
                draw = draw,
                recordsFiltered = humanBannedListHistoryModels.Count,
                recordsTotal = humanBannedListHistoryModels.Count,
                data = humanBannedListHistoryModels
            });
        }

        private string GetHistoryActionTypeText(string type)
        {
            switch (type)
            {
                case "1":
                    return "Cấm";
                case "0":
                    return "Hủy cấm";
            }

            return "";
        }

        private string ConvertDateTimeToString(DateTime? dateTime, string format = "dd/MM/yyyy HH:mm:ss")
        {
            if (dateTime.HasValue)
                return dateTime.Value.ToString(format);

            return string.Empty;
        }

        private bool OnAddBlackList(BlackListModel blackList)
        {
            FormatTimeForACS(blackList);

            Dictionary<string, string> queryString = new Dictionary<string, string>();
            queryString.Add("hoTen", blackList.HoTen);
            queryString.Add("loaiGiayTo", blackList.LoaiGiayTo);
            queryString.Add("maGiayTo", blackList.MaGiayTo);
            queryString.Add("ngayCap", blackList.NgayCap == null ? string.Empty : blackList.NgayCap);
            queryString.Add("noiCap", blackList.NoiCap == null ? string.Empty : blackList.NoiCap);
            queryString.Add("queQuan", blackList.QueQuan == null ? string.Empty : blackList.QueQuan);
            queryString.Add("ngaySinh", blackList.NgaySinh == null ? string.Empty : blackList.NgaySinh);
            queryString.Add("congTy", blackList.CongTy == null ? string.Empty : blackList.CongTy);
            queryString.Add("soDT", blackList.Sdt == null ? string.Empty : blackList.Sdt);
            queryString.Add("diaChi", blackList.DiaChi == null ? string.Empty : blackList.DiaChi);
            queryString.Add("ngayGioThem", blackList.NgayThemVaoDanhSachDen == null ? DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss") : blackList.NgayThemVaoDanhSachDen);
            queryString.Add("userThem", blackList.NguoiThem);
            queryString.Add("lyDoThem", blackList.LyDo);
            var response = SendHttpRequest("add_black_list", queryString);
            return response.retCode == 0;
        }

        [HttpPost("SecurityBlackList/AddBlackList")]
        public ActionResult AddBlackList(BlackListModel blackList)
        {
            if (!IsValid(blackList))
                return Json(new
                {
                    success = false,
                    message = "Vui lòng nhập đầy đủ thông tin"
                });

            var user = GetSesson();
            if (user == null)
            {
                return Json(new
                {
                    success = false,
                    message = "Vui lòng đăng nhập để tiếp tục"
                });
            }

            _logger.LogInformation(string.Format("SecurityBlackList/AddBlackList 1: nguoi dung {0}, them khach {1} vao danh sach den", user.UserName, blackList.HoTen));

            blackList.NguoiThem = user.UserName;
            blackList.NgayThemVaoDanhSachDen = DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss");
            if (OnAddBlackList(blackList))
            {
                _logger.LogInformation(string.Format("SecurityBlackList/AddBlackList 2: nguoi dung {0}, them khach {1} vao danh sach den thanh cong", user.UserName, blackList.HoTen));
                InsertHistorySystem(EACTION_TYPE.BANNED, EnumControllerName.BLACK_LIST, string.Format("Thêm khách {0} vào danh sách đen", blackList.HoTen));
                return Json(new
                {
                    success = true
                });
            }

            return Json(new
            {
                success = false,
                message = "Thêm thất bại. Vui lòng kiểm tra kết nối đến ACS!"
            });
        }

        [HttpPost("SecurityBlackList/DeleteBlackList")]
        public ActionResult DeleteBlackList(string id)
        {
            var user = GetSesson();
            if (user == null)
            {
                return Json(new
                {
                    success = false,
                    message = "Vui lòng đăng nhập để tiếp tục"
                });
            }

            _logger.LogInformation(string.Format("SecurityBlackList/DeleteBlackList 1: nguoi dung {0}, xoa khach {1} khoi danh sach den", user.UserName, id));

            Dictionary<string, string> queryString = new Dictionary<string, string>();
            queryString.Add("idKhachHang", id);
            queryString.Add("ngayGioXoa", DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss"));
            queryString.Add("userXoa", user.UserName);
            var response = SendHttpRequest("remove_black_list", queryString);
            if(response.retCode == 0)
            {
                _logger.LogInformation(string.Format("SecurityBlackList/DeleteBlackList 2: nguoi dung {0}, xoa khach {1} khoi danh sach den thanh cong", user.UserName, id));
                InsertHistorySystem(EACTION_TYPE.DELETE, EnumControllerName.BLACK_LIST, string.Format("Xóa khách {0} khỏi danh sách đen", id));
                return Json(new
                {
                    success = true
                });
            }

            return Json(new
            {
                success = false,
                message = "Xóa thất bại. Vui lòng kiểm tra kết nối đến ACS"
            });
        }

        private void FormatTimeForACS(BlackListModel blackList)
        {
            if (blackList.NgaySinh != null && blackList.NgaySinh.Length == "dd/MM/yyyy".Length)
            {
                blackList.NgaySinh += " 00:00:00";
            }
            if (blackList.NgayCap != null && blackList.NgayCap.Length == "dd/MM/yyyy".Length)
            {
                blackList.NgayCap += " 00:00:00";
            }
        }

        private bool OnUpdateBlackList(BlackListModel blackList)
        {
            if(string.IsNullOrEmpty(blackList.IdKhachHang))
            {
                return false;
            }
            FormatTimeForACS(blackList);

            Dictionary<string, string> queryString = new Dictionary<string, string>();
            queryString.Add("idKhachHang", blackList.IdKhachHang);
            queryString.Add("hoTen", blackList.HoTen);
            queryString.Add("loaiGiayTo", blackList.LoaiGiayTo);
            queryString.Add("maGiayTo", blackList.MaGiayTo);
            queryString.Add("queQuan", blackList.QueQuan == null ? string.Empty : blackList.QueQuan);
            queryString.Add("ngaySinh", blackList.NgaySinh == null ? string.Empty : blackList.NgaySinh);
            queryString.Add("congTy", blackList.CongTy == null ? string.Empty : blackList.CongTy);
            queryString.Add("soDT", blackList.Sdt == null ? string.Empty : blackList.Sdt);
            queryString.Add("diaChi", blackList.DiaChi == null ? string.Empty : blackList.DiaChi);
            queryString.Add("thoiGianEdit", DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss"));
            queryString.Add("userEdit", blackList.NguoiThem);
            var response = SendHttpRequest("edit_black_list", queryString);
            return response.retCode == 0;

        }

        [HttpPost("SecurityBlackList/UpdateBlackList")]
        public ActionResult UpdateBlackList(BlackListModel blackList)
        {
            if (!IsValid(blackList))
                return Json(new
                {
                    success = false,
                    message = "Vui lòng nhập đầy đủ thông tin"
                });

            var user = GetSesson();
            if (user == null)
            {
                return Json(new
                {
                    success = false,
                    message = "Vui lòng đăng nhập để tiếp tục"
                });
            }

            blackList.NguoiThem = user.UserName;
            if (OnUpdateBlackList(blackList))
            {
                return Json(new
                {
                    success = true
                });
            }

            return Json(new
            {
                success = false,
                message = "Cập nhật thất bại. Vui lòng kiểm tra kết nối đến ACS!"
            });
        }

        [HttpGet]
        public IActionResult DownloadExcelTemplate(string f)
        {
            try
            {
                string fileName = System.IO.Path.Combine(_hostingEnvironment.ContentRootPath, "ExcelTemplate", f);
                var net = new System.Net.WebClient();
                var data = net.DownloadData(fileName);
                var content = new System.IO.MemoryStream(data);
                var contentType = "APPLICATION/octet-stream";
                return File(content, contentType, f);
            }
            catch
            {
                return NotFound();
            }
        }

        private bool IsValidExcelFile(IFormFile file)
        {
            if (file == null || !Path.GetExtension(file.FileName).Equals(".xlsx", StringComparison.OrdinalIgnoreCase))
                return false;

            return true;
        }

        private bool ParseDateTime(string dateTime, string format, out DateTime dt)
        {
            if (string.IsNullOrEmpty(dateTime))
            {
                dt = DateTime.Now;
                return false;
            }
            try
            {
                dt = DateTime.ParseExact(dateTime, format, System.Globalization.CultureInfo.InvariantCulture);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("SecurityBlackList/ParseDateTime: " + ex.Message);
                dt = DateTime.Now;
                return false;
            }
        }

        private string GetExcelCellStringValue(ExcelWorksheet worksheet, int row, int column)
        {
            if (worksheet.Cells[row, column].Value != null)
                return worksheet.Cells[row, column].Value.ToString().Trim();

            return string.Empty;
        }


        [HttpPost("SecurityBlackList/ImportBlackList")]
        public async Task<JsonResult> ImportBlackList(IFormFile file, int bannedType)
        {
            if (!IsValidExcelFile(file))
            {
                return Json(new
                {
                    success = false,
                    message = "Vui lòng chọn file"
                });
            }

            var user = GetSesson();
            if (user == null)
            {
                return Json(new
                {
                    success = false,
                    message = "Vui lòng đăng nhập để tiếp tục"
                });
            }

            int totalImport = 0;
            List<BlackListModel> blackLists = new List<BlackListModel>();
            try
            {
                using (var stream = new MemoryStream())
                {
                    await file.CopyToAsync(stream);

                    using (var package = new ExcelPackage(stream))
                    {
                        ExcelWorksheet worksheet = package.Workbook.Worksheets[0];
                        var rowCount = worksheet.Dimension.End.Row;

                        const int START_ROW = 5;
                        for (int row = START_ROW; row <= rowCount; row++)
                        {
                            var blackList = ReadBlackListFromExcel(worksheet, row, user.Id, ref totalImport);
                            if (blackList != null)
                                blackLists.Add(blackList);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("SecurityBlackList/ImportBlackList: " + ex.Message);
                Console.WriteLine(ex.Message);
            }

            int numOfImportSuccess = 0;

            foreach (var blackList in blackLists)
            {
                if (!IsValid(blackList))
                    continue;

                if(string.IsNullOrWhiteSpace(blackList.NguoiThem))
                    blackList.NguoiThem = user.UserName;

                if(string.IsNullOrWhiteSpace(blackList.NgayThemVaoDanhSachDen))
                    blackList.NgayThemVaoDanhSachDen = DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss");

                if (OnAddBlackList(blackList))
                    numOfImportSuccess++;
            }

            return Json(new
            {
                success = numOfImportSuccess > 0,
                numOfImportSuccess = numOfImportSuccess,
                totalImport = totalImport
            });
        }

        private BlackListModel ReadBlackListFromExcel(ExcelWorksheet worksheet, int row, int loginUserId, ref int totalImport)
        {
            if (string.IsNullOrEmpty(GetExcelCellStringValue(worksheet, row, (int)BlackListExcelColumnIndex.LOAI_GIAY_TO)) ||
                string.IsNullOrEmpty(GetExcelCellStringValue(worksheet, row, (int)BlackListExcelColumnIndex.MA_GIAY_TO)) ||
                string.IsNullOrEmpty(GetExcelCellStringValue(worksheet, row, (int)BlackListExcelColumnIndex.HO_TEN)))
                return null;

            totalImport++;

            string PaperType = GetExcelCellStringValue(worksheet, row, (int)BlackListExcelColumnIndex.LOAI_GIAY_TO);
            if (string.IsNullOrEmpty(PaperType))
                return null;

            var blackList = new BlackListModel();
            if (PaperType.ToLower().Equals("cmnd"))
                blackList.LoaiGiayTo = ((int)EnumDocType.ID_CARD).ToString();
            else if (PaperType.ToLower().Equals("passport"))
                blackList.LoaiGiayTo = ((int)EnumDocType.PASSPORT).ToString();
            else if (PaperType.ToLower().Equals("bằng lái xe") || PaperType.ToLower().Equals("blx"))
                blackList.LoaiGiayTo = ((int)EnumDocType.DRIVER_LICENSE).ToString();
            else if (PaperType.ToLower().Equals("cccd") || PaperType.ToLower().Equals("căn cước công dân"))
                blackList.LoaiGiayTo = ((int)EnumDocType.CCCD).ToString();
            else
                blackList.LoaiGiayTo = ((int)EnumDocType.NONE).ToString();

            blackList.MaGiayTo = GetExcelCellStringValue(worksheet, row, (int)BlackListExcelColumnIndex.MA_GIAY_TO);
            if (string.IsNullOrWhiteSpace(blackList.MaGiayTo))
                return null;

            blackList.HoTen = GetExcelCellStringValue(worksheet, row, (int)BlackListExcelColumnIndex.HO_TEN);

            blackList.LyDo = GetExcelCellStringValue(worksheet, row, (int)BlackListExcelColumnIndex.LY_DO);
            blackList.NguoiThem = GetExcelCellStringValue(worksheet, row, (int)BlackListExcelColumnIndex.NGUOI_THEM);
            blackList.NgayThemVaoDanhSachDen = GetExcelCellDateTimeValueToString(worksheet, row, (int)BlackListExcelColumnIndex.THOI_GIAN_THEM, "dd/MM/yyyy HH:mm:ss", DateTime.Now);
            blackList.Sdt = GetExcelCellStringValue(worksheet, row, (int)BlackListExcelColumnIndex.SO_DT);
            blackList.CongTy = GetExcelCellStringValue(worksheet, row, (int)BlackListExcelColumnIndex.CONG_TY);
            blackList.NgaySinh = GetExcelCellDateTimeValueToString(worksheet, row, (int)BlackListExcelColumnIndex.NGAY_SINH, "dd/MM/yyyy", null);
            blackList.QueQuan = GetExcelCellStringValue(worksheet, row, (int)BlackListExcelColumnIndex.QUE_QUAN);
            blackList.DiaChi = GetExcelCellStringValue(worksheet, row, (int)BlackListExcelColumnIndex.DIA_CHI_THUONG_CHU);
            blackList.NoiCap = GetExcelCellStringValue(worksheet, row, (int)BlackListExcelColumnIndex.NOI_CAP_GIAY_TO);
            blackList.NgayCap = GetExcelCellDateTimeValueToString(worksheet, row, (int)BlackListExcelColumnIndex.NGAY_CAP_GIAY_TO, "dd/MM/yyyy", null);
            return blackList;
        }

        private string GetExcelCellDateTimeValueToString(ExcelWorksheet worksheet, int row, int column, string format, DateTime? defaultDate)
        {
            try
            {
                if (worksheet.Cells[row, column].Value != null)
                {
                    if (double.TryParse(worksheet.Cells[row, column].Value.ToString().Trim(), out double value))
                    {
                        return DateTime.FromOADate(value).ToString(format);
                    }
                }
            }
            catch
            {

            }

            if (defaultDate.HasValue)
                return defaultDate.Value.ToString(format);

            return string.Empty;
        }

        private int FindUserIdByName(string name)
        {
            name = name.ToLower();
            var user = _userRepository.GetBy(x => x.UserName.ToLower() == name).FirstOrDefault();
            if (user != null)
                return user.Id;

            if (HttpContext.Session.GetString("SessionUserSystemId") != null)
            {
                int userId;
                if (int.TryParse(HttpContext.Session.GetString("SessionUserSystemId"), out userId))
                    return userId;
            }

            return 0;
        }


        private bool IsValid(BlackListModel blackList)
        {
            if (string.IsNullOrEmpty(blackList.MaGiayTo) ||
                string.IsNullOrEmpty(blackList.HoTen))
                return false;

            return true;
        }

        public User GetSesson()
        {
            var SessionUserSystem = HttpContext.Session.GetComplexData<User>("SessionUserSystemId_" + HttpContext.Session.GetString("SessionUserSystemId"));
            if (SessionUserSystem != null)
            {
                var _user = _userRepository.GetById(SessionUserSystem.Id);

                if (_user != null)
                {
                    if (!_user.Actived)
                    {
                        HttpContext.Session.Clear();
                        return null;
                    }
                    return _user;
                }
            }
            return null;
        }

        private void InsertHistorySystem(EACTION_TYPE action_type, EnumControllerName controllerName, string description)
        {
            try
            {
                var ipAddress = HttpContext.Connection.RemoteIpAddress.ToString();

                HistorySystem history = new HistorySystem
                {
                    ActionType = (int)action_type,
                    ActionTime = DateTime.Now,
                    Description = description,
                    OldObject = string.Empty,
                    NewObject = string.Empty,
                    UserId = int.Parse(HttpContext.Session.GetString("SessionUserSystemId")),
                    IpAddress = ipAddress,
                    ControllerName = (int)controllerName
                };

                _historySystemRepository.Insert(history);

                _historySystemRepository.SaveChanges();
            }
            catch (Exception ex)
            {
                _logger.LogError("SecurityBlackList/InsertHistorySystem: " + ex.Message);
            }
        }



        private enum BlackListExcelColumnIndex
        {
            LOAI_GIAY_TO = 1,
            MA_GIAY_TO,
            HO_TEN,
            LY_DO,
            NGUOI_THEM,
            THOI_GIAN_THEM,

            SO_DT,
            CONG_TY,
            NGAY_SINH,
            QUE_QUAN,
            DIA_CHI_THUONG_CHU,
            NOI_CAP_GIAY_TO,
            NGAY_CAP_GIAY_TO
        }

    }
}