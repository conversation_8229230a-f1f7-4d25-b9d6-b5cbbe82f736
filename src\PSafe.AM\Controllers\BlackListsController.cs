﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using PSafe.Core.Domains;
using PSafe.Core.Interface;
using PSafe.Core.SharedKernel;
using static PSafe.Common.CommonEnums;

namespace PSafe.AM.Controllers
{
    public class BlackListsController : Controller
    {
        private readonly IBlackListRepository _blackListRepository;
        private readonly ILogger<BlackListsController> _logger;

        public BlackListsController(IUnitOfWork unitOfWork, ILogger<BlackListsController> logger)
        {
            _blackListRepository = unitOfWork.BlackListRepository;
            _logger = logger;
        }

        public IActionResult Index()
        {
            return View();
        }

        public JsonResult SearchBlackList(string paperId)
        {
            try
            {
                var blackList = _blackListRepository.GetBy(p => p.PaperId == paperId || p.LicensePlates == paperId).SingleOrDefault();

                if (blackList != null)
                {
                    return Json(blackList);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("BlackList/SearchBlackList: " + ex.Message);
                Console.WriteLine(ex.Message);
            }

            return Json(null);
        }

        public JsonResult GetListBlackList()
        {
            try
            {
                var blackList = _blackListRepository.GetAll().ToList();

                if (blackList != null)
                {
                    return Json(blackList);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("BlackList/GetListBlackList: " + ex.Message);
                Console.WriteLine(ex.Message);
            }

            return Json(null);
        }
    }
}