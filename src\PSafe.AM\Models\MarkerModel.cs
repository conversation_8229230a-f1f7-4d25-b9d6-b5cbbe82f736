﻿using Microsoft.AspNetCore.Mvc.Rendering;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace PSafe.AM.Models
{
    public class MarkerModel
    {
        [Key]
        [Display(Name = "MarkerID", ResourceType = typeof(Resources.Resource_marker))]
        public int MarkerID { get; set; }

        //[Required(ErrorMessage = "Vui lòng điền mã code")]
        //[StringLength(255, MinimumLength = 1, ErrorMessage = "Đ<PERSON> dài từ 1-255 ký tự")]
        [Display(Name = "MarkerCode", ResourceType = typeof(Resources.Resource_marker))]
        public string MarkerCode { get; set; }

        [Required(ErrorMessage = "Vui lòng điền tên lực lượng")]
        [StringLength(255, MinimumLength = 1, ErrorMessage = "<PERSON><PERSON> dài từ 1-255 ký tự")]
        [Display(Name = "MarkerName", ResourceType = typeof(Resources.Resource_marker))]
        public string MarkerName { get; set; }

        [Display(Name = "Phone", ResourceType = typeof(Resources.Resource_marker))]
        public string Phone { get; set; }

        [DisplayFormat(DataFormatString = "{0:00.000000000}", ApplyFormatInEditMode = true)]
        [Range(-2147483645.999999999999999, 2147483646.999999999999999, ErrorMessage = "Giá trị không hợp lệ")]
        [Display(Name = "Latitude", ResourceType = typeof(Resources.Resource_marker))]
        public decimal LATITUDE { get; set; }

        [Range(-2147483645.999999999999999, 2147483646.999999999999999, ErrorMessage = "Giá trị không hợp lệ")]
        [DisplayFormat(DataFormatString = "{0:000.000000000}", ApplyFormatInEditMode = true)]
        [Display(Name = "Longitude", ResourceType = typeof(Resources.Resource_marker))]
        public decimal LONGITUDE { get; set; }

        [Required]
        [Range(1, 3, ErrorMessage = "Vui lòng chọn loại lực lượng")]
        [Display(Name = "MarkerTypeID", ResourceType = typeof(Resources.Resource_marker))]
        public int MarkerTypeID { get; set; }

        [Display(Name = "Description", ResourceType = typeof(Resources.Resource_marker))]
        public string Description { get; set; }

        [Display(Name = "ListTypeOfMarkers", ResourceType = typeof(Resources.Resource_marker))]
        public SelectList ListTypeOfMarkers { get; set; }
    }
}
