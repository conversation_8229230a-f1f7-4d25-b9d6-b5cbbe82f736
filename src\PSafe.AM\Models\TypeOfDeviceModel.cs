﻿using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Web;

namespace PSafe.AM.Models
{
    public class TypeOfDeviceModel
    {
        [Key]
        [Display(Name = "TYPEOFDEVICEID", ResourceType = typeof(Resources.Resource__typeOfDevice))]
        public int TYPEOFDEVICEID { get; set; }

        [Required(ErrorMessage = "Vui lòng nhập tên loại thiết bị")]
        [StringLength(255, MinimumLength = 1, ErrorMessage = "Độ dài từ 1-255 ký tự")]
        [Display(Name = "TYPENAME", ResourceType = typeof(Resources.Resource__typeOfDevice))]
        public string TYPENAME { get; set; } = string.Empty;

        [Display(Name = "DESCRIPTION", ResourceType = typeof(Resources.Resource__typeOfDevice))]
        public string DESCRIPTION { get; set; } = string.Empty;

        [Display(Name = "ICON", ResourceType = typeof(Resources.Resource__typeOfDevice))]
        public string ICON { get; set; } = string.Empty;

        [DataType(DataType.Date)]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd}", ApplyFormatInEditMode = true)]
        [Display(Name = "CREATEDDATE", ResourceType = typeof(Resources.Resource__typeOfDevice))]
        public DateTime CREATEDDATE { get; set; }

        [Display(Name = "CREATEDUSER", ResourceType = typeof(Resources.Resource__typeOfDevice))]
        public int CREATEDUSER { get; set; }

        [DataType(DataType.Date)]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd}", ApplyFormatInEditMode = true)]
        [Display(Name = "UPDATEDDATE", ResourceType = typeof(Resources.Resource__typeOfDevice))]
        public DateTime UPDATEDDATE { get; set; }

        [Display(Name = "UPDATEDUSER", ResourceType = typeof(Resources.Resource__typeOfDevice))]
        public int UPDATEDUSER { get; set; }

        public IFormFile ImageFile { get; set; }
    }
}