﻿@model PSafe.AM.Models.GroupModel

@{
    ViewBag.Title = "Chi tiết";

}
<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-lg-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>@PSafe.AM.Resources.Resource.Detail @PSafe.AM.Resources.Resource.Group</h5>
                    <div class="ibox-tools">
                        @Html.ActionLink(@PSafe.AM.Resources.Resource.BackToList, "Index", "Groups", null, new { @class = "btn btn-primary btn-xs" })
                    </div>
                </div>
                <div class="ibox-content">

                    <dl class="dl-horizontal">

                        <dt>
                            @Html.DisplayNameFor(model => model.GroupName)
                        </dt>
                        <dd>
                            @Html.DisplayFor(model => model.GroupName)
                        </dd>

                        <dt>
                            @Html.DisplayNameFor(model => model.GroupDescription)
                        </dt>
                        <dd>
                            @Html.DisplayFor(model => model.GroupDescription)
                        </dd>

                    </dl>
                    @Html.ActionLink(@PSafe.AM.Resources.Resource.Edit, "Edit", "Groups", new { id = Model.GroupID }, new { @class = "btn btn-primary" })

                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <environment names="Development,Staging,Production">
        <link rel="stylesheet" href="~/lib/toastr/toastr.min.css" />
    </environment>
}

@section Scripts {
    <environment names="Development,Staging,Production">
        <script src="~/lib/validate/jquery.validate.min.js"></script>
        <script src="~/lib/toastr/toastr.js"></script>
        <script src="~/lib/toastr/toastr.min.js"></script>
    </environment>

    @if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "warning")
    {
        <script type="text/javascript">
            toastr.warning('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "error")
    {
        <script type="text/javascript">
            toastr.error('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "success")
    {
        <script type="text/javascript">
            toastr.success('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
}