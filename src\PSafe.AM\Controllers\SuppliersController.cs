﻿using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using PSafe.AM.Common;
using PSafe.AM.Models;
using PSafe.Common.UserEnums;
using PSafe.Core.Domains;
using PSafe.Core.Interface;
using PSafe.Infrastructure.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Security.Claims;
using System.Web;
using static PSafe.Common.CommonEnums;

namespace PSafe.AM.Controllers
{
    [AuthorizeActionFilter(EPERMISSIONS_AM.categoryManage)]
    public class SuppliersController : Controller
    {
        private readonly ISupplierRepository _supplierRepository;
        private readonly IUserRepository _userRepository;
        private readonly IMapper _mapper;
        private readonly IHistorySystemRepository _historySystemRepository;
        private readonly IHttpContextAccessor _accessor;
        private readonly ILogger<SuppliersController> _logger;

        public SuppliersController(ISupplierRepository supplierRepository, IUserRepository userRepository, IMapper mapper, IHistorySystemRepository historySystemRepository,
            IHttpContextAccessor accessor, ILogger<SuppliersController> logger)
        {
            _supplierRepository = supplierRepository;
            _userRepository = userRepository;
            _mapper = mapper;
            _historySystemRepository = historySystemRepository;
            _accessor = accessor;
            _logger = logger;
        }

        public ActionResult Index()
        {
            StatusQuery Notification;
            List<SupplierModel> _listSupplierModel = new List<SupplierModel>();
            try
            {
                var _listSupplier = _supplierRepository.GetAll().ToList();

                _listSupplierModel = _mapper.Map<List<Supplier>, List<SupplierModel>>(_listSupplier);

                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }
            }
            catch(Exception ex)
            {
                _logger.LogError("Supplier/Index: " + ex.Message);

                Notification = new StatusQuery("error", "", "Xem danh sách thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;
            }

            return View(_listSupplierModel);
        }

        // GET: /supplier/Details/
        public ActionResult Details(int id)
        {
            try
            {
                var _supplier = _supplierRepository.GetById(id);

                var _listSupplier = _mapper.Map<Supplier, SupplierModel>(_supplier);

                if (_listSupplier != null)
                {
                    try
                    {
                        if (TempDataHelper.Get<StatusQuery>(TempData, "Notification") is StatusQuery Notification)
                        {
                            ViewBag.Status = Notification.Status;
                            ViewBag.Value = Notification.Value;
                            ViewBag.Type = Notification.Type;
                        }

                        ViewBag.UPDATEDUSER = _userRepository.GetById(_listSupplier.UPDATEDUSER).UserName;
                        ViewBag.CREATEDUSER = _userRepository.GetById(_listSupplier.CREATEDUSER).UserName;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError("Supplier/Details: " + ex.Message);
                        Console.WriteLine(ex.Message);
                    }

                    return View(_listSupplier);
                }
                else
                { 
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                    return RedirectToAction("Index");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Supplier/Details: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                return RedirectToAction("Index");
            }
        }

        // GET: /supplier/Create
        public ActionResult Create()
        {
            return View();
        }

        // POST: /supplier/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create([Bind("NAME, DESCRIPTION")] SupplierModel supplierModel)
        {
            StatusQuery Notification;
            if (ModelState.IsValid)
            {
                try
                {
                    var _systemUser = GetSesson();

                    if (_systemUser == null)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                        return RedirectToAction("Logout", "Security");
                    }

                    supplierModel.CREATEDDATE = DateTime.Now;
                    supplierModel.CREATEDUSER = _systemUser.Id;
                    supplierModel.UPDATEDDATE = DateTime.Now;
                    supplierModel.UPDATEDUSER = _systemUser.Id;

                    var _supplier = _mapper.Map<SupplierModel, Supplier>(supplierModel);

                    _supplierRepository.Insert(_supplier);

                    var statusInsert = _supplierRepository.SaveChanges();

                    if (statusInsert > 0)
                    {
                        string StringDescription = new GetStringHistorySystem().Get(_systemUser.UserName, Resources.Resource.Create, _supplier.Name.ToString(), Resources.Resource.Suppliers);

                        InsertHistorySystem((int)EACTION_TYPE.CREATE, (int)EnumControllerName.SUPPLIERS, StringDescription, null, _supplier);

                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Thêm mới thành công"));
                        return RedirectToAction("Index");
                    }
                    else
                    {
                        Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        return View(supplierModel);
                    }
                }
                catch(Exception ex)
                {
                    _logger.LogError("Supplier/Create: " + ex.Message);

                    Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return View(supplierModel);
                }
            }
            Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;

            return View(supplierModel);
        }

        // GET: /supplier/Edit/5
        public ActionResult Edit(int id)
        {
            try
            {
                var _supplier = _supplierRepository.GetById(id);

                var _supplierModel = _mapper.Map<Supplier, SupplierModel>(_supplier);

                if (_supplierModel != null)
                {
                    return View(_supplierModel);
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                    return RedirectToAction("Index");
                }
            }
            catch(Exception ex)
            {
                _logger.LogError("Supplier/Edit: " + ex.Message);
                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                return RedirectToAction("Index");
            }
        }

        // POST: /supplier/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit([Bind("SUPPLIERID, NAME, DESCRIPTION")] SupplierModel supplierModel)
        {
            StatusQuery Notification;
            if (ModelState.IsValid)
            {
                try
                {
                    var _systemUser = GetSesson();

                    if (_systemUser == null)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                        return RedirectToAction("Logout", "Security");
                    }

                    var supplier = _supplierRepository.GetById(supplierModel.SUPPLIERID);

                    var _supplierTemp = _mapper.Map<Supplier, SupplierModel>(supplier);
                    var supplierOld = _mapper.Map<SupplierModel, Supplier>(_supplierTemp);

                    supplier.Name = supplierModel.NAME;
                    supplier.Description = supplierModel.DESCRIPTION;

                    supplier.UpdatedDate = DateTime.Now;
                    supplier.UpdatedBy = _systemUser.Id;

                    _supplierRepository.Update(supplier);

                    var updateStatus = _supplierRepository.SaveChanges();

                    if (updateStatus > 0)
                    {
                        string StringDescription = new GetStringHistorySystem().Get(_systemUser.UserName, Resources.Resource.Edit, supplier.Name.ToString(), Resources.Resource.Suppliers);

                        InsertHistorySystem((int)EACTION_TYPE.EDIT, (int)EnumControllerName.SUPPLIERS, StringDescription, supplierOld, supplier);

                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Sửa thành công"));

                        return RedirectToAction("Index");
                    }
                    else
                    {
                        Notification = new StatusQuery("error", "", "Sửa thất bại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        return View(supplierModel);
                    }
                }
                catch(Exception ex)
                {
                    _logger.LogError("Supplier/Edit: " + ex.Message);

                    Notification = new StatusQuery("error", "", "Sửa thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return View(supplierModel);
                }
            }

            Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;

            return View(supplierModel);
        }

        // GET: /supplier/Delete/5
        public ActionResult Delete(int id)
        {
            StatusQuery Notification;
            SupplierModel _supplierModel = new SupplierModel();
            try
            {
                var _supplier = _supplierRepository.GetById(id);

                _supplierModel = _mapper.Map<Supplier, SupplierModel>(_supplier);

                try
                {
                    ViewBag.CREATEDUSER = _userRepository.GetById(_supplierModel.CREATEDUSER).UserName;
                    ViewBag.UPDATEDUSER = _userRepository.GetById(_supplierModel.UPDATEDUSER).UserName;
                }
                catch (Exception ex)
                {
                    _logger.LogError("Supplier/Delete: " + ex.Message);

                    Console.WriteLine(ex.Message);
                }

                if (_supplierModel == null)
                {
                    Notification = new StatusQuery("warning", "", "Nhà cung cấp không tồn tại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return RedirectToAction("Index");
                }
            }
            catch(Exception ex)
            {
                _logger.LogError("Supplier/Delete: " + ex.Message);

                Notification = new StatusQuery("warning", "", "Xóa thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;

                return RedirectToAction("Index");
            }
            return View(_supplierModel);
        }


        // POST: /Suppliers/Delete/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int id)
        {
            try
            {
                var systemUser = GetSesson();

                if (systemUser == null)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                    return RedirectToAction("Logout", "Security");
                }


                var supplier = _supplierRepository.GetById(id);

                _supplierRepository.Delete(supplier);

                var deleteStatus = _supplierRepository.SaveChanges();

                if (deleteStatus > 0)
                {
                    
                    string StringDescription = new GetStringHistorySystem().Get(systemUser.UserName, Resources.Resource.Delete, supplier.Name.ToString(), Resources.Resource.Suppliers);

                    InsertHistorySystem((int)EACTION_TYPE.DELETE, (int)EnumControllerName.SUPPLIERS, StringDescription, supplier, null);

                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Xóa thành công"));
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));
                }

                return RedirectToAction("Index");
            }
            catch(Exception ex)
            {
                _logger.LogError("Supplier/DeleteConfirmed: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));
            }
            return RedirectToAction("Index");
        }

        public User GetSesson()
        {
            var SessionUserSystem = HttpContext.Session.GetComplexData<User>("SessionUserSystemId_" + HttpContext.Session.GetString("SessionUserSystemId"));
            if (SessionUserSystem != null)
            {
                var _user = _userRepository.GetById(SessionUserSystem.Id);

                if (_user != null)
                {
                    if (!_user.Actived)
                    {
                        HttpContext.Session.Clear();
                        return null;
                    }
                    return _user;
                }
            }
            return null;
        }

        private void InsertHistorySystem(int action_type, int controllerName, string description, object OldModel, object NewModel)
        {
            try
            {
                var ipAddress = _accessor.HttpContext.Connection.RemoteIpAddress.ToString();

                HistorySystem history = new HistorySystem
                {
                    ActionType = action_type,
                    ActionTime = DateTime.Now,
                    Description = description,
                    OldObject = null,
                    NewObject = null,
                    UserId = int.Parse(HttpContext.Session.GetString("SessionUserSystemId")),
                    IpAddress = ipAddress,
                    ControllerName = controllerName
                };

                _historySystemRepository.Insert(history);

                _historySystemRepository.SaveChanges();
            }
            catch (Exception ex)
            {
                _logger.LogError("Supplier/InsertHistorySystem: " + ex.Message);
            }
        }
    }
}