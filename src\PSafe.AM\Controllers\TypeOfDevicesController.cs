﻿using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PSafe.AM.Common;
using PSafe.AM.Models;
using PSafe.Common;
using PSafe.Common.UserEnums;
using PSafe.Core.Domains;
using PSafe.Core.Interface;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using static PSafe.Common.CommonEnums;

namespace PSafe.AM.Controllers
{
    [AuthorizeActionFilter(EPERMISSIONS_AM.categoryManage)]
    public class TypeOfDevicesController : Controller
    {
        private readonly ITypeOfDeviceRepository _typeOfDeviceRepository;
        private readonly IUserRepository _userRepository;
        private readonly IMapper _mapper;
        private readonly IHistorySystemRepository _historySystemRepository;
        private readonly IHttpContextAccessor _accessor;
        private readonly IConfiguration _configuration;
        private readonly ILogger<TypeOfDevicesController> _logger;

        public TypeOfDevicesController(ITypeOfDeviceRepository typeOfDeviceRepository, IUserRepository userRepository, IMapper mapper, 
            IHistorySystemRepository historySystemRepository, IHttpContextAccessor accessor, ILogger<TypeOfDevicesController> logger,
            IConfiguration configuration)
        {
            _typeOfDeviceRepository = typeOfDeviceRepository;
            _userRepository = userRepository;
            _mapper = mapper;
            _historySystemRepository = historySystemRepository;
            _accessor = accessor;
            _configuration = configuration;
            _logger = logger;
        }

        public ActionResult Index()
        {
            StatusQuery Notification;
            List<TypeOfDeviceModel> _listTypeOfDeviceModel = new List<TypeOfDeviceModel>();
            try
            {
                var _listTypeOfDevice = _typeOfDeviceRepository.GetAll().ToList();

                _listTypeOfDeviceModel = _mapper.Map<List<Core.Domains.TypeOfDevice>, List<TypeOfDeviceModel>>(_listTypeOfDevice);

                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }
            }
            catch(Exception ex)
            {
                _logger.LogError("TypeOfDevices/Index: " + ex.Message);

                Notification = new StatusQuery("error", "", "Xem danh sách thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;
            }

            return View(_listTypeOfDeviceModel);
        }

        // GET: /typeOfDevice/Details/
        public ActionResult Details(int id)
        {
            try
            {
                var _typeOfDevice = _typeOfDeviceRepository.GetById(id);

                var _typeOfDeviceModel = _mapper.Map<Core.Domains.TypeOfDevice, TypeOfDeviceModel>(_typeOfDevice);

                if (_typeOfDeviceModel != null)
                {
                    try
                    {
                        if (TempDataHelper.Get<StatusQuery>(TempData, "Notification") is StatusQuery Notification)
                        {
                            ViewBag.Status = Notification.Status;
                            ViewBag.Value = Notification.Value;
                            ViewBag.Type = Notification.Type;
                        }

                        ViewBag.UPDATEDUSER = _userRepository.GetById(_typeOfDeviceModel.UPDATEDUSER).UserName;
                        ViewBag.CREATEDUSER = _userRepository.GetById(_typeOfDeviceModel.CREATEDUSER).UserName; 
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError("TypeOfDevices/Details: " + ex.Message);
                        Console.WriteLine(ex.Message);
                    }

                    return View(_typeOfDeviceModel);
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                    return RedirectToAction("Index");
                }
            }
            catch(Exception ex)
            {
                _logger.LogError("TypeOfDevices/Details: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                return RedirectToAction("Index");
            }
        }

        // GET: /typeOfDevice/Create
        public ActionResult Create()
        {
            return View();
        }

        // POST: /typeOfDevice/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create([Bind("TYPEOFDEVICEID, TYPENAME, DESCRIPTION, ImageFile, ICON")] TypeOfDeviceModel typeOfDeviceModel)
        {
            StatusQuery Notification;

            if(typeOfDeviceModel.ImageFile == null)
            {
                ModelState.AddModelError("ICON", "Vui lòng chọn file");
            }

            if (ModelState.IsValid)
            {
                try
                {
                    var systemUser = GetSesson();

                    if (systemUser == null)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                        return RedirectToAction("Logout", "Security");
                    }

                    if (typeOfDeviceModel.ImageFile != null && typeOfDeviceModel.ImageFile.FileName.Length > 0)
                    {
                        var fileName = DateTime.Now.Date.ToString("MM_dd_yyyy") + "_" + typeOfDeviceModel.ImageFile.FileName.Substring(typeOfDeviceModel.ImageFile.FileName.LastIndexOf("\\") + 1);
                        typeOfDeviceModel.ICON = fileName;

                        var userNameFTP = Utils.DecodePassword(_configuration.GetSection("FTP:UserName").Value, Utils.EncodeType.SHA_256);
                        var passwordFTP = Utils.DecodePassword(_configuration.GetSection("FTP:Password").Value, Utils.EncodeType.SHA_256);
                        var hostFTP = _configuration.GetSection("FTP:Host").Value + ":" + _configuration.GetSection("FTP:Port").Value;

                        byte[] fileBytes;

                        using (var ms = new MemoryStream())
                        {
                            typeOfDeviceModel.ImageFile.CopyTo(ms);
                            fileBytes = ms.ToArray();
                        }

                        var pathString = "Images/Icons/";
                        var checkDirectory = hostFTP + "/";

                        foreach (var item in pathString.Split("/"))
                        {
                            checkDirectory += item + "/";
                            if (!GetDirectoryExits(checkDirectory, userNameFTP, passwordFTP))
                            {
                                CreateDirectory(checkDirectory, userNameFTP, passwordFTP);
                            }
                        }

                        using var client = new WebClient();
                        client.Credentials = new NetworkCredential(userNameFTP, passwordFTP);
                        client.UploadData(checkDirectory + fileName, fileBytes);
                    }

                    typeOfDeviceModel.CREATEDDATE = DateTime.Now;
                    typeOfDeviceModel.CREATEDUSER = systemUser.Id;
                    typeOfDeviceModel.UPDATEDDATE = DateTime.Now;
                    typeOfDeviceModel.UPDATEDUSER = systemUser.Id;

                    var _typeOfDevice = _mapper.Map<TypeOfDeviceModel, Core.Domains.TypeOfDevice>(typeOfDeviceModel);

                    _typeOfDeviceRepository.Insert(_typeOfDevice);

                    var statusInsert = _typeOfDeviceRepository.SaveChanges();

                    if (statusInsert > 0)
                    {
                        string StringDescription = new GetStringHistorySystem().Get(systemUser.UserName, Resources.Resource.Edit, _typeOfDevice.TypeName.ToString(), Resources.Resource.TypeOfDevice);

                        InsertHistorySystem((int)EACTION_TYPE.CREATE, (int)EnumControllerName.TYPEOFDEVICES, StringDescription, null, _typeOfDevice);

                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Thêm mới thành công"));
                        return RedirectToAction("Index");
                    }
                    else
                    {
                        Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        return View(typeOfDeviceModel);
                    }
                }
                catch(Exception ex)
                {
                    _logger.LogError("TypeOfDevices/Create: " + ex.Message);

                    Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return View(typeOfDeviceModel);
                }
            }
            Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;

            return View(typeOfDeviceModel);
        }

        // GET: /typeOfDevice/Edit/5
        public ActionResult Edit(int id)
        {
            try
            {
                var _typeOfDevice = _typeOfDeviceRepository.GetById(id);

                var _typeOfDeviceModel = _mapper.Map<Core.Domains.TypeOfDevice, TypeOfDeviceModel>(_typeOfDevice);

                if (_typeOfDeviceModel != null)
                {
                    return View(_typeOfDeviceModel);
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                    return RedirectToAction("Index");
                }
            }
            catch(Exception ex)
            {
                _logger.LogError("TypeOfDevices/Create: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                return RedirectToAction("Index");
            }
        }

        // POST: /typeOfDevice/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit([Bind("TYPEOFDEVICEID, TYPENAME, DESCRIPTION, ImageFile")] TypeOfDeviceModel typeOfDeviceModel)
        {
            StatusQuery Notification;
            if (ModelState.IsValid)
            {
                try
                {
                    var systemUser = GetSesson();
                    if (systemUser == null)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                        return RedirectToAction("Logout", "Security");
                    }

                    var typeOfDevice = _typeOfDeviceRepository.GetById(typeOfDeviceModel.TYPEOFDEVICEID);

                    var _typeOfDeviceTemp = _mapper.Map<Core.Domains.TypeOfDevice, TypeOfDeviceModel>(typeOfDevice);
                    var typeOfDeviceOld = _mapper.Map<TypeOfDeviceModel, Core.Domains.TypeOfDevice>(_typeOfDeviceTemp);

                    if (typeOfDeviceModel.ImageFile != null && typeOfDeviceModel.ImageFile.FileName.Length > 0)
                    {
                        var fileName = DateTime.Now.Date.ToString("MM_dd_yyyy") + "_" + typeOfDeviceModel.ImageFile.FileName.Substring(typeOfDeviceModel.ImageFile.FileName.LastIndexOf("\\") + 1);
                        typeOfDevice.Icon = fileName;

                        var userNameFTP = Utils.DecodePassword(_configuration.GetSection("FTP:UserName").Value, Utils.EncodeType.SHA_256);
                        var passwordFTP = Utils.DecodePassword(_configuration.GetSection("FTP:Password").Value, Utils.EncodeType.SHA_256);
                        var hostFTP = _configuration.GetSection("FTP:Host").Value + ":" + _configuration.GetSection("FTP:Port").Value;

                        byte[] fileBytes;

                        using (var ms = new MemoryStream())
                        {
                            typeOfDeviceModel.ImageFile.CopyTo(ms);
                            fileBytes = ms.ToArray();
                        }

                        var pathString = "Images/Icons/";
                        var checkDirectory = hostFTP + "/";

                        foreach (var item in pathString.Split("/"))
                        {
                            checkDirectory += item + "/";
                            if (!GetDirectoryExits(checkDirectory, userNameFTP, passwordFTP))
                            {
                                CreateDirectory(checkDirectory, userNameFTP, passwordFTP);
                            }
                        }

                        using var client = new WebClient();
                        client.Credentials = new NetworkCredential(userNameFTP, passwordFTP);
                        client.UploadData(checkDirectory + fileName, fileBytes);
                    }

                    typeOfDevice.TypeName = typeOfDeviceModel.TYPENAME;
                    typeOfDevice.Description = typeOfDeviceModel.DESCRIPTION;

                    typeOfDevice.UpdatedDate = DateTime.Now;
                    typeOfDevice.UpdatedBy = systemUser.Id;

                    _typeOfDeviceRepository.Update(typeOfDevice);

                    var updateStatus = _typeOfDeviceRepository.SaveChanges();

                    if (updateStatus > 0)
                    {
                        string StringDescription = new GetStringHistorySystem().Get(systemUser.UserName, Resources.Resource.Edit, typeOfDevice.TypeName.ToString(), Resources.Resource.TypeOfDevice);
                        InsertHistorySystem((int)EACTION_TYPE.EDIT, (int)EnumControllerName.TYPEOFDEVICES, StringDescription, typeOfDeviceOld, typeOfDevice);

                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Sửa thành công"));

                        return RedirectToAction("Index");
                    }
                    else
                    {
                        Notification = new StatusQuery("error", "", "Sửa thất bại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        return View(typeOfDeviceModel);
                    }
                }
                catch(Exception ex)
                {
                    _logger.LogError("TypeOfDevices/Edit: " + ex.Message);

                    Notification = new StatusQuery("error", "", "Sửa thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return View(typeOfDeviceModel);
                }
            }

            Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;

            return View(typeOfDeviceModel);
        }

        // GET: /typeOfDevice/Delete/5
        public ActionResult Delete(int id)
        {
            StatusQuery Notification;
            try
            {
                var _typeOfDevice = _typeOfDeviceRepository.GetById(id);

                var _typeOfDeviceModel = _mapper.Map<Core.Domains.TypeOfDevice, TypeOfDeviceModel>(_typeOfDevice);

                if (_typeOfDeviceModel == null)
                {
                    Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return RedirectToAction("Index");
                }

                try
                {
                    ViewBag.UPDATEDUSER = _userRepository.GetById(_typeOfDeviceModel.UPDATEDUSER).UserName;
                    ViewBag.CREATEDUSER = _userRepository.GetById(_typeOfDeviceModel.CREATEDUSER).UserName;
                }
                catch (Exception ex)
                {
                    _logger.LogError("TypeOfDevices/Delete: " + ex.Message);
                    Console.WriteLine(ex.Message);
                }

                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }

                return View(_typeOfDeviceModel);
            }
            catch(Exception ex)
            {
                _logger.LogError("TypeOfDevices/Delete: " + ex.Message);

                Notification = new StatusQuery("warning", "", "Xóa thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;

                return RedirectToAction("Index");
            }
        }


        // POST: /TypeOfDevices/Delete/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int id)
        {
            try
            {
                var systemUserSession = GetSesson();

                if (systemUserSession == null)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                    return RedirectToAction("Logout", "Security");
                }

                var typeOfDevice = _typeOfDeviceRepository.GetById(id);

                _typeOfDeviceRepository.Delete(typeOfDevice);

                var deleteStatus = _typeOfDeviceRepository.SaveChanges();

                if (deleteStatus > 0)
                {
                    string StringDescription = new GetStringHistorySystem().Get(systemUserSession.UserName, Resources.Resource.Delete, typeOfDevice.TypeName.ToString(), Resources.Resource.TypeOfDevice);

                    InsertHistorySystem((int)EACTION_TYPE.DELETE, (int)EnumControllerName.TYPEOFDEVICES, StringDescription, typeOfDevice, null);

                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Xóa thành công"));
                    return RedirectToAction("Index");
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));
                    return RedirectToAction("Delete", "TypeOfDevices", new { id });
                }
            }
            catch(Exception ex)
            {
                _logger.LogError("TypeOfDevices/DeleteConfirmed: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));
                return RedirectToAction("Delete", "TypeOfDevices", new { id });
            }
        }

        public User GetSesson()
        {
            var SessionUserSystem = HttpContext.Session.GetComplexData<User>("SessionUserSystemId_" + HttpContext.Session.GetString("SessionUserSystemId"));
            if (SessionUserSystem != null)
            {
                var _user = _userRepository.GetById(SessionUserSystem.Id);

                if (_user != null)
                {
                    if (!_user.Actived)
                    {
                        HttpContext.Session.Clear();
                        return null;
                    }
                    return _user;
                }
            }
            return null;
        }

        private bool GetDirectoryExits(string path, string userNameFTP, string passwordFTP)
        {
            try
            {
                WebRequest request = WebRequest.Create(path);
                request.Credentials = new NetworkCredential(userNameFTP, passwordFTP);
                request.Method = WebRequestMethods.Ftp.ListDirectory;

                FtpWebResponse response = (FtpWebResponse)request.GetResponse();
                long size = response.ContentLength;
                response.Close();

                return true;
            }
            catch (WebException e)
            {
                _logger.LogInformation("CreateDirectory Status: {0}", e.Message);
                return false;
            }
        }

        private bool CreateDirectory(string path, string userNameFTP, string passwordFTP)
        {
            try
            {
                WebRequest request = WebRequest.Create(path);
                request.Credentials = new NetworkCredential(userNameFTP, passwordFTP);
                request.Method = WebRequestMethods.Ftp.MakeDirectory;
                using (var resp = (FtpWebResponse)request.GetResponse()) {
                    if (resp.StatusCode == FtpStatusCode.PathnameCreated)
                    {
                        resp.Close();
                        return true;
                    }
                    resp.Close();
                };
                return false;

            }
            catch (WebException e)
            {
                String status = ((FtpWebResponse)e.Response).StatusDescription;
                _logger.LogError(status);
                return false;
            }
        }

        private void InsertHistorySystem(int action_type, int controllerName, string description, object OldModel, object NewModel)
        {
            try
            {
                var ipAddress = _accessor.HttpContext.Connection.RemoteIpAddress.ToString();

                string jsonOldObject = Newtonsoft.Json.JsonConvert.SerializeObject(OldModel);
                string jsonNewObject = Newtonsoft.Json.JsonConvert.SerializeObject(NewModel);

                HistorySystem history = new HistorySystem
                {
                    ActionType = action_type,
                    ActionTime = DateTime.Now,
                    Description = description,
                    OldObject = jsonOldObject,
                    NewObject = jsonNewObject,
                    UserId = int.Parse(HttpContext.Session.GetString("SessionUserSystemId")),
                    IpAddress = ipAddress,
                    ControllerName = controllerName
                };

                _historySystemRepository.Insert(history);

                _historySystemRepository.SaveChanges();
            }
            catch (Exception ex)
            {
                _logger.LogError("TypeOfDevice/InsertHistorySystem: " + ex.Message);
            }
        }
    }
}