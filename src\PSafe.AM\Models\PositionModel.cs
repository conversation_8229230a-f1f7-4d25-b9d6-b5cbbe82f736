﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace PSafe.AM.Models
{
    public class PositionModel
    {
        [Key]
        [Display(Name = "Id", ResourceType = typeof(Resources.Resource_position))]
        public int Id { get; set; }

        [Required(ErrorMessage = "<PERSON>ui lòng nhập chức vụ")]
        [StringLength(255, MinimumLength = 3, ErrorMessage = "Độ dài từ 3-255 ký tự")]
        [Display(Name = "PositionName", ResourceType = typeof(Resources.Resource_position))]
        public string PositionName { get; set; } = string.Empty;

        [Display(Name = "Description", ResourceType = typeof(Resources.Resource_position))]
        public string Description { get; set; } = string.Empty;
    }
}
