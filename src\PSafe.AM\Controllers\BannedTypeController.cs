﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using PSafe.AM.Common;
using PSafe.Common.UserEnums;
using PSafe.Core.Interface;

namespace PSafe.AM.Controllers
{
    [AuthorizeActionFilter(EPERMISSIONS_AM.categoryManage)]
    public class BannedTypeController : Controller
    {
        private readonly IBannedTypeRepository _bannedTypeRepository;
        private readonly IUserRepository _userRepository;
        private readonly IHistorySystemRepository _historySystemRepository;
        private readonly ILogger<BannedTypeController> _logger;
        private readonly IHttpContextAccessor _accessor;

        public BannedTypeController(IBannedTypeRepository bannedTypeRepository, IUserRepository userRepository,
            IHistorySystemRepository historySystemRepository,
            ILogger<BannedTypeController> logger, IHttpContextAccessor accessor)
        {
            _bannedTypeRepository = bannedTypeRepository;
            _userRepository = userRepository;
            _historySystemRepository = historySystemRepository;
            _logger = logger;
            _accessor = accessor;
        }

        public IActionResult Index()
        {
            return View();
        }
    }
}