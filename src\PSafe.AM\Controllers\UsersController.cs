﻿using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using PSafe.AM.Common;
using PSafe.AM.Models;
using PSafe.Common;
using PSafe.Core.Domains;
using PSafe.Core.Interface;
using PSafe.Core.SharedKernel;
using System;
using System.Collections.Generic;
using System.Linq;
using static PSafe.Common.CommonEnums;
using System.DirectoryServices.AccountManagement;
using Microsoft.Extensions.Configuration;
using PSafe.Common.UserEnums;
using Microsoft.Extensions.Logging;
using System.Diagnostics;

namespace PSafe.AM.Controllers
{
    [AuthorizeActionFilter(EPERMISSIONS_AM.UserManage)]
    public class UsersController : Controller
    {
        private readonly IUserRepository _userRepository;
        private readonly IRoleRepository _roleRepository;
        private readonly ISiteRepository _siteRepository;
        private readonly IMapper _mapper;
        private readonly IHistorySystemRepository _historySystemRepository;
        private readonly IUserInRoleRepository _userInRoleRepository;
        private readonly IDepartmentRepository _departmentRepository;
        private readonly IPositionRepository _positionRepository;
        private readonly IPatrolCameraCalendarRepository _patrolCameraCalendarRepository;
        private readonly IVehicleBannedListHistoryRepository _vehicleBannedListHistoryRepository;
        private readonly IHttpContextAccessor _accessor;
        private readonly IConfiguration _configuration;
        private readonly ILogger<UsersController> _logger;

        public UsersController(IUnitOfWork unitOfWork, IMapper mapper, IHttpContextAccessor accessor,
            ILogger<UsersController> logger, IConfiguration configuration,
            IPatrolCameraCalendarRepository patrolCameraCalendarRepository,
            IVehicleBannedListHistoryRepository vehicleBannedListHistoryRepository,
            ISiteRepository siteRepository)
        {
            _userRepository = unitOfWork.UserRepository;
            _roleRepository = unitOfWork.RoleRepository;
            _mapper = mapper;
            _historySystemRepository = unitOfWork.HistorySystemRepository;
            _departmentRepository = unitOfWork.DepartmentRepository;
            _positionRepository = unitOfWork.PositionRepository;
            _userInRoleRepository = unitOfWork.UserInRoleRepository;
            _accessor = accessor;
            _configuration = configuration;
            _logger = logger;
            _patrolCameraCalendarRepository = patrolCameraCalendarRepository;
            _vehicleBannedListHistoryRepository = vehicleBannedListHistoryRepository;
            _siteRepository = siteRepository;
        }
        public ActionResult Index()
        {
            StatusQuery Notification;
            try
            {
                var listRole = _roleRepository.GetAll();

                var listDropBox = new List<DropDownList>();

                if (listRole != null)
                {
                    foreach (var item in listRole)
                    {
                        var dropDown = new DropDownList()
                        {
                            Id = item.RoleId,
                            Name = item.RoleName
                        };
                        listDropBox.Add(dropDown);
                    }
                }

                ViewBag.ListRole = listDropBox;

                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Users/Index: " + ex.Message);

                Notification = new StatusQuery("error", "", "Xem danh sách thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;
            }

            return View();
        }

        public JsonResult GetIndex()
        {
            try
            {
                List<UserModel> _listUserModel = new List<UserModel>();
                try
                {
                    var _listUser = _userRepository.GetAll().ToList();

                    _listUserModel = _mapper.Map<List<User>, List<UserModel>>(_listUser);

                    foreach (var item in _listUserModel)
                    {
                        var userInRole = _userInRoleRepository.Get(x => x.UserId == item.UserID, null, "Role").ToList();

                        foreach (var role in userInRole)
                        {
                            item.ListRole += role.Role.RoleName + ",";
                        }

                        item.ListRole = item.ListRole != "" ? item.ListRole.Substring(0, item.ListRole.Length - 1) : "";
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("User/Index: " + ex.Message);
                }

                return Json(_listUserModel);
            }
            catch (Exception ex)
            {
                _logger.LogError("Devices/GetIndex: " + ex.Message);

                return Json(null);
            }
        }

        [HttpGet]
        public JsonResult Filter(string Role, int Type_User)
        {
            try
            {
                if (Role != null)
                {
                    List<int> myRoleList = Role.Split(',').Select(s => int.Parse(s)).ToList();

                    var userInRoles = _userInRoleRepository.GetBy(x => myRoleList.Contains(x.RoleId)).Select(x => x.UserId).ToList();

                    if (userInRoles != null)
                    {
                        List<User> users = _userRepository.GetBy(x => userInRoles.Contains(x.Id)).ToList();

                        if (Type_User != -1)
                        {
                            var userList = users.Where(p => p.Type == Type_User).ToList();
                            var _listUserModel = _mapper.Map<List<User>, List<UserModel>>(userList);

                            foreach (var item in _listUserModel)
                            {
                                var userInRole = _userInRoleRepository.Get(x => x.UserId == item.UserID, null, "Role").ToList();

                                foreach (var role in userInRole)
                                {
                                    item.ListRole += role.Role.RoleName + ",";
                                }

                                item.ListRole = item.ListRole != "" ? item.ListRole.Substring(0, item.ListRole.Length - 1) : "";
                            }

                            return Json(_listUserModel);
                        }
                        else
                        {
                            var _listUserModel = _mapper.Map<List<User>, List<UserModel>>(users);
                            foreach (var item in _listUserModel)
                            {
                                var userInRole = _userInRoleRepository.Get(x => x.UserId == item.UserID, null, "Role").ToList();

                                foreach (var role in userInRole)
                                {
                                    item.ListRole += role.Role.RoleName + ",";
                                }

                                item.ListRole = item.ListRole != "" ? item.ListRole.Substring(0, item.ListRole.Length - 1) : "";
                            }
                            return Json(_listUserModel);
                        }
                    }
                }
                else if (Type_User != -1)
                {
                    var _listUser = _userRepository.GetAll().Where(p => p.Type == Type_User).ToList();
                    var _listUserModel = _mapper.Map<List<User>, List<UserModel>>(_listUser);
                    foreach (var item in _listUserModel)
                    {
                        var userInRole = _userInRoleRepository.Get(x => x.UserId == item.UserID, null, "Role").ToList();

                        foreach (var role in userInRole)
                        {
                            item.ListRole += role.Role.RoleName + ",";
                        }

                        item.ListRole = item.ListRole != "" ? item.ListRole.Substring(0, item.ListRole.Length - 1) : "";
                    }
                    return Json(new { _listUserModel });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Devices/GetIndex: " + ex.Message);
            }
            return Json(null);
        }

        // GET: /user/Details/
        public ActionResult Details(int id)
        {
            try
            {
                var _user = _userRepository.GetById(id);

                var _userModel = _mapper.Map<User, UserModel>(_user);

                if (_userModel != null)
                {
                    if (TempDataHelper.Get<StatusQuery>(TempData, "Notification") is StatusQuery Notification)
                    {
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;
                    }
                    try
                    {
                        if (_userModel.DepartmentId != null)
                        {
                            ViewBag.DepartmentName = _departmentRepository.GetById(_userModel.DepartmentId).DepartmentName;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError("User/Details: " + ex.Message);
                        Console.WriteLine(ex.Message);
                    }
                    try
                    {
                        if (_userModel.PositionId != null)
                        {
                            ViewBag.PositionName = _positionRepository.GetById(_userModel.PositionId).PositionName;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError("User/Details: " + ex.Message);
                        Console.WriteLine(ex.Message);
                    }
                    try
                    {
                        if (_userModel.PositionId != null)
                        {
                            ViewBag.SiteName = _siteRepository.GetById(_userModel.SiteID).SiteName;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError("User/Details: " + ex.Message);
                        Console.WriteLine(ex.Message);
                    }

                    try
                    {
                        ViewBag.CreatedUser = _userRepository.GetById(_userModel.CreatedUser).UserName;
                        ViewBag.UpdatedUser = _userRepository.GetById(_userModel.UpdatedUser).UserName;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError("User/Details: " + ex.Message);
                        Console.WriteLine(ex.Message);
                    }
                    return View(_userModel);
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại!"));

                    return RedirectToAction("Index");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("User/Details: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại!"));

                return RedirectToAction("Index");
            }
        }

        // GET: /user/Create
        public ActionResult Create()
        {
            UserModel user = new UserModel();
            user = GetListTypeUser(user);
            user.Actived = true;

            return View(user);
        }

        // POST: /user/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create([Bind("UserName, Password, Phone, Email, Actived, LockReason, DepartmentId, PositionId, Type, FullName, SiteID")] UserModel userModel)
        {
            StatusQuery Notification;
            if (userModel.Type != 1)
            {
                if (userModel.Password == null)
                {
                    ModelState.AddModelError("Password", "Vui lòng nhập mật khẩu");
                }
                else if (userModel.Password.Length < 3)
                {
                    ModelState.AddModelError("Password", "Độ dài từ 3-255 ký tự");
                }
            }

            if (ModelState.IsValid)
            {
                try
                {
                    var _systemUser = GetSesson();

                    if (_systemUser == null)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                        return RedirectToAction("Logout", "Security");
                    }

                    //Kiểm tra tài khoản bên AD
                    if (userModel.Type == 1)
                    {
                        Stopwatch stopWatch = new Stopwatch();
                        stopWatch.Start();

                        var validateUserAd = FnImp(userModel.UserName);

                        stopWatch.Stop();
                        TimeSpan ts = stopWatch.Elapsed;
                        string elapsedTime_FnImp = String.Format("{0:00}:{1:00}:{2:00}.{3:00}", ts.Hours, ts.Minutes, ts.Seconds, ts.Milliseconds / 10);
                        _logger.LogError("User/Create: Time_GetContext: " + elapsedTime_FnImp);

                        if (!validateUserAd)
                        {
                            Notification = new StatusQuery("error", "", "Xác thực thất bại");
                            ViewBag.Status = Notification.Status;
                            ViewBag.Value = Notification.Value;
                            ViewBag.Type = Notification.Type;

                            userModel = GetListTypeUser(userModel);

                            return View(userModel);
                        }
                    }
                    //End Kiểm tra tài khoản bên AD
                    var obj = _userRepository.GetAll().SingleOrDefault(item => item.UserName.ToLower() == userModel.UserName.ToLower() && item.Type == userModel.Type);
                    if (obj != null)
                    {
                        Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        userModel = GetListTypeUser(userModel);

                        ModelState.AddModelError("UserName", "Tên người dùng đã tồn tại");

                        return View(userModel);
                    }

                    HashEncoding hashEncoding = new HashEncoding();

                    userModel.CreatedDate = DateTime.Now;
                    userModel.CreatedUser = _systemUser.Id;
                    userModel.UpdatedDate = DateTime.Now;
                    userModel.UpdatedUser = _systemUser.Id;
                    userModel.PositionId = userModel.PositionId != -1 ? userModel.PositionId : null;
                    userModel.DepartmentId = userModel.DepartmentId != -1 ? userModel.DepartmentId : null;
                    userModel.SiteID = userModel.SiteID.Trim() != "-1" ? userModel.SiteID.Trim() : null;
                    userModel.Password = userModel.Type == 1 ? null : hashEncoding.HashPassword(userModel.Password,userModel.UserName);

                    var _user = _mapper.Map<UserModel, User>(userModel);

                    _userRepository.Insert(_user);

                    var addStatus = _userRepository.SaveChanges();

                    if (addStatus > 0)
                    {
                        string StringDescription = new GetStringHistorySystem().Get(_systemUser.UserName, Resources.Resource.Create, _user.UserName.ToString(), Resources.Resource.User);

                        InsertHistorySystem((int)EACTION_TYPE.CREATE, (int)EnumControllerName.USERS, StringDescription, null, _user);

                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Thêm mới thành công"));

                        return RedirectToAction("Index");
                    }
                    else
                    {
                        Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        userModel = GetListTypeUser(userModel);

                        return View(userModel);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("User/Create: " + ex.Message);

                    Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    userModel = GetListTypeUser(userModel);

                    return View(userModel);
                }
            }
            Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;

            userModel = GetListTypeUser(userModel);

            return View(userModel);
        }

        // GET: /user/Edit/5
        public ActionResult Edit(int id)
        {
            try
            {
                var _systemUser = GetSesson();

                if (_systemUser == null)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                    return RedirectToAction("Logout", "Security");
                }

                var _user = _userRepository.GetById(id);

                var _userModel = _mapper.Map<User, UserModel>(_user);

                _userModel = GetListTypeUser(_userModel);

                ViewBag.MySelf = _systemUser.Id == id;

                if (_userModel != null)
                {
                    return View(_userModel);
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                    return RedirectToAction("Index");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("User/Edit: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                return RedirectToAction("Index");
            }
        }

        // GET: /user/Edit/5
        public ActionResult EditWinform(int id)
        {
            try
            {
                var _systemUser = GetSesson();

                if (_systemUser == null)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                    return PartialView("Logout", "Security");
                }

                var _user = _userRepository.GetById(id);

                var _userModel = _mapper.Map<User, UserModel>(_user);

                _userModel = GetListTypeUser(_userModel);

                ViewBag.MySelf = _systemUser.Id == id;

                if (_userModel != null)
                {
                    return PartialView(_userModel);
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                    return PartialView("Index");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("User/Edit: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                return PartialView("Index");
            }
        }

        // POST: /user/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit([Bind("UserID, UserName, Phone, Email, LockReason, Actived, DepartmentId, PositionId, Type, FullName, SiteID")] UserModel userModel)
        {
            StatusQuery Notification;
            if (ModelState.IsValid)
            {
                try
                {
                    var _systemUser = GetSesson();

                    if (_systemUser == null)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                        return RedirectToAction("Logout", "Security");
                    }

                    var user = _userRepository.GetById(userModel.UserID);

                    var _userTemp = _mapper.Map<User, UserModel>(user);
                    var UserOld = _mapper.Map<UserModel, User>(_userTemp);

                    // kiểm tra tên người dùng có phải là người đang online hay không?
                    if (_systemUser.UserName.ToLower() == user.UserName.ToLower() && _systemUser.Type == user.Type)
                    {
                        var obj = _userRepository.Get(item => item.UserName.ToLower() == userModel.UserName.ToLower() && item.Type == userModel.Type).SingleOrDefault();

                        // Tên người dùng tồn tại nhưng không phải user hiện tại
                        if (obj == null || user.UserName == userModel.UserName)
                        {
                            if (user.Type != 1)
                            {
                                user.UserName = userModel.UserName;
                            }

                            user.UpdatedDate = DateTime.Now;
                            user.UpdatedBy = _systemUser.Id;
                            user.Phone = userModel.Phone;
                            user.Email = userModel.Email;
                            user.FullName = userModel.FullName;
                            user.SiteId = userModel.SiteID;
                            user.LockReason = userModel.LockReason;
                            user.Actived = userModel.Actived;
                            user.DepartmentId = userModel.DepartmentId != -1 ? userModel.DepartmentId : null;
                            user.PositionId = userModel.PositionId != -1 ? userModel.PositionId : null;
                            user.SiteId = userModel.SiteID.Trim() != "-1" ? userModel.SiteID.Trim() : null;

                            _userRepository.Update(user);

                            var updateStatusNow = _userRepository.SaveChanges();

                            if (updateStatusNow > 0)
                            {
                                string StringDescription = new GetStringHistorySystem().Get(_systemUser.UserName, Resources.Resource.Edit, user.UserName.ToString(), Resources.Resource.User);

                                InsertHistorySystem((int)EACTION_TYPE.EDIT, (int)EnumControllerName.USERS, StringDescription, UserOld, user);

                                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Sửa thành công, vui lòng đăng nhập lại"));
                                return RedirectToAction("Logout", "Security");
                            }
                            else
                            {
                                Notification = new StatusQuery("error", "", "Sửa thất bại");
                                ViewBag.Status = Notification.Status;
                                ViewBag.Value = Notification.Value;
                                ViewBag.Type = Notification.Type;

                                userModel = GetListTypeUser(userModel);

                                return View(userModel);
                            }
                        }
                        else
                        {

                            Notification = new StatusQuery("warning", "", "Tên người dùng đã tồn tại");
                            ViewBag.Status = Notification.Status;
                            ViewBag.Value = Notification.Value;
                            ViewBag.Type = Notification.Type;

                            userModel = GetListTypeUser(userModel);

                            ModelState.AddModelError("UserName", "Vui lòng nhập tên người dùng khác");

                            return View(userModel);
                        }
                    }
                    else
                    {
                        var obj = _userRepository.Get(item => item.UserName.ToLower() == userModel.UserName.ToLower() && item.Type == userModel.Type).SingleOrDefault();

                        // Tên người dùng chưa tồn tại
                        if (obj == null || obj.UserName.ToLower() == user.UserName.ToLower())
                        {
                            if (user.Type != 1)
                            {
                                user.UserName = userModel.UserName;
                            }

                            user.UpdatedDate = DateTime.Now;
                            user.UpdatedBy = _systemUser.Id;
                            user.Phone = userModel.Phone;
                            user.Email = userModel.Email;
                            user.FullName = userModel.FullName;
                            user.SiteId = userModel.SiteID;
                            user.LockReason = userModel.LockReason;
                            user.Actived = userModel.Actived;
                            user.DepartmentId = userModel.DepartmentId != -1 ? userModel.DepartmentId : null;
                            user.PositionId = userModel.PositionId != -1 ? userModel.PositionId : null;
                            user.SiteId = userModel.SiteID.Trim() != "-1" ? userModel.SiteID.Trim() : null;

                            _userRepository.Update(user);

                            var updateStatusNow = _userRepository.SaveChanges();

                            if (updateStatusNow > 0)
                            {
                                string StringDescription = new GetStringHistorySystem().Get(_systemUser.UserName, Resources.Resource.Edit, user.UserName.ToString(), Resources.Resource.User);

                                InsertHistorySystem((int)EACTION_TYPE.EDIT, (int)EnumControllerName.USERS, StringDescription, UserOld, user);

                                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Sửa thành công"));

                                return RedirectToAction("Index");
                            }
                            else
                            {
                                Notification = new StatusQuery("error", "", "Sửa thất bại");
                                ViewBag.Status = Notification.Status;
                                ViewBag.Value = Notification.Value;
                                ViewBag.Type = Notification.Type;

                                userModel = GetListTypeUser(userModel);

                                return View(userModel);
                            }
                        }
                        else
                        {
                            Notification = new StatusQuery("warning", "", "Tên người dùng đã tồn tại");
                            ViewBag.Status = Notification.Status;
                            ViewBag.Value = Notification.Value;
                            ViewBag.Type = Notification.Type;

                            userModel = GetListTypeUser(userModel);

                            ModelState.AddModelError("UserName", "Vui lòng nhập tên người dùng khác");

                            return View(userModel);
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("User/Edit: " + ex.Message);

                    Notification = new StatusQuery("error", "", "Sửa thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    userModel = GetListTypeUser(userModel);

                    return View(userModel);
                }
            }

            Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;

            userModel = GetListTypeUser(userModel);

            return View(userModel);
        }

        // GET: /user/Edit/5
        public ActionResult ResetPassword(int id)
        {
            StatusQuery Notification;
            try
            {
                var systemUserSession = GetSesson();

                if (systemUserSession == null)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                    return RedirectToAction("Logout", "Security");
                }

                var _user = _userRepository.GetById(id);

                if (_user.Type != 0)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("warning", "", "Tài khoản này không thể đổi mật khẩu ở đây"));
                    return RedirectToAction("Index");
                }

                ChangePassword _changePassword = new ChangePassword();

                if (_user != null && systemUserSession.Id == id)
                {
                    return RedirectToAction("ChangePassword", "Users", new { id });
                }
                else if (_user != null && systemUserSession.Id != id)
                {
                    _changePassword.UserName = _user.UserName;
                    return View(_changePassword);
                }

                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("User/ResetPassword: " + ex.Message);

                Notification = new StatusQuery("error", "", "Đổi mật khẩu thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;
            }
            return View();
        }

        // POST: /user/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult ResetPassword([Bind("NewPassword, Repeatpassword, UserName")] ChangePassword _changePassword)
        {
            StatusQuery Notification;
            if (ModelState.IsValid)
            {
                try
                {
                    var user = _userRepository.Get(u => u.UserName == _changePassword.UserName && u.Type == 0).SingleOrDefault();

                    var _userTemp = _mapper.Map<User, UserModel>(user);
                    var UserOld = _mapper.Map<UserModel, User>(_userTemp);
                    var has = new HashEncoding();
                    //user.Password = BCrypt.Net.BCrypt.HashPassword(_changePassword.NewPassword);
                    user.Password = has.HashPassword(_changePassword.NewPassword,_changePassword.UserName);

                    _userRepository.Update(user);

                    var updateStatus = _userRepository.SaveChanges();

                    if (updateStatus > 0)
                    {
                        var systemUserSession = GetSesson();

                        if (systemUserSession == null)
                        {
                            TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                            return RedirectToAction("Logout", "Security");
                        }
                        string StringDescription = new GetStringHistorySystem().Get(systemUserSession.UserName, Resources.Resource.ChangePassword, user.UserName.ToString(), Resources.Resource.User);

                        InsertHistorySystem((int)EACTION_TYPE.CHANGE_PASSWORD, (int)EnumControllerName.USERS, StringDescription, UserOld, user);

                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Đổi mật khẩu thành công"));

                        return RedirectToAction("Index");
                    }
                    else
                    {
                        Notification = new StatusQuery("error", "", "Đổi mật khẩu thất bại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        return View(_changePassword);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("User/ResetPassword: " + ex.Message);

                    Notification = new StatusQuery("error", "", "Đổi mật khẩu thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return View(_changePassword);
                }
            }

            Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;

            return View(_changePassword);
        }

        // GET: /user/Edit/5
        public ActionResult ChangePassword(int id)
        {
            StatusQuery Notification;
            try
            {
                var systemUserSession = GetSesson();

                if (systemUserSession == null)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                    return RedirectToAction("Logout", "Security");
                }

                var _user = _userRepository.GetById(id);

                if (_user.Type != 0)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("warning", "", "Tài khoản này không thể đổi mật khẩu ở đây"));
                    return RedirectToAction("Index");
                }

                ChangePassword _changePassword = new ChangePassword();

                if (_user != null)
                {
                    _changePassword.UserName = _user.UserName;
                    return View(_changePassword);
                }

                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("User/ChangePassword: " + ex.Message);

                Notification = new StatusQuery("error", "", "Đổi mật khẩu thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;
            }
            return View();
        }

        // POST: /user/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult ChangePassword([Bind("NewPassword, Repeatpassword, UserName", "OldPassword")] ChangePassword _changePassword)
        {
            StatusQuery Notification;
            if (ModelState.IsValid)
            {
                try
                {
                    var obj = _userRepository.Get(item => item.UserName.ToLower() == _changePassword.UserName.ToLower() && item.Type == 0).FirstOrDefault();

                    if (BCrypt.Net.BCrypt.Verify(_changePassword.OldPassword, obj.Password) != true)
                    {
                        Notification = new StatusQuery("error", "", "Mật khẩu cũ không đúng, vui lòng kiểm tra lại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        return View(_changePassword);
                    }

                    var _userTemp = _mapper.Map<User, UserModel>(obj);
                    var UserOld = _mapper.Map<UserModel, User>(_userTemp);

                    obj.Password = BCrypt.Net.BCrypt.HashPassword(_changePassword.NewPassword);

                    _userRepository.Update(obj);

                    var updateStatus = _userRepository.SaveChanges();

                    if (updateStatus > 0)
                    {
                        var systemUserSession = GetSesson();

                        if (systemUserSession == null)
                        {
                            TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                            return RedirectToAction("Logout", "Security");
                        }
                        string StringDescription = new GetStringHistorySystem().Get(systemUserSession.UserName, Resources.Resource.ChangePassword, obj.UserName.ToString(), Resources.Resource.User);

                        InsertHistorySystem((int)EACTION_TYPE.CHANGE_PASSWORD, (int)EnumControllerName.USERS, StringDescription, UserOld, obj);

                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Đổi mật khẩu thành công"));

                        return RedirectToAction("Index", "Home");
                    }
                    else
                    {
                        Notification = new StatusQuery("error", "", "Đổi mật khẩu thất bại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        return View(_changePassword);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("User/ChangePassword: " + ex.Message);

                    Notification = new StatusQuery("error", "", "Đổi mật khẩu thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return View(_changePassword);
                }
            }

            Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;

            return View(_changePassword);
        }

        // GET: /user/Delete/5
        public ActionResult Delete(int id)
        {
            StatusQuery Notification;
            try
            {
                var userSession = GetSesson();

                if (userSession == null)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                    return RedirectToAction("Logout", "Security");
                }

                var _user = _userRepository.GetById(id);

                if (userSession.Id == _user.Id)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("warning", "", "Không thể xóa người dùng đang hoạt động"));

                    return RedirectToAction("Index");
                }

                var _userModel = _mapper.Map<User, UserModel>(_user);

                try
                {
                    if (_userModel.DepartmentId != null)
                    {
                        ViewBag.DepartmentName = _departmentRepository.GetById(_userModel.DepartmentId).DepartmentName;
                    }
                    if (_userModel.PositionId != null)
                    {
                        ViewBag.PositionName = _positionRepository.GetById(_userModel.PositionId).PositionName;
                    }
                    if (_userModel.PositionId != null)
                    {
                        ViewBag.SiteName = _siteRepository.GetById(_userModel.SiteID).SiteName;
                    }

                    ViewBag.CreatedUser = _userRepository.GetById(_userModel.CreatedUser).UserName;
                    ViewBag.UpdatedUser = _userRepository.GetById(_userModel.UpdatedUser).UserName;
                }
                catch (Exception ex)
                {
                    _logger.LogError("User/Delete: " + ex.Message);
                    Console.WriteLine(ex.Message);
                }

                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }

                return View(_userModel);
            }
            catch (Exception ex)
            {
                _logger.LogError("User/Delete: " + ex.Message);

                Notification = new StatusQuery("warning", "", "Xóa thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;

                return RedirectToAction("Index");
            }
        }

        // POST: /Roles/Delete/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int id)
        {
            try
            {
                var userSession = GetSesson();

                if (userSession == null)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                    return RedirectToAction("Logout", "Security");
                }

                /*if(_patrolCameraCalendarRepository.GetBy(x=>x.UserID == id).ToList().Count > 0)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Người dùng đã có lịch sử tuần tra, không thể xóa người dùng này. p/s Nếu không muốn người dùng truy cập có thể khóa người dùng này lại!"));
                    return RedirectToAction("Delete", "Users", new { id });
                }

                if (_vehicleBannedListHistoryRepository.GetBy(x => x.CreatedUser == id).ToList().Count > 0 ||
                    _vehicleBannedListHistoryRepository.GetBy(x => x.VehicleClearBy == id).ToList().Count > 0)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Người dùng đã có lịch sử cấm vào ra, không thể xóa người dùng này. p/s Nếu không muốn người dùng truy cập có thể khóa người dùng này lại!"));
                    return RedirectToAction("Delete", "Users", new { id });
                }*/

                var user = _userRepository.GetById(id);

                _userRepository.Delete(user);

                var deleteStatus = _userRepository.SaveChanges();

                if (deleteStatus > 0)
                {
                    string StringDescription = new GetStringHistorySystem().Get(userSession.UserName, Resources.Resource.Delete, user.UserName.ToString(), Resources.Resource.User);

                    InsertHistorySystem((int)EACTION_TYPE.DELETE, (int)EnumControllerName.USERS, StringDescription, user, null);

                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Xóa thành công"));
                    return RedirectToAction("Index");
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));
                    return RedirectToAction("Delete", "Users", new { id });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("User/DeleteConfirmed: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));
                return RedirectToAction("Delete", "Users", new { id });
            }
        }

        public bool FnImp(string userNameAd)
        {
            try
            {
                var AccountAD = Utils.DecodePassword(_configuration.GetSection("AccountAD").Value, Utils.EncodeType.SHA_256);
                var PasswordAD = Utils.DecodePassword(_configuration.GetSection("PasswordAD").Value, Utils.EncodeType.SHA_256);

                var listDomain = _configuration.GetSection("DomainAD").Value.Split(',');
                foreach (var item in listDomain)
                {
                    var _contextTemp = GetContext(item);
                    if (_contextTemp != null)
                    {
                        using (var context = new PrincipalContext(ContextType.Domain, item, AccountAD, PasswordAD))
                        {
                            using (var searcher = new PrincipalSearcher(new UserPrincipal(context) { Enabled = true }))
                            {
                                var UserAD = searcher.FindAll().FirstOrDefault(p => p.SamAccountName.ToLower() == userNameAd.ToLower());

                                if (UserAD != null)
                                {
                                    return true;
                                }
                                else
                                {
                                    return false;
                                }
                            }
                        }
                    }
                }
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError("User/FnImp: " + ex.Message);

                return false;
            }
        }

        [HttpGet]
        public JsonResult GetFullNameAD(string UserNameAD)
        {
            try
            {
                var listDomain = _configuration.GetSection("DomainAD").Value;
                var AccountAD = Utils.DecodePassword(_configuration.GetSection("AccountAD").Value, Utils.EncodeType.SHA_256);
                var PasswordAD = Utils.DecodePassword(_configuration.GetSection("PasswordAD").Value, Utils.EncodeType.SHA_256);

                var result = Utils.GetFullNameUserAD(UserNameAD, listDomain, AccountAD, PasswordAD);

                if (result != null)
                {
                    if (result.Name == string.Empty)
                    {
                        result.Name = result.SAMAccountName;
                    }
                }

                return new JsonResult(result);
            }
            catch (Exception ex)
            {
                _logger.LogError("User/GetFullNameAD: " + ex.Message);

                return null;
            }
        }

        public PrincipalContext GetContext(string domain)
        {
            try
            {
                PrincipalContext context = new PrincipalContext(ContextType.Domain, domain);
                return context;
            }
            catch (Exception ex)
            {
                _logger.LogError("User/GetContext: " + ex.Message);

                return null;
            }
        }
        public bool FnValidateUser(PrincipalContext context, string username, string password)
        {
            bool validation;
            try
            {
                validation = context.ValidateCredentials(username, password);
            }
            catch (Exception ex)
            {
                _logger.LogError("User/FnValidateUser: " + ex.Message);
                validation = false;
            }
            return validation;
        }

        private UserModel GetListTypeUser(UserModel userModel)
        {
            try
            {
                List<DropDownList> _listPosition = new List<DropDownList>();
                List<DropDownList> _listdepartment = new List<DropDownList>();
                List<DropDownList2> _listSites = new List<DropDownList2>();

                var listPosition = _positionRepository.GetAll().ToList();
                var listDepartment = _departmentRepository.GetAll().ToList();
                var listSites = _siteRepository.GetAll().ToList();

                DropDownList drop = new DropDownList
                {
                    Id = -1,
                    Name = "Chọn"
                };

                DropDownList2 drop2 = new DropDownList2
                {
                    Id = "-1",
                    Name = "Chọn"
                };

                _listPosition.Add(drop);
                _listdepartment.Add(drop);
                _listSites.Add(drop2);

                foreach (var item in listPosition)
                {
                    DropDownList dropItem = new DropDownList
                    {
                        Id = item.Id,
                        Name = item.PositionName
                    };
                    _listPosition.Add(dropItem);
                }

                userModel.ListpositionId = ToSelectList(_listPosition);

                foreach (var item in listDepartment)
                {
                    DropDownList dropItem = new DropDownList
                    {
                        Id = item.Id,
                        Name = item.DepartmentName
                    };
                    _listdepartment.Add(dropItem);
                }

                userModel.ListDepartmentId = ToSelectList(_listPosition);

                foreach (var item in listSites)
                {
                    DropDownList2 dropItem = new DropDownList2
                    {
                        Id = item.SiteId,
                        Name = item.SiteName
                    };
                    _listSites.Add(dropItem);
                }
                userModel.ListSite = ToSelectList(_listSites);

            }
            catch (Exception ex)
            {
                _logger.LogError("User/GetListTypeUser: " + ex.Message);
                Console.WriteLine(ex.Message);
            }

            return userModel;
        }

        public User GetSesson()
        {
            var SessionUserSystem = HttpContext.Session.GetComplexData<User>("SessionUserSystemId_" + HttpContext.Session.GetString("SessionUserSystemId"));
            if (SessionUserSystem != null)
            {
                var _user = _userRepository.GetById(SessionUserSystem.Id);

                if (_user != null)
                {
                    if (!_user.Actived)
                    {
                        HttpContext.Session.Clear();
                        return null;
                    }
                    return _user;
                }
            }
            return null;
        }

        [NonAction]
        private SelectList ToSelectList(List<DropDownList> DropDownList)
        {
            List<SelectListItem> list = new List<SelectListItem>();

            foreach (var item in DropDownList)
            {
                list.Add(new SelectListItem()
                {
                    Text = item.Name,
                    Value = item.Id.ToString()
                });
            }

            return new SelectList(list, "Value", "Text");
        }

        [NonAction]
        private SelectList ToSelectList(List<DropDownList2> DropDownList)
        {
            List<SelectListItem> list = new List<SelectListItem>();

            foreach (var item in DropDownList)
            {
                list.Add(new SelectListItem()
                {
                    Text = item.Name,
                    Value = item.Id
                });
            }

            return new SelectList(list, "Value", "Text");
        }

        private void InsertHistorySystem(int action_type, int controllerName, string description, object OldModel, object NewModel)
        {
            try
            {
                var ipAddress = _accessor.HttpContext.Connection.RemoteIpAddress.ToString();

                string jsonOldObject = Newtonsoft.Json.JsonConvert.SerializeObject(OldModel);
                string jsonNewObject = Newtonsoft.Json.JsonConvert.SerializeObject(NewModel);

                HistorySystem history = new HistorySystem
                {
                    ActionType = action_type,
                    ActionTime = DateTime.Now,
                    Description = description,
                    OldObject = jsonOldObject,
                    NewObject = jsonNewObject,
                    UserId = int.Parse(HttpContext.Session.GetString("SessionUserSystemId")),
                    IpAddress = ipAddress,
                    ControllerName = controllerName
                };

                _historySystemRepository.Insert(history);

                _historySystemRepository.SaveChanges();
            }
            catch (Exception ex)
            {
                _logger.LogError("User/InsertHistorySystem: " + ex.Message);
            }
        }
    }
}