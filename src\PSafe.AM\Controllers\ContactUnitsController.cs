﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Logging;
using PSafe.AM.Common;
using PSafe.AM.Models;
using PSafe.Common.UserEnums;
using PSafe.Core.Domains;
using PSafe.Core.Interface;

namespace PSafe.AM.Controllers
{
    [AuthorizeActionFilter(EPERMISSIONS_AM.categoryManage)]
    public class ContactUnitsController : Controller
    {
        private readonly IContactUnitRepository _contactUnitRepository;
        private readonly IContactPlaceRepository _contactPlaceRepository;
        private readonly IContactUnitPlaceRepository _contactUnitPlaceRepository;
        private readonly IUserRepository _userRepository;
        private readonly IMapper _mapper;
        private readonly IHttpContextAccessor _accessor;
        private readonly ILogger<ContactUnitsController> _logger;

        public ContactUnitsController(IContactUnitRepository contactUnitRepository,
            IContactPlaceRepository contactPlaceRepository, IMapper mapper, IUserRepository userRepository,
            IContactUnitPlaceRepository contactUnitPlaceRepository,
            IHttpContextAccessor accessor, ILogger<ContactUnitsController> logger)
        {
            _contactUnitRepository = contactUnitRepository;
            _contactPlaceRepository = contactPlaceRepository;
            _contactUnitPlaceRepository = contactUnitPlaceRepository;
            _userRepository = userRepository;
            _mapper = mapper;
            _accessor = accessor;
            _logger = logger;
        }

        public ActionResult Index()
        {
            StatusQuery Notification;
            List<ContactUnitModel> listContactUnitModel = new List<ContactUnitModel>();
            try
            {
                var listContactUnit = _contactUnitRepository.Get(null, null, "ContactUnitPlaces.ContactPlace").ToList();

                listContactUnitModel = _mapper.Map<List<ContactUnit>, List<ContactUnitModel>>(listContactUnit)
                    .OrderBy(x => x.Index).ToList();

                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("ContactUnit/Index: " + ex.Message);

                Notification = new StatusQuery("error", "", "Xem danh sách thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;
            }

            return View(listContactUnitModel);
        }

        // GET: /supplier/Details/
        public ActionResult Details(int id)
        {
            try
            {
                var contactUnit = _contactUnitRepository.Get(x=>x.Id == id, null, "ContactUnitPlaces.ContactPlace").FirstOrDefault();

                var contactUnitModel = _mapper.Map<ContactUnit, ContactUnitModel>(contactUnit);

                return View(contactUnitModel);
            }
            catch (Exception ex)
            {
                _logger.LogError("ContactUnit/Details: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                return RedirectToAction("Index");
            }
        }

        // GET: /supplier/Create
        public ActionResult Create()
        {
            var model = new ContactUnitModel
            {
                ContactPlaces = GetContactPlacesSelectList()
            };
            return View(model);
        }

        private SelectList GetContactPlacesSelectList()
        {
            var selectListItems = new List<SelectListItem>();
            try
            {
                var contactPlaces = _contactPlaceRepository.GetAll().ToList();
                foreach(var contactPlace in contactPlaces)
                {
                    selectListItems.Add(new SelectListItem()
                    {
                        Value = contactPlace.Id.ToString(),
                        Text = contactPlace.Name
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.Message);
            }

            return new SelectList(selectListItems, "Value", "Text");
        }

        // POST: /supplier/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create(ContactUnitModel contactUnitModel)
        {
            StatusQuery Notification;
            bool isValidContactPlaceId = contactUnitModel.ContactPlaceIds != null && contactUnitModel.ContactPlaceIds.Length > 0;
            if (ModelState.IsValid && isValidContactPlaceId)
            {
                try
                {
                    var _systemUser = GetSesson();

                    if (_systemUser == null)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                        return RedirectToAction("Logout", "Security");
                    }

                    var contactUnit = _mapper.Map<ContactUnitModel, ContactUnit>(contactUnitModel);
                    var allContacts = _contactUnitRepository.GetAll();
                    if (allContacts.Count() == 0)
                        contactUnit.Id = 1;
                    else
                        contactUnit.Id = allContacts.Max(x => x.Id) + 1;
                    //contactUnit.ContactPlaceId = int.Parse(contactUnitModel.ContactPlaceId);
                    contactUnit.CreatedBy = _systemUser.Id;
                    contactUnit.UpdatedBy = _systemUser.Id;
                    contactUnit.CreatedDate = DateTime.Now;
                    contactUnit.UpdatedDate = DateTime.Now;

                    _contactUnitRepository.Insert(contactUnit);


                    var statusInsert = _contactUnitRepository.SaveChanges();

                    if (statusInsert > 0)
                    {
                        foreach (int contactPlaceId in contactUnitModel.ContactPlaceIds)
                        {
                            var contactUnitPlace = new ContactUnitPlace
                            {
                                ContactPlaceId = contactPlaceId,
                                ContactUnitId = contactUnit.Id
                            };
                            _contactUnitPlaceRepository.Insert(contactUnitPlace);
                        }

                        if (_contactUnitPlaceRepository.SaveChanges() > 0)
                        {
                            TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Thêm mới thành công"));
                            return RedirectToAction("Index");
                        }
                        else
                        {
                            Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                            _contactUnitRepository.Delete(contactUnit);
                            _contactUnitRepository.SaveChanges();
                        }
                    }
                    else
                    {
                        Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("ContactUnit/Create: " + ex.Message);
                    Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                }
            }
            else
            {
                Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            }

            if (!isValidContactPlaceId)
                ModelState.AddModelError("ContactPlaceIds", "Vui lòng chọn nơi liên hệ");

            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;
            contactUnitModel.ContactPlaces = GetContactPlacesSelectList();

            return View(contactUnitModel);
        }

        // GET: /supplier/Edit/5
        public ActionResult Edit(int id)
        {
            try
            {
                var contactUnit = _contactUnitRepository.Get(x=>x.Id == id, null, "ContactUnitPlaces.ContactPlace").FirstOrDefault();

                var contactUnitModel = _mapper.Map<ContactUnit, ContactUnitModel>(contactUnit);
                if (contactUnitModel != null)
                {
                    contactUnitModel.ContactPlaceIds = contactUnit.ContactUnitPlaces.Select(x => x.ContactPlaceId).ToArray();
                    contactUnitModel.ContactPlaces = GetContactPlacesSelectList();
                    return View(contactUnitModel);
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                    return RedirectToAction("Index");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("ContactUnit/Edit: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                return RedirectToAction("Index");
            }
        }

        private bool UpdateContactUnitPlace(ContactUnit contactUnit, int[] newContactPlaceIds)
        {
            if (newContactPlaceIds == null)
                return false;

            if (contactUnit.ContactUnitPlaces == null)
                return false;

            int[] oldContactPlaceIds = contactUnit.ContactUnitPlaces.Select(x => x.ContactPlaceId).ToArray();

            List<int> inserts = new List<int>();
            List<int> deletes = new List<int>();

            foreach (int contactPlaceId in newContactPlaceIds)
            {
                if (!oldContactPlaceIds.Contains(contactPlaceId))
                    inserts.Add(contactPlaceId);
            }

            foreach (int contactPlaceId in oldContactPlaceIds)
            {
                if (!newContactPlaceIds.Contains(contactPlaceId))
                    deletes.Add(contactPlaceId);
            }

            if(deletes.Count > 0)
            {
                foreach (int id in deletes)
                {
                    try
                    {
                        _contactUnitPlaceRepository.Delete(x => x.ContactPlaceId == id && x.ContactUnitId == contactUnit.Id);
                    }
                    catch { }
                }
            }

            if (inserts.Count > 0)
            {
                foreach (int contactPlaceId in inserts)
                {
                    var contactUnitPlace = new ContactUnitPlace
                    {
                        ContactPlaceId = contactPlaceId,
                        ContactUnitId = contactUnit.Id
                    };
                    _contactUnitPlaceRepository.Insert(contactUnitPlace);
                }
            }

            if (deletes.Count > 0 || inserts.Count > 0)
            {
                _contactUnitPlaceRepository.SaveChanges();
            }

            return true;
        }

        // POST: /supplier/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit(ContactUnitModel contactUnitModel)
        {
            StatusQuery Notification;
            bool isValidContactPlaceId = contactUnitModel.ContactPlaceIds != null && contactUnitModel.ContactPlaceIds.Length > 0;
            if (ModelState.IsValid && isValidContactPlaceId)
            {
                try
                {
                    var _systemUser = GetSesson();

                    if (_systemUser == null)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                        return RedirectToAction("Logout", "Security");
                    }

                    var contactUnit = _contactUnitRepository.Get(x=>x.Id == contactUnitModel.Id, null, "ContactUnitPlaces").FirstOrDefault();

                    //contactUnit.ContactPlaceId = int.Parse(contactUnitModel.ContactPlaceId);
                    contactUnit.Name = contactUnitModel.Name;
                    contactUnit.Description = contactUnitModel.Description;
                    contactUnit.UpdatedBy = _systemUser.Id;
                    contactUnit.UpdatedDate = DateTime.Now;
                    contactUnit.Index = contactUnitModel.Index;

                    //var _supplier = _mapper.Map<SupplierModel, Supplier>(supplierModel);

                    _contactUnitRepository.Update(contactUnit);

                    var updateStatus = _contactUnitRepository.SaveChanges();

                    if (updateStatus > 0)
                    {
                        if (UpdateContactUnitPlace(contactUnit, contactUnitModel.ContactPlaceIds))
                        {
                            TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Sửa thành công"));

                            return RedirectToAction("Index");
                        }
                        else
                        {
                            Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                            _contactUnitRepository.Delete(contactUnit);
                            _contactUnitRepository.SaveChanges();
                        }
                    }
                    else
                    {
                        Notification = new StatusQuery("error", "", "Sửa thất bại");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("ContactUnit/Edit: " + ex.Message);

                    Notification = new StatusQuery("error", "", "Sửa thất bại");

                }
            }
            else
            {
                Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            }

            if (!isValidContactPlaceId)
                ModelState.AddModelError("ContactPlaceIds", "Vui lòng chọn nơi liên hệ");

            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;
            contactUnitModel.ContactPlaces = GetContactPlacesSelectList();

            return View(contactUnitModel);
        }

        // GET: /supplier/Delete/5
        public ActionResult Delete(int id)
        {
            StatusQuery Notification;
            ContactUnitModel contactUnitModel = new ContactUnitModel();
            try
            {
                var contactUnit = _contactUnitRepository.Get(x => x.Id == id, null, "ContactUnitPlaces.ContactPlace").FirstOrDefault();

                contactUnitModel = _mapper.Map<ContactUnit, ContactUnitModel>(contactUnit);

                if (contactUnit == null)
                {
                    Notification = new StatusQuery("warning", "", "Không tìm thấy nơi liên hệ");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return RedirectToAction("Index");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("ContactUnit/Delete: " + ex.Message);

                Notification = new StatusQuery("warning", "", "Xóa thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;

                return RedirectToAction("Index");
            }
            return View(contactUnitModel);
        }


        // POST: /Suppliers/Delete/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int id)
        {
            try
            {
                var systemUser = GetSesson();

                if (systemUser == null)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                    return RedirectToAction("Logout", "Security");
                }

                var contactUnit = _contactUnitRepository.GetById(id);

                _contactUnitPlaceRepository.Delete(x => x.ContactUnitId == id);
                if (_contactUnitPlaceRepository.SaveChanges() == -1)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));
                    return RedirectToAction("Index");
                }


                _contactUnitRepository.Delete(contactUnit);

                var deleteStatus = _contactUnitRepository.SaveChanges();

                if (deleteStatus > 0)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Xóa thành công"));
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));
                }

                return RedirectToAction("Index");
            }
            catch (Exception ex)
            {
                _logger.LogError("ContactUnit/DeleteConfirmed: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));
            }
            return RedirectToAction("Index");
        }

        public User GetSesson()
        {
            var sessionUser = HttpContext.Session.GetComplexData<User>("SessionUserSystemId_" + HttpContext.Session.GetString("SessionUserSystemId"));
            if (sessionUser != null)
            {
                var _user = _userRepository.GetById(sessionUser.Id);

                if (_user != null)
                {
                    if (!_user.Actived)
                    {
                        HttpContext.Session.Clear();
                        return null;
                    }
                    return _user;
                }
            }
            return null;
        }
    }
}
