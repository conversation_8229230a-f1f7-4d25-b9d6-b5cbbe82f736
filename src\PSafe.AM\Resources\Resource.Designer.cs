﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace PSafe.AM.Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class Resource {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resource() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("PSafe.AM.Resources.Resource", typeof(Resource).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thêm.
        /// </summary>
        public static string Add {
            get {
                return ResourceManager.GetString("Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thêm mới.
        /// </summary>
        public static string AddItem {
            get {
                return ResourceManager.GetString("AddItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Địa chỉ.
        /// </summary>
        public static string Address {
            get {
                return ResourceManager.GetString("Address", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quản trị.
        /// </summary>
        public static string admin {
            get {
                return ResourceManager.GetString("admin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cấu hình gửi tin nhắn cảnh báo.
        /// </summary>
        public static string AlarmConfig {
            get {
                return ResourceManager.GetString("AlarmConfig", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Khu vực.
        /// </summary>
        public static string Area {
            get {
                return ResourceManager.GetString("Area", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quay lại danh sách.
        /// </summary>
        public static string BackToList {
            get {
                return ResourceManager.GetString("BackToList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Loại vi phạm.
        /// </summary>
        public static string BannedType {
            get {
                return ResourceManager.GetString("BannedType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Danh sách đen.
        /// </summary>
        public static string BlackList {
            get {
                return ResourceManager.GetString("BlackList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hủy.
        /// </summary>
        public static string Cancel {
            get {
                return ResourceManager.GetString("Cancel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Đổi mật khẩu.
        /// </summary>
        public static string ChangePassword {
            get {
                return ResourceManager.GetString("ChangePassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Trung tâm chỉ huy.
        /// </summary>
        public static string CommandCenter {
            get {
                return ResourceManager.GetString("CommandCenter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Công ty.
        /// </summary>
        public static string Company {
            get {
                return ResourceManager.GetString("Company", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Công ty.
        /// </summary>
        public static string Companys {
            get {
                return ResourceManager.GetString("Companys", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nơi liên hệ / Kho.
        /// </summary>
        public static string ContactPlace {
            get {
                return ResourceManager.GetString("ContactPlace", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Đơn vị liên hệ / Công ty.
        /// </summary>
        public static string ContactUnit {
            get {
                return ResourceManager.GetString("ContactUnit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thiết kế và phát triển bởi công ty OTS.
        /// </summary>
        public static string Copyright {
            get {
                return ResourceManager.GetString("Copyright", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thêm.
        /// </summary>
        public static string Create {
            get {
                return ResourceManager.GetString("Create", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Phân quyền.
        /// </summary>
        public static string Decentralization {
            get {
                return ResourceManager.GetString("Decentralization", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Xóa.
        /// </summary>
        public static string Delete {
            get {
                return ResourceManager.GetString("Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Xóa.
        /// </summary>
        public static string DeleteItem {
            get {
                return ResourceManager.GetString("DeleteItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Phòng ban.
        /// </summary>
        public static string Department {
            get {
                return ResourceManager.GetString("Department", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Chi tiết.
        /// </summary>
        public static string Detail {
            get {
                return ResourceManager.GetString("Detail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thiết bị.
        /// </summary>
        public static string Device {
            get {
                return ResourceManager.GetString("Device", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quận/Huyện.
        /// </summary>
        public static string District {
            get {
                return ResourceManager.GetString("District", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tài liệu.
        /// </summary>
        public static string Document {
            get {
                return ResourceManager.GetString("Document", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sửa.
        /// </summary>
        public static string Edit {
            get {
                return ResourceManager.GetString("Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hiệu chỉnh.
        /// </summary>
        public static string EditItem {
            get {
                return ResourceManager.GetString("EditItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hiệu chỉnh người dùng.
        /// </summary>
        public static string EditUser {
            get {
                return ResourceManager.GetString("EditUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cấu hình chung.
        /// </summary>
        public static string GeneralConfig {
            get {
                return ResourceManager.GetString("GeneralConfig", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nhóm.
        /// </summary>
        public static string Group {
            get {
                return ResourceManager.GetString("Group", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lịch sử.
        /// </summary>
        public static string HistorySystem {
            get {
                return ResourceManager.GetString("HistorySystem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Danh sách cảnh báo.
        /// </summary>
        public static string HumanAlarmList {
            get {
                return ResourceManager.GetString("HumanAlarmList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Danh sách người cấm.
        /// </summary>
        public static string HumanBannedList {
            get {
                return ResourceManager.GetString("HumanBannedList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vị trí.
        /// </summary>
        public static string Location {
            get {
                return ResourceManager.GetString("Location", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Đăng nhập.
        /// </summary>
        public static string Login {
            get {
                return ResourceManager.GetString("Login", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Đăng xuất.
        /// </summary>
        public static string Logout {
            get {
                return ResourceManager.GetString("Logout", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lực lượng.
        /// </summary>
        public static string Marker {
            get {
                return ResourceManager.GetString("Marker", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Khu vực tuần tra.
        /// </summary>
        public static string PatrolArea {
            get {
                return ResourceManager.GetString("PatrolArea", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kịch bản tuần tra.
        /// </summary>
        public static string PatrolCamera {
            get {
                return ResourceManager.GetString("PatrolCamera", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lịch tuần tra.
        /// </summary>
        public static string PatrolCameraCalendar {
            get {
                return ResourceManager.GetString("PatrolCameraCalendar", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kịch bản tuần tra chiến sỹ.
        /// </summary>
        public static string PatrolForce {
            get {
                return ResourceManager.GetString("PatrolForce", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lịch tuần tra chiến sỹ.
        /// </summary>
        public static string PatrolForceCalendar {
            get {
                return ResourceManager.GetString("PatrolForceCalendar", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Chức vụ.
        /// </summary>
        public static string Position {
            get {
                return ResourceManager.GetString("Position", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tỉnh.
        /// </summary>
        public static string Province {
            get {
                return ResourceManager.GetString("Province", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mục đích vào cảng.
        /// </summary>
        public static string PurposeInPort {
            get {
                return ResourceManager.GetString("PurposeInPort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lý do.
        /// </summary>
        public static string Reason {
            get {
                return ResourceManager.GetString("Reason", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lý do vi phạm.
        /// </summary>
        public static string ReasonViolation {
            get {
                return ResourceManager.GetString("ReasonViolation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thùng rác.
        /// </summary>
        public static string RecycleBin {
            get {
                return ResourceManager.GetString("RecycleBin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nhóm.
        /// </summary>
        public static string Role {
            get {
                return ResourceManager.GetString("Role", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nhóm CBCS.
        /// </summary>
        public static string RoleCBCS {
            get {
                return ResourceManager.GetString("RoleCBCS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lưu.
        /// </summary>
        public static string Save {
            get {
                return ResourceManager.GetString("Save", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ca trực.
        /// </summary>
        public static string ScheduleTime {
            get {
                return ResourceManager.GetString("ScheduleTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Danh sách đen.
        /// </summary>
        public static string SecurityBlackList {
            get {
                return ResourceManager.GetString("SecurityBlackList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hồ sơ an ninh.
        /// </summary>
        public static string SecurityRecord {
            get {
                return ResourceManager.GetString("SecurityRecord", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cảng.
        /// </summary>
        public static string Site {
            get {
                return ResourceManager.GetString("Site", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nhà cung cấp.
        /// </summary>
        public static string Suppliers {
            get {
                return ResourceManager.GetString("Suppliers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Đồng bộ.
        /// </summary>
        public static string Sync {
            get {
                return ResourceManager.GetString("Sync", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Người dùng.
        /// </summary>
        public static string SystemUsers {
            get {
                return ResourceManager.GetString("SystemUsers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Loại thiết bị.
        /// </summary>
        public static string TypeOfDevice {
            get {
                return ResourceManager.GetString("TypeOfDevice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Loại lực lượng.
        /// </summary>
        public static string TypeOfMarker {
            get {
                return ResourceManager.GetString("TypeOfMarker", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Người dùng.
        /// </summary>
        public static string User {
            get {
                return ResourceManager.GetString("User", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CBCS.
        /// </summary>
        public static string UserCBCS {
            get {
                return ResourceManager.GetString("UserCBCS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lịch trực.
        /// </summary>
        public static string UserSchedule {
            get {
                return ResourceManager.GetString("UserSchedule", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Danh sách xe cấm.
        /// </summary>
        public static string VehicleBannedList {
            get {
                return ResourceManager.GetString("VehicleBannedList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Loại phương tiện.
        /// </summary>
        public static string VehicleType {
            get {
                return ResourceManager.GetString("VehicleType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Phường/xã.
        /// </summary>
        public static string Ward {
            get {
                return ResourceManager.GetString("Ward", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kho.
        /// </summary>
        public static string Warehouse {
            get {
                return ResourceManager.GetString("Warehouse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tải trọng.
        /// </summary>
        public static string Weight {
            get {
                return ResourceManager.GetString("Weight", resourceCulture);
            }
        }
    }
}
