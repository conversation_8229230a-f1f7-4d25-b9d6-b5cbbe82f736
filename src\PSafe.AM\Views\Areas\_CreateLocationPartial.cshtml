﻿@model PSafe.AM.Models.LocationModel

@{
    Layout = null;
}

<div class="modal-dialog">
    <div class="modal-content">
        <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
            <h4 class="modal-title">@PSafe.AM.Resources.Resource.Create @PSafe.AM.Resources.Resource.Location</h4>
        </div>
        @using (Html.BeginForm("CreateLocation", "Areas", FormMethod.Post, new { @class = "form-horizontal", role = "form", enctype = "multipart/form-data" }))
        {
            @Html.AntiForgeryToken()
            <div class="modal-body">
                <div class="form-horizontal">
                    @Html.ValidationSummary(true, "Lỗi nhập liệu!", new { @class = "text-danger" })
                    @Html.HiddenFor(model => model.AreaId)
                    <div class="form-group">
                        <label class="control-label col-md-12">@Html.LabelFor(model => model.LocationName) (*)</label>
                        <div class="col-md-12">
                            @Html.EditorFor(model => model.LocationName, new { htmlAttributes = new { @minlength = "3", @required = "required", @oninput = "this.setCustomValidity('')", @oninvalid = "this.setCustomValidity('Độ dài từ 3-255 ký tự!')" } })
                            @Html.ValidationMessageFor(model => model.LocationName, null, new { @class = "text-danger" })
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="control-label col-md-2">@Html.LabelFor(model => model.Map) (*)</label>
                        <div class="col-md-10">
                            @Html.TextBoxFor(model => model.ImageFile, new { @type = "file", @class = "btn btn-primary btn-file", accept = ".jpg, .jpeg, .gif, .png", required = true })
                            @Html.ValidationMessageFor(model => model.ImageFile, null, new { @class = "text-danger" })
                        </div>
                    </div>
                    <div class="form-group">
                        @Html.LabelFor(model => model.Document, new { @class = "control-label col-md-2" })
                        <div class="col-md-10">
                            <select data-placeholder="Chọn tài liệu..." class="chosen-select" name="document_select" multiple tabindex="4">
                                @foreach (var item in Model.ListDocumentNotOnLocation)
                                {
                                    <option value="@item.Id">@Html.DisplayFor(modelItem => item.FileName)</option>
                                }
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        @Html.LabelFor(model => model.Description, new { @class = "control-label col-md-12" })
                        <div class="col-md-12">
                            @Html.EditorFor(model => model.Description)
                            @Html.ValidationMessageFor(model => model.Description, null, new { @class = "text-danger" })
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <input type="submit" value="@PSafe.AM.Resources.Resource.Save" class="btn btn-primary" />
                <button type="button" class="btn btn-white" data-dismiss="modal">@PSafe.AM.Resources.Resource.Cancel</button>
            </div>
        }
    </div>
</div>

<script type="text/javascript">
        $('.chosen-select').chosen({ width: "100%" });
</script>