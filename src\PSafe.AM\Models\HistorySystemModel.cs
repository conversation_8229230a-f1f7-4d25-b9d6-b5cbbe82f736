﻿
using Microsoft.AspNetCore.Mvc.Rendering;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace PSafe.AM.Models
{
    public class HistorySystemModel
    {
        [Key]
        [Display(Name = "Id", ResourceType = typeof(Resources.Resource_historySystem))]
        public int Id { get; set; }

        [DataType(DataType.Date)]
        [DisplayFormat(DataFormatString = "{0:MM-dd-yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        [Display(Name = "ActionTime", ResourceType = typeof(Resources.Resource_historySystem))]
        public DateTime ActionTime { get; set; }

        [Display(Name = "UserId", ResourceType = typeof(Resources.Resource_historySystem))]
        public int UserId { get; set; }

        [Display(Name = "ActionType", ResourceType = typeof(Resources.Resource_historySystem))]
        public int ActionType { get; set; }

        [Display(Name = "OldObject", ResourceType = typeof(Resources.Resource_historySystem))]
        public string OldObject { get; set; } = string.Empty;

        [Display(Name = "NewObject", ResourceType = typeof(Resources.Resource_historySystem))]
        public string NewObject { get; set; } = string.Empty;

        [Display(Name = "IpAddress", ResourceType = typeof(Resources.Resource_historySystem))]
        public string IpAddress { get; set; } = string.Empty;

        [Display(Name = "Description", ResourceType = typeof(Resources.Resource_historySystem))]
        public string Description { get; set; } = string.Empty;

        [Display(Name = "UserName", ResourceType = typeof(Resources.Resource_historySystem))]
        public string UserName { get; set; } = string.Empty;

        [Display(Name = "ActionType", ResourceType = typeof(Resources.Resource_historySystem))]
        public string ActionTypeName { get; set; } = string.Empty;

        [Display(Name = "ControllerType", ResourceType = typeof(Resources.Resource_historySystem))]
        public int ControllerType { get; set; }
    }
}
