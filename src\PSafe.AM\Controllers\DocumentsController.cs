﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http.Headers;
using AutoMapper;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PSafe.AM.Common;
using PSafe.AM.Models;
using PSafe.Common;
using PSafe.Common.UserEnums;
using PSafe.Core.Domains;
using PSafe.Core.Interface;
using static PSafe.Common.CommonEnums;

namespace PSafe.AM.Controllers
{
    [AuthorizeActionFilter(EPERMISSIONS_AM.DeviceManage)]
    public class DocumentsController : Controller
    {
        private readonly IDocumentRepository _documentRepository;
        private readonly IUserRepository _userRepository;
        private readonly IMapper _mapper;
        private readonly IHistorySystemRepository _historySystemRepository;
        private readonly IHttpContextAccessor _accessor;
        private readonly IConfiguration _configuration;
        private readonly ILogger<DocumentsController> _logger;

        public DocumentsController(IDocumentRepository documentRepository, IUserRepository userRepository, IMapper mapper,
            IHistorySystemRepository historySystemRepository, IHttpContextAccessor accessor, ILogger<DocumentsController> logger,
            IConfiguration configuration)
        {
            _documentRepository = documentRepository;
            _userRepository = userRepository;
            _mapper = mapper;
            _historySystemRepository = historySystemRepository;
            _accessor = accessor;
            _configuration = configuration;
            _logger = logger;
        }

        public ActionResult Index()
        {
            StatusQuery Notification;
            List<DocumentModel> _listDocumenModel = new List<DocumentModel>();
            try
            {
                var _listDocuments = _documentRepository.GetAll().ToList();

                _listDocumenModel = _mapper.Map<List<Document>, List<DocumentModel>>(_listDocuments);

                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }
            }
            catch(Exception ex)
            {
                _logger.LogError("Document/Index: " + ex.Message);

                Notification = new StatusQuery("error", "", "Xem danh sách thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;
            }

            return View(_listDocumenModel);
        }

        public ActionResult Details(int id)
        {
            try
            {
                var document = _documentRepository.GetById(id);

                var _DocumentModel = _mapper.Map<Document, DocumentModel>(document);

                try
                {
                    if (TempDataHelper.Get<StatusQuery>(TempData, "Notification") is StatusQuery Notification)
                    {
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;
                    }

                    if(_DocumentModel.Type != -1)
                    {
                        ViewBag.type = new ListTypeOfSingnal().List.Where(p => p.Id == _DocumentModel.Type).SingleOrDefault().Name;
                    }

                    ViewBag.UPDATEDUSER = _userRepository.GetById(_DocumentModel.UpdatedBy).UserName;
                    ViewBag.CREATEDUSER = _userRepository.GetById(_DocumentModel.CreatedBy).UserName;
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex.Message);
                }

                if (_DocumentModel != null)
                {
                    return View(_DocumentModel);
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                    return RedirectToAction("Index");
                }
            }
            catch(Exception ex)
            {
                _logger.LogError("Document/Details: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                return RedirectToAction("Index");
            }
        }

        public ActionResult Create()
        {
            DocumentModel documentModel = new DocumentModel
            {
                ListType = ToSelectList(new ListTypeOfSingnal().List)
            };

            return View(documentModel);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create([Bind("Id, DocumentName, Description, ImageFile, Type")] DocumentModel documentModel)
        {
            StatusQuery Notification;

            if(documentModel.ImageFile == null)
            {
                ModelState.AddModelError("FileName", "Vui lòng chọn file!");
            }

            if (ModelState.IsValid)
            {
                try
                {
                    var _systemUser = GetSesson();

                    if (_systemUser == null)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                        return RedirectToAction("Logout", "Security");
                    }

                    if (documentModel.ImageFile != null && documentModel.ImageFile.FileName.Length > 0)
                    {
                        var fileName = DateTime.Now.Date.ToString("MM_dd_yyyy") + "_" + documentModel.ImageFile.FileName.Substring(documentModel.ImageFile.FileName.LastIndexOf("\\") + 1);
                        documentModel.FileName = fileName;

                        var userNameFTP = Utils.DecodePassword(_configuration.GetSection("FTP:UserName").Value, Utils.EncodeType.SHA_256);
                        var passwordFTP = Utils.DecodePassword(_configuration.GetSection("FTP:Password").Value, Utils.EncodeType.SHA_256);
                        var hostFTP = _configuration.GetSection("FTP:Host").Value + ":" + _configuration.GetSection("FTP:Port").Value;

                        byte[] fileBytes;

                        using (var ms = new MemoryStream())
                        {
                            documentModel.ImageFile.CopyTo(ms);
                            fileBytes = ms.ToArray();
                        }

                        var pathString = "Document/";
                        var checkDirectory = hostFTP + "/";
                        foreach (var item in pathString.Split("/"))
                        {
                            checkDirectory += item + "/";
                            if (!GetDirectoryExits(checkDirectory, userNameFTP, passwordFTP))
                            {
                                CreateDirectory(checkDirectory, userNameFTP, passwordFTP);
                            }
                        }

                        using var client = new WebClient();
                        client.Credentials = new NetworkCredential(userNameFTP, passwordFTP);
                        client.UploadData(checkDirectory + "/" + fileName, fileBytes);
                    }

                    documentModel.CreatedDate = DateTime.Now;
                    documentModel.CreatedBy = _systemUser.Id;
                    documentModel.UpdatedDate = DateTime.Now;
                    documentModel.UpdatedBy = _systemUser.Id;

                    var _document = _mapper.Map<DocumentModel, Document>(documentModel);

                    _documentRepository.Insert(_document);

                    var statusInsert = _documentRepository.SaveChanges();

                    if (statusInsert > 0)
                    {
                        string StringDescription = new GetStringHistorySystem().Get(_systemUser.UserName, Resources.Resource.Create, _document.DocumentName.ToString(), Resources.Resource.Document);

                        InsertHistorySystem((int)EACTION_TYPE.CREATE, (int)EnumControllerName.DOCUMENTS, StringDescription, null, _document);

                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Thêm mới thành công"));
                        return RedirectToAction("Index");
                    }
                    else
                    {
                        Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        documentModel.ListType = ToSelectList(new ListTypeOfSingnal().List);
                       
                        return View(documentModel);
                    }
                }
                catch(Exception ex)
                {
                    _logger.LogError("Document/Create: " + ex.Message);

                    Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    documentModel.ListType = ToSelectList(new ListTypeOfSingnal().List);

                    return View(documentModel);
                }
            }
            Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;

            documentModel.ListType = ToSelectList(new ListTypeOfSingnal().List);

            return View(documentModel);
        }

        public ActionResult Edit(int id)
        {
            try
            {
                var _document = _documentRepository.GetById(id);

                var _DocumentModel = _mapper.Map<Document, DocumentModel>(_document);

                if (_DocumentModel != null)
                {
                    _DocumentModel.ListType = ToSelectList(new ListTypeOfSingnal().List);

                    return View(_DocumentModel);
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                    return RedirectToAction("Index");
                }
            }
            catch(Exception ex)
            {
                _logger.LogError("Document/Edit: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                return RedirectToAction("Index");
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit([Bind("Id, FileName, ImageFile, DocumentName, Description, Type")] DocumentModel documentModel)
        {
            StatusQuery Notification;
            if (ModelState.IsValid)
            {
                try
                {
                    var _systemUser = GetSesson();

                    if (_systemUser == null)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                        return RedirectToAction("Logout", "Security");
                    }

                    var document = _documentRepository.GetById(documentModel.Id);

                    var _documentTemp = _mapper.Map<Document, DocumentModel>(document);
                    var documentOld = _mapper.Map<DocumentModel, Document>(_documentTemp);

                    if (documentModel.ImageFile != null && documentModel.ImageFile.FileName.Length > 0)
                    {
                        var fileName = DateTime.Now.Date.ToString("MM_dd_yyyy") + "_" + documentModel.ImageFile.FileName.Substring(documentModel.ImageFile.FileName.LastIndexOf("\\") + 1);
                        documentModel.FileName = fileName;

                        var userNameFTP = Utils.DecodePassword(_configuration.GetSection("FTP:UserName").Value, Utils.EncodeType.SHA_256);
                        var passwordFTP = Utils.DecodePassword(_configuration.GetSection("FTP:Password").Value, Utils.EncodeType.SHA_256);
                        var hostFTP = _configuration.GetSection("FTP:Host").Value + ":" + _configuration.GetSection("FTP:Port").Value;

                        byte[] fileBytes;

                        using (var ms = new MemoryStream())
                        {
                            documentModel.ImageFile.CopyTo(ms);
                            fileBytes = ms.ToArray();
                        }

                        var pathString = "Document/";
                        var checkDirectory = hostFTP + "/";
                        foreach (var item in pathString.Split("/"))
                        {
                            checkDirectory += item + "/";
                            if (!GetDirectoryExits(checkDirectory, userNameFTP, passwordFTP))
                            {
                                CreateDirectory(checkDirectory, userNameFTP, passwordFTP);
                            }
                        }

                        using var client = new WebClient();
                        client.Credentials = new NetworkCredential(userNameFTP, passwordFTP);
                        client.UploadData(checkDirectory + "/" + fileName, fileBytes);
                    }

                    document.DocumentName = documentModel.DocumentName;
                    document.Description = documentModel.Description;
                    document.Type = documentModel.Type;
                    document.UpdatedDate = DateTime.Now;
                    document.UpdatedBy = _systemUser.Id;

                    _documentRepository.Update(document);

                    var updateStatus = _documentRepository.SaveChanges();

                    if (updateStatus > 0)
                    {
                        string StringDescription = new GetStringHistorySystem().Get(_systemUser.UserName, Resources.Resource.Edit, document.DocumentName.ToString(), Resources.Resource.Document);

                        InsertHistorySystem((int)EACTION_TYPE.EDIT, (int)EnumControllerName.DOCUMENTS, StringDescription, documentOld, document);

                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Sửa thành công"));

                        return RedirectToAction("Index");
                    }
                    else
                    {
                        Notification = new StatusQuery("error", "", "Sửa thất bại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        documentModel.ListType = ToSelectList(new ListTypeOfSingnal().List);

                        return View(documentModel);
                    }
                }
                catch(Exception ex)
                {
                    _logger.LogError("Document/Edit: " + ex.Message);

                    Notification = new StatusQuery("error", "", "Sửa thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    documentModel.ListType = ToSelectList(new ListTypeOfSingnal().List);

                    return View(documentModel);
                }
            }

            Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;

            documentModel.ListType = ToSelectList(new ListTypeOfSingnal().List);

            return View(documentModel);
        }

        public ActionResult Delete(int id)
        {
            StatusQuery Notification;
            try
            {
                var _document = _documentRepository.GetById(id);

                var _DocumentModel = _mapper.Map<Document, DocumentModel>(_document);

                try
                {
                    if (_DocumentModel.Type != -1)
                    {
                        ViewBag.type = new ListTypeOfSingnal().List.Where(p => p.Id == _DocumentModel.Type).SingleOrDefault().Name;
                    }

                    ViewBag.UPDATEDUSER = _userRepository.GetById(_DocumentModel.UpdatedBy).UserName;
                    ViewBag.CREATEDUSER = _userRepository.GetById(_DocumentModel.CreatedBy).UserName;
                }
                catch (Exception ex)
                {
                    _logger.LogError("Document/Delete: " + ex.Message);
                    Console.WriteLine(ex.Message);
                }

                if (_DocumentModel == null)
                {
                    Notification = new StatusQuery("warning", "", "Tải lên tài liệu thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return RedirectToAction("Index");
                }

                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }

                return View(_DocumentModel);
            }
            catch(Exception ex)
            {
                _logger.LogError("Document/Delete: " + ex.Message);

                Notification = new StatusQuery("warning", "", "Xóa thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;

                return RedirectToAction("Index");
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int id)
        {
            try
            {
                var systemUser = GetSesson();

                if (systemUser == null)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                    return RedirectToAction("Logout", "Security");
                }

                var document = _documentRepository.GetById(id);

                _documentRepository.Delete(document);

                var deleteStatus = _documentRepository.SaveChanges();

                if (deleteStatus > 0)
                {
                    string StringDescription = new GetStringHistorySystem().Get(systemUser.UserName, Resources.Resource.Delete, document.DocumentName.ToString(), Resources.Resource.Document);

                    InsertHistorySystem((int)EACTION_TYPE.DELETE, (int)EnumControllerName.DOCUMENTS, StringDescription, document, null);

                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Xóa thành công"));
                    return RedirectToAction("Index");
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));
                    return RedirectToAction("Delete", "documents", new { id });
                }
            }
            catch(Exception ex)
            {
                _logger.LogError("Document/DeleteConfirmed: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));
                return RedirectToAction("Delete", "documents", new { id });
            }
        }

        [NonAction]
        private SelectList ToSelectList(List<DropDownList> DropDownList)
        {
            List<SelectListItem> list = new List<SelectListItem>
            {
                new SelectListItem()
                {
                    Text = "Tài liệu chung",
                    Value = "-1"
                }
            };

            foreach (var item in DropDownList)
            {
                list.Add(new SelectListItem()
                {
                    Text = item.Name,
                    Value = item.Id.ToString()
                });
            }

            return new SelectList(list, "Value", "Text");
        }

        public User GetSesson()
        {
            var SessionUserSystem = HttpContext.Session.GetComplexData<User>("SessionUserSystemId_" + HttpContext.Session.GetString("SessionUserSystemId"));
            if (SessionUserSystem != null)
            {
                var _user = _userRepository.GetById(SessionUserSystem.Id);

                if (_user != null)
                {
                    if (!_user.Actived)
                    {
                        HttpContext.Session.Clear();
                        return null;
                    }
                    return _user;
                }
            }
            return null;
        }

        private void InsertHistorySystem(int action_type, int controllerName, string description, object OldModel, object NewModel)
        {
            try
            {
                var ipAddress = _accessor.HttpContext.Connection.RemoteIpAddress.ToString();

                string jsonOldObject = Newtonsoft.Json.JsonConvert.SerializeObject(OldModel);
                string jsonNewObject = Newtonsoft.Json.JsonConvert.SerializeObject(NewModel);

                HistorySystem history = new HistorySystem
                {
                    ActionType = action_type,
                    ActionTime = DateTime.Now,
                    Description = description,
                    OldObject = jsonOldObject,
                    NewObject = jsonNewObject,
                    UserId = int.Parse(HttpContext.Session.GetString("SessionUserSystemId")),
                    IpAddress = ipAddress,
                    ControllerName = controllerName
                };

                _historySystemRepository.Insert(history);

                _historySystemRepository.SaveChanges();
            }
            catch (Exception ex)
            {
                _logger.LogError("Document/InsertHistorySystem: " + ex.Message);
            }
        }

        private bool GetDirectoryExits(string path, string userNameFTP, string passwordFTP)
        {
            try
            {
                WebRequest request = WebRequest.Create(path);
                request.Credentials = new NetworkCredential(userNameFTP, passwordFTP);
                request.Method = WebRequestMethods.Ftp.ListDirectory;

                FtpWebResponse response = (FtpWebResponse)request.GetResponse();
                long size = response.ContentLength;
                response.Close();

                return true;
            }
            catch (WebException e)
            {
                _logger.LogInformation("CreateDirectory Status: {0}", e.Message);
                return false;
            }
        }

        private bool CreateDirectory(string path, string userNameFTP, string passwordFTP)
        {
            try
            {
                WebRequest request = WebRequest.Create(path);
                request.Credentials = new NetworkCredential(userNameFTP, passwordFTP);
                request.Method = WebRequestMethods.Ftp.MakeDirectory;
                using var resp = (FtpWebResponse)request.GetResponse();
                if (resp.StatusCode == FtpStatusCode.PathnameCreated)
                {
                    resp.Close();
                    return true;
                }
                resp.Close();
                return false;

            }
            catch (WebException e)
            {
                String status = ((FtpWebResponse)e.Response).StatusDescription;
                _logger.LogError(status);
                return false;
            }
        }
    }
}