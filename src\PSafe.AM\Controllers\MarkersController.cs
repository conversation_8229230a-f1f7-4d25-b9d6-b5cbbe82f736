﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PSafe.AM.Common;
using PSafe.AM.Models;
using PSafe.Common.UserEnums;
using PSafe.Core.Domains;
using PSafe.Core.Interface;
using static PSafe.Common.CommonEnums;

namespace PSafe.AM.Controllers
{
    [AuthorizeActionFilter(EPERMISSIONS_AM.DeviceManage)]
    public class MarkersController : Controller
    {
        private readonly IMarkerRepository _markerRepository;
        private readonly IUserRepository _userRepository;
        private readonly IMapper _mapper;
        private readonly IHistorySystemRepository _historySystemRepository;
        private readonly IHttpContextAccessor _accessor;
        private readonly ILogger<TypeOfMarkersController> _logger;
        private readonly ITypeOfMarkerRepository _typeOfMarkerRepository;

        public MarkersController(IMarkerRepository markerRepository, IUserRepository userRepository, IMapper mapper, IHistorySystemRepository historySystemRepository, IHttpContextAccessor accessor, ILogger<TypeOfMarkersController> logger, ITypeOfMarkerRepository typeOfMarkerRepository)
        {
            _markerRepository = markerRepository;
            _userRepository = userRepository;
            _historySystemRepository = historySystemRepository;
            _mapper = mapper;
            _historySystemRepository = historySystemRepository;
            _accessor = accessor;
            _logger = logger;
            _typeOfMarkerRepository = typeOfMarkerRepository;
        }

        public ActionResult Index()
        {
            StatusQuery Notification;
            try
            {
                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Marker/Index: " + ex.Message);

                Notification = new StatusQuery("error", "Thất bại!", "Xem danh sách");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;
            }

            return View();
        }

        public JsonResult GetIndex()
        {
            List<Marker> _listMarker = new List<Marker>();
            try
            {
                _listMarker = _markerRepository.GetAll().ToList();

                var query = from d in _listMarker
                            select new
                            {
                                Description = d.Description ?? string.Empty,
                                d.MarkerID,
                                MarkerCode = d.MarkerCode ?? string.Empty,
                                Phone = d.Phone ?? string.Empty,
                                d.Latitude,
                                d.Longitude,
                                MarkerTypeID = d.TypeOfMarkerID,
                                d.MarkerName
                            };

                return Json(query);

            }
            catch (Exception ex)
            {
                _logger.LogError("Markers/GetIndex: " + ex.Message);
            }
            return Json(_listMarker);
        }

        public ActionResult Details(int id)
        {
            try
            {
                var _Marker = _markerRepository.GetById(id);
                var _MarkerModel = _mapper.Map<Marker, MarkerModel>(_Marker);

                if (_MarkerModel.MarkerTypeID > 0)
                {
                    ViewBag.MarkerTypeID = _typeOfMarkerRepository.GetById(_MarkerModel.MarkerTypeID).Name;
                }

                if (TempDataHelper.Get<StatusQuery>(TempData, "Notification") is StatusQuery Notification)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }

                return View(_MarkerModel);
            }
            catch (Exception ex)
            {
                _logger.LogError("Markers/Details: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "Thất bại", "Xem chi tiết"));

                return RedirectToAction("Index");
            }
        }

        public ActionResult Create()
        {
            MarkerModel markerModel = new MarkerModel();
            
            try
            {
                //markerModel.ListTypeOfMarkers = GetListDropDownListTypeOfMarker();

                if (TempDataHelper.Get<StatusQuery>(TempData, "Notification") is StatusQuery Notification)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Markers/Create: " + ex.Message);

                StatusQuery Notification;
                Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;
            }
            return View(markerModel);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create([Bind("MarkerName, Phone, LATITUDE, LONGITUDE, MarkerTypeID, Description")] MarkerModel MarkerModel)
        {
            if (MarkerModel.LATITUDE == 0)
            {
                ModelState.Remove("LATITUDE");
                ModelState.SetModelValue("LATITUDE", null, null);
                ModelState.AddModelError("LATITUDE", "Giá trị nhập vào chưa đúng");
            }
            if (MarkerModel.LONGITUDE == 0)
            {
                ModelState.Remove("LONGITUDE");
                ModelState.SetModelValue("LONGITUDE", null, null);
                ModelState.AddModelError("LONGITUDE", "Giá trị nhập vào chưa đúng");
            }

            StatusQuery Notification;
            if (ModelState.IsValid)
            {
                try
                {
                    var _systemUser = GetSesson();

                    if (_systemUser == null)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                        return RedirectToAction("Logout", "Security");
                    }

                    var Marker = _markerRepository.Get(p => p.MarkerName.ToLower() == MarkerModel.MarkerName.ToLower()).SingleOrDefault();

                    if (Marker != null)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("warning", "", "Vui lòng kiểm tra lại"));
                        ModelState.AddModelError("BRANCHNAME", "Tên khu vực đã tồn tại");

                        return View();
                    }

                    var _Marker = _mapper.Map<MarkerModel, Marker>(MarkerModel);

                    _markerRepository.Insert(_Marker);

                    var statusInsert = _markerRepository.SaveChanges();

                    if (statusInsert > 0)
                    {
                        string StringDescription = new GetStringHistorySystem().Get(_systemUser.UserName, Resources.Resource.AddItem, _Marker.MarkerName.ToString(), Resources.Resource.Marker);

                        InsertHistorySystem((int)EACTION_TYPE.CREATE, (int)EnumControllerName.MARKER, StringDescription, null, _Marker);

                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Thêm mới thành công"));
                        return RedirectToAction("Index");
                    }
                    else
                    {
                        Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        //MarkerModel.ListTypeOfMarkers = GetListDropDownListTypeOfMarker();

                        return View(MarkerModel);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("Markers/Create: " + ex.Message);

                    Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    //MarkerModel.ListTypeOfMarkers = GetListDropDownListTypeOfMarker();

                    return View(MarkerModel);
                }
            }
            Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;

            //MarkerModel.ListTypeOfMarkers = GetListDropDownListTypeOfMarker();

            return View(MarkerModel);
        }

        private SelectList GetListDropDownListTypeOfMarker()
        {
            List<DropDownList> listDropDownListTypeOfMarker = new List<DropDownList>();
            try
            {
                var commandCenter = _typeOfMarkerRepository.GetAll();

                foreach (var item in commandCenter)
                {
                    DropDownList drop = new DropDownList
                    {
                        Id = item.Id,
                        Name = item.Name
                    };
                    listDropDownListTypeOfMarker.Add(drop);
                }

                return ToSelectList(listDropDownListTypeOfMarker);
            }
            catch (Exception ex)
            {
                _logger.LogError("Markers/GetListDropDownListCommandCenter: " + ex.Message);
                Console.WriteLine(ex.Message);
            }
            return ToSelectList(listDropDownListTypeOfMarker);
        }

        public ActionResult Edit(int id)
        {
            StatusQuery Notification;
            try
            {
                var _Marker = _markerRepository.GetById(id);

                var _MarkerModel = _mapper.Map<Marker, MarkerModel>(_Marker);

                if (_MarkerModel != null)
                {
                    Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                    if (Notification != null)
                    {
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;
                    }

                    //_MarkerModel.ListTypeOfMarkers = GetListDropDownListTypeOfMarker();

                    var builder = new ConfigurationBuilder()
                                     .SetBasePath(Directory.GetCurrentDirectory())
                                     .AddJsonFile("appsettings.json");

                    var configuration = builder.Build();

                    return View(_MarkerModel);
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                    return RedirectToAction("Index");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Markers/Edit: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                return RedirectToAction("Index");
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit([Bind("MarkerID, MarkerName, Phone, LATITUDE, LONGITUDE, MarkerTypeID, Description")] MarkerModel MarkerModel)
        {
            StatusQuery Notification;

            if (MarkerModel.LATITUDE == 0)
            {
                ModelState.Remove("LATITUDE");
                ModelState.SetModelValue("LATITUDE", null, null);
                ModelState.AddModelError("LATITUDE", "Giá trị nhập vào chưa đúng");
            }
            if (MarkerModel.LONGITUDE == 0)
            {
                ModelState.Remove("LONGITUDE");
                ModelState.SetModelValue("LONGITUDE", null, null);
                ModelState.AddModelError("LONGITUDE", "Giá trị nhập vào chưa đúng");
            }

            if (ModelState.IsValid)
            {
                try
                {
                    var Marker = _markerRepository.GetById(MarkerModel.MarkerID);

                    var _MarkerTemp = _mapper.Map<Marker, MarkerModel>(Marker);
                    var MarkerOld = _mapper.Map<MarkerModel, Marker>(_MarkerTemp);

                    var _systemUserSession = GetSesson();
                    var _systemUser = GetSesson();


                    if (_systemUser == null)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                        return RedirectToAction("Logout", "Security");
                    }

                    Marker.MarkerName = MarkerModel.MarkerName;
                    Marker.Latitude = MarkerModel.LATITUDE;
                    Marker.Longitude = MarkerModel.LONGITUDE;
                    //Marker.MarkerCode = MarkerModel.MarkerCode;
                    Marker.Phone = MarkerModel.Phone;
                    Marker.TypeOfMarkerID = MarkerModel.MarkerTypeID;
                    Marker.Description = MarkerModel.Description;

                    _markerRepository.Update(Marker);

                    var updateStatus = _markerRepository.SaveChanges();

                    if (updateStatus > 0)
                    {
                        string StringDescription = new GetStringHistorySystem().Get(_systemUser.UserName, Resources.Resource.Edit, Marker.MarkerName.ToString(), Resources.Resource.Marker);

                        InsertHistorySystem((int)EACTION_TYPE.EDIT, (int)EnumControllerName.MARKER, StringDescription, MarkerOld, Marker);

                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Sửa thành công"));

                        return RedirectToAction("Index");
                    }
                    else
                    {
                        Notification = new StatusQuery("error", "", "Sửa thất bại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        //MarkerModel.ListTypeOfMarkers = GetListDropDownListTypeOfMarker();

                        return View(MarkerModel);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("Markers/Edit: " + ex.Message);

                    Notification = new StatusQuery("error", "", "Sửa thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    //MarkerModel.ListTypeOfMarkers = GetListDropDownListTypeOfMarker();

                    return View(MarkerModel);
                }
            }

            Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;

            //MarkerModel.ListTypeOfMarkers = GetListDropDownListTypeOfMarker();

            return View(MarkerModel);
        }

        public ActionResult Delete(int id)
        {
            StatusQuery Notification;
            try
            {
                var _Marker = _markerRepository.GetById(id);

                var _MarkerModel = _mapper.Map<Marker, MarkerModel>(_Marker);

                if (_MarkerModel == null)
                {
                    Notification = new StatusQuery("warning", "", "Không tìm thấy khu vực");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return RedirectToAction("Index");
                }

                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }

                return View(_MarkerModel);
            }
            catch (Exception ex)
            {
                _logger.LogError("Markers/Delete: " + ex.Message);

                Notification = new StatusQuery("warning", "", "Xóa thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;

                return RedirectToAction("Index");
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int id)
        {
            try
            {
                var systemUser = GetSesson();

                if (systemUser == null)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                    return RedirectToAction("Logout", "Security");
                }

                var Marker = _markerRepository.GetById(id);

                _markerRepository.Delete(Marker);

                var deleteStatus = _markerRepository.SaveChanges();

                if (deleteStatus > 0)
                {
                    string StringDescription = new GetStringHistorySystem().Get(systemUser.UserName, Resources.Resource.Delete, Marker.MarkerName.ToString(), Resources.Resource.Marker);

                    InsertHistorySystem((int)EACTION_TYPE.DELETE, (int)EnumControllerName.MARKER, StringDescription, Marker, null);

                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Xóa thành công"));
                    return RedirectToAction("Index");
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));
                    return RedirectToAction("Delete", "Markers", new { id });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Markers/Delete: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));
                return RedirectToAction("Delete", "Markers", new { id });
            }
        }

        [NonAction]
        private SelectList ToSelectList(List<DropDownList> DropDownList)
        {
            List<SelectListItem> list = new List<SelectListItem>();

            foreach (var item in DropDownList)
            {
                list.Add(new SelectListItem()
                {
                    Text = item.Name,
                    Value = item.Id.ToString()
                });
            }

            return new SelectList(list, "Value", "Text");
        }

        public User GetSesson()
        {
            var SessionUserSystem = HttpContext.Session.GetComplexData<User>("SessionUserSystemId_" + HttpContext.Session.GetString("SessionUserSystemId"));
            if (SessionUserSystem != null)
            {
                var _user = _userRepository.GetById(SessionUserSystem.Id);

                if (_user != null)
                {
                    if (!_user.Actived)
                    {
                        HttpContext.Session.Clear();
                        return null;
                    }
                    return _user;
                }
            }
            return null;
        }

        private void InsertHistorySystem(int action_type, int controllerName, string description, object OldModel, object NewModel)
        {
            try
            {
                var ipAddress = _accessor.HttpContext.Connection.RemoteIpAddress.ToString();

                string jsonOldObject = Newtonsoft.Json.JsonConvert.SerializeObject(OldModel);
                string jsonNewObject = Newtonsoft.Json.JsonConvert.SerializeObject(NewModel);

                HistorySystem history = new HistorySystem
                {
                    ActionType = action_type,
                    ActionTime = DateTime.Now,
                    Description = description,
                    OldObject = jsonOldObject,
                    NewObject = jsonNewObject,
                    UserId = int.Parse(HttpContext.Session.GetString("SessionUserSystemId")),
                    IpAddress = ipAddress,
                    ControllerName = controllerName
                };

                _historySystemRepository.Insert(history);

                _historySystemRepository.SaveChanges();
            }
            catch (Exception ex)
            {
                _logger.LogError("Marker/InsertHistorySystem: " + ex.Message);
            }
        }
    }
}