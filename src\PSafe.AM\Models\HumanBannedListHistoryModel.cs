﻿using PSafe.AM.ACS;
using PSafe.Core.Domains;
using System;


namespace PSafe.AM.Models
{
    public class HumanBannedListHistoryModel
    {
        public string ActionType { get; set; }
        public string CreatedBy { get; set; }
        public string ActiveTime { get; set; }
        public string CreatedDate { get; set; }
        public string ExpiryTime { get; set; }

        public string ClearBy { get; set; }

        public string ClearDate { get; set; }

        public string ReasonViolation { get; set; }
        public string ReasonClear { get; set; }
        public string ClearFlag { get; set; }
    }
}
