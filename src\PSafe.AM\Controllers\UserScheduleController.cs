﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using PSafe.AM.Common;
using PSafe.AM.Models;
using PSafe.Core.Domains;
using PSafe.Core.Interface;
using Microsoft.AspNetCore.Http;
using static PSafe.Common.CommonEnums;
using Microsoft.AspNetCore.Mvc.Rendering;
using PSafe.Common.UserEnums;
using Microsoft.Extensions.Logging;
using System.IO;
using OfficeOpenXml;
using Microsoft.AspNetCore.Hosting;
using PSafe.Common;

namespace PSafe.AM.Controllers
{
    [AuthorizeActionFilter(EPERMISSIONS_AM.UserManage)]
    public class UserScheduleController : Controller
    {
        private readonly IUserScheduleRepository _userScheduleRepository;
        private readonly IUserRepository _userRepository;
        private readonly IHistorySystemRepository _historySystemRepository;
        private readonly IScheduleTimeRepository _scheduleTimeRepository;
        private readonly IPatrolAreaRepository _patrolAreaRepository;
        private readonly ILogger<UserScheduleController> _logger;
        private readonly IHostingEnvironment _hostingEnvironment;

        public UserScheduleController(IUserScheduleRepository userScheduleRepository, IUserRepository userRepository, IHistorySystemRepository historySystemRepository, IScheduleTimeRepository scheduleTimeRepository,
            IPatrolAreaRepository patrolAreaRepository, ILogger<UserScheduleController> logger, IHostingEnvironment hostingEnvironment)
        {
            _userScheduleRepository = userScheduleRepository;
            _userRepository = userRepository;
            _historySystemRepository = historySystemRepository;
            _scheduleTimeRepository = scheduleTimeRepository;
            _patrolAreaRepository = patrolAreaRepository;
            _logger = logger;
            _hostingEnvironment = hostingEnvironment;
        }

        public IActionResult Index()
        {
            StatusQuery Notification;
            List<UserScheduleModel> _listUserScheduleModel = new List<UserScheduleModel>();
            try
            {
                List<UserSchedule> _listUserSchedule = _userScheduleRepository.Get(null, null, "User,PatrolArea").ToList();
                _listUserSchedule = SortListUserSchedule(_listUserSchedule);
                var _listScheduleTime = GetListScheduleTime(_listUserSchedule);
                foreach (var userSchedule in _listUserSchedule)
                {
                    UserScheduleModel userScheduleModel = new UserScheduleModel();
                    userScheduleModel.Id = userSchedule.Id;
                    CopyTo(userScheduleModel, userSchedule);
                    userScheduleModel.UserName = userSchedule.User.UserName;
                    userScheduleModel.PatrolArea = userSchedule.PatrolArea.Name;
                    List<int> listScheduleId = ParseListScheduleId(userSchedule);
                    userScheduleModel.ScheduleTime = 
                        string.Join(", ", _listScheduleTime.Where(x => listScheduleId.Contains(x.Id)).Select(x => x.Name).ToList());
                    _listUserScheduleModel.Add(userScheduleModel);
                }

                ViewBag.scheduleTimeList = _scheduleTimeRepository.GetAll().ToList();
                ViewBag.patrolAreaList = _patrolAreaRepository.GetAll().ToList();

                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }
            }
            catch(Exception ex)
            {
                _logger.LogError("UserSchedule/Index: " + ex.Message);

                Notification = new StatusQuery("error", "", "Xem danh sách thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;
            }

            return View(_listUserScheduleModel);
        }

        [HttpPost("UserSchedule/GetUserScheduleByDate")]
        public JsonResult GetUserScheduleByDate(DateTime dateTime)
        {
            List<UserSchedule> listUserSchedule = _userScheduleRepository.Get(x => x.ScheduleDate.Date == dateTime.Date, null, "User").ToList();
            List<UserScheduleModel> listUserScheduleModel = new List<UserScheduleModel>();
            foreach(var userSchedule in listUserSchedule)
            {
                UserScheduleModel userScheduleModel = new UserScheduleModel();
                userScheduleModel.Id = userSchedule.Id;
                CopyTo(userScheduleModel, userSchedule);
                userScheduleModel.UserName = userSchedule.User.UserName;
                listUserScheduleModel.Add(userScheduleModel);
            }
            return Json(listUserScheduleModel);
        }

        // GET: /typeOfDevice/Details/
        public ActionResult Details(int id)
        {
            try
            {
                var _userSchedule = _userScheduleRepository.Get(x => x.Id == id, null, "User,PatrolArea").FirstOrDefault();

                if (_userSchedule != null)
                {
                    try
                    {
                        if (TempDataHelper.Get<StatusQuery>(TempData, "Notification") is StatusQuery Notification)
                        {
                            ViewBag.Status = Notification.Status;
                            ViewBag.Value = Notification.Value;
                            ViewBag.Type = Notification.Type;
                        }

                    }
                    catch (Exception ex)
                    {
                        _logger.LogError("UserSchedule/Details: " + ex.Message);
                        Console.WriteLine(ex.Message);
                    }

                    UserScheduleModel userScheduleModel = new UserScheduleModel();

                    var _listScheduleTime = GetListScheduleTime(_userSchedule);
                    userScheduleModel.Id = _userSchedule.Id;
                    CopyTo(userScheduleModel, _userSchedule);
                    userScheduleModel.UserName = _userSchedule.User.UserName;
                    userScheduleModel.PatrolArea = _userSchedule.PatrolArea.Name;
                    List<int> listScheduleId = ParseListScheduleId(_userSchedule);
                    userScheduleModel.ScheduleTime =
                        string.Join(", ", _listScheduleTime.Where(x => listScheduleId.Contains(x.Id)).Select(x => x.Name).ToList());
                    return View(userScheduleModel);
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                    return RedirectToAction("Index");
                }
            }
            catch(Exception ex)
            {
                _logger.LogError("UserSchedule/Details: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                return RedirectToAction("Index");
            }
        }

        private void SetComboboxData()
        {
            List<SelectListItem> userListItems = new List<SelectListItem>();
            List<SelectListItem> patrolAreaListItems = new List<SelectListItem>();
            List<SelectListItem> scheduleTimeListItems = new List<SelectListItem>();
            try
            {
                var userList = _userRepository.GetAll().OrderBy(x => x.UserName).ToList();
                foreach (var user in userList)
                {
                    userListItems.Add(new SelectListItem()
                    {
                        Text = user.UserName,
                        Value = user.Id.ToString()
                    });
                }

                var patrolAreaList = _patrolAreaRepository.GetAll().ToList();
                foreach (var patrolArea in patrolAreaList)
                {
                    patrolAreaListItems.Add(new SelectListItem()
                    {
                        Text = patrolArea.Name,
                        Value = patrolArea.Id.ToString()
                    });
                }

                var scheduleTimeList = _scheduleTimeRepository.GetAll().ToList();
                foreach (var scheduleTime in scheduleTimeList)
                {
                    scheduleTimeListItems.Add(new SelectListItem()
                    {
                        Text = scheduleTime.Name,
                        Value = scheduleTime.Id.ToString()
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("UserSchedule/SetComboboxData: " + ex.Message);
                Console.WriteLine(ex.Message);
            }
            ViewBag.userListItems = userListItems;
            ViewBag.patrolAreaListItems = patrolAreaListItems;
            ViewBag.schduleTimeListItems = scheduleTimeListItems;
        }

        // GET: /typeOfDevice/Create
        public ActionResult Create()
        {
            SetComboboxData();
            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create(UserScheduleModel userScheduleModel)
        {
            SetComboboxData();
            StatusQuery Notification;
            if (ModelState.IsValid)
            {
                try
                {
                    var systemUser = GetSesson();
                    if (systemUser == null)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                        return RedirectToAction("Logout", "Security");
                    }
                    string userName = GetUserNameFromId(userScheduleModel.UserId);
                    if(userName == null)
                    {
                        Notification = new StatusQuery("error", "", "Không tìm thấy nhân viên trực");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        return View(userScheduleModel);
                    }

                    UserSchedule _userSchedule = new UserSchedule();
                    CopyTo(_userSchedule, userScheduleModel);

                    _userScheduleRepository.Insert(_userSchedule);

                    var statusInsert = _userScheduleRepository.SaveChanges();

                    if (statusInsert > 0)
                    {
                        string StringDescription = new GetStringHistorySystem().Get(systemUser.UserName, Resources.Resource.Create, userName.ToString(), Resources.Resource.UserSchedule);

                        InsertHistorySystem((int)EACTION_TYPE.CREATE, (int)EnumControllerName.USER_SCHEDULE, StringDescription, null, userScheduleModel);

                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Thêm mới thành công"));
                        return RedirectToAction("Index");
                    }
                    else
                    {
                        Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        return View(userScheduleModel);
                    }
                }
                catch(Exception ex)
                {
                    _logger.LogError("UserSchedule/Create: " + ex.Message);

                    Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return View(userScheduleModel);
                }
            }

            Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;

            return View(userScheduleModel);
        }

        // GET: /typeOfDevice/Edit/5
        public ActionResult Edit(int id)
        {
            try
            {
                var _userSchedule = _userScheduleRepository.GetById(id);
                if (_userSchedule != null)
                {
                    SetComboboxData();
                    UserScheduleModel userScheduleModel = new UserScheduleModel();
                    CopyTo(userScheduleModel, _userSchedule);
                    return View(userScheduleModel);
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                    return RedirectToAction("Index");
                }
            }
            catch(Exception ex)
            {
                _logger.LogError("UserSchedule/Edit: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                return RedirectToAction("Index");
            }
        }

        // POST: /typeOfDevice/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit(UserScheduleModel userScheduleModel)
        {
            StatusQuery Notification;
            if (ModelState.IsValid)
            {
                try
                {
                    var systemUser = GetSesson();
                    if (systemUser == null)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                        return RedirectToAction("Logout", "Security");
                    }

                    var _userSchedule = _userScheduleRepository.GetById(userScheduleModel.Id);
                    if (_userSchedule == null)
                    {
                        SetComboboxData();
                        Notification = new StatusQuery("error", "", "Không tìm thấy lịch trực");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;
                        return View(userScheduleModel);
                    }

                    string userName = GetUserNameFromId(_userSchedule.UserId);
                    if (userName == null)
                    {
                        SetComboboxData();
                        Notification = new StatusQuery("error", "", "Không tìm thấy nhân viên trực");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        return View(userScheduleModel);
                    }

                    CopyTo(_userSchedule, userScheduleModel);

                    _userScheduleRepository.Update(_userSchedule);

                    var updateStatus = _userScheduleRepository.SaveChanges();

                    if (updateStatus > 0)
                    {
                        string StringDescription = new GetStringHistorySystem().Get(systemUser.UserName, Resources.Resource.Edit, userName, Resources.Resource.UserSchedule);
                        InsertHistorySystem((int)EACTION_TYPE.EDIT, (int)EnumControllerName.USER_SCHEDULE, StringDescription, userScheduleModel, null);
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Sửa thành công"));

                        return RedirectToAction("Index");
                    }
                    else
                    {
                        SetComboboxData();
                        Notification = new StatusQuery("error", "", "Sửa thất bại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        return View(userScheduleModel);
                    }
                }
                catch(Exception ex)
                {
                    _logger.LogError("UserSchedule/Edit: " + ex.Message);

                    SetComboboxData();
                    Notification = new StatusQuery("error", "", "Sửa thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return View(userScheduleModel);
                }
            }

            SetComboboxData();
            Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;

            return View(userScheduleModel);
        }

        // GET: /typeOfDevice/Delete/5
        public ActionResult Delete(int id)
        {
            StatusQuery Notification;
            try
            {
                var _userSchedule = _userScheduleRepository.Get(x => x.Id == id, null, "User,PatrolArea").FirstOrDefault();

                if (_userSchedule == null)
                {
                    Notification = new StatusQuery("warning", "", "Không tìm thấy đối tương xóa");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return RedirectToAction("Index");
                }

                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }

                UserScheduleModel userScheduleModel = new UserScheduleModel();

                var _listScheduleTime = GetListScheduleTime(_userSchedule);
                userScheduleModel.Id = _userSchedule.Id;
                CopyTo(userScheduleModel, _userSchedule);
                userScheduleModel.UserName = _userSchedule.User.UserName;
                userScheduleModel.PatrolArea = _userSchedule.PatrolArea.Name;
                List<int> listScheduleId = ParseListScheduleId(_userSchedule);
                userScheduleModel.ScheduleTime =
                    string.Join(", ", _listScheduleTime.Where(x => listScheduleId.Contains(x.Id)).Select(x => x.Name).ToList());
                
                return View(userScheduleModel);
            }
            catch(Exception ex)
            {
                _logger.LogError("UserSchedule/Delete: " + ex.Message);

                Notification = new StatusQuery("warning", "", "Xóa thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;

                return RedirectToAction("Index");
            }
        }


        // POST: /ScheduleTime/Delete/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int id)
        {
            try
            {
                var systemUserSession = GetSesson();

                if (systemUserSession == null)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                    return RedirectToAction("Logout", "Security");
                }

                var userSchedule = _userScheduleRepository.GetById(id);
                string userName = GetUserNameFromId(userSchedule.UserId);
                if (userName == null)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Không tìm thấy nhân viên trực"));
                    return RedirectToAction("Delete", "UserSchedule", new { id });
                }

                _userScheduleRepository.Delete(userSchedule);

                var deleteStatus = _userScheduleRepository.SaveChanges();

                if (deleteStatus > 0)
                {
                    string StringDescription = new GetStringHistorySystem().Get(systemUserSession.UserName, Resources.Resource.Delete, userName, Resources.Resource.TypeOfDevice);

                    InsertHistorySystem((int)EACTION_TYPE.DELETE, (int)EnumControllerName.USER_SCHEDULE, StringDescription, userSchedule, null);

                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Xóa thành công"));
                    return RedirectToAction("Index");
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));
                    return RedirectToAction("Delete", "UserSchedule", new { id });
                }
            }
            catch(Exception ex)
            {
                _logger.LogError("UserSchedule/DeleteConfirmed: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));
                return RedirectToAction("Delete", "UserSchedule", new { id });
            }
        }

        [HttpGet]
        public IActionResult DownloadExcelTemplate(string f)
        {
            try
            {
                string fileName = System.IO.Path.Combine(_hostingEnvironment.ContentRootPath, "ExcelTemplate", f);
                var net = new System.Net.WebClient();
                if (Utils.IsSafeUrl(fileName) == false)
                {
                    throw new Exception("URL is not safe!");
                }
                var data = net.DownloadData(fileName);
                var content = new System.IO.MemoryStream(data);
                var contentType = "APPLICATION/octet-stream";
                return File(content, contentType, f);
            }
            catch
            {
                return NotFound();
            }
        }

        [HttpPost("UserSchedule/Import")]
        public async Task<JsonResult> Import(IFormFile file)
        {
            if (!IsValidExcelFile(file))
            {
                return Json(new
                {
                    success = false,
                    message = "Vui lòng chọn file"
                });
            }

            int totalImport = 0;
            List<UserSchedule> UserScheduleLists = new List<UserSchedule>();

            try
            {
                using (var stream = new MemoryStream())
                {
                    await file.CopyToAsync(stream);

                    using (var package = new ExcelPackage(stream))
                    {
                        ExcelWorksheet worksheet = package.Workbook.Worksheets[0];
                        var rowCount = worksheet.Dimension.Rows;

                        const int START_ROW = 2;
                        for (int row = START_ROW; row <= rowCount; row++)
                        {
                            var userSchedule = ReadUserScheduleFromExcel(worksheet, row, ref totalImport);
                            if (userSchedule != null)
                                UserScheduleLists.Add(userSchedule);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("UserSchedule/Import: " + ex.Message);
                Console.WriteLine(ex.Message);
            }

            int numOfImportSuccess = 0;
            foreach (var userSchedule in UserScheduleLists)
            {
                try
                {
                    _userScheduleRepository.Insert(userSchedule);
                    var statusInsert = _userScheduleRepository.SaveChanges();
                    numOfImportSuccess++;
                }
                catch(Exception ex)
                {
                    _logger.LogError("UserSchedule/Import: " + ex.Message);
                }
            }

            return Json(new
            {
                success = numOfImportSuccess > 0,
                numOfImportSuccess = numOfImportSuccess,
                totalImport = totalImport
            });
        }

        private UserSchedule ReadUserScheduleFromExcel(ExcelWorksheet worksheet, int row, ref int totalImport)
        {
            if (worksheet.Cells[row, (int)UserScheduleExcelColumnIndex.USER_NAME].Value == null &&
                worksheet.Cells[row, (int)UserScheduleExcelColumnIndex.AREA_NAME].Value == null &&
                worksheet.Cells[row, (int)UserScheduleExcelColumnIndex.SCHEDULE_ID].Value == null)
                return null;

            totalImport++;
            var userSchedule = new UserSchedule();
            userSchedule.UserId = FindUserIdByserName(GetExcelCellStringValue(worksheet, row, (int)UserScheduleExcelColumnIndex.USER_NAME));
            if (userSchedule.UserId == -1)
                return null;

            DateTime scheduleDate;
            if (ParseDateTime(GetExcelCellStringValue(worksheet, row, (int)UserScheduleExcelColumnIndex.SCHEDULE_DATE), "M/d/yyyy", out scheduleDate))
                userSchedule.ScheduleDate = scheduleDate;
            else
                userSchedule.ScheduleDate = DateTime.Now.Date;

            userSchedule.PatrolAreaID = FindPatrolAreaByName(GetExcelCellStringValue(worksheet, row, (int)UserScheduleExcelColumnIndex.AREA_NAME));
            if (userSchedule.PatrolAreaID == -1)
                return null;

            var scheduleTimeList = FindScheduleTimeByName(GetExcelCellStringValue(worksheet, row, (int)UserScheduleExcelColumnIndex.SCHEDULE_ID));
            if (scheduleTimeList.Count == 0)
                return null;

            userSchedule.ListScheduleId = String.Join(',', scheduleTimeList.ToArray());

            return userSchedule;
        }

        private bool ParseDateTime(string dateTime, string format, out DateTime dt)
        {
            try
            {
                dt = DateTime.ParseExact(dateTime, format, System.Globalization.CultureInfo.InvariantCulture);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("UserSchedule/ParseDateTime: " + ex.Message);
                dt = DateTime.Now;
                return false;
            }
        }

        private string GetExcelCellStringValue(ExcelWorksheet worksheet, int row, int column)
        {
            if (worksheet.Cells[row, column].Value != null)
                return worksheet.Cells[row, column].Value.ToString().Trim();

            return string.Empty;
        }

        private List<int> FindScheduleTimeByName(string scheduleTime)
        {
            string[] scheduleTimes = scheduleTime.Split(",");
            if (scheduleTimes.Length == 0)
                return new List<int>();

            List<string> scheduleTimeList = new List<string>();
            foreach(var scheduleTimeName in scheduleTimes)
            {
                scheduleTimeList.Add(scheduleTimeName.Trim());
            }

            return _scheduleTimeRepository.GetBy(x => scheduleTimeList.Contains(x.Name)).Select(x=>x.Id).ToList();
        }

        private int FindUserIdByserName(string userName)
        {
            if (string.IsNullOrEmpty(userName))
                return -1;

            userName = userName.ToLower();
            var user = _userRepository.GetBy(x => x.UserName.ToLower() == userName).FirstOrDefault();
            if (user == null)
                return -1;

            return user.Id;
        }

        private int FindPatrolAreaByName(string patrolAreaName)
        {
            if (string.IsNullOrEmpty(patrolAreaName))
                return -1;

            patrolAreaName = patrolAreaName.ToLower();
            var patrolArea = _patrolAreaRepository.GetBy(x => x.Name.ToLower() == patrolAreaName).FirstOrDefault();
            if (patrolArea == null)
                return -1;

            return patrolArea.Id;
        }

        private bool IsValidExcelFile(IFormFile file)
        {
            if (file == null || !Path.GetExtension(file.FileName).Equals(".xlsx", StringComparison.OrdinalIgnoreCase))
                return false;

            return true;
        }

        private enum UserScheduleExcelColumnIndex
        {
            USER_NAME = 1,
            SCHEDULE_DATE,
            AREA_NAME,
            SCHEDULE_ID
        }

        private void CopyTo(UserSchedule dest, UserScheduleModel src)
        {
            dest.ScheduleDate = src.ScheduleDate;
            dest.PatrolAreaID = src.PatrolAreaID;
            dest.UserId = src.UserId;
            dest.ListScheduleId = src.ListScheduleId;
        }

        private void CopyTo(UserScheduleModel dest, UserSchedule src)
        {
            dest.ScheduleDate = src.ScheduleDate;
            dest.PatrolAreaID = src.PatrolAreaID;
            dest.UserId = src.UserId;
            dest.ListScheduleId = src.ListScheduleId;
        }

        public User GetSesson()
        {
            var SessionUserSystem = HttpContext.Session.GetComplexData<User>("SessionUserSystemId_" + HttpContext.Session.GetString("SessionUserSystemId"));
            if (SessionUserSystem != null)
            {
                var _user = _userRepository.GetById(SessionUserSystem.Id);

                if (_user != null)
                {
                    if (!_user.Actived)
                    {
                        HttpContext.Session.Clear();
                        return null;
                    }
                    return _user;
                }
            }
            return null;
        }

        private string GetUserNameFromId(int id)
        {
            var _user = _userRepository.GetById(id);
            if (_user == null)
                return null;

            return _user.UserName;
        }

        private void InsertHistorySystem(int action_type, int controllerName, string description, object OldModel, object NewModel)
        {
            IPHostEntry heserver = Dns.GetHostEntry(Dns.GetHostName());
            var ipAddress = heserver.AddressList.FirstOrDefault(p => p.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork).ToString();

            string jsonOldObject = Newtonsoft.Json.JsonConvert.SerializeObject(OldModel);
            string jsonNewObject = Newtonsoft.Json.JsonConvert.SerializeObject(NewModel);

            HistorySystem history = new HistorySystem
            {
                ActionType = action_type,
                ActionTime = DateTime.Now,
                Description = description,
                OldObject = jsonOldObject,
                NewObject = jsonNewObject,
                UserId = int.Parse(HttpContext.Session.GetString("SessionUserSystemId")),
                IpAddress = ipAddress,
                ControllerName = controllerName
            };

            _historySystemRepository.Insert(history);

            _historySystemRepository.SaveChanges();
        }

        private List<UserSchedule> SortListUserSchedule(List<UserSchedule> listUserSchedule)
        {
            List<UserSchedule> retVal = new List<UserSchedule>();
            DateTime curDate = DateTime.Now.Date;
            foreach(var userSchedule in listUserSchedule)
            {
                if(userSchedule.ScheduleDate.Date == curDate)
                    retVal.Add(userSchedule);
            }

            foreach(var userSchedule in retVal)
            {
                listUserSchedule.Remove(userSchedule);
            }

            retVal = retVal.OrderByDescending(x => x.Id).ToList();
            listUserSchedule = listUserSchedule.OrderByDescending(x => x.ScheduleDate).ToList();
            foreach(var userSchedule in listUserSchedule)
            {
                retVal.Add(userSchedule);
            }

            return retVal;
        }


        private List<ScheduleTime> GetListScheduleTime(List<UserSchedule> listUserSchedule)
        {
            List<int> scheduleTimeIdNeedGet = GetScheduleTimeIdNeedGet(listUserSchedule);
            if (scheduleTimeIdNeedGet.Count > 0)
            {
                try
                {
                    return _scheduleTimeRepository.Get(x => scheduleTimeIdNeedGet.Contains(x.Id)).ToList();
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex.Message);
                    _logger.LogError("UserSchedule/GetListScheduleTime: " + ex.Message);
                }
            }

            return null;
        }

        private List<ScheduleTime> GetListScheduleTime(UserSchedule userSchedule)
        {
            List<UserSchedule> listUserSchedule = new List<UserSchedule>();
            listUserSchedule.Add(userSchedule);
            List<int> scheduleTimeIdNeedGet = GetScheduleTimeIdNeedGet(listUserSchedule);
            if (scheduleTimeIdNeedGet.Count > 0)
            {
                try
                {
                    return _scheduleTimeRepository.Get(x => scheduleTimeIdNeedGet.Contains(x.Id)).ToList();
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex.Message);
                    _logger.LogError("UserSchedule/GetListScheduleTime: " + ex.Message);
                }
            }

            return null;
        }

        private List<int> GetScheduleTimeIdNeedGet(List<UserSchedule> listUserSchedule)
        {
            List<int> scheduleTimeIdNeedGet = new List<int>();
            foreach (var userSchedule in listUserSchedule)
            {
                scheduleTimeIdNeedGet.AddRange(ParseListScheduleId(userSchedule));
            }

            return scheduleTimeIdNeedGet;
        }

        private List<int> ParseListScheduleId(UserSchedule userSchedule)
        {
            List<int> listScheduleId = new List<int>();
            if (!string.IsNullOrEmpty(userSchedule.ListScheduleId))
            {
                string[] scheduleIds = userSchedule.ListScheduleId.Split(',');
                foreach (string scheduleId in scheduleIds)
                {
                    int scheduleTimeId;
                    if (int.TryParse(scheduleId, out scheduleTimeId))
                        listScheduleId.Add(scheduleTimeId);
                }
            }

            return listScheduleId;
        }
    }
}