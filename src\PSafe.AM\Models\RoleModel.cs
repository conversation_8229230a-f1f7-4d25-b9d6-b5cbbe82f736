﻿using Microsoft.AspNetCore.Mvc.Rendering;
using System;
using System.ComponentModel.DataAnnotations;

namespace PSafe.AM.Models
{
    public class RoleModel
    {
        [Key]
        [Display(Name = "RoleId", ResourceType = typeof(Resources.Resource__role))]
        public int RoleId { get; set; }

        [Required(ErrorMessage = "Vui lòng nhập tên nhóm")]
        [StringLength(50, MinimumLength = 3, ErrorMessage = "Độ dài từ 3-50 ký tự")]
        [Display(Name = "RoleName", ResourceType = typeof(Resources.Resource__role))]
        public string RoleName { get; set; } = string.Empty;

        [Display(Name = "RoleDescription", ResourceType = typeof(Resources.Resource__role))]
        public string RoleDescription { get; set; } = string.Empty;

        [Display(Name = "ListFunction_AM", ResourceType = typeof(Resources.Resource__role))]
        public string ListFunction_AM { get; set; } = string.Empty;

        [Display(Name = "ListFunction_TTAN", ResourceType = typeof(Resources.Resource__role))]
        public string ListFunction_TTAN { get; set; } = string.Empty;

        [Display(Name = "ListFunction_REPORT", ResourceType = typeof(Resources.Resource__role))]
        public string ListFunction_REPORT { get; set; } = string.Empty;

        [Display(Name = "ListFunction_ACS", ResourceType = typeof(Resources.Resource__role))]
        public string ListFunction_ACS { get; set; } = string.Empty;

        [Display(Name = "ListFunction_VMS", ResourceType = typeof(Resources.Resource__role))]
        public string ListFunction_VMS { get; set; } = string.Empty;

        [Display(Name = "Type", ResourceType = typeof(Resources.Resource__role))]
        public int Type { get; set; }

        [Display(Name = "PhoneNumber", ResourceType = typeof(Resources.Resource__role))]
        public string PhoneNumber { get; set; }

        [Display(Name = "Type", ResourceType = typeof(Resources.Resource__role))]
        public SelectList ListType { get; set; }
    }
}