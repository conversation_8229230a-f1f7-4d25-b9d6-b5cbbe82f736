﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace PSafe.AM.Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "15.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class Resource__typeOfDevice {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resource__typeOfDevice() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("PSafe.AM.Resources.Resource _typeOfDevice", typeof(Resource__typeOfDevice).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ngày tạo.
        /// </summary>
        public static string CREATEDDATE {
            get {
                return ResourceManager.GetString("CREATEDDATE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Người tạo.
        /// </summary>
        public static string CREATEDUSER {
            get {
                return ResourceManager.GetString("CREATEDUSER", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mô tả.
        /// </summary>
        public static string DESCRIPTION {
            get {
                return ResourceManager.GetString("DESCRIPTION", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Icon.
        /// </summary>
        public static string ICON {
            get {
                return ResourceManager.GetString("ICON", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tên loại thiết bị.
        /// </summary>
        public static string TYPENAME {
            get {
                return ResourceManager.GetString("TYPENAME", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mã loại thiết bị.
        /// </summary>
        public static string TYPEOFDEVICEID {
            get {
                return ResourceManager.GetString("TYPEOFDEVICEID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ngày cập nhật.
        /// </summary>
        public static string UPDATEDDATE {
            get {
                return ResourceManager.GetString("UPDATEDDATE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Người cập nhật.
        /// </summary>
        public static string UPDATEDUSER {
            get {
                return ResourceManager.GetString("UPDATEDUSER", resourceCulture);
            }
        }
    }
}
