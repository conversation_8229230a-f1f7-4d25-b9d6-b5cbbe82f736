﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using PSafe.AM.Common;
using PSafe.AM.Models;
using PSafe.Common.UserEnums;
using PSafe.Core.Domains;
using PSafe.Core.Interface;
using static PSafe.Common.CommonEnums;

namespace PSafe.AM.Controllers
{
    [AuthorizeActionFilter(EPERMISSIONS_AM.categoryManage)]
    public class DepartmentsController : Controller
    {
        private readonly IDepartmentRepository _departmentRepository;
        private readonly IUserRepository _userRepository;
        private readonly IMapper _mapper;
        private readonly IHistorySystemRepository _historySystemRepository;
        private readonly IHttpContextAccessor _accessor;
        private readonly ILogger<DepartmentsController> _logger;

        public DepartmentsController(IDepartmentRepository departmentRepository, IMapper mapper, IUserRepository userRepository, IHistorySystemRepository historySystemRepository,
            IHttpContextAccessor accessor, ILogger<DepartmentsController> logger)
        {
            _departmentRepository = departmentRepository;
            _userRepository = userRepository;
            _mapper = mapper;
            _historySystemRepository = historySystemRepository;
            _accessor = accessor;
            _logger = logger;
        }

        public ActionResult Index()
        {
            StatusQuery Notification;
            List<DepartmentModel> _listDepartmentModel = new List<DepartmentModel>();
            try
            {
                var _listDepartment = _departmentRepository.GetAll().ToList();

                _listDepartmentModel = _mapper.Map<List<Department>, List<DepartmentModel>>(_listDepartment);

                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }
            }
            catch(Exception ex)
            {
                _logger.LogError("Department/Index: " + ex.Message);

                Notification = new StatusQuery("error", "", "Xem danh sách thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;
            }

            return View(_listDepartmentModel);
        }

        // GET: /supplier/Details/
        public ActionResult Details(int id)
        {
            try
            {
                var _department = _departmentRepository.GetById(id);

                var _departmentModel = _mapper.Map<Department, DepartmentModel>(_department);

                return View(_departmentModel);
            }
            catch(Exception ex)
            {
                _logger.LogError("Department/Details: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                return RedirectToAction("Index");
            }
        }

        // GET: /supplier/Create
        public ActionResult Create()
        {
            return View();
        }

        // POST: /supplier/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create([Bind("DepartmentName, Description")] DepartmentModel DepartmentModel)
        {
            StatusQuery Notification;
            if (ModelState.IsValid)
            {
                try
                {
                    var _systemUser = GetSesson();

                    if (_systemUser == null)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                        return RedirectToAction("Logout", "Security");
                    }

                    var _Department = _mapper.Map<DepartmentModel, Department>(DepartmentModel);

                    _departmentRepository.Insert(_Department);

                    var statusInsert = _departmentRepository.SaveChanges();

                    if (statusInsert > 0)
                    {
                        string StringDescription = new GetStringHistorySystem().Get(_systemUser.UserName, Resources.Resource.Create, _Department.DepartmentName.ToString(), Resources.Resource.Department);

                        InsertHistorySystem((int)EACTION_TYPE.CREATE, (int)EnumControllerName.SUPPLIERS, StringDescription, null, _Department);

                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Thêm mới thành công"));
                        return RedirectToAction("Index");
                    }
                    else
                    {
                        Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        return View(DepartmentModel);
                    }
                }
                catch(Exception ex)
                {
                    _logger.LogError("Department/Create: " + ex.Message);

                    Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return View(DepartmentModel);
                }
            }
            Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;

            return View(DepartmentModel);
        }

        // GET: /supplier/Edit/5
        public ActionResult Edit(int id)
        {
            try
            {
                var _supplier = _departmentRepository.GetById(id);

                var _supplierModel = _mapper.Map<Department, DepartmentModel>(_supplier);

                if (_supplierModel != null)
                {
                    return View(_supplierModel);
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                    return RedirectToAction("Index");
                }
            }
            catch(Exception ex)
            {
                _logger.LogError("Department/Edit: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                return RedirectToAction("Index");
            }
        }

        // POST: /supplier/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit([Bind("Id, DepartmentName, Description")] DepartmentModel DepartmentModel)
        {
            StatusQuery Notification;
            if (ModelState.IsValid)
            {
                try
                {
                    var _systemUser = GetSesson();

                    if (_systemUser == null)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                        return RedirectToAction("Logout", "Security");
                    }

                    var Department = _departmentRepository.GetById(DepartmentModel.Id);

                    var _DepartmentTemp = _mapper.Map<Department, DepartmentModel>(Department);
                    var DepartmentOld = _mapper.Map<DepartmentModel, Department>(_DepartmentTemp);

                    Department.DepartmentName = DepartmentModel.DepartmentName;
                    Department.Description = DepartmentModel.Description;

                    //var _supplier = _mapper.Map<SupplierModel, Supplier>(supplierModel);

                    _departmentRepository.Update(Department);

                    var updateStatus = _departmentRepository.SaveChanges();

                    if (updateStatus > 0)
                    {
                        string StringDescription = new GetStringHistorySystem().Get(_systemUser.UserName, Resources.Resource.Edit, Department.DepartmentName.ToString(), Resources.Resource.Department);

                        //chưa xong
                        InsertHistorySystem((int)EACTION_TYPE.EDIT, (int)EnumControllerName.SUPPLIERS, StringDescription, DepartmentOld, Department);

                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Sửa thành công"));

                        return RedirectToAction("Index");
                    }
                    else
                    {
                        Notification = new StatusQuery("error", "", "Sửa thất bại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        return View(Department);
                    }
                }
                catch(Exception ex)
                {
                    _logger.LogError("Department/Edit: " + ex.Message);

                    Notification = new StatusQuery("error", "", "Sửa thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return View(DepartmentModel);
                }
            }

            Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;

            return View(DepartmentModel);
        }

        // GET: /supplier/Delete/5
        public ActionResult Delete(int id)
        {
            StatusQuery Notification;
            DepartmentModel _departmentModel = new DepartmentModel();
            try
            {
                var Department = _departmentRepository.GetById(id);

                _departmentModel = _mapper.Map<Department, DepartmentModel>(Department);

                if (Department == null)
                {
                    Notification = new StatusQuery("warning", "", "Không tìm thấy phòng ban");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return RedirectToAction("Index");
                }
            }
            catch(Exception ex)
            {
                _logger.LogError("Department/Delete: " + ex.Message);

                Notification = new StatusQuery("warning", "", "Xóa thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;

                return RedirectToAction("Index");
            }
            return View(_departmentModel);
        }


        // POST: /Suppliers/Delete/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int id)
        {
            try
            {
                var systemUser = GetSesson();

                if (systemUser == null)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                    return RedirectToAction("Logout", "Security");
                }

                var Department = _departmentRepository.GetById(id);

                _departmentRepository.Delete(Department);

                var deleteStatus = _departmentRepository.SaveChanges();

                if (deleteStatus > 0)
                {

                    string StringDescription = new GetStringHistorySystem().Get(systemUser.UserName, Resources.Resource.Delete, Department.DepartmentName.ToString(), Resources.Resource.Suppliers);

                    InsertHistorySystem((int)EACTION_TYPE.DELETE, (int)EnumControllerName.SUPPLIERS, StringDescription, Department, null);

                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Xóa thành công"));
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));
                }

                return RedirectToAction("Index");
            }
            catch(Exception ex)
            {
                _logger.LogError("Department/DeleteConfirmed: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));
            }
            return RedirectToAction("Index");
        }

        public User GetSesson()
        {
            var SessionUserSystem = HttpContext.Session.GetComplexData<User>("SessionUserSystemId_" + HttpContext.Session.GetString("SessionUserSystemId"));
            if (SessionUserSystem != null)
            {
                var _user = _userRepository.GetById(SessionUserSystem.Id);

                if (_user != null)
                {
                    if (!_user.Actived)
                    {
                        HttpContext.Session.Clear();
                        return null;
                    }
                    return _user;
                }
            }
            return null;
        }

        private void InsertHistorySystem(int action_type, int controllerName, string description, object OldModel, object NewModel)
        {
            try
            {
                //IPHostEntry heserver = Dns.GetHostEntry(Dns.GetHostName());
                //var ipAddress = heserver.AddressList.FirstOrDefault(p => p.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork).ToString();

                var ipAddress = _accessor.HttpContext.Connection.RemoteIpAddress.ToString();

                string jsonOldObject = Newtonsoft.Json.JsonConvert.SerializeObject(OldModel);
                string jsonNewObject = Newtonsoft.Json.JsonConvert.SerializeObject(NewModel);

                HistorySystem history = new HistorySystem
                {
                    ActionType = action_type,
                    ActionTime = DateTime.Now,
                    Description = description,
                    OldObject = jsonOldObject,
                    NewObject = jsonNewObject,
                    UserId = int.Parse(HttpContext.Session.GetString("SessionUserSystemId")),
                    IpAddress = ipAddress,
                    ControllerName = controllerName
                };

                _historySystemRepository.Insert(history);

                _historySystemRepository.SaveChanges();
            }
            catch (Exception ex)
            {
                _logger.LogError("Department/InsertHistorySystem: " + ex.Message);
            }
        }
    }
}