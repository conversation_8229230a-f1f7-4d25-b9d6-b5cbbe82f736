﻿@model PSafe.AM.Models.ContactUnitModel
@{
    ViewBag.Title = "Hiệu chỉnh";
}

<environment names="Development">
    <script src="~/lib/jquery/dist/jquery.js"></script>
    <script src="~/lib/validate/jquery.validate.min.js"></script>
    <script src="~/js/script.js" asp-append-version="true"></script>
</environment>
<environment names="Staging,Production">
    <script src="~/lib/jquery/dist/jquery.js"></script>
    <script src="~/lib/validate/jquery.validate.min.js"></script>
    <script src="~/js/script.js" asp-append-version="true"></script>
</environment>

<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-lg-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>@PSafe.AM.Resources.Resource.Edit @PSafe.AM.Resources.Resource.ContactUnit</h5>
                    <div class="ibox-tools">
                        @Html.ActionLink(PSafe.AM.Resources.Resource.BackToList, "Index", "ContactUnits", null, new { @class = "btn btn-primary btn-xs" })
                    </div>
                </div>
                <div class="ibox-content">
                    @using (Html.BeginForm())
                    {
                        @Html.AntiForgeryToken()
                        @Html.HiddenFor(model => model.Id)

                    <div class="form-horizontal">
                        @Html.ValidationSummary(true)

                        <div class="form-group">
                            <label class="control-label col-md-2">@Html.LabelFor(model => model.Name) (*)</label>
                            <div class="col-md-10">
                                @Html.EditorFor(model => model.Name)
                                @Html.ValidationMessageFor(model => model.Name, null, new { @class = "text-danger" })
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label col-md-2">@Html.LabelFor(model => model.ContactPlaceIds) (*)</label>
                            <div class="col-md-10">
                                @Html.DropDownListFor(model => model.ContactPlaceIds, Model.ContactPlaces as SelectList, new { @class = "form-control chosen-select", multiple = true })
                                @Html.ValidationMessageFor(model => model.ContactPlaceIds, null, new { @class = "text-danger" })
                            </div>
                        </div>

                        <div class="form-group">
                            @Html.LabelFor(model => model.Description, new { @class = "control-label col-md-2" })
                            <div class="col-md-10">
                                @Html.TextAreaFor(model => model.Description, new { @class = "form-control" })
                                @Html.ValidationMessageFor(model => model.Description, null, new { @class = "text-danger" })
                            </div>
                        </div>

                        <div class="form-group">
                            @Html.LabelFor(model => model.Index, new { @class = "control-label col-md-2" })
                            <div class="col-md-10">
                                @Html.TextBoxFor(model => model.Index, new { @class = "form-control", @type = "number" })
                                @Html.ValidationMessageFor(model => model.Index, null, new { @class = "text-danger" })
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="col-md-offset-2 col-md-10">
                                <input type="submit" value="@PSafe.AM.Resources.Resource.Save" class="btn btn-primary" />
                                @Html.ActionLink(PSafe.AM.Resources.Resource.Cancel, "Index", "ContactUnits", null, new { @class = "btn btn-white" })
                            </div>
                        </div>
                    </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .chosen-container-multi .chosen-choices .search-field input[type="text"] {
        padding-left: 10px !important;
    }
</style>

@section Styles {
    <environment names="Development,Staging,Production">
        <link rel="stylesheet" href="~/lib/toastr/toastr.min.css" />
        <link rel="stylesheet" href="~/lib/chosen/bootstrap-chosen.css" />
    </environment>
}

@section Scripts {
    <environment names="Development,Staging,Production">
        <script src="~/lib/validate/jquery.validate.min.js"></script>
        <script src="~/lib/toastr/toastr.js"></script>
        <script src="~/lib/toastr/toastr.min.js"></script>
        <script src="~/lib/chosen/chosen.jquery.js"></script>
    </environment>

    @if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "warning")
    {
        <script type="text/javascript">
            toastr.warning('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "error")
    {
        <script type="text/javascript">
            toastr.error('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "success")
    {
        <script type="text/javascript">
            toastr.success('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }

    <script type="text/javascript">
        $(document).ready(function () {
            var selectedValues = [@string.Join(",", Model.ContactPlaceIds)];
            for (var key in selectedValues) {
                $("#ContactPlaceIds option[value='" + selectedValues[key] + "']").prop("selected", true);
            }
            $("#ContactPlaceIds").attr("data-placeholder", "Chọn nơi liên hệ / kho...").chosen();
        });
    </script>
}