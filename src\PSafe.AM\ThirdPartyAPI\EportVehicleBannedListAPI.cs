﻿using PSafe.Common.Enums;
using PSafe.Core.Domains;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ExternalServices.EPort2PSafeService;
using PSafe.AM.Models;
using PSafe.Infrastructure.Services.EPort;
using Microsoft.Extensions.Logging;

namespace PSafe.AM.ThirdPartyAPI
{
    public class EportVehicleBannedListAPI
    {
        public static void Banned(VehicleBannedList vehicleBannedList, BannedGroupUserInfo group, UserBannedInfo user, Dictionary<string, string> sites, IEPortSender ePortSender)
        {
            if (vehicleBannedList.ExpiryTime.Value < DateTime.Now) //nếu ngày hết hạn < ngày hệ thống => đã hết cấm => không thông báo
                return;

            var request = new ViolationRequest()
            {
                ActiveTime = vehicleBannedList.ActiveTime,
                ExpiryTime = vehicleBannedList.ExpiryTime.Value,
                CreatedDate = DateTime.Now,
                DepartmentId = group.GroupId.ToString(),
                DepartmentName = group.GroupName,
                Note = vehicleBannedList.Note,
                PhoneNo = group.PhoneNumber == null ? string.Empty : group.PhoneNumber,
                ReasonClear = string.Empty,
                ReasonViolation = vehicleBannedList.ReasonViolation,
                Sites = new Dictionary<string, string>(),
                Status = ((int)EBANNED_HISTORY_ACTION_TYPE.CAM).ToString(),
                StatusName = "Cấm vào ra",
                TruckNo = vehicleBannedList.TruckID,
                UpdatedDate = null,
                ViolationType = vehicleBannedList.ViolationType
            };

            request.Sites = new Dictionary<string, string>();
            foreach (var site in sites)
                request.Sites.Add(site.Key, site.Value);

            ePortSender.SentViolationInfoCust(user.SiteId, request);
        }

        public static void Clear(VehicleBannedListHistory history, BannedGroupUserInfo group, UserBannedInfo user, Dictionary<string, string> sites, IEPortSender ePortSender)
        {
            if (history.ExpiryTime.Value < DateTime.Now) //nếu ngày hết hạn < ngày hệ thống => đã hết cấm => không thông báo
                return;

            var request = new ViolationRequest()
            {
                ActiveTime = history.ActiveTime,
                ExpiryTime = history.ExpiryTime.Value,
                CreatedDate = DateTime.Now,
                DepartmentId = group.GroupId.ToString(),
                DepartmentName = group.GroupName,
                Note = string.Empty,
                PhoneNo = group.PhoneNumber == null ? string.Empty : group.PhoneNumber,
                ReasonClear = history.ReasonClear,
                ReasonViolation = history.ReasonViolation,
                Sites = new Dictionary<string, string>(),
                Status = ((int)EBANNED_HISTORY_ACTION_TYPE.HUY_CAM).ToString(),
                StatusName = "Hủy cấm vào ra",
                TruckNo = history.TruckID,
                UpdatedDate = DateTime.Now,
                ViolationType = history.ViolationType
            };

            request.Sites = new Dictionary<string, string>();
            foreach (var site in sites)
                request.Sites.Add(site.Key, site.Value);

            ePortSender.SentViolationInfoCust(user.SiteId, request);
        }

        public static void Update(VehicleBannedListHistory history, BannedGroupUserInfo group, UserBannedInfo user, Dictionary<string, string> sites, string oldExpiryTime, string note, IEPortSender ePortSender)
        {
            if (history.ExpiryTime.Value < DateTime.Now || oldExpiryTime == history.ExpiryTime.Value.ToString("dd/MM/yyyy HH:mm:ss") || oldExpiryTime == history.ExpiryTime.Value.ToString("dd/MM/yyyy HH:mm"))
                return;

            var request = new ViolationRequest()
            {
                ActiveTime = history.ActiveTime,
                ExpiryTime = history.ExpiryTime.Value,
                CreatedDate = history.CreatedDate,
                DepartmentId = group.GroupId.ToString(),
                DepartmentName = group.GroupName,
                Note = note,
                PhoneNo = group.PhoneNumber == null ? string.Empty : group.PhoneNumber,
                ReasonClear = history.ReasonClear,
                ReasonViolation = history.ReasonViolation,
                Sites = new Dictionary<string, string>(),
                Status = ((int)EBANNED_HISTORY_ACTION_TYPE.UPDATED).ToString(),
                StatusName = "Cập nhật cấm vào ra",
                TruckNo = history.TruckID,
                UpdatedDate = null,
                ViolationType = history.ViolationType
            };

            request.Sites = new Dictionary<string, string>();
            foreach (var site in sites)
                request.Sites.Add(site.Key, site.Value);

            ePortSender.SentViolationInfoCust(user.SiteId, request);
        }

        public static void Delete(VehicleBannedListHistory history, BannedGroupUserInfo group, UserBannedInfo user, Dictionary<string, string> sites, IEPortSender ePortSender)
        {
            if (history.ExpiryTime.Value < DateTime.Now) //nếu ngày hết hạn < ngày hệ thống => đã hết cấm => không thông báo
                return;

            var request = new ViolationRequest()
            {
                ActiveTime = history.ActiveTime,
                ExpiryTime = history.ExpiryTime.Value,
                CreatedDate = DateTime.Now,
                DepartmentId = group.GroupId.ToString(),
                DepartmentName = group.GroupName,
                Note = string.Empty,
                PhoneNo = group.PhoneNumber == null ? string.Empty : group.PhoneNumber,
                ReasonClear = "Xóa khỏi danh sách xe cấm",
                ReasonViolation = history.ReasonViolation,
                Sites = new Dictionary<string, string>(),
                Status = ((int)EBANNED_HISTORY_ACTION_TYPE.HUY_CAM).ToString(),
                StatusName = "Hủy cấm vào ra",
                TruckNo = history.TruckID,
                UpdatedDate = DateTime.Now,
                ViolationType = history.ViolationType
            };

            request.Sites = new Dictionary<string, string>();
            foreach (var site in sites)
                request.Sites.Add(site.Key, site.Value);

            ePortSender.SentViolationInfoCust(user.SiteId, request);
        }
    }
}
