﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace PSafe.AM.Models
{
    public class BannedGroupUserInfo
    {
        public int GroupId { get; set; }
        public string GroupName { get; set; }
        public string PhoneNumber { get; set; }
        public List<int> UserIds { get; set; }
        public string[] Functions { get; set; }

        public BannedGroupUserInfo(int groupId, string groupName, string phoneNumber, string[] functions)
        {
            GroupId = groupId;
            GroupName = groupName;
            PhoneNumber = phoneNumber;
            Functions = functions;

            UserIds = new List<int>();
        }
    }
}
