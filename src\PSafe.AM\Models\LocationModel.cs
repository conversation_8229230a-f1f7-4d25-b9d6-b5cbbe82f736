﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.Rendering;
using PSafe.Core.Domains;

namespace PSafe.AM.Models
{
    public class LocationModel
    {
        [Key]
        [Display(Name = "LocationId", ResourceType = typeof(Resources.Resource__location))]
        public int LocationId { get; set; }

        [Range(0, 2147483647, ErrorMessage = "Vui lòng chọn khu vực")]
        [Display(Name = "AreaId", ResourceType = typeof(Resources.Resource__location))]
        public int? AreaId { get; set; }

        [Display(Name = "AreaId", ResourceType = typeof(Resources.Resource__location))]
        public SelectList ListAreas { get; set; }

        [Required(ErrorMessage = "Vui lòng nhập tên vị trí")]
        [StringLength(255, MinimumLength = 1, ErrorMessage = "Đ<PERSON> dài từ 1-255 ký tự")]
        [Display(Name = "LocationName", ResourceType = typeof(Resources.Resource__location))]
        public string LocationName { get; set; } = string.Empty;

        [DisplayFormat(DataFormatString = "{0:00.000000000}", ApplyFormatInEditMode = true)]
        [Range(-2147483645.999999999999999, 2147483646.999999999999999, ErrorMessage = "Giá trị không hợp lệ")]
        [Display(Name = "LATITUDE", ResourceType = typeof(Resources.Resource__location))]
        public decimal LATITUDE { get; set; }

        [Range(-2147483645.999999999999999, 2147483646.999999999999999, ErrorMessage = "Giá trị không hợp lệ")]
        [DisplayFormat(DataFormatString = "{0:000.000000000}", ApplyFormatInEditMode = true)]
        [Display(Name = "LONGITUDE", ResourceType = typeof(Resources.Resource__location))]
        public decimal LONGITUDE { get; set; }

        [Required(ErrorMessage = "Vui lòng nhập địa chỉ")]
        [Display(Name = "Address", ResourceType = typeof(Resources.Resource__location))]
        public string Address { get; set; } = string.Empty;

        [Display(Name = "Map", ResourceType = typeof(Resources.Resource__location))]
        public string Map { get; set; } = string.Empty;

        public IFormFile ImageFile { get; set; }

        [Display(Name = "Description", ResourceType = typeof(Resources.Resource__location))]
        public string Description { get; set; } = string.Empty;

        [Display(Name = "Actived", ResourceType = typeof(Resources.Resource__location))]
        public bool Actived { get; set; }

        [DataType(DataType.Date)]
        [DisplayFormat(DataFormatString = "{0:MM-dd-yyyy}", ApplyFormatInEditMode = true)]
        [Display(Name = "CreatedDate", ResourceType = typeof(Resources.Resource__location))]
        public DateTime CreatedDate { get; set; }

        [Display(Name = "CreatedBy", ResourceType = typeof(Resources.Resource__location))]
        public int CreatedBy { get; set; }

        [DataType(DataType.Date)]
        [DisplayFormat(DataFormatString = "{0:MM-dd-yyyy}", ApplyFormatInEditMode = true)]
        [Display(Name = "UpdatedDate", ResourceType = typeof(Resources.Resource__location))]
        public DateTime? UpdatedDate { get; set; }

        [Display(Name = "UpdatedBy", ResourceType = typeof(Resources.Resource__location))]
        public int? UpdatedBy { get; set; }

        [Display(Name = "Document", ResourceType = typeof(Resources.Resource))]
        public int? Document { get; set; }

        public string AreaName { get; set; } = string.Empty;

        public List<PSafe.Core.Domains.Document> ListDocumentOnLocation { get; set; }

        public List<Document> ListDocumentNotOnLocation { get; set; }
    }
}