﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using PSafe.AM.Common;
using PSafe.AM.Models;
using PSafe.Common;
using PSafe.Common.UserEnums;
using PSafe.Core.Domains;
using PSafe.Core.Interface;

namespace PSafe.AM.Controllers
{
    [AuthorizeActionFilter(EPERMISSIONS_AM.categoryManage)]
    public class ContactPlacesController : Controller
    {
        private readonly IContactPlaceRepository _contactPlaceRepository;
        private readonly IContactUnitRepository _contactUnitRepository;
        private readonly IContactUnitPlaceRepository _contactUnitPlaceRepository;
        private readonly IUserRepository _userRepository;
        private readonly IMapper _mapper;
        private readonly IHttpContextAccessor _accessor;
        private readonly ILogger<ContactPlacesController> _logger;

        public ContactPlacesController(IContactPlaceRepository contactPlaceRepository,
            IContactUnitPlaceRepository contactUnitPlaceRepository,
            IContactUnitRepository contactUnitRepository, IMapper mapper, IUserRepository userRepository,
            IHttpContextAccessor accessor, ILogger<ContactPlacesController> logger)
        {
            _contactPlaceRepository = contactPlaceRepository;
            _contactUnitRepository = contactUnitRepository;
            _contactUnitPlaceRepository = contactUnitPlaceRepository;
            _userRepository = userRepository;
            _mapper = mapper;
            _accessor = accessor;
            _logger = logger;
        }

        public ActionResult Index()
        {
            StatusQuery Notification;
            List<ContactPlaceModel> listContactPlaceModel = new List<ContactPlaceModel>();
            try
            {
                var listContactPlace = _contactPlaceRepository.GetAll().OrderBy(x => x.Index).ToList();

                listContactPlaceModel = _mapper.Map<List<ContactPlace>, List<ContactPlaceModel>>(listContactPlace);

                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("ContactPlace/Index: " + ex.Message);

                Notification = new StatusQuery("error", "", "Xem danh sách thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;
            }

            return View(listContactPlaceModel);
        }

        // GET: /supplier/Details/
        public ActionResult Details(int id)
        {
            try
            {
                var contactPlace = _contactPlaceRepository.GetById(id);

                var contactPlaceModel = _mapper.Map<ContactPlace, ContactPlaceModel>(contactPlace);

                return View(contactPlaceModel);
            }
            catch (Exception ex)
            {
                _logger.LogError("ContactPlace/Details: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                return RedirectToAction("Index");
            }
        }

        // GET: /supplier/Create
        public ActionResult Create()
        {
            return View(new ContactPlaceModel());
        }

        private bool IsContactPlaceNameExist(string contactPlaceName, int? contactPlaceId = null)
        {
            contactPlaceName = Utils.ToUnsignedLowerText(contactPlaceName);
            var query = _contactPlaceRepository.GetAll().ToList();
            var _contactPlace = query.Where(x =>
                Utils.ToUnsignedLowerText(x.Name) == contactPlaceName
            ).FirstOrDefault();

            if (_contactPlace == null)
                return false;

            if (contactPlaceId.HasValue && _contactPlace.Id == contactPlaceId.Value)
                return false;

            return true;
        }

        // POST: /supplier/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create([Bind("Name, Description")] ContactPlaceModel contactPlaceModel)
        {
            StatusQuery Notification;
            if (ModelState.IsValid)
            {
                try
                {
                    var _systemUser = GetSesson();

                    if (_systemUser == null)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                        return RedirectToAction("Logout", "Security");
                    }

                    if(!IsContactPlaceNameExist(contactPlaceModel.Name))
                    {
                        var contactPlace = _mapper.Map<ContactPlaceModel, ContactPlace>(contactPlaceModel);
                        var allContacts = _contactPlaceRepository.GetAll();
                        if (allContacts.Count() == 0)
                            contactPlace.Id = 1;
                        else
                            contactPlace.Id = allContacts.Max(x => x.Id) + 1;
                        contactPlace.CreatedBy = _systemUser.Id;
                        contactPlace.UpdatedBy = _systemUser.Id;
                        contactPlace.CreatedDate = DateTime.Now;
                        contactPlace.UpdatedDate = DateTime.Now;

                        _contactPlaceRepository.Insert(contactPlace);

                        var statusInsert = _contactPlaceRepository.SaveChanges();

                        if (statusInsert > 0)
                        {
                            TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Thêm mới thành công"));
                            return RedirectToAction("Index");
                        }
                        else
                        {
                            Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                            ViewBag.Status = Notification.Status;
                            ViewBag.Value = Notification.Value;
                            ViewBag.Type = Notification.Type;

                            return View(contactPlaceModel);
                        }
                    } 
                    else
                    {
                        Notification = new StatusQuery("error", "", "Tên nơi liên hệ đã tồn tại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        return View(contactPlaceModel);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("ContactPlace/Create: " + ex.Message);

                    Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return View(contactPlaceModel);
                }
            }
            Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;

            return View(contactPlaceModel);
        }

        // GET: /supplier/Edit/5
        public ActionResult Edit(int id)
        {
            try
            {
                var contactPlace = _contactPlaceRepository.GetById(id);

                var contactPlaceModel = _mapper.Map<ContactPlace, ContactPlaceModel>(contactPlace);

                if (contactPlaceModel != null)
                {
                    return View(contactPlaceModel);
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                    return RedirectToAction("Index");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("ContactPlace/Edit: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                return RedirectToAction("Index");
            }
        }

        // POST: /supplier/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit(ContactPlaceModel contactPlaceModel)
        {
            StatusQuery Notification;
            if (ModelState.IsValid)
            {
                try
                {
                    var _systemUser = GetSesson();

                    if (_systemUser == null)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                        return RedirectToAction("Logout", "Security");
                    }

                    if (!IsContactPlaceNameExist(contactPlaceModel.Name, contactPlaceModel.Id))
                    {
                        var contactPlace = _contactPlaceRepository.GetById(contactPlaceModel.Id);

                        contactPlace.Name = contactPlaceModel.Name;
                        contactPlace.Description = contactPlaceModel.Description;
                        contactPlace.UpdatedBy = _systemUser.Id;
                        contactPlace.UpdatedDate = DateTime.Now;
                        contactPlace.Index = contactPlaceModel.Index;

                        //var _supplier = _mapper.Map<SupplierModel, Supplier>(supplierModel);

                        _contactPlaceRepository.Update(contactPlace);

                        var updateStatus = _contactPlaceRepository.SaveChanges();

                        if (updateStatus > 0)
                        {
                            TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Sửa thành công"));

                            return RedirectToAction("Index");
                        }
                        else
                        {
                            Notification = new StatusQuery("error", "", "Sửa thất bại");
                            ViewBag.Status = Notification.Status;
                            ViewBag.Value = Notification.Value;
                            ViewBag.Type = Notification.Type;

                            return View(contactPlaceModel);
                        }
                    }
                    else
                    {
                        Notification = new StatusQuery("error", "", "Tên nơi liên hệ đã tồn tại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        return View(contactPlaceModel);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("ContactPlace/Edit: " + ex.Message);

                    Notification = new StatusQuery("error", "", "Sửa thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return View(contactPlaceModel);
                }
            }

            Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;

            return View(contactPlaceModel);
        }

        // GET: /supplier/Delete/5
        public ActionResult Delete(int id)
        {
            StatusQuery Notification;
            ContactPlaceModel contactPlaceModel = new ContactPlaceModel();
            try
            {
                var contactPlace = _contactPlaceRepository.GetById(id);

                contactPlaceModel = _mapper.Map<ContactPlace, ContactPlaceModel>(contactPlace);

                if (contactPlace == null)
                {
                    Notification = new StatusQuery("warning", "", "Không tìm thấy nơi liên hệ");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return RedirectToAction("Index");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("ContactPlace/Delete: " + ex.Message);

                Notification = new StatusQuery("warning", "", "Xóa thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;

                return RedirectToAction("Index");
            }
            return View(contactPlaceModel);
        }

        private bool HasContactUnit(int contactPlaceId)
        {
            var contactUnit = _contactUnitPlaceRepository.GetBy(x => x.ContactPlaceId == contactPlaceId).FirstOrDefault();
            return contactUnit != null;
        }

        // POST: /Suppliers/Delete/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int id)
        {
            try
            {
                var systemUser = GetSesson();

                if (systemUser == null)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                    return RedirectToAction("Logout", "Security");
                }

                if(HasContactUnit(id))
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Nơi liên hệ này đã có đơn vị liên hệ. Vui lòng xóa đơn vị liên hệ trước khi xóa nơi liên hệ"));
                    return RedirectToAction("Index");
                }
                

                var contactPlace = _contactPlaceRepository.GetById(id);

                _contactPlaceRepository.Delete(contactPlace);

                var deleteStatus = _contactPlaceRepository.SaveChanges();

                if (deleteStatus > 0)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Xóa thành công"));
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));
                }

                return RedirectToAction("Index");
            }
            catch (Exception ex)
            {
                _logger.LogError("ContactPlace/DeleteConfirmed: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));
            }
            return RedirectToAction("Index");
        }

        public User GetSesson()
        {
            var sessionUser = HttpContext.Session.GetComplexData<User>("SessionUserSystemId_" + HttpContext.Session.GetString("SessionUserSystemId"));
            if (sessionUser != null)
            {
                var _user = _userRepository.GetById(sessionUser.Id);

                if (_user != null)
                {
                    if (!_user.Actived)
                    {
                        HttpContext.Session.Clear();
                        return null;
                    }
                    return _user;
                }
            }
            return null;
        }
    }
}
