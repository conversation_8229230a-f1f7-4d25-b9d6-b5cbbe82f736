﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Security.AccessControl;
using System.Threading.Tasks;

namespace PSafe.AM.Models
{
    public class BlackVehicleListModel
    {
        [Key]
        [Display(Name = "Id", ResourceType = typeof(Resources.Resource_BlackVehicleList))]
        public int Id { get; set; }

        [Display(Name = "FullName", ResourceType = typeof(Resources.Resource_BlackVehicleList))]
        public string FullName { get; set; }

        [Required(ErrorMessage = "Vui lòng nhập biển số xe")]
        [Display(Name = "VehicleNumber", ResourceType = typeof(Resources.Resource_BlackVehicleList))]
        public string VehicleNumber { get; set; }

        [Display(Name = "Description", ResourceType = typeof(Resources.Resource_BlackVehicleList))]
        public string Description { get; set; }

        [Display(Name = "Active", ResourceType = typeof(Resources.Resource_BlackVehicleList))]
        public bool Active { get; set; }
    }
}
