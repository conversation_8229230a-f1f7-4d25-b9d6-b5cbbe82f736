﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace PSafe.AM.Models
{
    public class SearchHistory
    {
        [DataType(DataType.Date)]
        [DisplayFormat(DataFormatString = "{0:MM-dd-yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        [Display(Name = "ActionTime", ResourceType = typeof(Resources.Resource_historySystem))]
        public DateTime ActionTime { get; set; }

        [Display(Name = "UserId", ResourceType = typeof(Resources.Resource_historySystem))]
        public int UserId { get; set; }
    }
}
