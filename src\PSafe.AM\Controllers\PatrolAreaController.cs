﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using PSafe.AM.Common;
using PSafe.AM.Models;
using PSafe.Core.Domains;
using PSafe.Core.Interface;
using Microsoft.AspNetCore.Http;
using static PSafe.Common.CommonEnums;
using PSafe.Common.UserEnums;
using Microsoft.Extensions.Logging;

namespace PSafe.AM.Controllers
{
    [AuthorizeActionFilter(EPERMISSIONS_AM.categoryManage)]
    public class PatrolAreaController : Controller
    {
        private readonly IPatrolAreaRepository _patrolAreaRepository;
        private readonly IUserRepository _userRepository;
        private readonly IHistorySystemRepository _historySystemRepository;
        private readonly ILogger<PatrolAreaController> _logger;
        private readonly IHttpContextAccessor _accessor;

        public PatrolAreaController(IPatrolAreaRepository patrolAreaRepository, IUserRepository userRepository, IHistorySystemRepository historySystemRepository,
            ILogger<PatrolAreaController> logger, IHttpContextAccessor accessor)
        {
            _patrolAreaRepository = patrolAreaRepository;
            _userRepository = userRepository;
            _historySystemRepository = historySystemRepository;
            _logger = logger;
            _accessor = accessor;
        }

        public IActionResult Index()
        {
            StatusQuery Notification;
            List<PatrolAreaModel> _listPatrolAreaModel = new List<PatrolAreaModel>();
            try
            {
                var _listPatrolArea = _patrolAreaRepository.GetAll().ToList();
                foreach (var patrolArea in _listPatrolArea)
                {
                    PatrolAreaModel patrolAreaModel = new PatrolAreaModel();
                    patrolAreaModel.Id = patrolArea.Id;
                    CopyTo(patrolAreaModel, patrolArea);
                    _listPatrolAreaModel.Add(patrolAreaModel);
                }

                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }
            }
            catch(Exception ex)
            {
                _logger.LogError("PatrolArea/Index: " + ex.Message);

                Notification = new StatusQuery("error", "", "Xem danh sách thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;
            }

            return View(_listPatrolAreaModel);
        }

        public ActionResult Details(int id)
        {
            try
            {
                var _patrolArea = _patrolAreaRepository.GetById(id);

                if (_patrolArea != null)
                {
                    var _patrolAreaModel = new PatrolAreaModel();
                    _patrolAreaModel.Id = id;
                    CopyTo(_patrolAreaModel, _patrolArea);
                    try
                    {
                        if (TempDataHelper.Get<StatusQuery>(TempData, "Notification") is StatusQuery Notification)
                        {
                            ViewBag.Status = Notification.Status;
                            ViewBag.Value = Notification.Value;
                            ViewBag.Type = Notification.Type;
                        }

                    }
                    catch (Exception ex)
                    {
                        _logger.LogError("PatrolArea/Details: " + ex.Message);
                        Console.WriteLine(ex.Message);
                    }

                    return View(_patrolAreaModel);
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                    return RedirectToAction("Index");
                }
            }
            catch(Exception ex)
            {
                _logger.LogError("PatrolArea/Details: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                return RedirectToAction("Index");
            }
        }

        public ActionResult Create()
        {
            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create(PatrolAreaModel patrolAreaModel)
        {
            StatusQuery Notification;
            if (ModelState.IsValid)
            {
                try
                {
                    var systemUser = GetSesson();
                    if (systemUser == null)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                        return RedirectToAction("Logout", "Security");
                    }

                    PatrolArea _patrolArea = new PatrolArea();
                    CopyTo(_patrolArea, patrolAreaModel);

                    _patrolAreaRepository.Insert(_patrolArea);

                    var statusInsert = _patrolAreaRepository.SaveChanges();

                    if (statusInsert > 0)
                    {
                        string StringDescription = new GetStringHistorySystem().Get(systemUser.UserName, Resources.Resource.Edit, _patrolArea.Name.ToString(), Resources.Resource.PatrolArea);

                        InsertHistorySystem((int)EACTION_TYPE.CREATE, (int)EnumControllerName.PATROL_AREA, StringDescription, null, _patrolArea);

                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Thêm mới thành công"));
                        return RedirectToAction("Index");
                    }
                    else
                    {
                        Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        return View(patrolAreaModel);
                    }
                }
                catch(Exception ex)
                {
                    _logger.LogError("PatrolArea/Create: " + ex.Message);

                    Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return View(patrolAreaModel);
                }
            }

            Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;

            return View(patrolAreaModel);
        }

        public ActionResult Edit(int id)
        {
            try
            {
                var _patrolArea = _patrolAreaRepository.GetById(id);
                if (_patrolArea != null)
                {
                    PatrolAreaModel patrolAreaModel = new PatrolAreaModel();
                    CopyTo(patrolAreaModel, _patrolArea);
                    return View(patrolAreaModel);
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                    return RedirectToAction("Index");
                }
            }
            catch(Exception ex)
            {
                _logger.LogError("PatrolArea/Edit: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                return RedirectToAction("Index");
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit(PatrolAreaModel patrolAreaModel)
        {
            StatusQuery Notification;
            if (ModelState.IsValid)
            {
                try
                {
                    var systemUser = GetSesson();
                    if (systemUser == null)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                        return RedirectToAction("Logout", "Security");
                    }

                    var _patrolArea = _patrolAreaRepository.GetById(patrolAreaModel.Id);
                    if (_patrolArea == null)
                    {
                        Notification = new StatusQuery("error", "", "Không tìm thấy khu vực cần sửa");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        return View(patrolAreaModel);
                    }

                    CopyTo(_patrolArea, patrolAreaModel);

                    _patrolAreaRepository.Update(_patrolArea);

                    var updateStatus = _patrolAreaRepository.SaveChanges();

                    if (updateStatus > 0)
                    {
                        string StringDescription = new GetStringHistorySystem().Get(systemUser.UserName, Resources.Resource.Edit, patrolAreaModel.Name.ToString(), Resources.Resource.PatrolArea);
                        InsertHistorySystem((int)EACTION_TYPE.EDIT, (int)EnumControllerName.PATROL_AREA, StringDescription, patrolAreaModel, _patrolArea);

                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Sửa thành công"));

                        return RedirectToAction("Index");
                    }
                    else
                    {
                        Notification = new StatusQuery("error", "", "Sửa thất bại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        return View(patrolAreaModel);
                    }
                }
                catch(Exception ex)
                {
                    _logger.LogError("PatrolArea/Edit: " + ex.Message);

                    Notification = new StatusQuery("error", "", "Sửa thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return View(patrolAreaModel);
                }
            }

            Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;

            return View(patrolAreaModel);
        }

        public ActionResult Delete(int id)
        {
            StatusQuery Notification;
            try
            {
                var _patrolArea = _patrolAreaRepository.GetById(id);

                if (_patrolArea == null)
                {
                    Notification = new StatusQuery("warning", "", "Không tìm thấy khu vực cần xóa");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return RedirectToAction("Index");
                }

                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }

                PatrolAreaModel patrolAreaModel = new PatrolAreaModel();
                CopyTo(patrolAreaModel, _patrolArea);
                return View(patrolAreaModel);
            }
            catch(Exception ex)
            {
                _logger.LogError("PatrolArea/Delete: " + ex.Message);

                Notification = new StatusQuery("warning", "", "Xóa thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;

                return RedirectToAction("Index");
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int id)
        {
            try
            {
                var systemUserSession = GetSesson();

                if (systemUserSession == null)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                    return RedirectToAction("Logout", "Security");
                }

                var patrolArea = _patrolAreaRepository.GetById(id);
                _patrolAreaRepository.Delete(patrolArea);

                var deleteStatus = _patrolAreaRepository.SaveChanges();

                if (deleteStatus > 0)
                {
                    string StringDescription = new GetStringHistorySystem().Get(systemUserSession.UserName, Resources.Resource.Delete, patrolArea.Name.ToString(), Resources.Resource.PatrolArea);

                    InsertHistorySystem((int)EACTION_TYPE.DELETE, (int)EnumControllerName.PATROL_AREA, StringDescription, patrolArea, null);

                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Xóa thành công"));
                    return RedirectToAction("Index");
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));
                    return RedirectToAction("Delete", "PatrolArea", new { id });
                }
            }
            catch(Exception ex)
            {
                _logger.LogError("PatrolArea/DeleteConfirmed: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));
                return RedirectToAction("Delete", "PatrolArea", new { id });
            }
        }

        private void CopyTo(PatrolArea dest, PatrolAreaModel src)
        {
            dest.Name = src.Name;
            dest.Description = src.Description;
        }

        private void CopyTo(PatrolAreaModel dest, PatrolArea src)
        {
            dest.Name = src.Name;
            dest.Description = src.Description;
        }

        public User GetSesson()
        {
            var SessionUserSystem = HttpContext.Session.GetComplexData<User>("SessionUserSystemId_" + HttpContext.Session.GetString("SessionUserSystemId"));
            if (SessionUserSystem != null)
            {
                var _user = _userRepository.GetById(SessionUserSystem.Id);

                if (_user != null)
                {
                    if (!_user.Actived)
                    {
                        HttpContext.Session.Clear();
                        return null;
                    }
                    return _user;
                }
            }
            return null;
        }

        private void InsertHistorySystem(int action_type, int controllerName, string description, object OldModel, object NewModel)
        {
            try
            {
                var ipAddress = _accessor.HttpContext.Connection.RemoteIpAddress.ToString();

                string jsonOldObject = Newtonsoft.Json.JsonConvert.SerializeObject(OldModel);
                string jsonNewObject = Newtonsoft.Json.JsonConvert.SerializeObject(NewModel);

                HistorySystem history = new HistorySystem
                {
                    ActionType = action_type,
                    ActionTime = DateTime.Now,
                    Description = description,
                    OldObject = jsonOldObject,
                    NewObject = jsonNewObject,
                    UserId = int.Parse(HttpContext.Session.GetString("SessionUserSystemId")),
                    IpAddress = ipAddress,
                    ControllerName = controllerName
                };

                _historySystemRepository.Insert(history);

                _historySystemRepository.SaveChanges();
            }
            catch (Exception ex)
            {
                _logger.LogError("PatrolArea/InsertHistorySystem: " + ex.Message);
            }
        }
    }
}