﻿using PSafe.Core.Domains;
using System;
using System.Collections.Generic;

namespace PSafe.AM.Models
{
    public class UserBannedInfo
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string SiteName { get; set; }
        public string SiteId { get; set; }

        public UserBannedInfo(User user, List<Site> sites)
        {
            Name = string.IsNullOrEmpty(user.FullName) ? user.UserName : user.FullName;
            Id = user.Id;
            SiteId = user.SiteId;
            if (SiteId != null)
            {
                var site = sites.Find(x => x.SiteId == user.SiteId);
                SiteName = site == null ? SiteId : site.SiteName;
            }
            else
            {
                SiteId = "CTL";
                SiteName = "Cảng Cát Lái";
            }
        }
    }
}
