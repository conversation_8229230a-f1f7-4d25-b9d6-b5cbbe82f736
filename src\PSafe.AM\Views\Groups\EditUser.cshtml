﻿@model PSafe.AM.Models.SystemUserViewModel

@{
    ViewBag.Title = "Hiệu chỉnh nhóm";
}

<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-lg-12">
            <div class="ibox">
                <div class="ibox-title">
                    <h5>@PSafe.AM.Resources.Resource.Edit @PSafe.AM.Resources.Resource.EditUser</h5>
                    <div class="ibox-tools">
                        @Html.ActionLink(@PSafe.AM.Resources.Resource.BackToList, "Index", "Groups", null, new { @class = "btn btn-primary btn-xs" })
                    </div>
                </div>
                <div class="ibox-content">
                    @using (Html.BeginForm("EditUser", "Groups", FormMethod.Post))
                    {
                        @Html.AntiForgeryToken()
                        @Html.HiddenFor(model => model.GroupId, new { @id = "groupId" })

                        <select class="form-control dual_select" name="dual_select" multiple>

                            @foreach (var item in Model.UserInGroup)
                            {
                                <option selected="selected" value="@item.Id">@Html.DisplayFor(modelItem => item.UserName)</option>
                            }

                            @foreach (var item in Model.UserNotInGroup)
                            {
                                <option value="@item.Id">@Html.DisplayFor(modelItem => item.UserName)</option>
                            }
                        </select>
                        <br>
                        <button type="submit" class="btn btn-primary btn-block">@PSafe.AM.Resources.Resource.Save</button>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <environment names="Development,Staging,Production">
        <link rel="stylesheet" href="~/lib/toastr/toastr.min.css" />
        <link rel="stylesheet" href="~/lib/dualListbox/bootstrap-duallistbox.min.css" />
        <link rel="stylesheet" href="~/lib/dualListbox/bootstrap-duallistbox.min.css" />
    </environment>
}

@section Scripts {
    <environment names="Development,Staging,Production">
        <script src="~/lib/validate/jquery.validate.min.js"></script>
        <script src="~/lib/toastr/toastr.js"></script>
        <script src="~/lib/toastr/toastr.min.js"></script>
        <script src="~/lib/duallistbox/jquery.bootstrap-duallistbox.js"></script>
        <script src="~/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js"></script>
        <script src="~/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js"></script>
        <script src="~/lib/dualListbox/jquery.bootstrap-duallistbox.js"></script>
    </environment>

    @if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "warning")
    {
        <script type="text/javascript">
            toastr.warning('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "error")
    {
        <script type="text/javascript">
            toastr.error('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "success")
    {
        <script type="text/javascript">
            toastr.success('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }

    <script type="text/javascript">
        $(document).ready(function () {
            $('.dual_select').bootstrapDualListbox({
                nonselectedlistlabel: 'Non-selected',
                selectedlistlabel: 'Selected',
                preserveselectiononmove: 'moved',
                moveonselect: false,
                selectorMinimalHeight: 200,
                filterTextClear: "Lấy tất cả",
                infoText: "Danh sách có {0}",
                infoTextEmpty: "Danh sách rỗng",
                filterOnValues: '',
            });
        });
        function OnSuccess(data) {
            alert(data);
        }
    </script>
}