﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace PSafe.AM.Resources.Resource_Enums {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class Resource_C3_Report {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resource_C3_Report() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("PSafe.AM.Resources.Resource_Enums.Resource_C3_Report", typeof(Resource_C3_Report).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cảnh báo từ camera.
        /// </summary>
        public static string AlarmFromDetect {
            get {
                return ResourceManager.GetString("AlarmFromDetect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thống kê cảnh báo.
        /// </summary>
        public static string AlarmStatistics {
            get {
                return ResourceManager.GetString("AlarmStatistics", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lịch sử sự kiện.
        /// </summary>
        public static string EventHistory {
            get {
                return ResourceManager.GetString("EventHistory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dữ liệu vào ra.
        /// </summary>
        public static string InOutData {
            get {
                return ResourceManager.GetString("InOutData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thống kê lượt vào, ra.
        /// </summary>
        public static string InOutStatistics {
            get {
                return ResourceManager.GetString("InOutStatistics", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thống kê tổng hợp.
        /// </summary>
        public static string IntegratedStatistics {
            get {
                return ResourceManager.GetString("IntegratedStatistics", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Khách hàng đăng ký.
        /// </summary>
        public static string RegisterUser {
            get {
                return ResourceManager.GetString("RegisterUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Biểu đồ thống kê.
        /// </summary>
        public static string StatisticsChart {
            get {
                return ResourceManager.GetString("StatisticsChart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lịch sử thao tác người dùng.
        /// </summary>
        public static string UserLog {
            get {
                return ResourceManager.GetString("UserLog", resourceCulture);
            }
        }
    }
}
