﻿using System.Collections.Generic;
using System.Linq;
using PSafe.AM.Models;
using PSafe.Core.Domains;
using PSafe.Core.Interface;
using System.Transactions;
using Microsoft.AspNetCore.Mvc;
using AutoMapper;
using PSafe.AM.Common;
using System;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.Rendering;
using PSafe.Common.UserEnums;
using Microsoft.Extensions.Logging;

namespace PSafe.AM.Controllers
{
    [AuthorizeActionFilter(EPERMISSIONS_AM.UserManage)]
    public class RoleCBCSsController : Controller
    {
        private readonly IRoleCBCSRepository _roleCBCSRepository;
        private readonly IUserInRoleCBCSRepository _userInRoleCBCSRepository;
        private readonly IUserCBCSRepository _userCBCSRepository;
        private readonly IUserRepository _userRepository;
        private readonly IMapper _mapper;
        private readonly IHistorySystemRepository _historySystemRepository;
        private readonly IDeviceRepository _deviceRepository;
        private readonly IHttpContextAccessor _accessor;
        private readonly ILogger<RoleCBCSsController> _logger;

        public RoleCBCSsController(
            IUserInRoleCBCSRepository userInRoleRepository,
            IRoleCBCSRepository roleCBCSRepository,
            IUserCBCSRepository userCBCSRepository,
            IUserRepository userRepository,
            IMapper mapper, 
            IDeviceRepository deviceRepository,
            IHistorySystemRepository historySystemRepository,
            IHttpContextAccessor accessor, 
            ILogger<RoleCBCSsController> logger)
        {
            _roleCBCSRepository = roleCBCSRepository;
            _userInRoleCBCSRepository = userInRoleRepository;
            _userCBCSRepository = userCBCSRepository;
            _userRepository = userRepository;
            _mapper = mapper;
            _historySystemRepository = historySystemRepository;
            _accessor = accessor;
            _logger = logger;
            _deviceRepository = deviceRepository;
        }

        public ActionResult Index()
        {
            StatusQuery Notification;
            List<RoleCBCSModel> _listRoleCBCSModel = new List<RoleCBCSModel>();
            try
            {
                var _listRole = _roleCBCSRepository.GetAll().ToList();

                _listRoleCBCSModel = _mapper.Map<List<RoleCBCS>, List<RoleCBCSModel>>(_listRole);

                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("RoleCBCS/Index: " + ex.Message);

                Notification = new StatusQuery("error", "", "Xem danh sách thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;
            }

            return View(_listRoleCBCSModel);
        }

        public ActionResult Details(int id)
        {
            try
            {
                var _role = _roleCBCSRepository.GetById(id);

                if (TempDataHelper.Get<StatusQuery>(TempData, "Notification") is StatusQuery Notification)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }

                var _RoleCBCSModel = _mapper.Map<RoleCBCS, RoleCBCSModel>(_role);

                if (_RoleCBCSModel != null)
                {
                    return View(_RoleCBCSModel);
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                    return RedirectToAction("Index");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("RoleCBCS/Details: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                return RedirectToAction("Index");
            }
        }

        public ActionResult EditUser(int id)
        {
            StatusQuery Notification;
            try
            {
                UserCBCSViewModel view = new UserCBCSViewModel();
                List<UserCBCS> listViewUserInRole = new List<UserCBCS>();
                List<UserCBCS> listViewUserNotInRole = new List<UserCBCS>();
                List<UserCBCS> listUserNotInRole = new List<UserCBCS>();

                var listUserInRole = _userInRoleCBCSRepository.GetBy(r => r.RoleId == id).ToList();
                var RoleName = _roleCBCSRepository.GetById(id).RoleName;
                var listSystemUser = _userCBCSRepository.GetAll();

                ViewBag.Rolename = RoleName;

                var check = true;
                foreach (var itemSystemUser in listSystemUser)
                {
                    check = true;
                    foreach (var itemUserInRol in listUserInRole)
                    {
                        if (itemSystemUser.Id == itemUserInRol.UserId)
                        {
                            check = false;
                            break;
                        }
                    }

                    if (check)
                    {
                        listUserNotInRole.Add(itemSystemUser);
                    }
                }

                foreach (var item in listUserInRole)
                {
                    UserCBCS user = _userCBCSRepository.GetById(item.UserId);
                    listViewUserInRole.Add(user);
                }

                foreach (var item in listUserNotInRole)
                {
                    UserCBCS user = _userCBCSRepository.GetById(item.Id);
                    listViewUserNotInRole.Add(user);
                }

                view.UserInRole = listViewUserInRole;
                view.UserNotInRole = listViewUserNotInRole;

                view.RoleId = id;

                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }

                return View(view);
            }
            catch (Exception ex)
            {
                _logger.LogError("RoleCBCS/EditUser: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Hiệu chỉnh người dùng thất bại"));

                return RedirectToAction("Index");
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult EditUser(int RoleId, List<int> dual_select)
        {
            using (TransactionScope scope = new TransactionScope())
            {
                try
                {
                    var systemUser = GetSesson();

                    if (systemUser == null)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                        return RedirectToAction("Logout", "Security");
                    }

                    List<UserInRoleCBCSModel> listUserInRoleNew = new List<UserInRoleCBCSModel>();
                    var listUserInRole = _userInRoleCBCSRepository.GetBy(r => r.RoleId == RoleId).ToList();

                    foreach (var item in listUserInRole)
                    {
                        UserInRoleCBCSModel userInRoleCBCSModel = new UserInRoleCBCSModel
                        {
                            UserId = item.UserId,
                            RoleId = item.RoleId
                        };

                        _userInRoleCBCSRepository.Delete(item);
                    }

                    if (dual_select != null)
                    {
                        foreach (var item in dual_select)
                        {
                            UserInRoleCBCS userInRole = new UserInRoleCBCS
                            {
                                UserId = item,
                                RoleId = RoleId
                            };

                            _userInRoleCBCSRepository.Insert(userInRole);
                        }
                    }
                    var StatusUserInRole = _userInRoleCBCSRepository.SaveChanges();

                    if (StatusUserInRole > 0)
                        scope.Complete();
                }
                catch (Exception ex)
                {
                    _logger.LogError("RoleCBCS/EditUser: " + ex.Message);

                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Sửa thất bại"));
                    return RedirectToAction("EditUser", "RoleCBCSs", new { RoleId });
                }
            }

            TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Sửa thành công"));
            return RedirectToAction("EditUser", "RoleCBCSs", new { RoleId });
        }

        public ActionResult Create()
        {
            return View(new RoleCBCSModel());
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create([Bind("RoleId, RoleName, RoleDescription, Type, PhoneNumber")] RoleCBCSModel RoleCBCSModel)
        {
            StatusQuery Notification;
            if (ModelState.IsValid)
            {
                try
                {
                    var _role = _mapper.Map<RoleCBCSModel, RoleCBCS>(RoleCBCSModel);

                    _roleCBCSRepository.Insert(_role);

                    var statusInsert = _roleCBCSRepository.SaveChanges();

                    if (statusInsert > 0)
                    {
                        var systemUser = GetSesson();

                        if (systemUser == null)
                        {
                            TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                            return RedirectToAction("Logout", "Security");
                        }

                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Thêm mới thành công"));
                        return RedirectToAction("Index");
                    }
                    else
                    {
                        Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        return View(RoleCBCSModel);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("RoleCBCS/Create: " + ex.Message);

                    Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return View(RoleCBCSModel);
                }
            }

            Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;
            return View(RoleCBCSModel);
        }

        public ActionResult Edit(int id)
        {
            try
            {
                var _role = _roleCBCSRepository.GetById(id);
                if (_role == null)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));
                    return RedirectToAction("Index");
                }

                var _RoleCBCSModel = _mapper.Map<RoleCBCS, RoleCBCSModel>(_role);

                return View(_RoleCBCSModel);
            }
            catch (Exception ex)
            {
                _logger.LogError("RoleCBCS/Edit: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                return RedirectToAction("Index");
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit([Bind("RoleId,RoleName,RoleDescription,Type, PhoneNumber")] RoleCBCSModel RoleCBCSModel)
        {
            StatusQuery Notification;
            ListTypeOfSingnal listTypeOfSingnal = new ListTypeOfSingnal();
            if (ModelState.IsValid)
            {
                try
                {
                    var role = _roleCBCSRepository.GetById(RoleCBCSModel.RoleId);

                    var _roleTemp = _mapper.Map<RoleCBCS, RoleCBCSModel>(role);

                    role.RoleName = RoleCBCSModel.RoleName;
                    role.RoleDescription = RoleCBCSModel.RoleDescription;

                    _roleCBCSRepository.Update(role);

                    var updateStatus = _roleCBCSRepository.SaveChanges();

                    if (updateStatus > 0)
                    {
                        var systemUser = GetSesson();

                        if (systemUser == null)
                        {
                            TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                            return RedirectToAction("Logout", "Security");
                        }

                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Sửa thành công"));

                        return RedirectToAction("Index");
                    }
                    else
                    {
                        Notification = new StatusQuery("error", "", "Sửa thất bại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        return View(RoleCBCSModel);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("RoleCBCS/Edit: " + ex.Message);

                    Notification = new StatusQuery("error", "", "Sửa thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return View(RoleCBCSModel);
                }

            }

            Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;

            return View(RoleCBCSModel);
        }

        public ActionResult Delete(int id)
        {
            StatusQuery Notification;

            try
            {
                var _role = _roleCBCSRepository.GetById(id);

                var _RoleCBCSModel = _mapper.Map<RoleCBCS, RoleCBCSModel>(_role);

                if (_RoleCBCSModel == null)
                {
                    Notification = new StatusQuery("warning", "", "Không tìm thấy nhóm");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return RedirectToAction("Index");
                }

                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }

                return View(_RoleCBCSModel);
            }
            catch (Exception ex)
            {
                _logger.LogError("RoleCBCS/Delete: " + ex.Message);

                Notification = new StatusQuery("warning", "", "Xóa thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;

                return RedirectToAction("Index");
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int id)
        {
            try
            {
                var systemUser = GetSesson();

                if (systemUser == null)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                    return RedirectToAction("Logout", "Security");
                }

                var role = _roleCBCSRepository.GetById(id);

                _roleCBCSRepository.Delete(role);

                var deleteStatus = _roleCBCSRepository.SaveChanges();

                if (deleteStatus > 0)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Xóa thành công"));
                    return RedirectToAction("Index");
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));
                    return RedirectToAction("Delete", "RoleCBCSs", new { id });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("RoleCBCS/DeleteConfirmed: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));
                return RedirectToAction("Delete", "RoleCBCSs", new { id });
            }
        }

        [NonAction]
        private SelectList ToSelectList(List<DropDownList> DropDownList)
        {
            List<SelectListItem> list = new List<SelectListItem>();

            foreach (var item in DropDownList)
            {
                list.Add(new SelectListItem()
                {
                    Text = item.Name,
                    Value = item.Id.ToString()
                });
            }

            return new SelectList(list, "Value", "Text");
        }
        public User GetSesson()
        {
            var SessionUserSystem = HttpContext.Session.GetComplexData<User>("SessionUserSystemId_" + HttpContext.Session.GetString("SessionUserSystemId"));
            if (SessionUserSystem != null)
            {
                var _user = _userRepository.GetById(SessionUserSystem.Id);

                if (_user != null)
                {
                    if (!_user.Actived)
                    {
                        HttpContext.Session.Clear();
                        return null;
                    }
                    return _user;
                }
            }
            return null;
        }

        private void InsertHistorySystem(int action_type, int controllerName, string description, object OldModel, object NewModel)
        {
            try
            {
                var ipAddress = _accessor.HttpContext.Connection.RemoteIpAddress.ToString();

                string jsonOldObject = Newtonsoft.Json.JsonConvert.SerializeObject(OldModel);
                string jsonNewObject = Newtonsoft.Json.JsonConvert.SerializeObject(NewModel);

                HistorySystem history = new HistorySystem
                {
                    ActionType = action_type,
                    ActionTime = DateTime.Now,
                    Description = description,
                    OldObject = jsonOldObject,
                    NewObject = jsonNewObject,
                    UserId = int.Parse(HttpContext.Session.GetString("SessionUserSystemId")),
                    IpAddress = ipAddress,
                    ControllerName = controllerName
                };

                _historySystemRepository.Insert(history);

                _historySystemRepository.SaveChanges();
            }
            catch (Exception ex)
            {
                _logger.LogError("RoleCBCS/InsertHistorySystem: " + ex.Message);
            }
        }
    }
}
