﻿using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace PSafe.AM.Models
{
    public class UserScheduleModel
    {
        [Key]
        [Display(Name = "Id", ResourceType = typeof(Resources.Resource_userSchedule))]
        public int Id { get; set; }

        [Required(ErrorMessage = "Giá trị không được trống!")]
        [Range(1, int.MaxValue, ErrorMessage = "Vui lòng chọn nhân viên trực")]
        [Display(Name = "UserId", ResourceType = typeof(Resources.Resource_userSchedule))]
        public int UserId { get; set; }

        [Required(ErrorMessage = "Giá trị không được trống!")]
        [Range(1, int.MaxValue, ErrorMessage = "Vui lòng chọn khu vực trực")]
        [Display(Name = "PatrolAreaID", ResourceType = typeof(Resources.Resource_userSchedule))]
        public int PatrolAreaID { get; set; }

        [DataType(DataType.Date)]
        [Required(ErrorMessage = "Giá trị không được trống!")]
        [DisplayFormat(DataFormatString = "{0:dd/MM/yyyy}", ApplyFormatInEditMode = true)]
        [Display(Name = "ScheduleDate", ResourceType = typeof(Resources.Resource_userSchedule))]
        public DateTime ScheduleDate { get; set; }

        [Required(ErrorMessage = "Giá trị không được trống!")]
        [StringLength(50, MinimumLength = 1, ErrorMessage = "Độ dài tối đa 50 ký tự!")]
        [Display(Name = "ListScheduleId", ResourceType = typeof(Resources.Resource_userSchedule))]
        public string ListScheduleId { get; set; } = string.Empty;

        [Display(Name = "UserId", ResourceType = typeof(Resources.Resource_userSchedule))]
        public string UserName { get; set; }

        [Display(Name = "PatrolAreaID", ResourceType = typeof(Resources.Resource_userSchedule))]
        public string PatrolArea { get; set; }

        [Display(Name = "ListScheduleId", ResourceType = typeof(Resources.Resource_userSchedule))]
        public string ScheduleTime { get; set; }
    }
}
