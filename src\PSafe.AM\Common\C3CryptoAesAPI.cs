﻿using System;
using System.IO;
using System.Security.Cryptography;

namespace PSafe.AM.Common
{
    public class C3CryptoAesAPI
    {
        public class C3CryptoAes
        {
            private static byte[] secretKey256 =    {
                                                        (byte)0x13, (byte)0x23, (byte)0xdf, (byte)0xad,
                                                        (byte)0x09, (byte)0x1f, (byte)0x1c, (byte)0x5e,
                                                        (byte)0x60, (byte)0x1e, (byte)0x9f, (byte)0x4c,
                                                        (byte)0xff, (byte)0xfd, (byte)0xdd, (byte)0x45,
                                                        (byte)0x1a, (byte)0xd3, (byte)0x40, (byte)0xfe,
                                                        (byte)0x11, (byte)0x42, (byte)0xaf, (byte)0xfc,
                                                        (byte)0xac, (byte)0x45, (byte)0x41, (byte)0xf0,
                                                        (byte)0xde, (byte)0x35, (byte)0x3f, (byte)0xdd
                                                    };

            private static byte[] iv = new byte[] { 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00 };

            /*
             * Return the encrypted byte array if encrypt successfully
             * Otherwise return null
             */
            public static byte[] EncryptStringToBytes(String plainText, byte[] secretKey, byte[] IV)
            {
                try
                {
                    // Check arguments. 
                    if (plainText == null || plainText.Length <= 0)
                        throw new ArgumentNullException("plainText");
                    if (secretKey == null || secretKey.Length <= 0)
                        throw new ArgumentNullException("secretKey");
                    if (IV == null || IV.Length <= 0)
                        throw new ArgumentNullException("secretKey");

                    byte[] encrypted;

                    // Create an AesCryptoServiceProvider object 
                    // with the specified key and IV. 
                    using (AesCryptoServiceProvider aesAlg = new AesCryptoServiceProvider())
                    {
                        aesAlg.Key = secretKey;
                        aesAlg.IV = IV;

                        // Create a decrytor to perform the stream transform.
                        ICryptoTransform encryptor = aesAlg.CreateEncryptor(aesAlg.Key, aesAlg.IV);

                        // Create the streams used for encryption. 
                        using (MemoryStream msEncrypt = new MemoryStream())
                        {
                            using (CryptoStream csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write))
                            {
                                using (StreamWriter swEncrypt = new StreamWriter(csEncrypt))
                                {
                                    swEncrypt.Write(plainText);
                                }
                                encrypted = msEncrypt.ToArray();
                            }
                        }
                    }
                    return encrypted;
                }
                catch
                {
                    return null;
                }
            }

            /*
             * Return the decrypted String if decrypt successfully
             * Otherwise return null
             */
            public static String DecryptStringFromBytes(byte[] cipherText, byte[] secretKey, byte[] IV)
            {
                try
                {
                    // Check arguments. 
                    if (cipherText == null || cipherText.Length <= 0)
                        throw new ArgumentNullException("cipherText");
                    if (secretKey == null || secretKey.Length <= 0)
                        throw new ArgumentNullException("secretKey");
                    if (IV == null || IV.Length <= 0)
                        throw new ArgumentNullException("IV");

                    // Declare the string used to hold 
                    // the decrypted text. 
                    String plaintext = null;

                    // Create an AesCryptoServiceProvider object 
                    // with the specified key and IV. 
                    using (AesCryptoServiceProvider aesAlg = new AesCryptoServiceProvider())
                    {
                        aesAlg.Key = secretKey;
                        aesAlg.IV = IV;

                        // Create a decrytor to perform the stream transform.
                        ICryptoTransform decryptor = aesAlg.CreateDecryptor(aesAlg.Key, aesAlg.IV);

                        // Create the streams used for decryption. 
                        using (MemoryStream msDecrypt = new MemoryStream(cipherText))
                        {
                            using (CryptoStream csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read))
                            {
                                using (StreamReader srDecrypt = new StreamReader(csDecrypt))
                                {
                                    plaintext = srDecrypt.ReadToEnd();
                                }
                            }
                        }
                    }
                    return plaintext;
                }
                catch
                {
                    return null;
                }
            }

            public static byte[] EncryptStringToBytes256(String plainText)
            {
                return EncryptStringToBytes(plainText, secretKey256, iv);
            }
            public static String DecryptStringFromBytes256(byte[] cipherText)
            {
                return DecryptStringFromBytes(cipherText, secretKey256, iv);
            }

            public static String EncryptStringToString256(String plainText)
            {
                byte[] encryptedTextAsBytes = EncryptStringToBytes(plainText, secretKey256, iv);
                if (encryptedTextAsBytes == null)
                    return null;
                return Base64EncoderToString(encryptedTextAsBytes);
            }
            public static String DecryptStringFromString256(String cipherText)
            {
                byte[] cipherTextAsBytes = Base64DecoderToBytes(cipherText);
                return DecryptStringFromBytes(cipherTextAsBytes, secretKey256, iv);
            }

            public static String Base64EncoderToString(byte[] data)
            {
                try
                {
                    return Convert.ToBase64String(data);
                }
                catch
                {
                    return null;
                }
            }

            public static byte[] Base64DecoderToBytes(String encryptedText)
            {
                try
                {
                    return Convert.FromBase64String(encryptedText);
                }
                catch
                {
                    return null;
                }
            }
        }
    }
}
