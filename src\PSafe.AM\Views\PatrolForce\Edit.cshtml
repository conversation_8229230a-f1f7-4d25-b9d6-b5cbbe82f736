﻿@model PSafe.AM.Models.PatrolForceModel
@{
    ViewBag.Title = "Hiệu chỉnh " + PSafe.AM.Resources.Resource.PatrolForce;
    var areaListItems = (List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem>)ViewBag.areaListItems;
    var locationListItems = new List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem>();

    var patrolType = new List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem>();
    patrolType.Add(new SelectListItem()
    {
        Text = "Theo khu vực",
        Value = ((int)PSafe.Common.PatrolEnums.EPATROL_TYPE.MainPatrol).ToString()
    });
    patrolType.Add(new SelectListItem()
    {
        Text = "Theo tuyến",
        Selected = true,
        Value = ((int)PSafe.Common.PatrolEnums.EPATROL_TYPE.RoutePatrol).ToString()
    });
}

<script language="javascript" type="text/javascript">
    function openChild(file, window) {
        childWindow = open(file, window, 'resizable=no,width=700,height=400,scrollbars,resizable,toolbar,status');
        if (childWindow.opener == null) childWindow.opener = self;
    }
</script>

<environment names="Development">
    <script src="~/lib/jquery/dist/jquery.js"></script>
    <script src="~/lib/validate/jquery.validate.min.js"></script>
    <script src="~/js/script.js" asp-append-version="true"></script>
</environment>
<environment names="Staging,Production">
    <script src="~/lib/jquery/dist/jquery.js"></script>
    <script src="~/lib/validate/jquery.validate.min.js"></script>
    <script src="~/js/script.js" asp-append-version="true"></script>
</environment>
<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-lg-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>@PSafe.AM.Resources.Resource.Edit @PSafe.AM.Resources.Resource.PatrolForce</h5>
                    <div class="ibox-tools">
                        @Html.ActionLink(PSafe.AM.Resources.Resource.BackToList, "Index", "PatrolForce", null, new { @class = "btn btn-primary btn-xs" })
                    </div>
                </div>
                <div class="ibox-content">
                    @using (Html.BeginForm("Edit", "PatrolForce", FormMethod.Post, new { id = "PatrolForceForm" }))
                    {
                        @Html.AntiForgeryToken()

                        <div class="form-horizontal">
                            @Html.ValidationSummary(true)

                            @Html.HiddenFor(model => model.Id)

                            <div class="row form-group">
                                @Html.LabelFor(model => model.Name, new { @class = "control-label col-md-2" })
                                <div class="col-md-4">
                                    @Html.EditorFor(model => model.Name)
                                    @Html.ValidationMessageFor(model => model.Name, null, new { @class = "text-danger" })
                                </div>
                            </div>

                            <div class="form-group row">
                                @Html.LabelFor(model => model.Directions, new { @class = "control-label col-md-2" })
                                <div class="col-md-8">
                                    @Html.TextBoxFor(model => model.Directions, new { @class = "form-control", @readonly = true })
                                </div>
                                <div class="col-md-2">
                                    <button class="btn btn-primary btn-xs" type="button" id="drawAreaBorderBtn">Vẽ bản đồ</button>
                                </div>
                            </div>

                            <div class="form-group">
                                <div class="col-md-offset-2 col-md-10">
                                    <input type="submit" value="@PSafe.AM.Resources.Resource.Save" class="btn btn-primary" />
                                    @Html.ActionLink(PSafe.AM.Resources.Resource.Cancel, "Index", "PatrolForce", null, new { @class = "btn btn-white" })
                                </div>
                            </div>

                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>
@Html.AntiForgeryToken()

@section Styles {
    <environment names="Development,Staging,Production">
        <link rel="stylesheet" href="~/lib/toastr/toastr.min.css" />
        <link rel="stylesheet" href="~/css/jquery-ui.min.css">
    </environment>

    <style>
        #sortable1, #sortable2 {
            list-style: none;
            border: 1px dotted #ccc;
            padding: 10px;
            min-height: 120px;
            background-color: #f0f1f3;
            max-height: 300px;
            overflow: auto
        }

        #sortable1 li, #sortable2 li {
            padding: 10px;
            margin-bottom: 5px;
            background-color: white;
        }

        #sortable2.empty {
            background-image: url("@Url.Content("~/images/drag_drop_location.jpg")");
            background-repeat: no-repeat;
            background-position: center;
            background-size: contain;
        }

        #sortable2.move {
            border: 1px solid blue;
        }
    </style>
}

@section Scripts {
    <environment names="Development,Staging,Production">
        <script src="~/lib/validate/jquery.validate.min.js"></script>
        <script src="~/lib/toastr/toastr.js"></script>
        <script src="~/lib/toastr/toastr.min.js"></script>
        <script src="~/js/jquery-ui.min.js"></script>
    </environment>

    @if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "warning")
    {
        <script type="text/javascript">
            toastr.warning('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "error")
    {
        <script type="text/javascript">
            toastr.error('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "success")
    {
        <script type="text/javascript">
            toastr.success('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }

    <script type="text/javascript">
        var controllerUrl = '@Url.Action("Index", "PatrolForce")';
        $("#drawAreaBorderBtn").on("click", function () {
            var areaBorderData = $("#Directions").val();
            openChild('/static/DrawDirections.htm?data=' + areaBorderData, 'win3');
        });

        $(document).ready(function () {
            $("#drawAreaBorderBtn").on("click", function () {
                var areaBorderData = $.trim($("#Directions").val());
                areaBorderData = areaBorderData.replace(/\[/g, "sdfqww");
                areaBorderData = areaBorderData.replace(/\]/g, "sdfqdd");

                var title = encodeURI($.trim($("#Name").val()));
                openChild('/static/DrawArea.htm?data=' + areaBorderData + "&title=" + title, 'win3');
            });
        });

        function updateAreaBorderData(data) {
            $("#Directions").val(data);
        }
    </script>
}