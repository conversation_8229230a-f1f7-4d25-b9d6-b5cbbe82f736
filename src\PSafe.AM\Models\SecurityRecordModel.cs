﻿using Microsoft.AspNetCore.Http;
using PSafe.Core.Domains;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace PSafe.AM.Models
{
    public class SecurityRecordModel
    {
        [Key]
        [Display(Name = "Id", ResourceType = typeof(Resources.Resource_securityRecord))]
        public int Id { get; set; }

        [Required(ErrorMessage = "Giá trị không được trống!")]
        [Display(Name = "HistoryEventId", ResourceType = typeof(Resources.Resource_securityRecord))]
        public int HistoryEventId { get; set; }

        [Required(ErrorMessage = "Gi<PERSON> trị không được trống!")]
        [StringLength(256, MinimumLength = 1, ErrorMessage = "Độ dài tối đa 256 ký tự!")]
        [Display(Name = "Name", ResourceType = typeof(Resources.Resource_securityRecord))]
        public string Name { get; set; } = string.Empty;

        [StringLength(2048)]
        [Display(Name = "ListDocument", ResourceType = typeof(Resources.Resource_securityRecord))]
        public string ListDocument { get; set; } = string.Empty;

        [StringLength(256, MinimumLength = 1, ErrorMessage = "Độ dài tối đa 256 ký tự!")]
        [Display(Name = "Description", ResourceType = typeof(Resources.Resource_securityRecord))]
        public string Description { get; set; } = string.Empty;

        public HistoryEvent HistoryEvent { get; set; }
        public int EventId { get; set; } = 0;
    }
}
