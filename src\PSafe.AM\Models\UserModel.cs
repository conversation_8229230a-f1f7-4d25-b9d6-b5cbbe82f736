﻿using Microsoft.AspNetCore.Mvc.Rendering;
using PSafe.AM.Common;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace PSafe.AM.Models
{
    public class UserModel
    {
        [Key]
        public int UserID { get; set; }

        [Display(Name = "CommandCenterID", ResourceType = typeof(Resources.Resource__user))]
        public int? CommandCenterID { get; set; }

        [Required(ErrorMessage = "Vui lòng nhập tên đăng nhập")]
        [StringLength(255, MinimumLength = 3, ErrorMessage = "Đ<PERSON> dài từ 3-255 ký tự")]
        [Display(Name = "UserName", ResourceType = typeof(Resources.Resource__user))]
        public string UserName { get; set; } = string.Empty;

        [DataType(DataType.Password)]
        [Display(Name = "Password", ResourceType = typeof(Resources.Resource__user))]
        public string Password { get; set; } = string.Empty;

        [DataType(DataType.PhoneNumber)]
        [Required(ErrorMessage = "<PERSON>ui lòng nhập số điện thoại")]
        [Display(Name = "Phone", ResourceType = typeof(Resources.Resource__user))]
        public string Phone { get; set; } = string.Empty;

        [RegularExpression("^([a-zA-Z0-9_\\-\\.]+)@((\\[[0-9]{1,3}" +
                  @"\.[0-9]{1,3}\.[0-9]{1,3}\.)|(([a-zA-Z0-9\-]+\" +
                  @".)+))([a-zA-Z]{2,4}|[0-9]{1,3})(\]?)$", ErrorMessage = "Địa chỉ Email không hợp lệ")]
        [Display(Name = "Email", ResourceType = typeof(Resources.Resource__user))]
        public string Email { get; set; } = string.Empty;

        [Display(Name = "LockReason", ResourceType = typeof(Resources.Resource__user))]
        public string LockReason { get; set; } = string.Empty;

        [Display(Name = "Actived", ResourceType = typeof(Resources.Resource__user))]
        public bool Actived { get; set; }

        [Display(Name = "Type", ResourceType = typeof(Resources.Resource__user))]
        public int Type { get; set; }

        [DataType(DataType.Date)]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd}", ApplyFormatInEditMode = true)]
        [Display(Name = "CreatedDate", ResourceType = typeof(Resources.Resource__user))]
        public DateTime CreatedDate { get; set; }

        [Display(Name = "CreatedUser", ResourceType = typeof(Resources.Resource__user))]
        public int CreatedUser { get; set; }

        [DataType(DataType.Date)]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd}", ApplyFormatInEditMode = true)]
        [Display(Name = "UpdatedDate", ResourceType = typeof(Resources.Resource__user))]
        public DateTime UpdatedDate { get; set; }

        [Display(Name = "UpdatedUser", ResourceType = typeof(Resources.Resource__user))]
        public int UpdatedUser { get; set; }

        [Display(Name = "DepartmentId", ResourceType = typeof(Resources.Resource__user))]
        public int? DepartmentId { get; set; }

        [Display(Name = "PositionId", ResourceType = typeof(Resources.Resource__user))]
        public int? PositionId { get; set; }

        [Display(Name = "FullName", ResourceType = typeof(Resources.Resource__user))]
        public string FullName { get; set; }

        [Display(Name = "ListRole", ResourceType = typeof(Resources.Resource__user))]
        public string ListRole { get; set; } = string.Empty;

        public SelectList ListDepartmentId { get; set; }

        public SelectList ListpositionId { get; set; }

        [Display(Name = "SiteID", ResourceType = typeof(Resources.Resource__user))]
        public string SiteID { get; set; }
        public SelectList ListSite { get; set; }
    }
}