﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace PSafe.AM.Models
{
    public class DocumentModel
    {
        [Key]
        [Display(Name = "Id", ResourceType = typeof(Resources.Resource_document))]
        public int Id { get; set; }

        [Required(ErrorMessage = "Vui lòng nhập tên tài liệu")]
        [StringLength(255, MinimumLength = 3, ErrorMessage = "Độ dài từ 3-255 ký tự")]
        [Display(Name = "DocumentName", ResourceType = typeof(Resources.Resource_document))]
        public string DocumentName { get; set; } = string.Empty;

        [Range(-1, 2147483647, ErrorMessage = "Vui lòng chọn loại tài liệu")]
        [Display(Name = "Type", ResourceType = typeof(Resources.Resource_document))]
        public int Type { get; set; }

        [Display(Name = "FileName", ResourceType = typeof(Resources.Resource_document))]
        public string FileName { get; set; } = string.Empty;

        [Display(Name = "Description", ResourceType = typeof(Resources.Resource_document))]
        public string Description { get; set; } = string.Empty;

        [DataType(DataType.Date)]
        [DisplayFormat(DataFormatString = "{0:MM-dd-yyyy}", ApplyFormatInEditMode = true)]
        [Display(Name = "CreatedDate", ResourceType = typeof(Resources.Resource__location))]
        public DateTime CreatedDate { get; set; }

        [Display(Name = "CreatedBy", ResourceType = typeof(Resources.Resource__location))]
        public int CreatedBy { get; set; }

        [DataType(DataType.Date)]
        [DisplayFormat(DataFormatString = "{0:MM-dd-yyyy}", ApplyFormatInEditMode = true)]
        [Display(Name = "UpdatedDate", ResourceType = typeof(Resources.Resource__location))]
        public DateTime? UpdatedDate { get; set; }

        [Display(Name = "UpdatedBy", ResourceType = typeof(Resources.Resource__location))]
        public int? UpdatedBy { get; set; }

        [Display(Name = "Map", ResourceType = typeof(Resources.Resource__location))]
        public IFormFile ImageFile { get; set; }

        public SelectList ListType { get; set; }
    }
}
