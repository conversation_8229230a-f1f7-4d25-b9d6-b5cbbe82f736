﻿@model PSafe.AM.Models.AreaModel
@{
    ViewBag.Title = "Tạo mới";
}

<environment names="Development">
    <script src="~/lib/jquery/dist/jquery.js"></script>
    <script src="~/lib/validate/jquery.validate.min.js"></script>
    <script src="~/js/script.js" asp-append-version="true"></script>
</environment>
<environment names="Staging,Production">
    <script src="~/lib/jquery/dist/jquery.js"></script>
    <script src="~/lib/validate/jquery.validate.min.js"></script>
    <script src="~/js/script.js" asp-append-version="true"></script>
</environment>

<script language="javascript" type="text/javascript">
    function openChild(file, window) {
        childWindow = open(file, window, 'resizable=no,width=700,height=400,scrollbars,resizable,toolbar,status');
        if (childWindow.opener == null) childWindow.opener = self;
    }
</script>

<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-lg-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>@PSafe.AM.Resources.Resource.AddItem @PSafe.AM.Resources.Resource.Area</h5>
                    <div class="ibox-tools">
                        @Html.ActionLink(PSafe.AM.Resources.Resource.BackToList, "Index", "Areas", null, new { @class = "btn btn-primary btn-xs" })
                    </div>
                </div>
                <div class="ibox-content">
                    @using (Html.BeginForm())
                    {
                        @Html.AntiForgeryToken()
                        @Html.ValidationSummary(true)

                    <div class="row">
                        <div class="col-lg-6">
                            <div class="form-group row">
                                <label class="control-label col-md-4">@Html.LabelFor(model => model.BRANCHNAME) (*)</label>
                                <div class="col-md-8">
                                    @Html.EditorFor(model => model.BRANCHNAME)
                                    @Html.ValidationMessageFor(model => model.BRANCHNAME, null, new { @class = "text-danger" })
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-6">
                            <div class="form-group row">
                                <label class="control-label col-md-4">@Html.LabelFor(model => model.LONGITUDE) (*)</label>
                                <div class="col-md-8">
                                    @Html.EditorFor(model => model.LONGITUDE)
                                    @Html.ValidationMessageFor(model => model.LONGITUDE, null, new { @class = "text-danger" })
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                @Html.LabelFor(model => model.REPRESENTATIVE, new { @class = "control-label col-md-4" })
                                <div class="col-md-8">
                                    @Html.EditorFor(model => model.REPRESENTATIVE)
                                    @Html.ValidationMessageFor(model => model.REPRESENTATIVE, null, new { @class = "text-danger" })
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                <label class="control-label col-md-4">@Html.LabelFor(model => model.LATITUDE) (*)</label>
                                <div class="col-md-5">
                                    @Html.EditorFor(model => model.LATITUDE)
                                    @Html.ValidationMessageFor(model => model.LATITUDE, null, new { @class = "text-danger" })
                                </div>
                                <div class="col-md-3">
                                    <input type="button" class="btn btn-primary btn-xs" btn- value="Lấy tọa độ" onClick="openChild('/static/GetLatlon.htm','win2')" />
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                @Html.LabelFor(model => model.CONTACTPERSON, new { @class = "control-label col-md-4" })
                                <div class="col-md-8">
                                    @Html.EditorFor(model => model.CONTACTPERSON)
                                    @Html.ValidationMessageFor(model => model.CONTACTPERSON, null, new { @class = "text-danger" })
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                @Html.LabelFor(model => model.CONTACTPHONE, new { @class = "control-label col-md-4" })
                                <div class="col-md-8">
                                    @Html.EditorFor(model => model.CONTACTPHONE)
                                    @Html.ValidationMessageFor(model => model.CONTACTPHONE, null, new { @class = "text-danger" })
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                @Html.LabelFor(model => model.EMAIL, new { @class = "control-label col-md-4" })
                                <div class="col-md-8">
                                    @Html.EditorFor(model => model.EMAIL)
                                    @Html.ValidationMessageFor(model => model.EMAIL, null, new { @class = "text-danger" })
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-6">
                            <div class="form-group row">
                                @Html.LabelFor(model => model.MESSAGEPHONE, new { @class = "control-label col-md-4" })
                                <div class="col-md-8">
                                    @Html.EditorFor(model => model.MESSAGEPHONE)
                                    @Html.ValidationMessageFor(model => model.MESSAGEPHONE, null, new { @class = "text-danger" })
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                @Html.LabelFor(model => model.FAX, new { @class = "control-label col-md-4" })
                                <div class="col-md-8">
                                    @Html.EditorFor(model => model.FAX)
                                    @Html.ValidationMessageFor(model => model.FAX, null, new { @class = "text-danger" })
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                @Html.LabelFor(model => model.DESCRIPTION, new { @class = "control-label col-md-4" })
                                <div class="col-md-8">
                                    @Html.TextAreaFor(model => model.DESCRIPTION, new { @class = "form-control" })
                                    @Html.ValidationMessageFor(model => model.DESCRIPTION, null, new { @class = "text-danger" })
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-12">
                            <div class="form-group row">
                                @Html.LabelFor(model => model.AREABORDER, new { @class = "control-label col-md-2" })
                                <div class="col-md-8">
                                    @Html.TextBoxFor(model => model.AREABORDER, new { @class = "form-control", @readonly = true })
                                </div>
                                <div class="col-md-2">
                                    <button class="btn btn-primary btn-xs" type="button" id="drawAreaBorderBtn">Vẽ bản đồ</button>
                                </div>

                            </div>
                        </div>

                    </div>
                        <div class="row">
                            <div class="col-md-offset-2 col-md-10">
                                <input type="submit" value="@PSafe.AM.Resources.Resource.Save" class="btn btn-primary" />
                                @Html.ActionLink(PSafe.AM.Resources.Resource.Cancel, "Index", "Areas", null, new { @class = "btn btn-white" })
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>


@section Styles {
    <environment names="Development,Staging,Production">
        <link rel="stylesheet" href="~/lib/toastr/toastr.min.css" />
    </environment>
}

@section Scripts {
    <environment names="Development,Staging,Production">
        <script src="~/lib/validate/jquery.validate.min.js"></script>
        <script src="~/lib/toastr/toastr.js"></script>
        <script src="~/lib/toastr/toastr.min.js"></script>
    </environment>

    @if (!string.IsNullOrEmpty(ViewBag.Type) && ViewBag.Type == "warning")
    {
        <script type="text/javascript">
            toastr.warning('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Status) && ViewBag.Type == "error")
    {
        <script type="text/javascript">
            toastr.error('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Status) && ViewBag.Type == "success")
    {
        <script type="text/javascript">
            toastr.success('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }

    <script>
    $(document).ready(function () {
        $("#drawAreaBorderBtn").on("click", function () {
            var lon = $.trim($("#LONGITUDE").val());
            lon = lon || "";
            var lat = $.trim($("#LATITUDE").val());
            lat = lat || "";

            var areaBorderData = $.trim($("#AREABORDER").val());
            areaBorderData = areaBorderData.replace(/\[/g, "sdfqww");
            areaBorderData = areaBorderData.replace(/\]/g, "sdfqdd");

            var title = encodeURI($.trim($("#BRANCHNAME").val()));
            openChild('/static/DrawArea.htm?lon=' + lon + "&lat=" + lat + "&data=" + areaBorderData + "&title=" + title, 'win3');
        });
    });

    function getMin(jsonData, index) {
        var minVal = 99999999999;
        for (var key in jsonData) {
            if (jsonData[key][index] < minVal) {
                minVal = jsonData[key][index];
            }
        }

        return minVal;
    }

    function getMax(jsonData, index) {
        var maxVal = 0;
        for (var key in jsonData) {
            if (jsonData[key][index] > maxVal) {
                maxVal = jsonData[key][index];
            }
        }

        return maxVal;
    }

    function updateAreaBorderData(data) {
        $("#AREABORDER").val(data);
    }


    </script>
}