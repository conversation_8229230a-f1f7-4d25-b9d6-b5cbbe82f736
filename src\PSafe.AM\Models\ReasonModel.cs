﻿using Microsoft.AspNetCore.Mvc.Rendering;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using static PSafe.Common.CommonEnums;

namespace PSafe.AM.Models
{
    public class ReasonModel
    {
        [Key]
        [Display(Name = "Id", ResourceType = typeof(Resources.Resource_reason))]
        public int Id { get; set; }

        [Required(ErrorMessage = "Vui lòng nhập tên lý do")]
        [StringLength(255, MinimumLength = 1, ErrorMessage = "Độ dài từ 1-255 ký tự")]
        [Display(Name = "Name", ResourceType = typeof(Resources.Resource_reason))]
        public string Name { get; set; } = string.Empty;

        [Required(ErrorMessage = "Vui lòng chọn loại lý do")]
        [EnumDataType(typeof(EREASON_TYPE), ErrorMessage = "Loại lý do không hợp lệ")]
        [Display(Name = "Type", ResourceType = typeof(Resources.Resource_reason))]
        public int Type { get; set; }

        [Display(Name = "CreatedBy", ResourceType = typeof(Resources.Resource_reason))]
        public int CreatedBy { get; set; }

        [Display(Name = "UpdatedBy", ResourceType = typeof(Resources.Resource_reason))]
        public int UpdatedBy { get; set; }

        [Display(Name = "CreatedDate", ResourceType = typeof(Resources.Resource_reason))]
        public DateTime CreatedDate { get; set; }

        [Display(Name = "UpdatedDate", ResourceType = typeof(Resources.Resource_reason))]
        public DateTime UpdatedDate { get; set; }

        [Display(Name = "Description", ResourceType = typeof(Resources.Resource_reason))]
        public string Description { get; set; } = string.Empty;

        public SelectList ListType { get; set; }
        public string TypeString { get; set; }
    }
}
