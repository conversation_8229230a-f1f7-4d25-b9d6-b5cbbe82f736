﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace PSafe.AM.Models
{
    public class AlarmConfigModel
    {
        private string _alarmLevel1Security = null;
        public string AlarmLevel1Security
        {
            get
            {
                if (_alarmLevel1Security == null)
                    return string.Empty;

                return _alarmLevel1Security;
            }
            set
            {
                _alarmLevel1Security = value;
            }
        }

        private string _alarmLevel2Security = null;
        public string AlarmLevel2Security
        {
            get
            {
                if (_alarmLevel2Security == null)
                    return string.Empty;

                return _alarmLevel2Security;
            }
            set
            {
                _alarmLevel2Security = value;
            }
        }

        private string _alarmLevel3Security = null;
        public string AlarmLevel3Security
        {
            get
            {
                if (_alarmLevel3Security == null)
                    return string.Empty;

                return _alarmLevel3Security;
            }
            set
            {
                _alarmLevel3Security = value;
            }
        }

        private string _alarmLevel4Security = null;
        public string AlarmLevel4Security
        {
            get
            {
                if (_alarmLevel4Security == null)
                    return string.Empty;

                return _alarmLevel4Security;
            }
            set
            {
                _alarmLevel4Security = value;
            }
        }

        private string _alarmLevel5Security = null;
        public string AlarmLevel5Security
        {
            get
            {
                if (_alarmLevel5Security == null)
                    return string.Empty;

                return _alarmLevel5Security;
            }
            set
            {
                _alarmLevel5Security = value;
            }
        }

        private string _alarmLevel1FireSafety = null;
        public string AlarmLevel1FireSafety
        {
            get
            {
                if (_alarmLevel1FireSafety == null)
                    return string.Empty;

                return _alarmLevel1FireSafety;
            }
            set
            {
                _alarmLevel1FireSafety = value;
            }
        }

        private string _alarmLevel2FireSafety = null;
        public string AlarmLevel2FireSafety
        {
            get
            {
                if (_alarmLevel2FireSafety == null)
                    return string.Empty;

                return _alarmLevel2FireSafety;
            }
            set
            {
                _alarmLevel2FireSafety = value;
            }
        }

        private string _alarmLevel3FireSafety = null;
        public string AlarmLevel3FireSafety
        {
            get
            {
                if (_alarmLevel3FireSafety == null)
                    return string.Empty;

                return _alarmLevel3FireSafety;
            }
            set
            {
                _alarmLevel3FireSafety = value;
            }
        }

        private string _alarmLevel4FireSafety = null;
        public string AlarmLevel4FireSafety
        {
            get
            {
                if (_alarmLevel4FireSafety == null)
                    return string.Empty;

                return _alarmLevel4FireSafety;
            }
            set
            {
                _alarmLevel4FireSafety = value;
            }
        }

        private string _alarmLevel5FireSafety = null;
        public string AlarmLevel5FireSafety
        {
            get
            {
                if (_alarmLevel5FireSafety == null)
                    return string.Empty;

                return _alarmLevel5FireSafety;
            }
            set
            {
                _alarmLevel5FireSafety = value;
            }
        }

        private string _alarmLevel1Connection = null;
        public string AlarmLevel1Connection
        {
            get
            {
                if (_alarmLevel1Connection == null)
                    return string.Empty;

                return _alarmLevel1Connection;
            }
            set
            {
                _alarmLevel1Connection = value;
            }
        }

        private string _alarmLevel2Connection = null;
        public string AlarmLevel2Connection
        {
            get
            {
                if (_alarmLevel2Connection == null)
                    return string.Empty;

                return _alarmLevel2Connection;
            }
            set
            {
                _alarmLevel2Connection = value;
            }
        }

        private string _alarmLevel3Connection = null;
        public string AlarmLevel3Connection
        {
            get
            {
                if (_alarmLevel3Connection == null)
                    return string.Empty;

                return _alarmLevel3Connection;
            }
            set
            {
                _alarmLevel3Connection = value;
            }
        }

        private string _alarmLevel4Connection = null;
        public string AlarmLevel4Connection
        {
            get
            {
                if (_alarmLevel4Connection == null)
                    return string.Empty;

                return _alarmLevel4Connection;
            }
            set
            {
                _alarmLevel4Connection = value;
            }
        }

        private string _alarmLevel5Connection = null;
        public string AlarmLevel5Connection
        {
            get
            {
                if (_alarmLevel5Connection == null)
                    return string.Empty;

                return _alarmLevel5Connection;
            }
            set
            {
                _alarmLevel5Connection = value;
            }
        }



        private string _alarmLevel1VehicleBanned = null;
        public string AlarmLevel1VehicleBanned
        {
            get
            {
                if (_alarmLevel1VehicleBanned == null)
                    return string.Empty;

                return _alarmLevel1VehicleBanned;
            }
            set
            {
                _alarmLevel1VehicleBanned = value;
            }
        }

        private string _alarmLevel1HumanBanned = null;
        public string AlarmLevel1HumanBanned
        {
            get
            {
                if (_alarmLevel1HumanBanned == null)
                    return string.Empty;

                return _alarmLevel1HumanBanned;
            }
            set
            {
                _alarmLevel1HumanBanned = value;
            }
        }

        private string _alarmLevel1PatrolCamera = null;
        public string AlarmLevel1PatrolCamera
        {
            get
            {
                if (_alarmLevel1PatrolCamera == null)
                    return string.Empty;

                return _alarmLevel1PatrolCamera;
            }
            set
            {
                _alarmLevel1PatrolCamera = value;
            }
        }
    }
}
