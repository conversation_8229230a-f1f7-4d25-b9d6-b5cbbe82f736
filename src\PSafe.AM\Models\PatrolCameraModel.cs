﻿
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace PSafe.AM.Models
{
    public class PatrolCameraModel
    {
        [Key]
        [Display(Name = "Id", ResourceType = typeof(Resources.Resource_patrolCamera))]
        public int Id { get; set; }

        [Required(ErrorMessage = "Gi<PERSON> trị không được trống!")]
        [StringLength(50, MinimumLength = 1, ErrorMessage = "Độ dài tối đa 50 ký tự!")]
        [Display(Name = "Name", ResourceType = typeof(Resources.Resource_patrolCamera))]
        public string Name { get; set; } = string.Empty;

        [Required(ErrorMessage = "Giá trị không được trống!")]
        [Display(Name = "ListCamera", ResourceType = typeof(Resources.Resource_patrolCamera))]
        public string ListCamera { get; set; } = string.Empty;

        [Range(0, 3, ErrorMessage = "<PERSON>ui lòng chọn loại tuần tra")]
        [Display(Name = "Type", ResourceType = typeof(Resources.Resource_patrolCamera))]
        public int Type { get; set; } = (int)PSafe.Common.PatrolEnums.EPATROL_TYPE.RoutePatrol;

        public string TypeText
        {
            get
            {
                switch(Type)
                {
                    case (int)PSafe.Common.PatrolEnums.EPATROL_TYPE.MainPatrol:
                        return "Theo khu vực";
                    case (int)PSafe.Common.PatrolEnums.EPATROL_TYPE.RoutePatrol:
                        return "Theo tuyến";
                }

                return "";
            }
        }

        [Display(Name = "AreaId", ResourceType = typeof(Resources.Resource_patrolCamera))]
        public int? AreaId { get; set; } = 0;

        [Display(Name = "LocationId", ResourceType = typeof(Resources.Resource_patrolCamera))]
        public int? LocationId { get; set; } = 0;

        public string Directions { get; set; }
        public List<PatrolCameraPresetModel> ListCameraSelected { get; set; }
        public List<PatrolCameraPresetModel> ListCameraNoSelected { get; set; }
    }
}
