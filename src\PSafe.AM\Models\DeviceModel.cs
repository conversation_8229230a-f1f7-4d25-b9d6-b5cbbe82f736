﻿using PSafe.AM.Common;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using Microsoft.AspNetCore.Mvc.Rendering;

namespace PSafe.AM.Models
{
    public class DeviceModel
    {
        [Key]
        [Display(Name = "DEVICEID", ResourceType = typeof(Resources.Resource__device))]
        public int DEVICEID { get; set; }

        [Range(0, 2147483647, ErrorMessage = "Vui lòng chọn loại thiết bị")]
        [Display(Name = "TYPEOFDEVICEID", ResourceType = typeof(Resources.Resource__device))]
        public int? TYPEOFDEVICEID { get; set; }

        [Display(Name = "LOCATIONID", ResourceType = typeof(Resources.Resource__device))]
        public int? LOCATIONID { get; set; }

        [Display(Name = "USERID", ResourceType = typeof(Resources.Resource__device))]
        public int USERID { get; set; }

        [Required(ErrorMessage = "<PERSON>ui lòng nhập tên thiết bị")]
        [StringLength(255, MinimumLength = 1, ErrorMessage = "Độ dài từ 1-255 ký tự")]
        [Display(Name = "DEVICENAME", ResourceType = typeof(Resources.Resource__device))]
        public string DEVICENAME { get; set; } = string.Empty;

        [Required(ErrorMessage = "Vui lòng nhập số serial")]
        [StringLength(255, MinimumLength = 1, ErrorMessage = "Độ dài từ 1-255 ký tự")]
        [Display(Name = "SERIALNUMBER", ResourceType = typeof(Resources.Resource__device))]
        public string SERIALNUMBER { get; set; } = string.Empty;

        [Display(Name = "ACTIVED", ResourceType = typeof(Resources.Resource__device))]
        public bool ACTIVED { get; set; }

        [Display(Name = "SERVER", ResourceType = typeof(Resources.Resource__device))]
        public string SERVER { get; set; } = string.Empty;

        [Display(Name = "PORT", ResourceType = typeof(Resources.Resource__device))]
        public int? PORT { get; set; }

        [Display(Name = "URL", ResourceType = typeof(Resources.Resource__device))]
        public string URL { get; set; } = string.Empty;

        [Display(Name = "USERNAME", ResourceType = typeof(Resources.Resource__device))]
        public string USERNAME { get; set; } = string.Empty;

        [Display(Name = "PASSWORD", ResourceType = typeof(Resources.Resource__device))]
        public string PASSWORD { get; set; } = string.Empty;

        [Display(Name = "DESCRIPTION", ResourceType = typeof(Resources.Resource__device))]
        public string DESCRIPTION { get; set; } = string.Empty;

        [DataType(DataType.Date)]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd}", ApplyFormatInEditMode = true)]
        [Display(Name = "LASTCONNECTTIME", ResourceType = typeof(Resources.Resource__device))]
        public DateTime? LASTCONNECTTIME { get; set; }

        [DataType(DataType.Date)]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd}", ApplyFormatInEditMode = true)]
        [Display(Name = "CREATEDDATE", ResourceType = typeof(Resources.Resource__device))]
        public DateTime CREATEDDATE { get; set; }

        [Display(Name = "CREATEDUSER", ResourceType = typeof(Resources.Resource__device))]
        public int CREATEDUSER { get; set; }

        [DataType(DataType.Date)]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd}", ApplyFormatInEditMode = true)]
        [Display(Name = "UPDATEDDATE", ResourceType = typeof(Resources.Resource__device))]
        public DateTime UPDATEDDATE { get; set; }

        [Display(Name = "UPDATEDUSER", ResourceType = typeof(Resources.Resource__device))]
        public int UPDATEDUSER { get; set; }

        [Display(Name = "SUPPLIERID", ResourceType = typeof(Resources.Resource__device))]
        public int? SUPPLIERID { get; set; }

        [Display(Name = "DEVICESTATUS", ResourceType = typeof(Resources.Resource__device))]
        public int? DEVICESTATUS { get; set; }

        [Display(Name = "ALLOWUSERREMOTEACCESS", ResourceType = typeof(Resources.Resource__device))]
        public bool ALLOWUSERREMOTEACCESS { get; set; }

        [Display(Name = "CONNECTSTATUS", ResourceType = typeof(Resources.Resource__device))]
        public int? CONNECTSTATUS { get; set; }

        [Display(Name = "IPLOCAL", ResourceType = typeof(Resources.Resource__device))]
        public string IPLOCAL { get; set; } = string.Empty;

        [Display(Name = "VIDEOLINK", ResourceType = typeof(Resources.Resource__device))]
        public string VIDEOLINK { get; set; } = string.Empty;

        [Range(0, 2147483647, ErrorMessage = "Số lượng kênh không được âm")]
        [Display(Name = "QUANTITYCHANNEL", ResourceType = typeof(Resources.Resource__device))]
        public int? QUANTITYCHANNEL { get; set; }

        [Display(Name = "RENEWDAY", ResourceType = typeof(Resources.Resource__device))]
        public DateTime? RENEWDAY { get; set; }

        [Display(Name = "EXPDAY", ResourceType = typeof(Resources.Resource__device))]
        public DateTime? EXPDAY { get; set; }

        [Display(Name = "AREAID", ResourceType = typeof(Resources.Resource__area))]
        public int? areas { get; set; }

        [Range(0, 2147483647, ErrorMessage = "Vui lòng chọn loại thiết bị")]
        [Display(Name = "TYPEOFSINGNAL", ResourceType = typeof(Resources.Resource__device))]
        public int TYPEOFSINGNAL { get; set; }

        [Range(0, 2147483647, ErrorMessage = "Vui lòng chọn camera quan sát")]
        [Display(Name = "CameraMonitorId", ResourceType = typeof(Resources.Resource__device))]
        public int CameraMonitorId { get; set; }

        [Display(Name = "MacAddress", ResourceType = typeof(Resources.Resource__device))]
        public string MacAddress { get; set; } = string.Empty;

        [Display(Name = "DriverName", ResourceType = typeof(Resources.Resource__device))]
        public string DriverName { get; set; } = string.Empty;

        [Display(Name = "TrafficCamera", ResourceType = typeof(Resources.Resource__device))]
        public bool TrafficCamera { get; set; } = false;

        public string AreaNameNonUnicode { get; set; } = string.Empty;

        public string LocationNameNonUnicode { get; set; }

        public Guid? MilestoneId { get; set; }

        public string Preset { get; set; }
        public SelectList ListPreset { get; set; }
        public List<PatrolCameraPresetModel> ListAllPreset { get; set; }

        [Display(Name = "IP", ResourceType = typeof(Resources.Resource__device))]
        public string Ip { get; set; }

        //[Required(ErrorMessage = "Vui lòng nhập Zone")]
        //[Range(0, 8, ErrorMessage = "Giá trị nhập vào chưa đúng, giá trị hợp lệ từ 1-8")]
        [Display(Name = "Zone", ResourceType = typeof(Resources.Resource__device))]
        public string Zone { get; set; }

        public string LocationName { get; set; } = string.Empty;

        public string AreaName { get; set; } = string.Empty;

        [Range(0, 2147483647, ErrorMessage = "Thông tin bắt buộc, Vui lòng chọn tài khoản quản lý.")]
        [Display(Name = "USERID", ResourceType = typeof(Resources.Resource__device))]
        public SelectList ListUsers { get; set; }

        [Display(Name = "LOCATIONID", ResourceType = typeof(Resources.Resource__device))]
        public SelectList ListLocations { get; set; }

        [Display(Name = "TYPEOFDEVICEID", ResourceType = typeof(Resources.Resource__device))]
        public SelectList ListTypeOfDevices { get; set; }

        public SelectList ListAreas { get; set; }

        public string CreateUserViewIndex { get; set; }

        public string DeviceStatusViewIndex { get; set; }

        [Display(Name = "SUPPLIERID", ResourceType = typeof(Resources.Resource__device))]
        public SelectList ListSuppliers { get; set; }

        [Display(Name = "DEVICESTATUS", ResourceType = typeof(Resources.Resource__device))]
        public SelectList ListDeviceStatus { get; set; }

        public SelectList ListTYPEOFSINGNAL { get; set; }

        [Display(Name = "CameraMonitorId", ResourceType = typeof(Resources.Resource__device))]
        public SelectList ListCameraMonitorId { get; set; }

        [Display(Name = "SpeedLimit", ResourceType = typeof(Resources.Resource__device))]
        public double? SpeedLimit { get; set; }

        [Range(1, 2147483647, ErrorMessage = "Vui lòng chọn tỉnh")]
        [Required(ErrorMessage = "Vui lòng chọn tỉnh")]
        [Display(Name = "ProvinceId", ResourceType = typeof(Resources.Resource__device))]
        public int? ProvinceId { get; set; }

        [Display(Name = "ProvinceId", ResourceType = typeof(Resources.Resource__device))]
        public SelectList ProvinceSelectableList { get; set; }

        [Range(1, 2147483647, ErrorMessage = "Vui lòng chọn phường/xã")]
        [Required(ErrorMessage = "Vui lòng chọn phường/xã")]
        [Display(Name = "WardId", ResourceType = typeof(Resources.Resource__device))]
        public int? WardId { get; set; }

        [Display(Name = "WardId", ResourceType = typeof(Resources.Resource__device))]
        public SelectList WardSelectableList { get; set; }
    }
}