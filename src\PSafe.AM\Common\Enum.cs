﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace PSafe.AM.Common
{
    public enum EnumControllerName
    {
        AREAS = 0,
        COMMANDCENTERS,
        DEVICES,
        DOCUMENTS,
        DOCUMENTS_BE_LONG,
        <PERSON>RO<PERSON>,
        G<PERSON><PERSON>,
        EDIT_USER_ON_GROUP,
        <PERSON><PERSON><PERSON><PERSON>Y<PERSON>YSEMS,
        <PERSON>OME,
        LOC<PERSON>IONS,
        ROL<PERSON>,
        EDIT_USER_ON_ROLE,
        SECURITYS,
        SUPPLIERS,
        SYSTEMUSERS,
        TYPEOFDEVICES,
        USERS,
        SCHEDULE_TIME,
        PATROL_AREA,
        USER_SCHEDULE,
        PATR<PERSON>_CAMERA,
        PATROL_CAMERA_CALENDAR,
        TYPE_OF_MARKER,
        MARKE<PERSON>,
        SECURITY_RECORD,
        BANNED_LIST,
        PROHIBIT_LIST,
        BLACK_LIST,
        SITE
    }

    public enum SearchVehicleBannedListType
    {
        BIEN_SO_XE,
        CAVET,
        CHU_XE,
        LY_DO_VI_PHAM,
        LY_DO_HUY,
        GHI_CHU,
        NGUOI_PHAT_HIEN
    }

    public enum FormOfFeeCollection
    {
        CHARGE_ON_SITE,
        CHARGE_AFTER_COMPLETELY,
        CHARGE_AFTER_OPTION
    }
}
