﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace PSafe.AM.Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "15.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class Resource_marker {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resource_marker() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("PSafe.AM.Resources.Resource_marker", typeof(Resource_marker).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mô tả.
        /// </summary>
        public static string Description {
            get {
                return ResourceManager.GetString("Description", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vĩ độ.
        /// </summary>
        public static string Latitude {
            get {
                return ResourceManager.GetString("Latitude", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Loại lực lượng.
        /// </summary>
        public static string ListTypeOfMarkers {
            get {
                return ResourceManager.GetString("ListTypeOfMarkers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kinh độ.
        /// </summary>
        public static string Longitude {
            get {
                return ResourceManager.GetString("Longitude", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mã lực lượng.
        /// </summary>
        public static string MarkerCode {
            get {
                return ResourceManager.GetString("MarkerCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mã.
        /// </summary>
        public static string MarkerID {
            get {
                return ResourceManager.GetString("MarkerID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tên lực lượng.
        /// </summary>
        public static string MarkerName {
            get {
                return ResourceManager.GetString("MarkerName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Loại lực lượng.
        /// </summary>
        public static string MarkerTypeID {
            get {
                return ResourceManager.GetString("MarkerTypeID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Số điện thoại.
        /// </summary>
        public static string Phone {
            get {
                return ResourceManager.GetString("Phone", resourceCulture);
            }
        }
    }
}
