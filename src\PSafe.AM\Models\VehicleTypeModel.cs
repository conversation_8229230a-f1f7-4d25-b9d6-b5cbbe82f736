﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace PSafe.AM.Models
{
    public class VehicleTypeModel
    {
        [Key]
        [Display(Name = "Id", ResourceType = typeof(Resources.Resource_vehicleType))]
        public int Id { get; set; }

        [Required(ErrorMessage = "Vui lòng nhập tên loại phương tiện")]
        [StringLength(255, MinimumLength = 1, ErrorMessage = "Độ dài từ 1-255 ký tự")]
        [Display(Name = "Name", ResourceType = typeof(Resources.Resource_vehicleType))]
        public string Name { get; set; } = string.Empty;

        [Display(Name = "Description", ResourceType = typeof(Resources.Resource_vehicleType))]
        public string Description { get; set; } = string.Empty;
    }
}
