﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace PSafe.AM.Models
{
    public class DepartmentModel
    {
        [Key]
        [Display(Name = "Id", ResourceType = typeof(Resources.Resource_department))]
        public int Id { get; set; }

        [Required(ErrorMessage = "Vui lòng nhập tên phòng ban")]
        [StringLength(255, MinimumLength = 1, ErrorMessage = "Độ dài từ 1-255 ký tự")]
        [Display(Name = "DepartmentName", ResourceType = typeof(Resources.Resource_department))]
        public string DepartmentName { get; set; } = string.Empty;

        [Display(Name = "Description", ResourceType = typeof(Resources.Resource_department))]
        public string Description { get; set; } = string.Empty;
    }
}
