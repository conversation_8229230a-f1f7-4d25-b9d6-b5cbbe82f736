﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using PSafe.AM.Common;
using PSafe.AM.Models;
using PSafe.Core.Domains;
using PSafe.Core.Interface;
using static PSafe.Common.CommonEnums;
using Microsoft.AspNetCore.Http;
using PSafe.Common.UserEnums;
using Microsoft.Extensions.Logging;
using System.Net.Http;
using System.Net.Http.Headers;
using Newtonsoft.Json.Linq;
using Microsoft.Extensions.Configuration;
using PSafe.Common;
using Newtonsoft.Json;
using System.Text;
using System.Threading;
using System.Text.Json;
using PSafe.Common.PatrolEnums;

namespace PSafe.AM.Controllers
{
    [AuthorizeActionFilter(EPERMISSIONS_AM.SchedulePatrolvirtualManage)]
    public class PatrolCameraController : Controller
    {
        private readonly IAreaRepository _areaRepository;
        private readonly ILocationRepository _locationRepository;
        private readonly ITypeOfDeviceRepository _typeOfDeviceRepository;
        private readonly IDeviceRepository _deviceRepository;
        private readonly IPatrolCameraRepository _patrolCameraRepository;
        private readonly IUserRepository _userRepository;
        private readonly IHistorySystemRepository _historySystemRepository;
        private readonly ILogger<SecurityController> _logger;
        private readonly IConfiguration _configuration;
        private readonly IHttpContextAccessor _accessor;

        public PatrolCameraController(IAreaRepository areaRepository, ILocationRepository locationRepository, ITypeOfDeviceRepository typeOfDeviceRepository, IDeviceRepository deviceRepository, IPatrolCameraRepository patrolCameraRepository, IUserRepository userRepository, IHistorySystemRepository historySystemRepository, ILogger<SecurityController> logger, IHttpContextAccessor accessor, IConfiguration configuration)
        {
            _areaRepository = areaRepository;
            _locationRepository = locationRepository;
            _typeOfDeviceRepository = typeOfDeviceRepository;
            _deviceRepository = deviceRepository;
            _patrolCameraRepository = patrolCameraRepository;
            _userRepository = userRepository;
            _historySystemRepository = historySystemRepository;
            _logger = logger;
            _accessor = accessor;
            _configuration = configuration;
        }

        public IActionResult Index()
        {
            StatusQuery Notification;
            List<PatrolCameraModel> _listPatrolCameraModel = new List<PatrolCameraModel>();

            try
            {
                List<PatrolCamera> _listPatrolCamera = _patrolCameraRepository.GetBy(p => p.PatrolType == (short)EPATROL_CALENDAR_TYPE.PatrolCamera).OrderByDescending(x => x.ID).ToList();
                foreach (var patrolCamera in _listPatrolCamera)
                {
                    PatrolCameraModel patrolCameraModel = new PatrolCameraModel
                    {
                        Id = patrolCamera.ID
                    };
                    CopyTo(patrolCameraModel, patrolCamera);
                    patrolCameraModel.ListCamera = patrolCameraModel.ListCamera.Replace(",", ", ");
                    _listPatrolCameraModel.Add(patrolCameraModel);
                }

                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }
            }
            catch(Exception ex)
            {
                _logger.LogError("PatrolCamera/Index: " + ex.Message);

                Notification = new StatusQuery("error", "", "Xem danh sách thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;
            }

            return View(_listPatrolCameraModel);
        }

        public ActionResult Details(int id)
        {
            try
            {
                var _patrolCamera = _patrolCameraRepository.GetById(id);

                if (_patrolCamera != null)
                {
                    var _patrolCameraModel = new PatrolCameraModel
                    {
                        Id = id
                    };
                    CopyTo(_patrolCameraModel, _patrolCamera);
                    _patrolCameraModel.ListCamera = _patrolCameraModel.ListCamera.Replace(",", ", ");
                    try
                    {
                        if (TempDataHelper.Get<StatusQuery>(TempData, "Notification") is StatusQuery Notification)
                        {
                            ViewBag.Status = Notification.Status;
                            ViewBag.Value = Notification.Value;
                            ViewBag.Type = Notification.Type;
                        }

                    }
                    catch (Exception ex)
                    {
                        _logger.LogError("PatrolCamera/Index: " + ex.Message);
                        Console.WriteLine(ex.Message);
                    }

                    return View(_patrolCameraModel);
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                    return RedirectToAction("Index");
                }
            }
            catch(Exception ex)
            {
                _logger.LogError("PatrolCamera/Details: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                return RedirectToAction("Index");
            }
        }

        public ActionResult Delete(int id)
        {
            StatusQuery Notification;
            try
            {
                var _patrolCamera = _patrolCameraRepository.GetById(id);

                if (_patrolCamera == null)
                {
                    Notification = new StatusQuery("warning", "", "Không tìm thấy kịch bản tuần tra cần xóa");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return RedirectToAction("Index");
                }

                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }

                PatrolCameraModel patrolCameraModel = new PatrolCameraModel();
                CopyTo(patrolCameraModel, _patrolCamera);
                patrolCameraModel.ListCamera = patrolCameraModel.ListCamera.Replace(",", ", ");
                return View(patrolCameraModel);
            }
            catch(Exception ex)
            {
                _logger.LogError("PatrolCamera/Delete: " + ex.Message);

                Notification = new StatusQuery("warning", "", "Xóa thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;

                return RedirectToAction("Index");
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int id)
        {
            try
            {
                var systemUserSession = GetSesson();

                if (systemUserSession == null)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                    return RedirectToAction("Logout", "Security");
                }

                var patrolCamera = _patrolCameraRepository.GetById(id);
                _patrolCameraRepository.Delete(patrolCamera);

                var deleteStatus = _patrolCameraRepository.SaveChanges();

                if (deleteStatus > 0)
                {
                    string StringDescription = new GetStringHistorySystem().Get(systemUserSession.UserName, Resources.Resource.Delete, patrolCamera.Name.ToString(), Resources.Resource.PatrolCamera);

                    InsertHistorySystem((int)EACTION_TYPE.DELETE, (int)EnumControllerName.PATROL_CAMERA, StringDescription, patrolCamera, null);

                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Xóa thành công"));
                    return RedirectToAction("Index");
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));
                    return RedirectToAction("Delete", "PatrolCamera", new { id });
                }
            }
            catch(Exception ex)
            {
                _logger.LogError("PatrolCamera/DeleteConfirmed: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));
                return RedirectToAction("Delete", "PatrolCamera", new { id });
            }
        }

        public ActionResult Create()
        {
            List<SelectListItem> areaListItems = GetAreaList();

            ViewBag.areaListItems = areaListItems;
            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create(PatrolCameraModel patrolCameraModel)
        {
            List<SelectListItem> areaListItems = null;
            StatusQuery Notification;
            if (ModelState.IsValid)
            {
                var systemUser = GetSesson();
                if (systemUser == null)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                    return RedirectToAction("Logout", "Security");
                }

                PatrolCamera patrolCamera = new PatrolCamera();
                CopyTo(patrolCamera, patrolCameraModel);
                try
                {
                    _patrolCameraRepository.Insert(patrolCamera);
                    var statusInsert = _patrolCameraRepository.SaveChanges();

                    if (statusInsert > 0)
                    {
                        string StringDescription = new GetStringHistorySystem().Get(systemUser.UserName, Resources.Resource.Create, patrolCameraModel.Name.ToString(), Resources.Resource.PatrolCamera);

                        InsertHistorySystem((int)EACTION_TYPE.CREATE, (int)EnumControllerName.PATROL_CAMERA, StringDescription, null, patrolCameraModel);

                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Thêm mới thành công"));
                        return RedirectToAction("Index");
                    }
                    else
                    {
                        areaListItems = GetAreaList();
                        ViewBag.areaListItems = areaListItems;
                        Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        return View(patrolCameraModel);
                    }
                }
                catch(Exception ex)
                {
                    _logger.LogError("PatrolCamera/Create: " + ex.Message);

                    areaListItems = GetAreaList();
                    ViewBag.areaListItems = areaListItems;
                    Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return View(patrolCameraModel);
                }
            }

            areaListItems = GetAreaList();
            ViewBag.areaListItems = areaListItems;
            Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;

            return View(patrolCameraModel);
        }

        private void GetComboboxData()
        {
            List<SelectListItem> areaListItems = GetAreaList();

            ViewBag.areaListItems = areaListItems;
        }

        public async Task<ActionResult> Edit(int id)
        {
            try
            {
                var patrolCamera = _patrolCameraRepository.GetById(id);
                if (patrolCamera != null)
                {
                    PatrolCameraModel patrolCameraModel = new PatrolCameraModel
                    {
                        Id = patrolCamera.ID
                    };
                    CopyTo(patrolCameraModel, patrolCamera);
                    GetComboboxData();

                    var options = new JsonSerializerOptions()
                    {
                        IncludeFields = true,
                    };
                    var listPatrolCameraPresetModel = System.Text.Json.JsonSerializer.Deserialize<List<PatrolCameraPresetModel>>(patrolCamera.ListCamera, options);

                    patrolCameraModel.ListCameraSelected = listPatrolCameraPresetModel;
                    var ListPreset = await GetListPreset(listPatrolCameraPresetModel);
                    patrolCameraModel.ListCameraNoSelected = ListPreset;
                    return View(patrolCameraModel);
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Không tìm thấy kịch bản tuần tra cần sửa"));

                    return RedirectToAction("Index");
                }
            }
            catch(Exception ex)
            {
                _logger.LogError("PatrolCamera/Edit: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                return RedirectToAction("Index");
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit(PatrolCameraModel patrolCameraModel)
        {
            StatusQuery Notification;
            if (ModelState.IsValid)
            {
                try
                {
                    var systemUser = GetSesson();
                    if (systemUser == null)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                        return RedirectToAction("Logout", "Security");
                    }

                    var _patrolCamera = _patrolCameraRepository.GetById(patrolCameraModel.Id);
                    if (_patrolCamera == null)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Không tìm thấy kịch bản tuần tra cần sửa"));

                        return RedirectToAction("Index");
                    }

                    CopyTo(_patrolCamera, patrolCameraModel);

                    _patrolCameraRepository.Update(_patrolCamera);

                    var updateStatus = _patrolCameraRepository.SaveChanges();

                    if (updateStatus > 0)
                    {
                        string StringDescription = new GetStringHistorySystem().Get(systemUser.UserName, Resources.Resource.Edit, patrolCameraModel.Name.ToString(), Resources.Resource.PatrolCamera);
                        InsertHistorySystem((int)EACTION_TYPE.EDIT, (int)EnumControllerName.PATROL_CAMERA, StringDescription, patrolCameraModel, _patrolCamera);
                       
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Sửa thành công"));

                        return RedirectToAction("Index");
                    }
                    else
                    {
                        Notification = new StatusQuery("error", "", "Sửa thất bại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;
                        GetComboboxData();
                        return View(patrolCameraModel);
                    }
                }
                catch(Exception ex)
                {
                    _logger.LogError("PatrolCamera/Edit: " + ex.Message);

                    Notification = new StatusQuery("error", "", "Sửa thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                    GetComboboxData();
                    return View(patrolCameraModel);
                }
            }

            Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;
            GetComboboxData();
            return View(patrolCameraModel);
        }

        private List<Location> GetLocationsByCamera(string listCamera)
        {
            List<Location> listLocation = new List<Location>();
            if (string.IsNullOrEmpty(listCamera))
                return listLocation;

            string[] listCameraArray = listCamera.Split(',');
            foreach (string cameraName in listCameraArray)
            {
                var camera = _deviceRepository.GetBy(x => x.DeviceName.Equals(cameraName)).FirstOrDefault();
                if (camera != null && camera.LocationId.HasValue)
                {
                    var location = _locationRepository.GetBy(x => x.Id == camera.LocationId).FirstOrDefault();
                    if (location != null)
                        listLocation.Add(location);
                }
            }

            return listLocation;
        }

        private string GetListCameraById(List<string> listCamera)
        {
            if (listCamera.Count > 0)
                return string.Join(',', _deviceRepository.GetListDeviceNameById(listCamera));

            return string.Empty;
        }

        private void CopyTo(PatrolCamera dest, PatrolCameraModel src)
        {
            dest.Name = src.Name;
            dest.ListCamera = src.ListCamera;
            dest.Type = src.Type;
            dest.PatrolType = (short)EPATROL_CALENDAR_TYPE.PatrolCamera;
        }

        private void CopyTo(PatrolCameraModel dest, PatrolCamera src)
        {
            dest.Name = src.Name;
            dest.ListCamera = src.ListCamera;
            dest.Type = src.Type;
        }

        private List<PatrolCameraPresetModel> GetDeviceCameraList(int locationId)
        {
            List<PatrolCameraPresetModel> retVal = new List<PatrolCameraPresetModel>();
            try
            {
                var typeOfDeviceCamera = _typeOfDeviceRepository.GetBy(x => x.TypeName.ToLower().Equals("camera")).FirstOrDefault();
                if (typeOfDeviceCamera == null)
                    return retVal;

                var deviceCameraList = _deviceRepository.GetBy(x => x.TypeOfDeviceId == typeOfDeviceCamera.Id && x.LocationId == locationId).OrderBy(x => x.DeviceName).ToList();
                foreach (var camera in deviceCameraList)
                {
                    retVal.Add(new PatrolCameraPresetModel()
                    {
                        CameraName = camera.DeviceName,
                        MilestoneId = camera.MilestoneId.Value == null ? Guid.Empty : camera.MilestoneId.Value,
                        ListPreset = new List<string>()
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("PatrolCamera/GetDeviceCameraList: " + ex.Message);
                Console.WriteLine(ex.Message);
            }

            return retVal;
        }

        private List<SelectListItem> GetDeviceCameraList(List<Location> locations)
        {
            List<SelectListItem> retVal = new List<SelectListItem>();
            try
            {
                var typeOfDeviceCamera = _typeOfDeviceRepository.GetBy(x => x.TypeName.ToLower().Equals("camera")).FirstOrDefault();
                if (typeOfDeviceCamera == null)
                    return retVal;

                List<int> listLocationId = new List<int>();
                foreach(var location in locations)
                {
                    listLocationId.Add(location.Id);
                }

                var deviceCameraList = _deviceRepository.GetBy(x => x.TypeOfDeviceId == typeOfDeviceCamera.Id && listLocationId.Contains(x.LocationId.Value)).OrderBy(x => x.DeviceName).ToList();
                foreach (var camera in deviceCameraList)
                {
                    retVal.Add(new SelectListItem()
                    {
                        Text = camera.DeviceName,
                        Value = camera.Id.ToString()
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("PatrolCamera/GetDeviceCameraList: " + ex.Message);
                Console.WriteLine(ex.Message);
            }

            return retVal;
        }

        private List<SelectListItem> GetAreaList()
        {
            List<SelectListItem> areaListItems = new List<SelectListItem>();
            try
            {
                var areaList = _areaRepository.GetAll().ToList();
                foreach (var area in areaList)
                {
                    areaListItems.Add(new SelectListItem()
                    {
                        Text = area.AreaName,
                        Value = area.Id.ToString()
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("PatrolCamera/GetAreaList: " + ex.Message);
                Console.WriteLine(ex.Message);
            }

            return areaListItems;
        }

        private List<SelectListItem> GetListLocationByAreaId(int areaId)
        {
            try
            {
                var locationList = _locationRepository.GetBy(x => x.AreaId == areaId).Select(x => new SelectListItem() { Value = x.Id.ToString(), Text = x.LocationName }).ToList();
                return locationList;
            }
            catch (Exception ex)
            {
                _logger.LogError("PatrolCamera/GetListLocationByAreaId: " + ex.Message);
                Console.WriteLine(ex.Message);
            }

            return new List<SelectListItem>();
        }

        [HttpPost("PatrolCamera/GetLocationByArea")]
        public JsonResult GetLocationByArea(int areaId)
        {
            return Json(GetListLocationByAreaId(areaId));
        }

        [HttpPost("PatrolCamera/GetCameraByLocation")]
        public async Task<JsonResult> GetCameraByLocation(int locationId)
        {
            var cameraList = GetDeviceCameraList(locationId);

            var cameraPatrolByLocation = await GetListPreset(cameraList);

            return Json(cameraPatrolByLocation);
        }

        
        private async Task<List<PatrolCameraPresetModel>> GetListPreset(List<PatrolCameraPresetModel> patrolCameraPresetModel)
        {
            List<PatrolCameraPresetModel> patrolCameraPreset = new List<PatrolCameraPresetModel>();
            try
            {
                string baseURL = _configuration.GetSection("linkGetAllCameraMilestone:BaseUrl").Value + "/api/cameras";

                string token = await GetToken();

                //var handler = new HttpClientHandler thêm dòng này để tắt SSL
                //{ thêm dòng này để tắt SSL
                //    ClientCertificateOptions = ClientCertificateOption.Automatic, thêm dòng này để tắt SSL
                //    ServerCertificateCustomValidationCallback = (httpRequestMessage, cert, cetChain, policyErrors) => thêm dòng này để tắt SSL
                //    { thêm dòng này để tắt SSL
                //        return true; thêm dòng này để tắt SSL
                //    } thêm dòng này để tắt SSL
                //}; thêm dòng này để tắt SSL

                using (HttpClient client = new HttpClient())
                {
                    client.DefaultRequestHeaders.Authorization
                    = new AuthenticationHeaderValue("Bearer", token);

                    using (HttpResponseMessage res = await client.GetAsync(baseURL))
                    {
                        using (HttpContent content = res.Content)
                        {
                            string data = await content.ReadAsStringAsync();
                            var dataObj = JObject.Parse(data);

                            if (data != null && data != string.Empty && JObject.Parse(data)["result"].ToString() != "2")
                            {
                                foreach(var item in patrolCameraPresetModel)
                                {
                                    var listPreset = new List<string>();
                                    var camera = dataObj["message"].Where(p => p["guid"].ToString().Trim() == item.MilestoneId.ToString()).FirstOrDefault();

                                    if (camera != null && camera["presetList"].ToList().Count > 0)
                                    {
                                        foreach (var preset in camera["presetList"].ToList())
                                        {
                                            listPreset.Add(preset.ToString());
                                        }
                                    }
                                    PatrolCameraPresetModel patrolCamera = new PatrolCameraPresetModel
                                    {
                                        CameraName = item.CameraName,
                                        MilestoneId = item.MilestoneId,
                                        ListPreset = listPreset
                                    };
                                    patrolCameraPreset.Add(patrolCamera);
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Devices/Sync: " + ex.Message);
            }

            return patrolCameraPreset;
        }
        private async Task<string> GetToken()
        {
            var handler = new HttpClientHandler
            {
                //ClientCertificateOptions = ClientCertificateOption.Automatic,  thêm dòng này để tắt SSL
                //ServerCertificateCustomValidationCallback = (httpRequestMessage, cert, cetChain, policyErrors) =>  thêm dòng này để tắt SSL
                //{  thêm dòng này để tắt SSL
                //    return true;  thêm dòng này để tắt SSL
                //},  thêm dòng này để tắt SSL
                UseProxy = false
            };

            string data = string.Empty;

            try
            {
                string baseGetTokenURL = _configuration.GetSection("linkGetAllCameraMilestone:BaseUrl").Value + "/auth/getToken";

                try
                {
                    using (HttpClient client = new HttpClient(handler))
                    {
                        var UsernameEncode = Utils.DecodePassword(_configuration.GetSection("linkGetAllCameraMilestone:Username").Value, Utils.EncodeType.SHA_256);
                        var PasswordEncode = Utils.DecodePassword(_configuration.GetSection("linkGetAllCameraMilestone:Password").Value, Utils.EncodeType.SHA_256);

                        var requestData = new Dictionary<string, string>();
                        requestData["Username"] = UsernameEncode;
                        requestData["Password"] = PasswordEncode;

                        using (HttpRequestMessage res = new HttpRequestMessage(HttpMethod.Post, baseGetTokenURL))
                        {
                            var json = JsonConvert.SerializeObject(requestData);
                            using (var stringContent = new StringContent(json, Encoding.UTF8, "application/json"))
                            {
                                res.Content = stringContent;

                                using (var response = await client.SendAsync(res, CancellationToken.None))
                                {
                                    response.EnsureSuccessStatusCode();
                                    var responseBody = await response.Content.ReadAsStringAsync();

                                    var dataObj = JObject.Parse(responseBody);

                                    data = dataObj["message"].ToString();

                                    _logger.LogInformation("Devices/GetToken: ok");

                                    client.Dispose();
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("Devices/GetToken: " + ex.Message);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Devices/GetToken: " + ex.Message);
            }

            return data;
        }

        public User GetSesson()
        {
            var SessionUserSystem = HttpContext.Session.GetComplexData<User>("SessionUserSystemId_" + HttpContext.Session.GetString("SessionUserSystemId"));
            if (SessionUserSystem != null)
            {
                var _user = _userRepository.GetById(SessionUserSystem.Id);

                if (_user != null)
                {
                    if (!_user.Actived)
                    {
                        HttpContext.Session.Clear();
                        return null;
                    }
                    return _user;
                }
            }
            return null;
        }

        private void InsertHistorySystem(int action_type, int controllerName, string description, object OldModel, object NewModel)
        {
            try
            {
                var ipAddress = _accessor.HttpContext.Connection.RemoteIpAddress.ToString();

                string jsonOldObject = Newtonsoft.Json.JsonConvert.SerializeObject(OldModel);
                string jsonNewObject = Newtonsoft.Json.JsonConvert.SerializeObject(NewModel);

                HistorySystem history = new HistorySystem
                {
                    ActionType = action_type,
                    ActionTime = DateTime.Now,
                    Description = description,
                    OldObject = jsonOldObject,
                    NewObject = jsonNewObject,
                    UserId = int.Parse(HttpContext.Session.GetString("SessionUserSystemId")),
                    IpAddress = ipAddress,
                    ControllerName = controllerName
                };

                _historySystemRepository.Insert(history);

                _historySystemRepository.SaveChanges();
            }
            catch (Exception ex)
            {
                _logger.LogError("PatrolCamera/InsertHistorySystem: " + ex.Message);
            }
        }
    }
}