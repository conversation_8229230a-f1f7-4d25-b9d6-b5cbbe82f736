﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace PSafe.AM.Models
{
    public class ContactPlaceModel
    {
        [Key]
        [Display(Name = "Id", ResourceType = typeof(Resources.Resource_contactPlace))]
        public int Id { get; set; }

        [Required(ErrorMessage = "<PERSON>ui lòng nhập tên nơi liên hệ")]
        [StringLength(255, MinimumLength = 1, ErrorMessage = "Độ dài từ 1-255 ký tự")]
        [Display(Name = "Name", ResourceType = typeof(Resources.Resource_contactPlace))]
        public string Name { get; set; } = string.Empty;

        [Display(Name = "CreatedBy", ResourceType = typeof(Resources.Resource_contactPlace))]
        public int CreatedBy { get; set; }

        [Display(Name = "UpdatedBy", ResourceType = typeof(Resources.Resource_contactPlace))]
        public int UpdatedBy { get; set; }

        [Display(Name = "CreatedDate", ResourceType = typeof(Resources.Resource_contactPlace))]
        public DateTime CreatedDate { get; set; }

        [Display(Name = "UpdatedDate", ResourceType = typeof(Resources.Resource_contactPlace))]
        public DateTime UpdatedDate { get; set; }

        [Display(Name = "Description", ResourceType = typeof(Resources.Resource_contactPlace))]
        public string Description { get; set; } = string.Empty;

        [Display(Name = "Index", ResourceType = typeof(Resources.Resource_contactUnit))]
        [Required]
        [Range(0, int.MaxValue)]
        public int Index { get; set; } = 0;
    }
}
