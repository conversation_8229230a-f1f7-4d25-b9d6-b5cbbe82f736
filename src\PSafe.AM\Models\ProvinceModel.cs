﻿using System;
using System.ComponentModel.DataAnnotations;

namespace PSafe.AM.Models
{
    public class ProvinceModel
    {
        [Key]
        [Display(Name = "Id", ResourceType = typeof(Resources.Resource_province))]
        public int Id { get; set; }

        [Required(ErrorMessage = "<PERSON><PERSON> lòng nhập tên Tỉnh")]
        [StringLength(255, MinimumLength = 1, ErrorMessage = "Độ dài từ 1-255 ký tự")]
        [Display(Name = "ProvinceName", ResourceType = typeof(Resources.Resource_province))]
        public string ProvinceName { get; set; } = string.Empty;

        [Display(Name = "Description", ResourceType = typeof(Resources.Resource_province))]
        public string Description { get; set; } = string.Empty;
    }
}
