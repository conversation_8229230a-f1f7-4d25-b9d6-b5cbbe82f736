﻿@model PSafe.AM.Models.DeviceModel

@{
    ViewBag.Title = "Hiệu chỉnh";
}

<environment names="Development">
    <script src="~/lib/jquery/dist/jquery.js"></script>
    <script src="~/lib/validate/jquery.validate.min.js"></script>
    <script src="~/js/script.js" asp-append-version="true"></script>
</environment>
<environment names="Staging,Production">
    <script src="~/lib/jquery/dist/jquery.js"></script>
    <script src="~/lib/validate/jquery.validate.min.js"></script>
    <script src="~/js/script.js" asp-append-version="true"></script>
</environment>

<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-lg-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>@PSafe.AM.Resources.Resource.Edit @PSafe.AM.Resources.Resource.Device</h5>
                    <div class="ibox-tools">
                        @Html.ActionLink(PSafe.AM.Resources.Resource.BackToList, "Index", "Devices", null, new { @class = "btn btn-primary btn-xs" })
                    </div>
                </div>
                <div class="ibox-content">
                    @using (Html.BeginForm())
                    {

                        @Html.AntiForgeryToken()
                        @Html.ValidationSummary(true)
                        @Html.HiddenFor(model => model.DEVICEID)
                    <div class="row">
                        <div class="col-lg-6">
                            <div class="form-group row">
                                <label class="control-label col-md-4">@Html.LabelFor(model => model.TYPEOFDEVICEID) (*)</label>
                                <div class="col-md-8">
                                    @Html.DropDownListFor(model => model.TYPEOFDEVICEID, Model.ListTypeOfDevices as SelectList, new { @class = "form-control" })
                                    @Html.ValidationMessageFor(model => model.TYPEOFDEVICEID, null, new { @class = "text-danger" })
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                <label class="control-label col-md-4">@Html.LabelFor(model => model.TYPEOFSINGNAL) (*)</label>
                                <div class="col-md-8">
                                    @Html.DropDownListFor(model => model.TYPEOFSINGNAL, Model.ListTYPEOFSINGNAL as SelectList, new { @class = "form-control" })
                                    @Html.ValidationMessageFor(model => model.TYPEOFSINGNAL, null, new { @class = "text-danger" })
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                <label class="control-label col-md-4">@Html.LabelFor(model => model.DEVICENAME) (*)</label>
                                <div class="col-md-8">
                                    @Html.EditorFor(model => model.DEVICENAME)
                                    @Html.ValidationMessageFor(model => model.DEVICENAME, null, new { @class = "text-danger" })
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                <label class="control-label col-md-4">@Html.LabelFor(model => model.SERIALNUMBER) (*)</label>
                                <div class="col-md-8">
                                    @Html.EditorFor(model => model.SERIALNUMBER)
                                    @Html.ValidationMessageFor(model => model.SERIALNUMBER, null, new { @class = "text-danger" })
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                <label class="control-label col-md-4">@PSafe.AM.Resources.Resource.Area</label>
                                <div class="col-md-8">
                                    @Html.DropDownList("areas", Model.ListAreas as SelectList, new { @class = "form-control" })
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                <label class="control-label col-md-4" id="labelCameraMonitorId">@Html.LabelFor(model => model.CameraMonitorId) (*)</label>
                                <div class="col-md-4">
                                    @Html.DropDownListFor(model => model.CameraMonitorId, Model.ListCameraMonitorId as SelectList, new { @class = "form-control" })
                                    @Html.ValidationMessageFor(model => model.CameraMonitorId, null, new { @class = "text-danger", id = "CameraMonitorIdValidate" })
                                </div>
                                <div class="col-md-4">
                                    <select class="form-control" id="Preset" name="Preset">
                                        <option value="-1">---Chọn---</option>
                                        @{
                                            var ListPreset = Model.ListAllPreset.Where(p => p.Id == Model.CameraMonitorId).FirstOrDefault();
                                            if (ListPreset != null)
                                            {
                                                foreach (var item in ListPreset.ListPreset)
                                                {
                                                    if (item == Model.Preset)
                                                    {
                                                            <option value="@item" selected>@item</option>
                                                    }
                                                    else
                                                    {
                                                            <option value="@item">@item</option>
                                                    }
                                                }
                                            }
                                        }
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-6">
                            <div class="form-group row">
                                @Html.LabelFor(model => model.LOCATIONID, new { @class = "control-label col-md-4" })
                                <div class="col-md-8">
                                    @Html.DropDownListFor(model => model.LOCATIONID, Model.ListLocations as SelectList, new { @class = "form-control" })
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                @Html.LabelFor(model => model.Zone, new { @class = "control-label col-md-4", id = "Zone_Label" })
                                <div class="col-md-8">
                                    @Html.EditorFor(model => model.Zone)
                                    @Html.ValidationMessageFor(model => model.Zone, null, new { @class = "text-danger" })
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-6">
                            <div class="form-group row">
                                @Html.LabelFor(model => model.LASTCONNECTTIME, new { @class = "control-label col-md-4" })
                                <div class="col-md-8">
                                    @Html.EditorFor(model => model.LASTCONNECTTIME)
                                    @Html.ValidationMessageFor(model => model.LASTCONNECTTIME, null, new { @class = "text-danger" })
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-6">
                            <div class="form-group row">
                                @Html.LabelFor(model => model.SUPPLIERID, new { @class = "control-label col-md-4" })
                                <div class="col-md-8">
                                    @Html.DropDownListFor(model => model.SUPPLIERID, Model.ListSuppliers as SelectList, new { @class = "form-control" })
                                    @Html.ValidationMessageFor(model => model.SUPPLIERID, null, new { @class = "text-danger" })
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                @Html.LabelFor(model => model.Ip, new { @class = "control-label col-md-4" })
                                <div class="col-md-8">
                                    @Html.EditorFor(model => model.Ip)
                                    @Html.ValidationMessageFor(model => model.Ip, null, new { @class = "text-danger" })
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                @Html.LabelFor(model => model.MacAddress, new { @class = "control-label col-md-4" })
                                <div class="col-md-8">
                                    @Html.EditorFor(model => model.MacAddress)
                                    @Html.ValidationMessageFor(model => model.MacAddress, null, new { @class = "text-danger" })
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                @Html.LabelFor(model => model.DriverName, new { @class = "control-label col-md-4" })
                                <div class="col-md-8">
                                    @Html.EditorFor(model => model.DriverName)
                                    @Html.ValidationMessageFor(model => model.DriverName, null, new { @class = "text-danger" })
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                @Html.LabelFor(model => model.ACTIVED, new { @class = "control-label col-md-4" })
                                <div class="col-md-8">
                                    @Html.EditorFor(model => model.ACTIVED)
                                    @Html.ValidationMessageFor(model => model.ACTIVED, null, new { @class = "text-danger" })
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                @Html.LabelFor(model => model.TrafficCamera, new { @class = "control-label col-md-4" })
                                <div class="col-md-8">
                                    @Html.EditorFor(model => model.TrafficCamera)
                                    @Html.ValidationMessageFor(model => model.TrafficCamera, null, new { @class = "text-danger" })
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                @Html.LabelFor(model => model.DESCRIPTION, new { @class = "control-label col-md-4" })
                                <div class="col-md-8">
                                    @Html.TextAreaFor(model => model.DESCRIPTION, new { @class = "form-control" })
                                    @Html.ValidationMessageFor(model => model.DESCRIPTION, null, new { @class = "text-danger" })
                                </div>
                            </div>
                        </div>
                        @* <div class="col-lg-6">
                            <div class="form-group row">
                                @Html.LabelFor(model => model.SpeedLimit, new { @class = "control-label col-md-4" })
                                <div class="col-md-8">
                                    @Html.EditorFor(model => model.SpeedLimit, new { htmlAttributes = new { @class = "form-control", @type = "number" } })
                                    @Html.ValidationMessageFor(model => model.SpeedLimit, null, new { @class = "text-danger" })
                                </div>
                            </div>
                        </div> *@
                            <div class="col-lg-6">
                                <div class="form-group row">
                                    <label class="control-label col-md-4">@Html.LabelFor(model => model.ProvinceId) (*)</label>
                                    <div class="col-md-8">
                                        @Html.DropDownListFor(model => model.ProvinceId, Model.ProvinceSelectableList as SelectList, "Vui lòng chọn tỉnh...", new { @class = "form-control" })
                                        @Html.ValidationMessageFor(model => model.ProvinceId, null, new { @class = "text-danger" })
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group row">
                                    <label class="control-label col-md-4">@Html.LabelFor(model => model.WardId) (*)</label>
                                    <div class="col-md-8">
                                        @Html.DropDownListFor(model => model.WardId, Model.WardSelectableList as SelectList, "Vui lòng chọn phường/xã...", new { @class = "form-control" })
                                        @Html.ValidationMessageFor(model => model.WardId, null, new { @class = "text-danger" })
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <hr />
                            </div>
                            <div class="col-lg-12">
                                <div class="form-group row">
                                    <label class="control-label col-md-7"><b>Loại phương tiện</b></label>
                                    <label class="control-label col-md-3"><b>Tốc độ tối đa(km/h)</b></label>
                                    <label class="control-label col-md-1"><b>Hành động</b></label>
                                    <button id="add-field-btn" type="button" class="btn btn-primary col-md-1">Thêm</button>
                                </div>
                                <div id="dynamic-fields-container">
                                    <!-- Các dòng ComboBox và Input sẽ được thêm vào đây -->
                                </div>
                            </div>
                    </div>
                        <div class="row">
                            <div class="col-md-offset-2 col-md-10">
                                <input type="submit" value="@PSafe.AM.Resources.Resource.Save" class="btn btn-primary" />
                                @Html.ActionLink(PSafe.AM.Resources.Resource.Cancel, "Index", "Devices", null, new { @class = "btn btn-white" })
                            </div>
                        </div>
                                            }
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <environment names="Development,Staging,Production">
        <link rel="stylesheet" href="~/lib/toastr/toastr.min.css" />
    </environment>
}

@section Scripts {
    <environment names="Development,Staging,Production">
        <script src="~/lib/validate/jquery.validate.min.js"></script>
        <script src="~/lib/toastr/toastr.js"></script>
        <script src="~/lib/toastr/toastr.min.js"></script>
    </environment>

    @if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "warning")
    {
        <script type="text/javascript">
            toastr.warning('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "error")
    {
        <script type="text/javascript">
            toastr.error('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "success")
    {
        <script type="text/javascript">
            toastr.success('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }

<script type="text/javascript">
    var TYPEOFSINGNALEnum = @ViewBag.TYPEOFSINGNALEnum;

    var presets = [];

    @foreach(var preset in Model.ListAllPreset)
    {
        @Html.Raw("var list = [];");


        foreach(var pre in preset.ListPreset)
        {
            @Html.Raw("list.push('"+ pre +"');");
        }

        @Html.Raw("presets.push({Id:" + preset.Id + ",CameraName:'" + preset.CameraName + "',ListPreset:list});");
    }
            $("#ProvinceId").change(async function () {
                var provinceId = $(this).val();
                if (provinceId) {
                        $.ajax({
                        url: '@Url.Action("GetListDropdownWards", "Devices")',
                        type: 'GET',
                        data: { provinceId: provinceId },
                        dataType: 'json',
                        success: function (response) {
                            var len = response.length;
                            $("#WardId").empty();
                            $("#WardId").append("<option value='-1'>Chọn</option>");
                            for (var i = 0; i < len; i++) {
                                var id = response[i]['id'];
                                var name = response[i]['name'];
                                $("#WardId").append("<option value='" + id + "'>" + name + "</option>");
                            }
                        },
                    });
                }
            });
        $("#areas").change(function () {
            var areasId = $(this).val();

            $.ajax({
                url: '@Url.Action("GetListDropdownLocations", "Locations")',
                type: 'GET',
                data: { AreasId: areasId },
                dataType: 'json',
                success: function (response) {

                    var len = response.length;

                    $("#LOCATIONID").empty();
                    $("#LOCATIONID").append("<option value='-1'>Chọn</option>");
                    for (var i = 0; i < len; i++) {
                        var id = response[i]['id'];
                        var name = response[i]['name'];
                        $("#LOCATIONID").append("<option value='" + id + "'>" + name + "</option>");
                    }
                }
            });
        });

        //$("#TYPEOFSINGNAL").change(function () {
        //    var TYPEOFSINGNAL = $(this).val();

        //    if (TYPEOFSINGNAL != TYPEOFSINGNALEnum) {
        //        $("#Zone").attr('readonly', 'readonly');
        //        $("#Zone").addClass("form-control");
        //        $("#Zone_Label").html("Zone");
        //    } else {
        //        $("#Zone").removeAttr('readonly');
        //        $("#Zone").removeClass("form-control");
        //        $("#Zone_Label").html("Zone (*)");
        //    }
        //});

        //var _TYPEOFSINGNAL = $("#TYPEOFSINGNAL").val();
        //if (_TYPEOFSINGNAL != TYPEOFSINGNALEnum) {
        //    $("#Zone").attr('readonly', 'readonly');
        //    $("#Zone").addClass("form-control");
        //    $("#Zone_Label").html("Zone");
        //} else {
        //    $("#Zone").removeAttr('readonly');
        //    $("#Zone").removeClass("form-control");
        //    $("#Zone_Label").html("Zone (*)");
        //}

        @*var _TYPEOFDEVICEID = $("#TYPEOFDEVICEID").val();
        if (_TYPEOFDEVICEID === '@ViewBag.IDTypeOfDevice') {
            $("#labelCameraMonitorId").html("Camera quan sát");
            $("#CameraMonitorIdValidate").html("");
            $("#CameraMonitorId").attr('readonly', 'readonly');
            $("#CameraMonitorId").attr('disabled', 'disabled');
        }
        else {
            $("#labelCameraMonitorId").html("Camera quan sát (*)");
            $("#CameraMonitorId").removeAttr('readonly');
            $("#CameraMonitorId").removeAttr('disabled');
        }*@

        @*$("#TYPEOFDEVICEID").change(function () {
            var TYPEOFDEVICEID = this.value;
            if (TYPEOFDEVICEID === '@ViewBag.IDTypeOfDevice') {
                $("#labelCameraMonitorId").html("Camera quan sát");
                $("#CameraMonitorIdValidate").html("");
                $("#CameraMonitorId").attr('readonly', 'readonly');
                $("#CameraMonitorId").attr('disabled', 'disabled');
                $("#Preset").attr('disabled', 'disabled');
            }
            else {
                $("#labelCameraMonitorId").html("Camera quan sát (*)");
                $("#CameraMonitorId").removeAttr('readonly');
                $("#CameraMonitorId").removeAttr('disabled');
                $("#Preset").removeAttr('disabled');
            }
        });*@

        $("#CameraMonitorId").change(function () {
            var CameraMonitor = this.value;
            var html = '<option value="-1">---Chọn---</option>';
            for (var i = 0; i < presets.length; i++) {
                if (presets[i].Id == CameraMonitor) {
                    for (var j = 0; j < presets[i].ListPreset.length; j++) {
                        html += '<option value="' + presets[i].ListPreset[j] + '">' + presets[i].ListPreset[j] +'</option>';
                    }
                }
            }

            $("#Preset").html(html);
        });

    $("#TYPEOFDEVICEID").change();
</script>

    <script type="text/javascript">
        $(document).ready(function () {
            let fieldIndex = 0;
            let data = [
                { value: "AMBULANCE", text: "Xe cứu thương" },
                { value: "BICYCLE", text: "Xe đạp" },
                { value: "CAR", text: "Ô tô" },
                { value: "CEMENT_TRUCK", text: "Ô tô trộn bê tông" },
                { value: "GT_3T5_TRUCK", text: "Xe tải trên 3.5 tấn" },
                { value: "LTE_3T5_TRUCK", text: "Xe tải dưới hoặc bằng 3.5 tấn" },
                { value: "MINI_TRUCK", text: "Xe tải nhỏ" },
                { value: "MINI_VAN", text: "Xe van nhỏ" },
                { value: "MOTORCYCLE", text: "Xe gắn máy" },
                { value: "PASSENGER16", text: "Xe ô tô chờ người 16 chỗ" },
                { value: "PASSENGER30", text: "Xe ô tô chờ người 30 chỗ" },
                { value: "PASSENGER50", text: "Xe ô tô chờ người trên 30 chỗ" },
                { value: "PICKUP", text: "Xe bán tải" },
                { value: "SEMI_TRUCK", text: "Xe đầu kéo" },
                { value: "SEMI_TRUCK_W_TRAILER", text: "Xe đầu kéo có rơ moóc" },
                { value: "SPECIALIZED_TRUCK", text: "Xe chuyên dùng" },
                { value: "TANKER_TRUCK", text: "Ô tô xi téc" },
                { value: "TOW_TRUCK", text: "Xe kéo" },

                { value: "SPECIAL_PURPOSE_MOTORCYCLE", text: "Xe máy chuyên dùng" },
                { value: "TRAILER_OR_SEMI_TRAILER", text: "Rơ moóc hoặc sơ mi rơ moóc được kéo bởi xe ô tô" },
                { value: "TWO_WHEEL_MOTORBIKE", text: "Xe mô tô hai bánh" },
                { value: "THREE_WHEEL_MOTORBIKE", text: "Xe mô tô ba bánh" },
                { value: "BUS", text: "Xe ô tô buýt" },
                { value: "MORTAR_MIXER_VEHICLE", text: "Ô tô trộn vữa" },
                { value: "TRAILER_TRUCK", text: "Ô tô kéo rơ moóc" },
                { value: "CAR_PULLING_OTHER_VEHICLE", text: "Ô tô kéo xe khác" },

                { value: "UNDEFINED", text: "Xe Khác" }
            ];
            // Hàm thêm ComboBox và Input mới
            function addDynamicField(vehicle = "", speed = "") {
                fieldIndex++;
                let fieldHtml = `<div class="form-group row dynamic-field" data-index="${fieldIndex}">
                                            <div class="col-md-7">
                                                        <select class="form-control required combobox cbbVehicleType" onchange="checkValueExistsChange(this)" name="${fieldIndex}_VehicleType">`;
                if (vehicle == "" && speed == "") {
                    fieldHtml += `<option disabled selected value="">Chọn phương tiện</option>`;
                } else {
                    fieldHtml += `<option disabled value="">Chọn phương tiện</option>`;
                }

                $.each(data, function (index, item) {
                    if (vehicle && item.value == vehicle) {
                        fieldHtml += `<option selected value="${item.value}">${item.text}</option>`;
                    } else {
                        fieldHtml += `<option value="${item.value}">${item.text}</option>`;
                    }
                });
                fieldHtml += `
                                                </select>
                                            </div>
                                            <div class="col-md-3">
                                                                            <input type="number" required value="${speed}" class="form-control" name="${fieldIndex}_SpeedLimited">
                                            </div>
                                                    <div class="col-md-2">
                                                                <button type="button" class="btn btn-danger remove-field-btn">Xóa</button>
                                                            </div>
                                            </div>
                                        `;

                $('#dynamic-fields-container').append(fieldHtml);
            }

            // Sự kiện nhấn nút "Thêm"
            $('#add-field-btn').click(function () {
                addDynamicField();
            });

            var speedLimitVehicleType = @Html.Raw(Newtonsoft.Json.JsonConvert.SerializeObject(ViewBag.SpeedLimitVehicleType));
            if (speedLimitVehicleType) {
                $.each(speedLimitVehicleType, function (index, item) {
                    addDynamicField(item.VehicleType, item.SpeedLimited);
                });
            }

            // Sự kiện nhấn nút "Xóa"
            $('#dynamic-fields-container').on('click', '.remove-field-btn', function () {
                $(this).closest('.dynamic-field').remove();
            });
        });

        function checkValueExistsChange(e) {
            let selectedValues = [];
            $('.cbbVehicleType').each(function () {
                let value = $(this).val();
                if (value && selectedValues.includes(value)) {
                    alert('Giá trị này đã được chọn!');
                    $(e).val('');
                } else {
                    selectedValues.push(value);
                }
            });
        }
    </script>
}