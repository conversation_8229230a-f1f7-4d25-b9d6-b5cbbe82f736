﻿@model PSafe.AM.Models.LocationModel

@{
    Layout = null;
}

<div class="modal-dialog">
    <div class="modal-content">
        <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
            <h4 class="modal-title">@PSafe.AM.Resources.Resource.Delete @PSafe.AM.Resources.Resource.Location</h4>
            <h3>Bạn có thật sự muốn xóa location này?</h3>
        </div>
        <div class="modal-body">
            <dl class="dl-horizontal">
                <dt>
                    @Html.DisplayNameFor(model => model.LocationName)
                </dt>
                <dd>
                    @Html.DisplayFor(model => model.LocationName)
                </dd>
                <dt>
                    @Html.DisplayNameFor(model => model.Map)
                </dt>
                <dd>
                    @Html.DisplayFor(model => model.Map)
                </dd>
                <dt>
                    @Html.DisplayNameFor(model => model.Description)
                </dt>
                <dd>
                    @Html.DisplayFor(model => model.Description)
                </dd>
                <dt>
                    @Html.DisplayNameFor(model => model.Actived)
                </dt>
                <dd>
                    @Html.DisplayFor(model => model.Actived)
                </dd>
                <dt>
                    @Html.DisplayNameFor(model => model.CreatedDate)
                </dt>
                <dd>
                    @Html.DisplayFor(model => model.CreatedDate, "{0:MM-dd-yyyy}")
                </dd>
                <dt>
                    @Html.DisplayNameFor(model => model.CreatedBy)
                </dt>
                <dd>
                    @ViewBag.CREATEDUSER
                </dd>
                <dt>
                    @Html.DisplayNameFor(model => model.UpdatedDate)
                </dt>
                <dd>
                    @Html.DisplayFor(model => model.UpdatedDate, "{0:MM-dd-yyyy}")
                </dd>
                <dt>
                    @Html.DisplayNameFor(model => model.UpdatedBy)
                </dt>
                <dd>
                    @ViewBag.UPDATEDUSER
                </dd>
            </dl>
            @using (Html.BeginForm("DeleteLocationConfirmed", "Areas", FormMethod.Post, new { @class = "form-horizontal", role = "form" }))
            //@using (Ajax.BeginForm("DeleteLocationConfirmed", "Areas", null, new AjaxOptions { HttpMethod = "Post", OnSuccess = "DeleteLocationSuccess" }, new { @class = "form-horizontal", role = "form" }))
            {
                @Html.AntiForgeryToken()
                <div class="form-actions no-color">
                    @Html.HiddenFor(x => x.LocationId)
                    @Html.HiddenFor(x => x.AreaId)
                    <input type="submit" value="@PSafe.AM.Resources.Resource.Delete" class="btn btn-danger" /> |
                    @Html.ActionLink("Hủy", "Edit", null, new { data_dismiss = "modal" })
                </div>
            }
        </div>
    </div>
</div>

@section Styles {
    <environment names="Development,Staging,Production">
        <link rel="stylesheet" href="~/lib/toastr/toastr.min.css" />
        <link rel="stylesheet" href="~/lib/datatables/datatables.min.css" />
    </environment>
}

@section Scripts {
    <environment names="Development,Staging,Production">
        <script src="~/lib/validate/jquery.validate.min.js"></script>
        <script src="~/lib/toastr/toastr.js"></script>
        <script src="~/lib/toastr/toastr.min.js"></script>
        <script src="~/lib/dataTables/datatables.min.js"></script>
    </environment>

    @if (!string.IsNullOrEmpty(ViewBag.Type) && ViewBag.Type == "warning")
    {
        <script type="text/javascript">
            toastr.warning('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Status) && ViewBag.Type == "error")
    {
        <script type="text/javascript">
            toastr.error('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Status) && ViewBag.Type == "success")
    {
        <script type="text/javascript">
            toastr.success('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
}


