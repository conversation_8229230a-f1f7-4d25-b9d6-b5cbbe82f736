﻿@model PSafe.AM.Models.LocationModel

@{
    ViewBag.Title = "Xóa";
}
<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-lg-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>@PSafe.AM.Resources.Resource.Delete @PSafe.AM.Resources.Resource.Location</h5>
                    <div class="ibox-tools">
                        @Html.ActionLink(PSafe.AM.Resources.Resource.BackToList, "Index", "Locations", null, new { @class = "btn btn-primary btn-xs" })
                    </div>
                </div>
                <div class="ibox-content">
                    @using (Html.BeginForm("DeleteConfirmed", "Locations", new { id = Model.LocationId }, FormMethod.Post))
                    {

                        @Html.AntiForgeryToken()
                    <div class="row">
                        <div class="col-lg-6">
                            <div class="form-group row">
                                <div class="col-md-4">
                                    <strong>
                                        @Html.DisplayNameFor(model => model.AreaId)
                                    </strong>
                                </div>
                                <div class="col-md-8">
                                    @ViewBag.AreaName
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                <div class="col-md-4">
                                    <strong>
                                        @Html.DisplayNameFor(model => model.Map)
                                    </strong>
                                </div>
                                <div class="col-md-8">
                                    @Html.DisplayFor(model => model.Map)
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                <div class="col-md-4">
                                    <strong>
                                        @Html.DisplayNameFor(model => model.LATITUDE)
                                    </strong>
                                </div>
                                <div class="col-md-8">
                                    @Html.DisplayFor(model => model.LATITUDE)
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                <div class="col-md-4">
                                    <strong>
                                        @Html.DisplayNameFor(model => model.LONGITUDE)
                                    </strong>
                                </div>
                                <div class="col-md-8">
                                    @Html.DisplayFor(model => model.LONGITUDE)
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                <div class="col-md-4">
                                    <strong>
                                        @Html.DisplayNameFor(model => model.CreatedDate)
                                    </strong>
                                </div>
                                <div class="col-md-8">
                                    @Html.DisplayFor(model => model.CreatedDate, "{0:MM-dd-yyyy}")
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                <div class="col-md-4">
                                    <strong>
                                        @Html.DisplayNameFor(model => model.CreatedBy)
                                    </strong>
                                </div>
                                <div class="col-md-8">
                                    @ViewBag.CREATEDUSER
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                <div class="col-md-4">
                                    <strong>
                                        @Html.DisplayNameFor(model => model.UpdatedDate)
                                    </strong>
                                </div>
                                <div class="col-md-8">
                                    @Html.DisplayFor(model => model.UpdatedDate, "{0:MM-dd-yyyy}")
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                <div class="col-md-4">
                                    <strong>
                                        @Html.DisplayNameFor(model => model.UpdatedBy)
                                    </strong>
                                </div>
                                <div class="col-md-8">
                                    @ViewBag.UPDATEDUSER
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group row">
                                <div class="col-md-4">
                                    <strong>
                                        @Html.DisplayNameFor(model => model.Description)
                                    </strong>
                                </div>
                                <div class="col-md-8">
                                    @Html.DisplayFor(model => model.Description)
                                </div>
                            </div>
                        </div>
                    </div>

                        <input type="button" onclick="DeleteLocation(@Model.LocationId);" value="@PSafe.AM.Resources.Resource.Delete" class="btn btn-danger" />
                        @Html.ActionLink(PSafe.AM.Resources.Resource.Cancel, "Index", "Locations", null, new { @class = "btn btn-white" })

                        <button type="button" hidden id="dialogDelete" name="dialogDelete" class="btn btn-primary" data-toggle="modal" data-target="#exampleModal"></button>

                        <div class="modal fade" id="exampleModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
                            <div class="modal-dialog" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h2 class="modal-title text-warning" id="exampleModalLabel"><strong>Cảnh báo!</strong></h2>
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                            <span aria-hidden="true">&times;</span>
                                        </button>
                                    </div>
                                    <div class="modal-body">
                                        <p>Có thiết bị thuộc Vị trí này! <br /> <strong>Bạn chắc chắn muốn xóa không?</strong></p>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="submit" id="btnDeleteLocation" class="btn btn-primary">Có</button>
                                        <button type="button" class="btn btn-white" data-dismiss="modal">@PSafe.AM.Resources.Resource.Cancel</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <environment names="Development,Staging,Production">
        <link rel="stylesheet" href="~/lib/toastr/toastr.min.css" />
    </environment>
}

@section Scripts {
    <environment names="Development,Staging,Production">
        <script src="~/lib/validate/jquery.validate.min.js"></script>
        <script src="~/lib/toastr/toastr.js"></script>
        <script src="~/lib/toastr/toastr.min.js"></script>
    </environment>

    @if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "warning")
    {
        <script type="text/javascript">
            toastr.warning('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "error")
    {
        <script type="text/javascript">
            toastr.error('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "success")
    {
        <script type="text/javascript">
            toastr.success('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }

    <script type="text/javascript">
        function DeleteLocation(id) {
            $.ajax({
                type: "GET",
                url: '@Url.Action("CountDeviceOfLocation", "Devices")',
                data: { id : id },
                dataType: "Json",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    if (data > 0) {
                        $('#dialogDelete').click();
                    } else {
                        $('#btnDeleteLocation').click();
                    }
                },
                error: function () {
                    alert("Không lấy được giá trị!");
                }
            });
        }
    </script>
}