﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="ACTIVE_NEW_CARD" xml:space="preserve">
    <value>Kích hoạt thẻ mới</value>
  </data>
  <data name="ADD_CARD_LOST_INFO" xml:space="preserve">
    <value>Thêm thông báo mất thẻ</value>
  </data>
  <data name="ADD_FREQUENT_GUEST" xml:space="preserve">
    <value>Thêm khách thường xuyên</value>
  </data>
  <data name="ADD_INTERNAL_EMPLOYEE" xml:space="preserve">
    <value>Thêm nhân viên nội bộ</value>
  </data>
  <data name="ADD_NONRESIDENT_GUEST" xml:space="preserve">
    <value>Thêm khách vãng lai</value>
  </data>
  <data name="ADD_SECURITY_VIOLATION" xml:space="preserve">
    <value>Thêm vi phạm an ninh</value>
  </data>
  <data name="ALLOW_EDIT_INFORMATION" xml:space="preserve">
    <value>Cho phép sửa các thông tin trừ thay đổi loại phương tiện, Cổng vào ra</value>
  </data>
  <data name="ALLOW_LICENSING_FREQUENT_GUEST" xml:space="preserve">
    <value>Cấp phép khách thường xuyên</value>
  </data>
  <data name="BACKUP_RESTORE" xml:space="preserve">
    <value>Backup, restore</value>
  </data>
  <data name="CANCEL_FORBID_INOUT" xml:space="preserve">
    <value>Hủy cấm vào ra</value>
  </data>
  <data name="CANCEL_INOUT_INTERNAL_EMPLOYEE" xml:space="preserve">
    <value>Hủy quyền ra vào nhân viên nội bộ nghỉ việc</value>
  </data>
  <data name="CANCEL_LICENSING_FREQUENT_GUEST" xml:space="preserve">
    <value>Hủy thẻ/vân tay/khuôn mặt khách thường xuyên</value>
  </data>
  <data name="CANCEL_LICENSING_INOUT_SIGNIFICANT_AREA" xml:space="preserve">
    <value>Hủy cấp phép vào ra khu trọng yếu</value>
  </data>
  <data name="CANCEL_LICENSING_INTERNAL_EMPLOYEE" xml:space="preserve">
    <value>Hủy thẻ/vân tay/khuôn mặt nhân viên nội bộ</value>
  </data>
  <data name="CANCEL_LICENSING_NONRESIDENT_GUEST" xml:space="preserve">
    <value>Hủy thẻ/vân tay/khuôn mặt khách vãng lai</value>
  </data>
  <data name="CANCEL_TEMPORARY_STOP_INOUT" xml:space="preserve">
    <value>Hủy tạm ngưng ra vào</value>
  </data>
  <data name="CHANGE_CUSTOMER_TYPE" xml:space="preserve">
    <value>Thay đổi loại khách hàng</value>
  </data>
  <data name="DELETE_CARD" xml:space="preserve">
    <value>Xóa thẻ</value>
  </data>
  <data name="DELETE_FREQUENT_GUEST" xml:space="preserve">
    <value>Xóa khách thường xuyên</value>
  </data>
  <data name="DELETE_INTERNAL_EMPLOYEE" xml:space="preserve">
    <value>Xóa nhân viên nội bộ</value>
  </data>
  <data name="DELETE_NONRESIDENT_GUEST" xml:space="preserve">
    <value>Xóa khách vãng lai</value>
  </data>
  <data name="DELETE_SECURITY_VIOLATION" xml:space="preserve">
    <value>Xóa vi phạm an ninh</value>
  </data>
  <data name="DISABLE_BARRIER" xml:space="preserve">
    <value>Mở xả, đóng hoàn toàn barrier</value>
  </data>
  <data name="EDIT_FREQUENT_GUEST" xml:space="preserve">
    <value>Sửa khách thường xuyên</value>
  </data>
  <data name="EDIT_INTERNAL_EMPLOYEE" xml:space="preserve">
    <value>Sửa nhân viên nội bộ</value>
  </data>
  <data name="EDIT_NONRESIDENT_GUEST" xml:space="preserve">
    <value>Sửa khách vãng lai</value>
  </data>
  <data name="FORBID_INOUT" xml:space="preserve">
    <value>Cấm vào ra</value>
  </data>
  <data name="FORCE_OPEN_BARRIER" xml:space="preserve">
    <value>Mở barier cưỡng bức</value>
  </data>
  <data name="IMPORT_FREQUENT_CURRENT" xml:space="preserve">
    <value>Import danh sách khách vãng lai</value>
  </data>
  <data name="IMPORT_FREQUENT_GUEST" xml:space="preserve">
    <value>Import danh sách khách thường xuyên</value>
  </data>
  <data name="IMPORT_FREQUENT_REGULAR" xml:space="preserve">
    <value>Import danh sách khách nhân viên</value>
  </data>
  <data name="LICENSING_FREQUENT_GUEST" xml:space="preserve">
    <value>Cấp thẻ/vân tay/khuôn mặt khách thường xuyên</value>
  </data>
  <data name="LICENSING_INOUT_SIGNIFICANT_AREA" xml:space="preserve">
    <value>Cấp phép vào ra khu trọng yếu</value>
  </data>
  <data name="LICENSING_INTERNAL_EMPLOYEE" xml:space="preserve">
    <value>Cấp thẻ/vân tay/khuôn mặt nhân viên nội bộ</value>
  </data>
  <data name="LICENSING_NONRESIDENT_GUEST" xml:space="preserve">
    <value>Cấp thẻ/vân tay/khuôn mặt khách vãng lai</value>
  </data>
  <data name="RESTORE_CARD_LOST" xml:space="preserve">
    <value>Hồi phục thẻ báo mất</value>
  </data>
  <data name="SETUP_GENERAL_DEVICE" xml:space="preserve">
    <value>Cấu hình chung thiết bị</value>
  </data>
  <data name="SETUP_GENERAL_FEATURE" xml:space="preserve">
    <value>Cấu hình chung tính năng</value>
  </data>
  <data name="SETUP_LANE_DEVICE" xml:space="preserve">
    <value>Cấu hình thiết bị từng làn</value>
  </data>
  <data name="SETUP_LANE_INOUT_OBJECT" xml:space="preserve">
    <value>Cấu hình làn cho phép chọn loại khách vào ra</value>
  </data>
  <data name="SETUP_SYS" xml:space="preserve">
    <value>Cấu hình hệ thống</value>
  </data>
  <data name="TEMPORARY_STOP_INOUT" xml:space="preserve">
    <value>Tạm ngưng ra vào</value>
  </data>
  <data name="UPDATE_LIST_INFO" xml:space="preserve">
    <value>Cập nhật danh mục</value>
  </data>
  <data name="VIEWCUSTOMERSCIRCULATE" xml:space="preserve">
    <value>Xem khách hàng vảng lai</value>
  </data>
  <data name="VIEWCUSTOMERSREGULAR" xml:space="preserve">
    <value>Xem khách hàng thường xuyên</value>
  </data>
  <data name="VIEWINTERNALSTAFF" xml:space="preserve">
    <value>Xem nhân viên nội bộ</value>
  </data>
  <data name="VIEW_LIST_CARD_LOST" xml:space="preserve">
    <value>Xem danh sách thông báo mất thẻ</value>
  </data>
  <data name="VIEW_LOG" xml:space="preserve">
    <value>Xem log</value>
  </data>
</root>