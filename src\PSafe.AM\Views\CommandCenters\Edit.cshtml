﻿@model PSafe.AM.Models.CommandCenterModel
@{
    ViewBag.Title = "Hiệu chỉnh";

}

<environment names="Development">
    <script src="~/lib/jquery/dist/jquery.js"></script>
    <script src="~/lib/validate/jquery.validate.min.js"></script>
    <script src="~/js/script.js" asp-append-version="true"></script>
</environment>
<environment names="Staging,Production">
    <script src="~/lib/jquery/dist/jquery.js"></script>
    <script src="~/lib/validate/jquery.validate.min.js"></script>
    <script src="~/js/script.js" asp-append-version="true"></script>
</environment>

<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-lg-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>@PSafe.AM.Resources.Resource.Edit @PSafe.AM.Resources.Resource.CommandCenter</h5>
                    <div class="ibox-tools">
                        @Html.ActionLink(PSafe.AM.Resources.Resource.BackToList, "Index", "CommandCenters", null, new { @class = "btn btn-primary btn-xs" })
                    </div>
                </div>
                <div class="ibox-content">

                    @using (Html.BeginForm())
                    {
                        @Html.AntiForgeryToken()
                        @Html.ValidationSummary(true)
                        @Html.HiddenFor(model => model.CommandCenterID)

                        <div class="row">
                            <div class="col-lg-6">
                                <div class="form-group row">
                                    @Html.LabelFor(model => model.CenterName, new { @class = "control-label col-md-4" })
                                    <div class="col-md-8">
                                        @Html.EditorFor(model => model.CenterName)
                                        @Html.ValidationMessageFor(model => model.CenterName, null, new { @class = "text-danger" })
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-6">
                                <div class="form-group row">
                                    @Html.LabelFor(model => model.Representative, new { @class = "control-label col-md-4" })
                                    <div class="col-md-8">
                                        @Html.EditorFor(model => model.Representative)
                                        @Html.ValidationMessageFor(model => model.Representative, null, new { @class = "text-danger" })
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group row">
                                    @Html.LabelFor(model => model.Address, new { @class = "control-label col-md-4" })
                                    <div class="col-md-8">
                                        @Html.EditorFor(model => model.Address)
                                        @Html.ValidationMessageFor(model => model.Address, null, new { @class = "text-danger" })
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group row">
                                    @Html.LabelFor(model => model.Email, new { @class = "control-label col-md-4" })
                                    <div class="col-md-8">
                                        @Html.EditorFor(model => model.Email)
                                        @Html.ValidationMessageFor(model => model.Email, null, new { @class = "text-danger" })
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group row">
                                    @Html.LabelFor(model => model.ContactPhone, new { @class = "control-label col-md-4" })
                                    <div class="col-md-8">
                                        @Html.EditorFor(model => model.ContactPhone)
                                        @Html.ValidationMessageFor(model => model.ContactPhone, null, new { @class = "text-danger" })
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group row">
                                    @Html.LabelFor(model => model.Fax, new { @class = "control-label col-md-4" })
                                    <div class="col-md-8">
                                        @Html.EditorFor(model => model.Fax)
                                        @Html.ValidationMessageFor(model => model.Fax, null, new { @class = "text-danger" })
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group row">
                                    @Html.LabelFor(model => model.Description, new { @class = "control-label col-md-4" })
                                    <div class="col-md-8">
                                        @Html.EditorFor(model => model.Description)
                                        @Html.ValidationMessageFor(model => model.Description, null, new { @class = "text-danger" })
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group row">
                                    @Html.LabelFor(model => model.Actived, new { @class = "control-label col-md-4" })
                                    <div class="col-md-8">
                                        @Html.EditorFor(model => model.Actived)
                                        @Html.ValidationMessageFor(model => model.Actived, null, new { @class = "text-danger" })
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-offset-2 col-md-10">
                                <input type="submit" value="@PSafe.AM.Resources.Resource.Save" class="btn btn-primary" />
                                @Html.ActionLink(PSafe.AM.Resources.Resource.Cancel, "Index", "CommandCenters", null, new { @class = "btn btn-white" })
                            </div>
                        </div>
                    }


                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <environment names="Development,Staging,Production">
        <link rel="stylesheet" href="~/lib/toastr/toastr.min.css" />
    </environment>
}

@section Scripts {
    <environment names="Development,Staging,Production">
        <script src="~/lib/validate/jquery.validate.min.js"></script>
        <script src="~/lib/toastr/toastr.js"></script>
        <script src="~/lib/toastr/toastr.min.js"></script>
    </environment>

    @if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "warning")
    {
        <script type="text/javascript">
            toastr.warning('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "error")
    {
        <script type="text/javascript">
            toastr.error('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
    else if (!string.IsNullOrEmpty(ViewBag.Value) && ViewBag.Type == "success")
    {
        <script type="text/javascript">
            toastr.success('@ViewBag.Status', '@ViewBag.Value')
        </script>
    }
}