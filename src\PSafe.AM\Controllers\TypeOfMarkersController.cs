﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PSafe.AM.Common;
using PSafe.AM.Models;
using PSafe.Common;
using PSafe.Common.UserEnums;
using PSafe.Core.Domains;
using PSafe.Core.Interface;
using static PSafe.Common.CommonEnums;

namespace PSafe.AM.Controllers
{
    [AuthorizeActionFilter(EPERMISSIONS_AM.categoryManage)]
    public class TypeOfMarkersController : Controller
    {
        private readonly ITypeOfMarkerRepository _typeOfMarkerRepository;
        private readonly IUserRepository _userRepository;
        private readonly IMapper _mapper;
        private readonly IHistorySystemRepository _historySystemRepository;
        private readonly IHttpContextAccessor _accessor;
        private readonly IConfiguration _configuration;
        private readonly ILogger<TypeOfMarkersController> _logger;

        public TypeOfMarkersController(
            ITypeOfMarkerRepository typeOfMarkerRepository,
            IUserRepository userRepository,
            IMapper mapper,
            IHistorySystemRepository historySystemRepository,
            IHttpContextAccessor accessor,
            ILogger<TypeOfMarkersController> logger,
            IConfiguration configuration)
        {
            _typeOfMarkerRepository = typeOfMarkerRepository;
            _userRepository = userRepository;
            _historySystemRepository = historySystemRepository;
            _mapper = mapper;
            _historySystemRepository = historySystemRepository;
            _accessor = accessor;
            _configuration = configuration;
            _logger = logger;
        }

        public ActionResult Index()
        {
            StatusQuery Notification;
            List<TypeOfMarkerModel> listTypeOfMarkerModel = new List<TypeOfMarkerModel>();
            try
            {
                var _typeOfMarke = _typeOfMarkerRepository.GetAll().ToList();

                listTypeOfMarkerModel = _mapper.Map<List<TypeOfMarker>, List<TypeOfMarkerModel>>(_typeOfMarke);

                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("TypeOfMarkers/Index: " + ex.Message);

                Notification = new StatusQuery("error", "Thất bại!", "Xem danh sách");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;
            }

            return View(listTypeOfMarkerModel);
        }

        public ActionResult Details(int id)
        {
            try
            {
                var _typeOfMarker = _typeOfMarkerRepository.GetById(id);

                var _typeOfMarkerModel = _mapper.Map<TypeOfMarker, TypeOfMarkerModel>(_typeOfMarker);

                if (_typeOfMarkerModel != null)
                {
                    try
                    {
                        if (TempDataHelper.Get<StatusQuery>(TempData, "Notification") is StatusQuery Notification)
                        {
                            ViewBag.Status = Notification.Status;
                            ViewBag.Value = Notification.Value;
                            ViewBag.Type = Notification.Type;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError("typeOfMarkers/Details: " + ex.Message);
                        Console.WriteLine(ex.Message);
                    }

                    return View(_typeOfMarkerModel);
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                    return RedirectToAction("Index");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("typeOfMarkers/Details: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                return RedirectToAction("Index");
            }
        }

        public ActionResult Create()
        {
            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create([Bind("Name, Description, ImageFile")] TypeOfMarkerModel typeOfMarkerModel)
        {
            StatusQuery Notification;

            if (typeOfMarkerModel.ImageFile == null)
            {
                ModelState.AddModelError("Icon", "Vui lòng chọn file");
            }

            ModelState.Remove("Icon");

            if (ModelState.IsValid)
            {
                try
                {
                    var systemUser = GetSesson();

                    if (systemUser == null)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                        return RedirectToAction("Logout", "Security");
                    }

                    if (typeOfMarkerModel.ImageFile != null && typeOfMarkerModel.ImageFile.FileName.Length > 0)
                    {
                        var fileName = DateTime.Now.Date.ToString("MM_dd_yyyy") + "_" + typeOfMarkerModel.ImageFile.FileName.Substring(typeOfMarkerModel.ImageFile.FileName.LastIndexOf("\\") + 1);
                        typeOfMarkerModel.Icon = fileName;

                        var userNameFTP = Utils.DecodePassword(_configuration.GetSection("FTP:UserName").Value, Utils.EncodeType.SHA_256);
                        var passwordFTP = Utils.DecodePassword(_configuration.GetSection("FTP:Password").Value, Utils.EncodeType.SHA_256);
                        var hostFTP = _configuration.GetSection("FTP:Host").Value + ":" + _configuration.GetSection("FTP:Port").Value;

                        byte[] fileBytes;

                        using (var ms = new MemoryStream())
                        {
                            typeOfMarkerModel.ImageFile.CopyTo(ms);
                            fileBytes = ms.ToArray();
                        }

                        using (var client = new WebClient())
                        {
                            client.Credentials = new NetworkCredential(userNameFTP, passwordFTP);
                            client.UploadData(hostFTP + "//Images//Icons//" + fileName, fileBytes);
                        }
                    }

                    var _typeOfMarker = _mapper.Map<TypeOfMarkerModel, TypeOfMarker>(typeOfMarkerModel);

                    _typeOfMarkerRepository.Insert(_typeOfMarker);

                    var statusInsert = _typeOfMarkerRepository.SaveChanges();

                    if (statusInsert > 0)
                    {
                        string StringDescription = new GetStringHistorySystem().Get(systemUser.UserName, Resources.Resource.Edit, _typeOfMarker.Name.ToString(), Resources.Resource.TypeOfMarker);

                        InsertHistorySystem((int)EACTION_TYPE.CREATE, (int)EnumControllerName.TYPE_OF_MARKER, StringDescription, null, _typeOfMarker);

                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Thêm mới thành công"));
                        return RedirectToAction("Index");
                    }
                    else
                    {
                        Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        return View(typeOfMarkerModel);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("typeOfMarkers/Create: " + ex.Message);

                    Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return View(typeOfMarkerModel);
                }
            }
            Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;

            return View(typeOfMarkerModel);
        }

        public ActionResult Edit(int id)
        {
            try
            {
                var _typeOfMarker = _typeOfMarkerRepository.GetById(id);

                var _typeOfMarkerModel = _mapper.Map<TypeOfMarker, TypeOfMarkerModel>(_typeOfMarker);

                if (_typeOfMarkerModel != null)
                {
                    return View(_typeOfMarkerModel);
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                    return RedirectToAction("Index");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("typeOfMarkers/Create: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                return RedirectToAction("Index");
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit([Bind("Id, Name, Description, ImageFile")] TypeOfMarkerModel typeOfMarkerModel)
        {
            StatusQuery Notification;
            ModelState.Remove("Icon");
            if (ModelState.IsValid)
            {
                try
                {
                    var systemUser = GetSesson();
                    if (systemUser == null)
                    {
                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                        return RedirectToAction("Logout", "Security");
                    }

                    var typeOfMarker = _typeOfMarkerRepository.GetById(typeOfMarkerModel.Id);

                    var _typeOfMarkerTemp = _mapper.Map<TypeOfMarker, TypeOfMarkerModel>(typeOfMarker);
                    var typeOfMarkerOld = _mapper.Map<TypeOfMarkerModel, TypeOfMarker>(_typeOfMarkerTemp);

                    if (typeOfMarkerModel.ImageFile != null && typeOfMarkerModel.ImageFile.FileName.Length > 0)
                    {
                        var fileName = DateTime.Now.Date.ToString("MM_dd_yyyy") + "_" + typeOfMarkerModel.ImageFile.FileName.Substring(typeOfMarkerModel.ImageFile.FileName.LastIndexOf("\\") + 1);
                        typeOfMarker.Icon = fileName;

                        var userNameFTP = Utils.DecodePassword(_configuration.GetSection("FTP:UserName").Value, Utils.EncodeType.SHA_256);
                        var passwordFTP = Utils.DecodePassword(_configuration.GetSection("FTP:Password").Value, Utils.EncodeType.SHA_256);
                        var hostFTP = _configuration.GetSection("FTP:Host").Value + ":" + _configuration.GetSection("FTP:Port").Value;

                        byte[] fileBytes;

                        using (var ms = new MemoryStream())
                        {
                            typeOfMarkerModel.ImageFile.CopyTo(ms);
                            fileBytes = ms.ToArray();
                        }

                        using (var client = new WebClient())
                        {
                            client.Credentials = new NetworkCredential(userNameFTP, passwordFTP);
                            client.UploadData(hostFTP + "//Images//Icons//" + fileName, fileBytes);
                        }
                    }

                    typeOfMarker.Name = typeOfMarkerModel.Name;
                    typeOfMarker.Description = typeOfMarkerModel.Description;

                    _typeOfMarkerRepository.Update(typeOfMarker);

                    var updateStatus = _typeOfMarkerRepository.SaveChanges();

                    if (updateStatus > 0)
                    {
                        string StringDescription = new GetStringHistorySystem().Get(systemUser.UserName, Resources.Resource.Edit, typeOfMarker.Name.ToString(), Resources.Resource.TypeOfMarker);
                        InsertHistorySystem((int)EACTION_TYPE.EDIT, (int)EnumControllerName.TYPE_OF_MARKER, StringDescription, typeOfMarkerOld, typeOfMarker);

                        TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Sửa thành công"));

                        return RedirectToAction("Index");
                    }
                    else
                    {
                        Notification = new StatusQuery("error", "", "Sửa thất bại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        return View(typeOfMarkerModel);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("typeOfMarkers/Edit: " + ex.Message);

                    Notification = new StatusQuery("error", "", "Sửa thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return View(typeOfMarkerModel);
                }
            }

            Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;

            return View(typeOfMarkerModel);
        }

        public ActionResult Delete(int id)
        {
            StatusQuery Notification;
            try
            {
                var _typeOfMarker = _typeOfMarkerRepository.GetById(id);

                var _typeOfMarkerModel = _mapper.Map<TypeOfMarker, TypeOfMarkerModel>(_typeOfMarker);

                if (_typeOfMarkerModel == null)
                {
                    Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return RedirectToAction("Index");
                }

                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }

                return View(_typeOfMarkerModel);
            }
            catch (Exception ex)
            {
                _logger.LogError("typeOfMarkers/Delete: " + ex.Message);

                Notification = new StatusQuery("warning", "", "Xóa thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;

                return RedirectToAction("Index");
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int id)
        {
            try
            {
                var systemUserSession = GetSesson();

                if (systemUserSession == null)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                    return RedirectToAction("Logout", "Security");
                }

                var typeOfMarker = _typeOfMarkerRepository.GetById(id);

                _typeOfMarkerRepository.Delete(typeOfMarker);

                var deleteStatus = _typeOfMarkerRepository.SaveChanges();

                if (deleteStatus > 0)
                {
                    string StringDescription = new GetStringHistorySystem().Get(systemUserSession.UserName, Resources.Resource.Delete, typeOfMarker.Name.ToString(), Resources.Resource.TypeOfMarker);

                    InsertHistorySystem((int)EACTION_TYPE.DELETE, (int)EnumControllerName.TYPE_OF_MARKER, StringDescription, typeOfMarker, null);

                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Xóa thành công"));
                    return RedirectToAction("Index");
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));
                    return RedirectToAction("Delete", "typeOfMarkers", new { id });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("typeOfMarkers/DeleteConfirmed: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));
                return RedirectToAction("Delete", "typeOfMarkers", new { id });
            }
        }

        public User GetSesson()
        {
            var SessionUserSystem = HttpContext.Session.GetComplexData<User>("SessionUserSystemId_" + HttpContext.Session.GetString("SessionUserSystemId"));
            if (SessionUserSystem != null)
            {
                var _user = _userRepository.GetById(SessionUserSystem.Id);

                if (_user != null)
                {
                    if (!_user.Actived)
                    {
                        HttpContext.Session.Clear();
                        return null;
                    }
                    return _user;
                }
            }
            return null;
        }

        private void InsertHistorySystem(int action_type, int controllerName, string description, object OldModel, object NewModel)
        {
            try
            {
                var ipAddress = _accessor.HttpContext.Connection.RemoteIpAddress.ToString();

                string jsonOldObject = Newtonsoft.Json.JsonConvert.SerializeObject(OldModel);
                string jsonNewObject = Newtonsoft.Json.JsonConvert.SerializeObject(NewModel);

                HistorySystem history = new HistorySystem
                {
                    ActionType = action_type,
                    ActionTime = DateTime.Now,
                    Description = description,
                    OldObject = jsonOldObject,
                    NewObject = jsonNewObject,
                    UserId = int.Parse(HttpContext.Session.GetString("SessionUserSystemId")),
                    IpAddress = ipAddress,
                    ControllerName = controllerName
                };

                _historySystemRepository.Insert(history);

                _historySystemRepository.SaveChanges();
            }
            catch (Exception ex)
            {
                _logger.LogError("typeOfMarker/InsertHistorySystem: " + ex.Message);
            }
        }
    }
}