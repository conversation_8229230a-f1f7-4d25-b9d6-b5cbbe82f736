﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Logging;
using PSafe.AM.Common;
using PSafe.AM.Models;
using PSafe.Common.UserEnums;
using PSafe.Core.Domains;
using PSafe.Core.Interface;
using PSafe.Core.SharedKernel;
using static PSafe.Common.CommonEnums;

namespace PSafe.AM.Controllers
{
    [AuthorizeActionFilter(EPERMISSIONS_AM.categoryManage)]
    public class SiteController : Controller
    {
        private readonly ISiteRepository _siteRepository;
        private readonly IUserRepository _userRepository;
        private readonly IMapper _mapper;
        private readonly IHistorySystemRepository _historySystemRepository;
        private readonly IHttpContextAccessor _accessor;
        private readonly ILogger<SiteController> _logger;

        public SiteController(IUnitOfWork unitOfWork, IMapper mapper, IHttpContextAccessor accessor, ILogger<SiteController> logger)
        {
            _userRepository = unitOfWork.UserRepository;
            _mapper = mapper;
            _historySystemRepository = unitOfWork.HistorySystemRepository;
            _accessor = accessor;
            _logger = logger;
            _siteRepository = unitOfWork.SiteRepository;
        }

        public ActionResult Index()
        {
            StatusQuery Notification;
            List<SiteModel> _listsiteModel = new List<SiteModel>();
            try
            {
                var _listsite = _siteRepository.GetAll().ToList();

                _listsiteModel = _mapper.Map<List<Site>, List<SiteModel>>(_listsite);

                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Site/Index: " + ex.Message);

                Notification = new StatusQuery("error", "", "Xem danh sách thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;
            }

            return View(_listsiteModel);
        }

        public ActionResult Details(string id)
        {
            try
            {
                var _site = _siteRepository.GetById(id);

                if (TempDataHelper.Get<StatusQuery>(TempData, "Notification") is StatusQuery Notification)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }

                var _siteModel = _mapper.Map<Site, SiteModel>(_site);

                if (_siteModel != null)
                {
                    if (_site.CreatedUser.HasValue)
                    {
                        try
                        {
                            var createdName = _userRepository.GetById(_site.CreatedUser).UserName;
                            _siteModel.CreatedUserName = createdName;
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError("Site/Delete: " + ex.Message);
                        }
                    }
                    if (_site.UpdatedUser.HasValue)
                    {
                        try
                        {
                            var updatedName = _userRepository.GetById(_site.UpdatedUser).UserName;

                            _siteModel.UpdateUserName = updatedName;
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError("Site/Delete: " + ex.Message);
                        }
                    }
                    return View(_siteModel);
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                    return RedirectToAction("Index");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Site/Details: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                return RedirectToAction("Index");
            }
        }

        public ActionResult Create()
        {
            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create([Bind("SiteId, SiteName")] SiteModel siteModel)
        {
            StatusQuery Notification;
            if (ModelState.IsValid)
            {
                try
                {
                    var site = _siteRepository.GetById(siteModel.SiteId.Trim());

                    if(site == null)
                    {
                        var _site = _mapper.Map<SiteModel, Site>(siteModel);

                        _site.SiteId = siteModel.SiteId.Trim();
                        _site.SiteName = siteModel.SiteName;
                        _site.CreatedDate = DateTime.Now;
                        _site.UpdatedDate = DateTime.Now;
                        _site.UpdatedUser = int.Parse(HttpContext.Session.GetString("SessionUserSystemId"));
                        _site.CreatedUser = int.Parse(HttpContext.Session.GetString("SessionUserSystemId"));

                        _siteRepository.Insert(_site);

                        var statusInsert = _siteRepository.SaveChanges();

                        if (statusInsert > 0)
                        {
                            var systemUser = GetSesson();

                            if (systemUser == null)
                            {
                                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                                return RedirectToAction("Logout", "Security");
                            }

                            string StringDescription = new GetStringHistorySystem().Get(systemUser.UserName, Resources.Resource.Create, _site.SiteName.ToString(), Resources.Resource.Site);

                            InsertHistorySystem((int)EACTION_TYPE.CREATE, (int)EnumControllerName.SITE, StringDescription, null, _site);

                            TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Thêm mới thành công"));
                            return RedirectToAction("Index");
                        }
                        else
                        {
                            Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                            ViewBag.Status = Notification.Status;
                            ViewBag.Value = Notification.Value;
                            ViewBag.Type = Notification.Type;

                            return View(siteModel);
                        }
                    }
                    else
                    {
                        Notification = new StatusQuery("error", "", "SiteID đã tồn tại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        return View(siteModel);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("Site/Create: " + ex.Message);

                    Notification = new StatusQuery("error", "", "Thêm mới thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return View(siteModel);
                }
            }

            Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;
            return View(siteModel);
        }

        public ActionResult Edit(string id)
        {
            try
            {
                var _site = _siteRepository.GetById(id);
                if (_site == null)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));
                    return RedirectToAction("Index");
                }

                var _siteModel = _mapper.Map<Site, SiteModel>(_site);
                _siteModel.SiteId = _siteModel.SiteId.Trim();

                return View(_siteModel);
            }
            catch (Exception ex)
            {
                _logger.LogError("Site/Edit: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xem chi tiết thất bại"));

                return RedirectToAction("Index");
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit([Bind("SiteId, SiteName")] SiteModel siteModel)
        {
            StatusQuery Notification;
            if (ModelState.IsValid)
            {
                try
                {
                    var site = _siteRepository.GetById(siteModel.SiteId.Trim());

                    if(site != null)
                    {
                        var _siteTemp = _mapper.Map<Site, SiteModel>(site);
                        var siteOld = _mapper.Map<SiteModel, Site>(_siteTemp);

                        site.SiteName = siteModel.SiteName;
                        site.UpdatedDate = DateTime.Now;

                        _siteRepository.Update(site);

                        var updateStatus = _siteRepository.SaveChanges();

                        if (updateStatus > 0)
                        {
                            var systemUser = GetSesson();

                            if (systemUser == null)
                            {
                                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                                return RedirectToAction("Logout", "Security");
                            }

                            string StringDescription = new GetStringHistorySystem().Get(systemUser.UserName, Resources.Resource.Edit, site.SiteName.ToString(), Resources.Resource.Site);

                            InsertHistorySystem((int)EACTION_TYPE.EDIT, (int)EnumControllerName.SITE, StringDescription, siteOld, site);

                            TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Sửa thành công"));

                            return RedirectToAction("Index");
                        }
                        else
                        {
                            Notification = new StatusQuery("error", "", "Sửa thất bại");
                            ViewBag.Status = Notification.Status;
                            ViewBag.Value = Notification.Value;
                            ViewBag.Type = Notification.Type;

                            return View(siteModel);
                        }
                    }
                    else
                    {
                        Notification = new StatusQuery("error", "", "Không tìm thấy thông tin, vui lòng kiểm tra lại");
                        ViewBag.Status = Notification.Status;
                        ViewBag.Value = Notification.Value;
                        ViewBag.Type = Notification.Type;

                        return View(siteModel);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("Site/Edit: " + ex.Message);

                    Notification = new StatusQuery("error", "", "Sửa thất bại");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return View(siteModel);
                }

            }

            Notification = new StatusQuery("warning", "", "Vui lòng kiểm tra lại");
            ViewBag.Status = Notification.Status;
            ViewBag.Value = Notification.Value;
            ViewBag.Type = Notification.Type;

            return View(siteModel);
        }

        public ActionResult Delete(string id)
        {
            StatusQuery Notification;

            try
            {
                var _site = _siteRepository.GetById(id);

                var _siteModel = _mapper.Map<Site, SiteModel>(_site);

                if (_siteModel == null)
                {
                    Notification = new StatusQuery("warning", "", "Không tìm thấy Site");
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;

                    return RedirectToAction("Index");
                }
                else
                {
                    if (_site.CreatedUser.HasValue)
                    {
                        try
                        {
                            var createdName = _userRepository.GetById(_site.CreatedUser).UserName;
                            _siteModel.CreatedUserName = createdName;
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError("Site/Delete: " + ex.Message);
                        }
                    }
                    if (_site.UpdatedUser.HasValue)
                    {
                        try
                        {
                            var updatedName = _userRepository.GetById(_site.UpdatedUser).UserName;

                            _siteModel.UpdateUserName = updatedName;
                        }
                        catch(Exception ex)
                        {
                            _logger.LogError("Site/Delete: " + ex.Message);
                        }
                    }
                }

                Notification = TempDataHelper.Get<StatusQuery>(TempData, "Notification");

                if (Notification != null)
                {
                    ViewBag.Status = Notification.Status;
                    ViewBag.Value = Notification.Value;
                    ViewBag.Type = Notification.Type;
                }

                return View(_siteModel);
            }
            catch (Exception ex)
            {
                _logger.LogError("Site/Delete: " + ex.Message);

                Notification = new StatusQuery("warning", "", "Xóa thất bại");
                ViewBag.Status = Notification.Status;
                ViewBag.Value = Notification.Value;
                ViewBag.Type = Notification.Type;

                return RedirectToAction("Index");
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(string id)
        {
            try
            {
                var systemUser = GetSesson();

                if (systemUser == null)
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Phiên làm việc hết hạn, vui lòng đăng nhập lại"));
                    return RedirectToAction("Logout", "Security");
                }

                var site = _siteRepository.GetById(id);

                _siteRepository.Delete(site);

                var deleteStatus = _siteRepository.SaveChanges();

                if (deleteStatus > 0)
                {
                    string StringDescription = new GetStringHistorySystem().Get(systemUser.UserName, Resources.Resource.Delete, site.SiteName.ToString(), Resources.Resource.Site);

                    InsertHistorySystem((int)EACTION_TYPE.DELETE, (int)EnumControllerName.SITE, StringDescription, site, null);

                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("success", "", "Xóa thành công"));
                    return RedirectToAction("Index");
                }
                else
                {
                    TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));
                    return RedirectToAction("Delete", "Site", new { id });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Site/DeleteConfirmed: " + ex.Message);

                TempDataHelper.Put<StatusQuery>(TempData, "Notification", new StatusQuery("error", "", "Xóa thất bại"));
                return RedirectToAction("Delete", "Site", new { id });
            }
        }

        [NonAction]
        private SelectList ToSelectList(List<DropDownList> DropDownList)
        {
            List<SelectListItem> list = new List<SelectListItem>();

            foreach (var item in DropDownList)
            {
                list.Add(new SelectListItem()
                {
                    Text = item.Name,
                    Value = item.Id.ToString()
                });
            }

            return new SelectList(list, "Value", "Text");
        }

        public User GetSesson()
        {
            var SessionUserSystem = HttpContext.Session.GetComplexData<User>("SessionUserSystemId_" + HttpContext.Session.GetString("SessionUserSystemId"));
            if (SessionUserSystem != null)
            {
                var _user = _userRepository.GetById(SessionUserSystem.Id);

                if (_user != null)
                {
                    if (!_user.Actived)
                    {
                        HttpContext.Session.Clear();
                        return null;
                    }
                    return _user;
                }
            }
            return null;
        }

        private void InsertHistorySystem(int action_type, int controllerName, string description, object OldModel, object NewModel)
        {
            try
            {
                var ipAddress = _accessor.HttpContext.Connection.RemoteIpAddress.ToString();

                string jsonOldObject = Newtonsoft.Json.JsonConvert.SerializeObject(OldModel);
                string jsonNewObject = Newtonsoft.Json.JsonConvert.SerializeObject(NewModel);

                HistorySystem history = new HistorySystem
                {
                    ActionType = action_type,
                    ActionTime = DateTime.Now,
                    Description = description,
                    OldObject = jsonOldObject,
                    NewObject = jsonNewObject,
                    UserId = int.Parse(HttpContext.Session.GetString("SessionUserSystemId")),
                    IpAddress = ipAddress,
                    ControllerName = controllerName
                };

                _historySystemRepository.Insert(history);

                _historySystemRepository.SaveChanges();
            }
            catch (Exception ex)
            {
                _logger.LogError("Site/InsertHistorySystem: " + ex.Message);
            }
        }
    }
}
