﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace PSafe.AM.Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "15.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class Resource__area {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resource__area() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("PSafe.AM.Resources.Resource _area", typeof(Resource__area).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kích hoạt.
        /// </summary>
        public static string ACTIVED {
            get {
                return ResourceManager.GetString("ACTIVED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kích hoạt Email.
        /// </summary>
        public static string ACTIVEEMAIL {
            get {
                return ResourceManager.GetString("ACTIVEEMAIL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kích hoạt tin nhắn điện thoại.
        /// </summary>
        public static string ACTIVEMESSAGEPHONE {
            get {
                return ResourceManager.GetString("ACTIVEMESSAGEPHONE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Địa chỉ.
        /// </summary>
        public static string ADDRESS {
            get {
                return ResourceManager.GetString("ADDRESS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bản đồ khu vực.
        /// </summary>
        public static string AREABORDER {
            get {
                return ResourceManager.GetString("AREABORDER", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Khu vực.
        /// </summary>
        public static string AREAID {
            get {
                return ResourceManager.GetString("AREAID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tên khu vực.
        /// </summary>
        public static string BRANCHNAME {
            get {
                return ResourceManager.GetString("BRANCHNAME", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mã trung tâm chỉ huy.
        /// </summary>
        public static string COMMANDCENTREID {
            get {
                return ResourceManager.GetString("COMMANDCENTREID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Người liên hệ.
        /// </summary>
        public static string CONTACTPERSON {
            get {
                return ResourceManager.GetString("CONTACTPERSON", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Điện thoại.
        /// </summary>
        public static string CONTACTPHONE {
            get {
                return ResourceManager.GetString("CONTACTPHONE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ngày tạo.
        /// </summary>
        public static string CREATEDDATE {
            get {
                return ResourceManager.GetString("CREATEDDATE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Người tạo.
        /// </summary>
        public static string CREATEDUSER {
            get {
                return ResourceManager.GetString("CREATEDUSER", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mô tả.
        /// </summary>
        public static string DESCRIPTION {
            get {
                return ResourceManager.GetString("DESCRIPTION", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email.
        /// </summary>
        public static string EMAIL {
            get {
                return ResourceManager.GetString("EMAIL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fax.
        /// </summary>
        public static string FAX {
            get {
                return ResourceManager.GetString("FAX", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vĩ độ.
        /// </summary>
        public static string LATITUDE {
            get {
                return ResourceManager.GetString("LATITUDE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kinh độ.
        /// </summary>
        public static string LONGITUDE {
            get {
                return ResourceManager.GetString("LONGITUDE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Số điện thoại nhận tin.
        /// </summary>
        public static string MESSAGEPHONE {
            get {
                return ResourceManager.GetString("MESSAGEPHONE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ngày đăng ký.
        /// </summary>
        public static string REGISTEREDDATE {
            get {
                return ResourceManager.GetString("REGISTEREDDATE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Đại diện.
        /// </summary>
        public static string REPRESENTATIVE {
            get {
                return ResourceManager.GetString("REPRESENTATIVE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ngày cập nhật.
        /// </summary>
        public static string UPDATEDDATE {
            get {
                return ResourceManager.GetString("UPDATEDDATE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Người cập nhật.
        /// </summary>
        public static string UPDATEDUSER {
            get {
                return ResourceManager.GetString("UPDATEDUSER", resourceCulture);
            }
        }
    }
}
