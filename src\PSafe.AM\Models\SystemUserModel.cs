﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Mvc.Rendering;

namespace PSafe.AM.Models
{
    public class SystemUserModel
    {
        [Key]
        [Display(Name = "SystemUserId", ResourceType = typeof(Resources.Resource_systemUser))]
        public int SystemUserId { get; set; }

        [Required(ErrorMessage = "Tên đăng nhập không được trống!")]
        [StringLength(255, MinimumLength = 3, ErrorMessage = "Độ dài từ 3-255 ký tự!")]
        [Display(Name = "UserName", ResourceType = typeof(Resources.Resource_systemUser))]
        public string UserName { get; set; } = string.Empty;

        [DataType(DataType.Password)]
        [Display(Name = "Password", ResourceType = typeof(Resources.Resource_systemUser))]
        public string Password { get; set; } = string.Empty;

        [Required(ErrorMessage = "<PERSON><PERSON><PERSON> trị không được trống!")]
        [StringLength(255, MinimumLength = 3, ErrorMessage = "Độ dài từ 3-255 ký tự!")]
        [RegularExpression("^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+.[a-zA-Z0-9-.]+$", ErrorMessage = "Địa chỉ Email không hợp lệ!")]
        [Display(Name = "Email", ResourceType = typeof(Resources.Resource_systemUser))]
        public string Email { get; set; } = string.Empty;

        [Display(Name = "PasswordQuestion", ResourceType = typeof(Resources.Resource_systemUser))]
        public string PasswordQuestion { get; set; } = string.Empty;

        [Display(Name = "PasswordAnswer", ResourceType = typeof(Resources.Resource_systemUser))]
        public string PasswordAnswer { get; set; } = string.Empty;

        [DataType(DataType.Date)]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd}", ApplyFormatInEditMode = true)]
        [Display(Name = "CreationDate", ResourceType = typeof(Resources.Resource_systemUser))]
        public DateTime CreationDate { get; set; }

        [DataType(DataType.Date)]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd}", ApplyFormatInEditMode = true)]
        [Display(Name = "LastActivityDate", ResourceType = typeof(Resources.Resource_systemUser))]
        public DateTime LastActivityDate { get; set; }

        [DataType(DataType.Date)]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd}", ApplyFormatInEditMode = true)]
        [Display(Name = "LastLoginDate", ResourceType = typeof(Resources.Resource_systemUser))]
        public DateTime LastLoginDate { get; set; }

        [DataType(DataType.Date)]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd}", ApplyFormatInEditMode = true)]
        [Display(Name = "LastPasswordChangeDate", ResourceType = typeof(Resources.Resource_systemUser))]
        public DateTime LastPasswordChangeDate { get; set; }

        [Display(Name = "IsApproved", ResourceType = typeof(Resources.Resource_systemUser))]
        public bool IsApproved { get; set; }

        [Display(Name = "IsOnline", ResourceType = typeof(Resources.Resource_systemUser))]
        public bool IsOnline { get; set; }

        [Display(Name = "GroupID", ResourceType = typeof(Resources.Resource_systemUser))]
        public int? GroupID { get; set; }

        [Display(Name = "DepartmentId", ResourceType = typeof(Resources.Resource_systemUser))]
        public int? DepartmentId { get; set; }

        [Display(Name = "PositionId", ResourceType = typeof(Resources.Resource_systemUser))]
        public int? PositionId { get; set; }

        [Display(Name = "PhoneNumber", ResourceType = typeof(Resources.Resource_systemUser))]
        public string PhoneNumber { get; set; } = string.Empty;

        public SelectList ListGroups { get; set; }

        public SelectList ListDepartmentId { get; set; }

        public SelectList ListpositionId { get; set; }
    }
}